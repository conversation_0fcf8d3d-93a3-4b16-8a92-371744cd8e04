module.exports = {
  extends: ['@commitlint/config-conventional'],
  parserPreset: 'conventional-changelog-conventionalcommits',
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'build',
        'chore',
        'ci',
        'docs',
        'feat',
        'fix',
        'init',
        'perf',
        'refactor',
        'revert',
        'style',
        'test',
        'log',
      ],
    ],
  },
  prompt: {
    settings: {},
    messages: {
      skip: ':skip',
      max: '最多%d个字符',
      min: '最少%d个字符',
      emptyWarning: '必须输入描述',
      upperLimitWarning: '超出描述长度限制',
      lowerLimitWarning: '文字太少了，多写点',
    },
    questions: {
      type: {
        description: '选择要提交的更改类型，使用方向键选择:',
        enum: {
          feat: {
            description: '新特性,新功能',
            title: 'Features',
            emoji: '✨',
          },
          fix: {
            description: '修复bug',
            title: 'Bug Fixes',
            emoji: '🐛',
          },
          docs: {
            description: '文档修改',
            title: 'Documentation',
            emoji: '📚',
          },
          style: {
            description: '不影响代码含义的更改（空白、格式、缺少分号等）',
            title: 'Styles',
            emoji: '💎',
          },
          refactor: {
            description: '既不修复bug也不添加功能的代码更改',
            title: 'Code Refactoring',
            emoji: '📦',
          },
          perf: {
            description: '提高性能的代码更改',
            title: 'Performance Improvements',
            emoji: '🚀',
          },
          test: {
            description: '添加测试或修改现有测试',
            title: 'Tests',
            emoji: '🚨',
          },
          build: {
            description:
              '影响构建系统或外部依赖项的更改（示例范围：gulp、brocoli、npm）',
            title: 'Builds',
            emoji: '🛠',
          },
          ci: {
            description:
              '对CI配置文件和脚本的更改（示例范围：Travis、Circle、BrowserStack、SauceLabs）',
            title: 'Continuous Integrations',
            emoji: '⚙️',
          },
          chore: {
            description: '不修改src或测试文件的其他更改',
            title: 'Chores',
            emoji: '♻️',
          },
          revert: {
            description: '恢复以前的提交',
            title: 'Reverts',
            emoji: '🗑',
          },
          init: {
            description: '初始化项目',
            title: 'Initial',
            emoji: '✨',
          },
        },
      },
      scope: {
        description: '此更改的范围是什么（例如组件或文件名）',
      },
      subject: {
        description: '写一个简短的、命令式的描述',
      },
      body: {
        description: '提供更改的详细说明',
      },
      isBreaking: {
        description: '有什么突破性的变化吗？',
      },
      breakingBody: {
        description: '破坏性更改提交需要一个主体。请输入提交本身的较长描述',
      },
      breaking: {
        description: '描述突破性的变化',
      },
      isIssueAffected: {
        description: '此更改是否会影响其他未解决的问题?',
      },
      issuesBody: {
        description:
          '如果问题已解决，则提交需要一个主体。请输入提交本身的较长描述',
      },
      issues: {
        description: '添加问题参考（例如“fix 123”、“re 123”。）',
      },
    },
  },
}
