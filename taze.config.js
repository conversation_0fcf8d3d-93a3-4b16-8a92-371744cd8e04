import { defineConfig } from 'taze'

export default defineConfig({
  // ignore packages from bumping
  exclude: [
    'vue',
    'vue-template-compiler',
    'sinitek-css',
    'sinitek-message',
    'sinitek-ui',
    'sinitek-util',
    'sinitek-workflow',
    'sirmapp',
    'jquery',
  ],
  // fetch latest package info from registry without cache
  force: true,
  // write to package.json
  write: true,
  // run `npm install` or `yarn install` right after bumping
  install: false,
  // ignore paths for looking for package.json in monorepo
  ignorePaths: ['**/node_modules/**', '**/test/**'],
})
