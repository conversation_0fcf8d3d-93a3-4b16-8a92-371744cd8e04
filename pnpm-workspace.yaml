packages:
  - packages/*
catalog:
  '@babel/core': 7.26.10
  '@babel/eslint-parser': 7.26.10
  '@babel/generator': 7.26.10
  '@babel/parser': 7.26.10
  '@iconify/vue2': 2.1.0
  '@rspack/cli': 1.3.0
  '@rspack/core': 1.3.0
  '@unocss/eslint-config': 66.1.2
  '@unocss/postcss': 66.1.2
  '@unocss/preset-icons': 66.1.2
  '@unocss/preset-legacy-compat': 66.1.2
  '@unocss/preset-rem-to-px': 66.1.2
  '@unocss/reset': 66.1.2
  '@unocss/transformer-attributify-jsx': 66.1.2
  '@unocss/webpack': 66.1.2
  '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
  async-validator: 4.2.5
  babel-loader: 10.0.0
  core-js: 3.41.0
  echarts: 5.0.1
  element-ui: 2.15.5
  eslint: 9.14.0
  eslint-config-prettier: 9.1.0
  eslint-plugin-prettier: 5.2.1
  eslint-plugin-vue: 9.31.0
  lint-staged: 15.2.10
  lodash: 4.17.21
  monaco-editor: 0.52.2
  prettier: 3.3.3
  sass: 1.86.0
  sass-loader: 16.0.5
  sinitek-css: 7.4.564
  sinitek-message: 7.4.564
  sinitek-ui: 7.4.564
  sinitek-util: 7.4.564
  sinitek-workflow: 7.4.564
  sirmapp: 7.4.564
  unocss: 66.1.2
  vue: 2.6.14
  vue-loader: 15.10.0
  vue-template-compiler: 2.6.14
