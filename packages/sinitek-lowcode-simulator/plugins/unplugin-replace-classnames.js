const { createUnplugin } = require('unplugin')
const unplugin = createUnplugin(() => ({
  name: 'replace-classnames',
  enforce: 'pre',
  transformInclude(id) {
    return id.indexOf('.vue') > -1
  },
  transform(code) {
    const staticClassRegex = new RegExp(
      'class="([^"{]*\\b(?<!lc-)block\\b[^"}]*)"',
      'g'
    )
    const dynamicClassRegex = new RegExp(
      'class="{([^}]*)\\b(?<!lc-)block\\b:([^}]*)}',
      'g'
    )
    code = code.replace(staticClassRegex, (_, classNames) => {
      return `class="${classNames.replace('block', 'lc-block')}"`
    })
    code = code.replace(dynamicClassRegex, (_, before, after) => {
      return `class="{${before}"lc-block":${after}}`
    })
    return code
  },
  vite: {
    apply: 'build',
  },
}))

module.exports = {
  vitePlugin: unplugin.vite,
  webpackPlugin: unplugin.webpack,
}
