export default function ReplaceClassNameBlockToLcBlock() {
  return {
    name: 'replace-classnames',
    enforce: 'post',
    apply: 'build',
    transform(code, id) {
      if (!id.endsWith('.vue')) return code
      const staticClassRegex = new RegExp(
        'staticClass:"([^"]*block[^"]*)"',
        'g'
      )
      const dynamicClassRegex = new RegExp(
        'class:\\s*{([^}]*)block:([^}]*)}',
        'g'
      )
      code = code.replace(staticClassRegex, (_, classNames) => {
        return `staticClass:"${classNames.replace('block', 'lc-block')}"`
      })
      code = code.replace(dynamicClassRegex, (_, before, after) => {
        return `class: {${before}"lc-block":${after}}`
      })
      return code
    },
  }
}
