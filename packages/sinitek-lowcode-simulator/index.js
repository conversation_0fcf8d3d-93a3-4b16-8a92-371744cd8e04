import 'uno.css'
import Simulator from './src/simulator'
import _Canvas from './src/canvas/index.vue'
import Tooltip from './src/common/tooltip/tooltip.vue'
// 循环引用，先引入render ArraySetter ObjectSetter
// import { components } from '@settings'
// let registryComponents = [...components]

export * from '@utils'

export const LCTooltip = Tooltip

export const Canvas = _Canvas

function install(vue, options) {
  if (install.installed) return
  install.installed = true
  if (options?.popupManager) {
    window.__LC_popupManager__ = options.popupManager
  }
  vue.component(Simulator.name, Simulator)
  vue.component(Canvas.name, Canvas)
  vue.component(Tooltip.name, Tooltip)
  // registryComponents.forEach((e) => {
  //   vue.component(e.name, e)
  // })
}
Simulator.install = install

export default Simulator
