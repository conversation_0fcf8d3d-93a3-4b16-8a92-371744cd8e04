import { defineConfig } from 'vite'
import Vue2JSX from '@vitejs/plugin-vue2-jsx'
import Vue2 from '@vitejs/plugin-vue2'
import UnoCSS from 'unocss/vite'
import path from 'path'
import fs from 'node:fs'
import { NodeGlobalsPolyfillPlugin } from '@esbuild-plugins/node-globals-polyfill'
import { NodeModulesPolyfillPlugin } from '@esbuild-plugins/node-modules-polyfill'
import monacoEditorPlugin from 'vite-plugin-monaco-editor'
import ReplaceClassNameBlockToLcBlock from './plugins/vite-plugin-replace-classnames'
const srcDirs = fs.readdirSync(path.resolve(__dirname, './src/'))
export default defineConfig(({ mode }) => {
  const config = {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        'sinitek-lowcode-simulator': path.resolve(__dirname, 'index.js'),
      },
      extensions: ['.js', '.mts', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    },
    optimizeDeps: {
      esbuildOptions: {
        plugins: [
          NodeGlobalsPolyfillPlugin({
            process: true,
            buffer: true,
          }),
          NodeModulesPolyfillPlugin(),
        ],
      },
    },
  }
  config.plugins = [
    Vue2(),
    Vue2JSX(),
    UnoCSS(),
    ReplaceClassNameBlockToLcBlock(),
  ]
  config.resolve.alias['adaptor/element-ui'] = path.resolve(
    __dirname,
    `./src/adaptor/element-ui/vue2.7.js`
  )
  config.resolve.alias['@iconify/vue2'] = '@iconify/vue2/dist/iconify.js'
  config.resolve.alias['vue'] = path.resolve(
    __dirname,
    `./node_modules/vue/dist/vue.esm.js`
  )
  srcDirs.forEach((dir) => {
    config.resolve.alias['@' + dir] = path.resolve(__dirname, `./src/${dir}`)
  })
  if (mode !== 'development') {
    config.build = {
      lib: {
        entry: './index.js',
        name: 'SinitekLowcodeSimulator',
        formats: ['umd', 'esm'],
        filename: 'sinitek-lowcode-simulator',
      },
      sourcemap: mode === 'development',
      assetsDir: 'static',
      cssCodeSplit: true,
      rollupOptions: {
        // 确保外部化处理那些你不想打包进库的依赖
        external: [
          'vue',
          'core-js',
          'element-ui',
          'monaco-editor',
          'sinitek-ui',
          '@iconify/vue2',
          'sirmapp',
          /^sinitek-lowcode-.*/,
          /^prettier.*/,
          /^@babel.*/,
        ],
        output: {
          assetFileNames: (v) => {
            if (v.name.endsWith('.css')) {
              return 'sinitek-lowcode-simulator.css'
            }
            return v.name
          },
        },
      },
    }
  } else {
    config.plugins.push(monacoEditorPlugin.default({}))
  }

  return config
})
