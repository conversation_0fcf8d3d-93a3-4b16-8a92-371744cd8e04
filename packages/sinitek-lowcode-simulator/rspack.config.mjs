import NodePolyfillPlugin from 'node-polyfill-webpack-plugin'
import MonacoPlugin from 'monaco-editor-webpack-plugin'
import rspack from '@rspack/core'
const HtmlWebpackPlugin = rspack.HtmlRspackPlugin
import replaceClassNameBlockToLCBlock from './plugins/unplugin-replace-classnames.js'
import defineConfig from '../../config/rspack/base.mjs'

import path from 'path'
import fs from 'fs'
function resolve(dir) {
  return path.resolve(dir)
}
const srcDirs = fs.readdirSync(resolve('./src/'))

export default defineConfig({
  context: resolve('./'),
  unoConfigPath: resolve('./uno.config.js'),
  library: {
    type: 'umd',
    name: 'sinitek-lowcode-simulator',
    entry: './index.js',
  },
  // transpileDependencies: ['sinitek-lowcode-shared'],
  css: {
    extract: {
      filename: '[name].css',
      chunkFilename: 'css/simulator.[contenthash:8].css',
    },
  },
  devServer: {
    port: '8991',
    static: [resolve('./node_modules/sinitek-lowcode-advanced/dist')],
    client: {
      progress: true,
      overlay: {
        warnings: false,
        runtimeErrors: (error) => {
          if (
            error.message ===
            'ResizeObserver loop completed with undelivered notifications.'
          ) {
            return false
          }
          return true
        },
      },
    },
  },
  assetsDir: 'static', // 静态资源目录名称
  productionSourceMap: process.env.NODE_ENV === 'development', // 去掉打包的时候生成的map文件
  // sass全局变量配置
  configureWebpack: (config) => {
    let plugins = config.plugins
    plugins.push(
      new NodePolyfillPlugin({
        additionalAliases: ['process'],
      })
    )
    if (process.env.NODE_ENV === 'development') {
      plugins.push(
        new MonacoPlugin({
          languages: ['json', 'javascript', 'typescript', 'css'],
          features: ['!gotoSymbol'],
        })
      )
      config.devtool = 'inline-source-map'
    } else {
      plugins.push(replaceClassNameBlockToLCBlock.webpackPlugin())
    }
    config.resolve = config.resolve || {}
    config.resolve.alias = config.resolve.alias || {}
    config.resolve.alias['adaptor/element-ui'] = resolve(
      `./src/adaptor/element-ui/vue2.7.js`
    )
    config.resolve.alias['@iconify/vue2'] = '@iconify/vue2/dist/iconify.js'
    config.resolve.alias['vue$'] = 'vue/dist/vue.esm.js'
    // config.resolve.alias['core-js'] = resolve('./node_modules/core-js')
    srcDirs.forEach((dir) => {
      config.resolve.alias['@' + dir] = resolve(`./src/${dir}`)
    })
    // console.log('[ config ] >', config)
  },
  chainWebpack: (config) => {
    // config.resolve.modules
    //   .add(resolve('./node_modules'))
    //   .add('../sinitek-lowcode-flow/node_modules')
    //   .add('../../node_modules')
    config.module.rule('ts').delete()
    config.module.rule('ts').test(/\.ts$/).type('asset/source')
    // 配置别名
    if (process.env.NODE_ENV === 'production') {
      config.module.rule('images').type('asset/inline')
      /* 为生产环境修改配置... */
      // config.optimization.minimizer('terser').tap((options) => {
      //   options[0].terserOptions.compress.warnings = false
      //   options[0].terserOptions.compress.drop_console = false
      //   options[0].terserOptions.compress.drop_debugger = true
      //   options[0].terserOptions.compress.pure_funcs = [
      //     'console.log',
      //     'console.info',
      //   ]
      //   return options
      // })

      // 打包时排除
      config.externals([
        {
          vue: 'vue',
          'core-js': 'core-js',
          'element-ui': 'element-ui',
          'monaco-editor': 'monaco-editor',
          'sinitek-ui': 'sinitek-ui',
          '@iconify/vue2': '@iconify/vue2',
          sirmapp: 'sirmapp',
          'sinitek-lowcode-render': 'sinitek-lowcode-render',
          'sinitek-lowcode-shared': 'sinitek-lowcode-shared',
          'sinitek-lowcode-flow': 'sinitek-lowcode-flow',
          'sinitek-lowcode-advanced': 'sinitek-lowcode-advanced',
          lodash: 'lodash',
        },
        /^lodash\//,
        /^core-js\//,
      ])
    } else {
      /* 为开发环境修改配置... */
      // config.module.rule('vue').uses.delete('cache-loader')
      // config.merge({
      //   cache: false,
      // })
      config.entryPoints
        .clear()
        .end()
        .entry('main')
        .add(resolve('./example/main.js'))

      // 修改 webpack-html-plugin 配置
      config.plugin('html').tap(() => {
        return [
          // 传递给 html-webpack-plugin 构造函数的新参数
          {
            template: './index.html',
            excludeChunks: ['canvas'],
          },
        ]
      })
      config.entry('canvas').add('./example/canvas.js')
      config.plugin('html-canvas').use(HtmlWebpackPlugin, [
        {
          template: './canvas.html',
          filename: 'canvas.html',
          excludeChunks: ['app', 'main'],
        },
      ])
    }
  },
})
