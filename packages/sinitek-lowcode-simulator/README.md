# sinitek-lowcode-simulator

低代码模拟器

参考 [https://github.com/opentiny/tiny-engine](https://github.com/opentiny/tiny-engine)

## 文件目录

- adaptor 适配vue2.7和3.0
- canvas 中间编辑区域
- common 重组件 || 二次封装组件
- doc 对schema的操作
- plugins 左边边栏
- settings 右边设置属性
- theme 主题
- toolbar 上面工具栏
- utils

## 问题

1. 在属性面板中修改属性或者添加children没有效果
   > 看看schema中是否定义了props或者children，在vue@2中必须要定义才能跟踪
2. 在获取current schema后，如果赋值给vue的响应对应会造成页面无法响应（改变了对象索引）。需要deepClone后在赋值给响应对象
3. 如果鼠标移动闪烁，或者选中的和实际组件不符合，应该是组件实现的问题，inheritAttrs设置为false导致的。
   > 选中框区域是根据data-uid 和data-tag来实现的如果找不到会到找到root的page。
   > 应该在meta配置里定义`useWrap:true`

## 拓展说明

### 顶部导航栏插件

打开文件夹，展开并找到src-toolbar：
![158](./pics/158.png)

在toolbar中新增一个newtool文件夹，添加index.js文件和src文件夹。
![159](./pics/159.png)

收起newtool文件夹，在toolbar中找到index.js文件，导出newtool：
![160](./pics/160.png)

回到newtool文件夹，配置newtool的index.js属性如图所示：
![166](./pics/166.png)

在src文件夹中新增main.vue文件，该文件主要用于newtool插件的样式和逻辑。
以下图为例，可自定义配置点击展示弹窗等各种逻辑。
![161](./pics/161.png)

运行项目，可看到页面效果如图所示：
![162](./pics/162.png)

### 左侧操作栏插件
打开文件夹，展开并找到src-plugins：
![163](./pics/163.png)

在plugins中新增一个newplug文件夹，添加index.js文件和src文件夹。
![164](./pics/164.png)

收起newplug文件夹，在plugins中找到index.js文件，导出newplug。
![165](./pics/165.png)

回到newplug文件夹，配置newplug的index.js属性如图所示：
![168](./pics/168.png)

在src文件夹中新增main.vue文件，该文件主要用于newplug插件的样式和逻辑。
以下图为例，可自定义配置点击展示新增属性侧边栏等各种逻辑。
![167](./pics/167.png)

运行项目，可看到页面效果如图所示：
![169](./pics/169.png)

### 右侧物料配置栏属性setter

以element ui中的滑块setter为例，新建一个滑块setter。
打开文件夹，展开并找到src-settings-props-src-setter,新建一个slide-setter.vue文件。
![170](./pics/170.png)

按照下图所示，配置好滑块setter的样式和结构：
![171](./pics/171.png)

点击进入element ui文件，注册ElSlider：
![172](./pics/172.png)
![173](./pics/173.png)
![174](./pics/174.png)

在config-item中新增sliderSetter：
![175](./pics/175.png)

打开sinitek-lowcode-materials文件夹，找到sinitek ui的input物料的meta.js，将props中的第一项宽度setter改为刚刚加入的sliderSetter,打开模拟器页面预览页面效果。
![176](./pics/176.png)

此时输入框的宽度可随滑块的位置改变而变化，setting导入成功。
![177](./pics/177.png)      
