const NodePolyfillPlugin = require('node-polyfill-webpack-plugin')
const MonacoPlugin = require('monaco-editor-webpack-plugin')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const replaceClassNameBlockToLCBlock = require('./plugins/unplugin-replace-classnames')

const path = require('path')
const fs = require('fs')
function resolve(dir) {
  return path.resolve(__dirname, dir)
}

const srcDirs = fs.readdirSync(resolve('./src/'))
module.exports = function () {
  return import('@unocss/webpack').then(({ default: UnoCSS }) => {
    return {
      lintOnSave: false,
      devServer: {
        port: '8991',
        client: {
          progress: true,
          overlay: {
            warnings: false,
            runtimeErrors: (error) => {
              if (
                error.message ===
                'ResizeObserver loop completed with undelivered notifications.'
              ) {
                return false
              }
              return true
            },
          },
        },
      },
      css: {
        extract:
          process.env.NODE_ENV === 'development'
            ? {
                filename: 'css/[name].css',
                chunkFilename: 'css/[name].css',
              }
            : true,
      },
      assetsDir: 'static', // 静态资源目录名称
      productionSourceMap: process.env.NODE_ENV === 'development', // 去掉打包的时候生成的map文件
      // sass全局变量配置
      configureWebpack: (config) => {
        let plugins = config.plugins
        plugins.push(UnoCSS())
        plugins.push(new NodePolyfillPlugin())
        if (process.env.NODE_ENV === 'development') {
          plugins.push(
            new MonacoPlugin({
              languages: ['json', 'javascript', 'typescript', 'css'],
              features: ['!gotoSymbol'],
            })
          )
          config.devtool = 'inline-source-map'
        } else {
          plugins.push(replaceClassNameBlockToLCBlock.webpackPlugin())
        }
        config.resolve = config.resolve || {}
        config.resolve.alias = config.resolve.alias || {}
        config.resolve.alias['adaptor/element-ui'] = path.resolve(
          __dirname,
          `./src/adaptor/element-ui/vue2.7.js`
        )
        config.resolve.alias['@iconify/vue2'] = '@iconify/vue2/dist/iconify.js'
        config.resolve.alias['vue$'] = 'vue/dist/vue.esm.js'
        srcDirs.forEach((dir) => {
          config.resolve.alias['@' + dir] = path.resolve(
            __dirname,
            `./src/${dir}`
          )
        })
        // config.optimization.realContentHash = true
      },
      chainWebpack: (config) => {
        // 配置别名
        if (process.env.NODE_ENV === 'production') {
          /* 为生产环境修改配置... */
          config.optimization.minimizer('terser').tap((options) => {
            options[0].terserOptions.compress.warnings = false
            options[0].terserOptions.compress.drop_console = false
            options[0].terserOptions.compress.drop_debugger = true
            options[0].terserOptions.compress.pure_funcs = [
              'console.log',
              'console.info',
            ]
            return options
          })

          // 打包时排除
          config.externals({
            vue: 'vue',
            'core-js': 'core-js',
            'element-ui': 'element-ui',
            'monaco-editor': 'monaco-editor',
            'sinitek-ui': 'sinitek-ui',
            '@iconify/vue2': '@iconify/vue2',
            sirmapp: 'sirmapp',
            'sinitek-lowcode-advanced': 'sinitek-lowcode-advanced',
            'sinitek-lowcode-materials': 'sinitek-lowcode-materials',
            'sinitek-lowcode-render': 'sinitek-lowcode-render',
            'sinitek-lowcode-shared': 'sinitek-lowcode-shared',
          })
        } else {
          /* 为开发环境修改配置... */
          // config.module.rule('vue').uses.delete('cache-loader')
          // config.merge({
          //   cache: false,
          // })
          config.entryPoints
            .clear()
            .end()
            .entry('main')
            .add(resolve('./example/main.js'))

          // 修改 webpack-html-plugin 配置
          config.plugin('html').tap(() => {
            return [
              // 传递给 html-webpack-plugin 构造函数的新参数
              {
                template: './index.html',
                excludeChunks: ['canvas'],
              },
            ]
          })
          config.entry('canvas').add('./example/canvas.js')
          config.plugin('html-canvas').use(HtmlWebpackPlugin, [
            {
              template: './canvas.html',
              filename: 'canvas.html',
              excludeChunks: ['app', 'main'],
            },
          ])
        }
      },
    }
  })
}
