<template>
  <div>
    <div
      class="i-material-symbols-light:preview h-6 w-6"
      float="预览"
      @click="onPreview"
    ></div>
    <xn-dialog show-fullscreen-icon title="预览" :show.sync="isShow">
      <LCPreviewRender v-if="isShow" :config="config" />
    </xn-dialog>
  </div>
</template>

<script>
import LCPreviewRender from './render.vue'
export default {
  name: 'LCPreview',
  components: {
    LCPreviewRender,
  },
  inject: ['$doc'],
  data() {
    return {
      isShow: false,
      config: {},
    }
  },
  methods: {
    onPreview() {
      this.isShow = true
      this.config = this.$doc.getSchema(true)
    },
  },
}
</script>
