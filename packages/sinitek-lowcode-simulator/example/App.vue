<template>
  <div id="app">
    <SinitekLowcodeSimulator ref="simulator" :mode="mode" :config="config">
      <template #test>
        <div>test-slot</div>
      </template>
      <template #test-scope="scope">
        <div>scope: {{ scope && scope.scope.row }}</div>
      </template>
    </SinitekLowcodeSimulator>
  </div>
</template>
<script>
import config from 'sinitek-lowcode-render/example/json/demo-element'
import demo2 from 'sinitek-lowcode-render/example/json/demo2'
import tabs from 'sinitek-lowcode-render/example/json/tabs'
// import materials from 'sinitek-lowcode-materials'
// import advanced from 'sinitek-lowcode-advanced'
import CustomPlugins from './plugins'
import { DesignMode } from 'sinitek-lowcode-shared'
import publish from './toolbar/publish.vue'
import Sirmapp from 'sirmapp'
export default {
  data() {
    return {
      mode: DesignMode.DESIGN,
    }
  },
  computed: {
    config() {
      return {
        getIcons: Sirmapp.getIcons,
        fetcher: {
          fetcher: async (options) => {
            return fetch(options.url, options).then((res) => {
              return res.json()
            })
          },
          getAIProps: async () => {
            return [
              {
                code: 'maxlength',
                value: '100',
              },
              {
                code: 'placeholder',
                value: '请输入',
              },
            ]
          },
          getTemplate: async () => {
            return {
              data: [
                { id: 0, title: '空白表单', empty: true, jsonSchema: {} },
                {
                  id: 1,
                  url: 'https://form.making.link/docs/assets/%E5%AD%97%E6%AE%B5%E6%A0%87%E8%AF%86.AO_dVlF0.png',
                  title: '典型表单',
                  jsonSchema: tabs,
                },
                {
                  id: 2,
                  url: 'https://form.making.link/docs/assets/%E6%A0%A1%E9%AA%8C.U2iLznBd.png',
                  title: '快速注册表单',
                  jsonSchema: demo2,
                },
                {
                  id: 3,
                  url: 'https://form.making.link/docs/assets/%E5%9F%BA%E7%A1%80%E7%94%A8%E6%B3%95.mLTVzAlo.png',
                  title: '复杂表格',
                  jsonSchema: config,
                },
                {
                  id: 4,
                  url: 'https://www.easck.com/d/file/p/2022/08-25/732b773e3a0d9f9760adc13183efb54c.png',
                  title: '步骤表单',
                  jsonSchema: config,
                },
                {
                  id: 5,
                  url: 'https://www.easck.com/d/file/p/2022/08-25/732b773e3a0d9f9760adc13183efb54c.png',
                  title: '步骤表单',
                  jsonSchema: config,
                },
                {
                  id: 6,
                  url: 'https://www.easck.com/d/file/p/2022/08-25/732b773e3a0d9f9760adc13183efb54c.png',
                  title: '步骤表单',
                  jsonSchema: config,
                },
              ],
              total: 7,
            }
          },
          setSchema: () => {
            return Promise.resolve(demo2)
          },
          getModelList: async () => {
            return [
              { label: 'modle1', value: 'modle1' },
              { label: 'modle2', value: 'modle2' },
              { label: 'modle3', value: 'modle3' },
            ]
          },
          getRefModelList: async () => {
            return [
              { label: 'modle1', value: 'modle1' },
              { label: 'modle2', value: 'modle2' },
              { label: 'modle3', value: 'modle3' },
            ]
          },
          // 获取模型字段用在条件筛选上
          getModelFields: async () => {
            return [
              {
                id: '1803298935582887938',
                modelCode: 'test_foreign_key_child',
                name: 'id',
                colName: 'id',
                type: 'KEY',
                clazz: null,
                relaModelCode: null,
                enumCatalog: null,
                enumType: null,
                format: null,
                sort: 1,
                validateJson: null,
                configJson: null,
                comments: '主键',
                componentType: 'TEXT',
                relaTable: null,
                relaModelName: null,
                propRelas: null,
                propName: 'id',
              },
              {
                id: '1803298935750660098',
                modelCode: 'test_foreign_key_child',
                name: 'version',
                colName: 'version',
                type: 'VERSION',
                clazz: null,
                relaModelCode: null,
                enumCatalog: null,
                enumType: null,
                format: null,
                sort: 2,
                validateJson: null,
                configJson: null,
                comments: '乐观锁',
                componentType: 'SWITCH',
                relaTable: null,
                relaModelName: null,
                propRelas: null,
                propName: 'version',
              },
              {
                id: '1803298935910043650',
                modelCode: 'test_foreign_key_child',
                name: 'createtimestamp',
                colName: 'createtimestamp',
                type: 'CREATETIMESTAMP',
                clazz: null,
                relaModelCode: null,
                enumCatalog: null,
                enumType: null,
                format: null,
                sort: 3,
                validateJson: null,
                configJson: null,
                comments: '新增时间',
                componentType: 'DATE',
                relaTable: null,
                relaModelName: null,
                propRelas: null,
                propName: 'createtimestamp',
              },
              {
                id: '1803298936128147458',
                modelCode: 'test_foreign_key_child',
                name: 'updatetimestamp',
                colName: 'updatetimestamp',
                type: 'UPDATETIMESTAMP',
                clazz: null,
                relaModelCode: null,
                enumCatalog: null,
                enumType: null,
                format: null,
                sort: 4,
                validateJson: null,
                configJson: null,
                comments: '更新时间',
                componentType: 'DATETIME',
                relaTable: null,
                relaModelName: null,
                propRelas: null,
                propName: 'updatetimestamp',
              },
              {
                id: '1803298936304308225',
                modelCode: 'test_foreign_key_child',
                name: 'name',
                colName: 'name',
                type: 'Number',
                clazz: '',
                relaModelCode: '',
                enumCatalog: '',
                enumType: '',
                format: 'yyyy-MM-dd',
                sort: 5,
                validateJson:
                  '{"required":false,"requiredValidateMsg":"","unique":false,"uniqueCols":[],"uniqueValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":"","relaModelCodeMsg":""}',
                configJson:
                  '{"association":"ONE2ONE","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0}',
                comments: '',
                componentType: 'NUMBER',
                relaTable: null,
                relaModelName: null,
                propRelas: null,
                propName: 'name',
              },
              {
                id: '1803298936459497474',
                modelCode: 'test_foreign_key_child',
                name: 'parentId',
                colName: 'parent_id',
                type: 'FOREIGN_KEY',
                clazz: '',
                relaModelCode: 'test_foreign_key_parent',
                enumCatalog: '',
                enumType: '',
                format: 'yyyy-MM-dd',
                sort: 6,
                validateJson:
                  '{"required":false,"requiredValidateMsg":"","unique":false,"uniqueCols":[],"uniqueValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":"","relaModelCodeMsg":""}',
                configJson:
                  '{"association":"ONE2ONE","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0}',
                comments: '',
                componentType: 'RELA_FORM',
                relaTable: null,
                relaModelName: 'test_foreign_key_parent',
                propRelas: [],
                propName: 'parentId',
              },
              {
                id: '1821004940097753090',
                modelCode: 'test_lc_model',
                name: 'as',
                colName: '',
                type: 'NUMBER_SIRMENUM',
                relaModelCode: '',
                enumCatalog: 'COMMON',
                enumType: 'APPROVESTATUS',
                format: '',
                sort: 7,
                validateJson:
                  '{"required":false,"requiredValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":""}',
                configJson:
                  '{"association":"","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0,"formatedName":"a1"}',
                comments: '',
                componentType: 'SELECT',
                relaTable: null,
                customHandler: null,
                relaModelName: null,
                propRelas: null,
              },
              {
                id: '1821005425722658818',
                modelCode: 'test_lc_model',
                name: 't2',
                colName: '',
                type: 'NUMBER_SIRMENUM_MV',
                relaModelCode: '',
                enumCatalog: 'a1',
                enumType: 'a2',
                format: '',
                sort: 8,
                validateJson:
                  '{"required":false,"requiredValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":""}',
                configJson:
                  '{"association":"","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0,"formatedName":"sss"}',
                comments: '',
                componentType: 'CHECKBOX_GROUP',
                relaTable: null,
                customHandler: null,
                relaModelName: null,
                propRelas: null,
              },
            ].map((e) => {
              return {
                label: e.comments || e.colName || e.name,
                value: e.name,
                data: e,
              }
            })
          },
          // 模型的crud
          modelFetchers: {
            create(params) {
              return fetch('', params).then((res) => {
                return res.json()
              })
            },
            update(params) {
              return fetch('', params).then((res) => {
                return res.json()
              })
            },
            delete(params) {
              return fetch('', params).then((res) => {
                return res.json()
              })
            },
            list() {
              return Promise.resolve({
                data: {
                  datalist: [
                    {
                      id: '1803298935582887938',
                      modelCode: 'test_foreign_key_child',
                      name: 'id',
                      colName: 'id',
                      type: 'KEY',
                      clazz: null,
                      relaModelCode: null,
                      enumCatalog: null,
                      enumType: null,
                      format: null,
                      sort: 1,
                      validateJson: null,
                      configJson: null,
                      comments: '主键',
                      componentType: 'TEXT',
                      relaTable: null,
                      relaModelName: null,
                      propRelas: null,
                      propName: 'id',
                    },
                    {
                      id: '1803298935750660098',
                      modelCode: 'test_foreign_key_child',
                      name: 'version',
                      colName: 'version',
                      type: 'VERSION',
                      clazz: null,
                      relaModelCode: null,
                      enumCatalog: null,
                      enumType: null,
                      format: null,
                      sort: 2,
                      validateJson: null,
                      configJson: null,
                      comments: '乐观锁',
                      componentType: 'SWITCH',
                      relaTable: null,
                      relaModelName: null,
                      propRelas: null,
                      propName: 'version',
                    },
                    {
                      id: '1803298935910043650',
                      modelCode: 'test_foreign_key_child',
                      name: 'createtimestamp',
                      colName: 'createtimestamp',
                      type: 'CREATETIMESTAMP',
                      clazz: null,
                      relaModelCode: null,
                      enumCatalog: null,
                      enumType: null,
                      format: null,
                      sort: 3,
                      validateJson: null,
                      configJson: null,
                      comments: '新增时间',
                      componentType: 'DATE',
                      relaTable: null,
                      relaModelName: null,
                      propRelas: null,
                      propName: 'createtimestamp',
                    },
                    {
                      id: '1803298936128147458',
                      modelCode: 'test_foreign_key_child',
                      name: 'updatetimestamp',
                      colName: 'updatetimestamp',
                      type: 'UPDATETIMESTAMP',
                      clazz: null,
                      relaModelCode: null,
                      enumCatalog: null,
                      enumType: null,
                      format: null,
                      sort: 4,
                      validateJson: null,
                      configJson: null,
                      comments: '更新时间',
                      componentType: 'DATETIME',
                      relaTable: null,
                      relaModelName: null,
                      propRelas: null,
                      propName: 'updatetimestamp',
                    },
                    {
                      id: '1803298936304308225',
                      modelCode: 'test_foreign_key_child',
                      name: 'name',
                      colName: 'name',
                      type: 'Number',
                      clazz: '',
                      relaModelCode: '',
                      enumCatalog: '',
                      enumType: '',
                      format: 'yyyy-MM-dd',
                      sort: 5,
                      validateJson:
                        '{"required":false,"requiredValidateMsg":"","unique":false,"uniqueCols":[],"uniqueValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":"","relaModelCodeMsg":""}',
                      configJson:
                        '{"association":"ONE2ONE","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0}',
                      comments: '',
                      componentType: 'NUMBER',
                      relaTable: null,
                      relaModelName: null,
                      propRelas: null,
                      propName: 'name',
                    },
                    {
                      id: '1803298936459497474',
                      modelCode: 'test_foreign_key_child',
                      name: 'parentId',
                      colName: 'parent_id',
                      type: 'FOREIGN_KEY',
                      clazz: '',
                      relaModelCode: 'test_foreign_key_parent',
                      enumCatalog: '',
                      enumType: '',
                      format: 'yyyy-MM-dd',
                      sort: 6,
                      validateJson:
                        '{"required":false,"requiredValidateMsg":"","unique":false,"uniqueCols":[],"uniqueValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":"","relaModelCodeMsg":""}',
                      configJson:
                        '{"association":"ONE2ONE","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0}',
                      comments: '',
                      componentType: 'RELA_FORM',
                      relaTable: null,
                      relaModelName: 'test_foreign_key_parent',
                      propRelas: [],
                      propName: 'parentId',
                    },
                    {
                      id: '1821004940097753090',
                      modelCode: 'test_lc_model',
                      name: 'as',
                      colName: '',
                      type: 'NUMBER_SIRMENUM',
                      relaModelCode: '',
                      enumCatalog: 'COMMON',
                      enumType: 'APPROVESTATUS',
                      format: '',
                      sort: 7,
                      validateJson:
                        '{"required":false,"requiredValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":""}',
                      configJson:
                        '{"association":"","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0,"formatedName":"a1"}',
                      comments: '',
                      componentType: 'SELECT',
                      relaTable: null,
                      customHandler: null,
                      relaModelName: null,
                      propRelas: null,
                    },
                    {
                      id: '1821005425722658818',
                      modelCode: 'test_lc_model',
                      name: 't2',
                      colName: '',
                      type: 'NUMBER_SIRMENUM_MV',
                      relaModelCode: '',
                      enumCatalog: 'a1',
                      enumType: 'a2',
                      format: '',
                      sort: 8,
                      validateJson:
                        '{"required":false,"requiredValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":""}',
                      configJson:
                        '{"association":"","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0,"formatedName":"sss"}',
                      comments: '',
                      componentType: 'CHECKBOX_GROUP',
                      relaTable: null,
                      customHandler: null,
                      relaModelName: null,
                      propRelas: null,
                    },
                  ],
                  totalsize: 8,
                },
              })
            },
            lazyList() {
              return Promise.resolve([])
            },
            lazySingle() {
              return Promise.resolve({})
            },
          },
          getEnum: async (ss) => {
            return [
              {
                label: ss.data.catalog + '-asddd',
                value: 'a',
                children: [{ label: 'a1', value: 'a1' }],
              },
              {
                label: ss.data.catalog + '-baaa',
                value: 'b',
                children: [{ label: 'a1', value: 'a1' }],
              },
            ]
          },
          getEnumList: async () => {
            return [
              {
                label: 'a',
                value: 'a',
                children: [{ label: 'a1', value: 'a1' }],
              },
              {
                label: 'b',
                value: 'b',
                children: [{ label: 'a1', value: 'a1' }],
              },
            ]
          },
        },
        message: {
          success: (text) => {
            this.$message.success(text)
          },
          error: (text) => {
            this.$message.error(text)
          },
        },
      }
    },
  },
  mounted() {
    // this.$refs.simulator.setMaterials(
    //   [].concat(materials).concat(advancedMaterials)
    // )
    // this.$refs.simulator.install(advanced)
    setTimeout(() => {
      // this.$refs.simulator.addComponents(advancedComponents)
      this.$refs.simulator.setSchema(config)
    }, 1000)
    // this.$refs.simulator.register({
    //   id: 'ccc',
    //   area: 'leftArea',
    //   ...CustomPlugins[0],
    //   order: 9,
    // })
    this.$refs.simulator.register(CustomPlugins[1])
    this.$refs.simulator.register({
      area: 'topArea',
      id: 'ToolbarPublish',
      name: 'publish',
      title: '发布',
      component: publish,
      align: 'right',
      order: 4,
    })
  },
}
</script>
<style>
html,
body,
#app {
  height: 100vh;
}
.lc-hidden {
  display: none;
}
</style>
