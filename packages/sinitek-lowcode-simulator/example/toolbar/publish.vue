<template>
  <div @click="publish">发布</div>
</template>

<script>
import { getLowcode } from '@utils'
import { download } from 'sinitek-lowcode-shared'
export default {
  name: 'LCToolbarPublish',
  inject: ['$doc'],
  methods: {
    publish() {
      // 修改默认值
      const schema = this.$doc.getSchema(true)
      if (schema?.material?.reflect) {
        schema.material.reflect.forEach((item) => {
          item.defaultValue = this.$doc.node.getNode(item.id).props[item.value]
        })
      }
      const material = getLowcode('LCComponent', schema, (id) => {
        return this.$doc.getMaterial(this.$doc.node.getNode(id).componentName)
      })

      download(material, 'text/javascript', 'lowcode.js')
    },
  },
}
</script>
