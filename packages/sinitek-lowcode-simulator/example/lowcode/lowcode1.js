import Render from 'sinitek-lowcode-render'

// 组件名称记得修改
const componentName = 'LCComponent'

export const material = {
  title: '低代码组件',
  lowcodeComponent: true,
  componentName,
  props: [
    {
      title: '属性标题',
      name: 'propName',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            { label: 'primary', value: 'primary' },
            { label: 'success', value: 'success' },
          ],
        },
      },
      defaultValue: 'primary',
      options: [
        { label: 'primary', value: 'primary' },
        { label: 'success', value: 'success' },
      ],
    },
  ],
  configure: {
    component: { isContainer: false },
    supports: { condition: true, style: true },
  },
  snippets: [
    {
      title: '低代码组件',
      screenshot: '',
      schema: {
        componentName,
        props: { propName: 'primary' },
        children: [],
      },
    },
  ],
}

export const config = {
  componentName: 'Page',
  name: '',
  style: '',
  props: {},
  ref: 'a1',
  id: '1e815565',
  css: '.line{text-align:center;}',
  state: {
    form: {
      name: '产品名称',
      productTypeName: '产品类型',
      investTypeName: '投资',
    },
    d1: false,
  },
  datasource: [
    {
      url: 'https://mock.apipark.cn/m1/594011-2734521-default/mock',
      method: 'get',
      id: 'mock1',
      auto: true,
      data: {},
      shouldFetch:
        "function shouldFetch() {\n        console.log('should fetch.....');\n        return true;\n      }",
      errorHandler: 'function errorHandler(err) {}',
      dataHandler:
        'function dataHandler(res) { console.log(res);return res.data }',
      willFetch:
        'function willFetch(options) {\n        options.b = 2\n        return options; }',
    },
  ],
  methods: {
    submit: {
      type: 'JSFunction',
      value: 'function submit(e) {\n  console.log("fn", this, this.state);\n}',
    },
    a: {
      type: 'JSFunction',
      value:
        "function a(...args) {\n  console.log(this, 'this');console.log(...args, 'a1');\n}",
    },
    close: {
      type: 'JSFunction',
      value: 'function close(v) {\n  this.state.d1 = v\n}',
    },
  },
  children: [
    {
      componentName: 'LCIcon',
      props: { icon: 'uil:icons', height: 24, width: 24 },
      title: '图标',
      hidden: false,
      id: '44553327',
      ref: 'LCIcon1',
    },
  ],
  hidden: false,
}

export default {
  name: componentName,
  props: ['propName'],
  material,
  provide() {
    return {
      lowcodeProps: this.$props,
      lowcodeEmit: this.emit,
    }
  },
  render() {
    return (
      <div>
        <Render config={config} isLCComponent ref="render" />
      </div>
    )
  },
  mounted() {
    // 实现methods调用
    // 下划线开头和生命周期不会被调用
    const methods = this.$refs.render.getMethods()
    if (Object.keys(methods).length) {
      Object.entries(methods).forEach(([key, value]) => {
        this[key] = value
      })
    }
  },
  methods: {
    emit(...args) {
      this.$emit(...args)
    },
  },
}
