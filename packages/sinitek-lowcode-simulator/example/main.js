import 'uno.css'
import '@unocss/reset/tailwind-compat.css'
import 'sinitek-css/dist/icons/iconfont.css'
import 'sinitek-css/dist/theme/default.css'
import 'sinitek-css/dist/theme/mars.css'
import 'sinitek-css/dist/theme/earth.css'

import Vue from 'vue'
import App from './App.vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import 'sinitek-lowcode-render/dist/sinitek-lowcode-render.css'
import SinitekLowcodeRender from 'sinitek-lowcode-render'
import SinitekLowcodeAdvanced from 'sinitek-lowcode-advanced'
import render from '../index.js'
import 'sinitek-lowcode-advanced/dist/sinitek-lowcode-advanced.css'
import materials from 'sinitek-lowcode-materials'
import advancedMaterials from 'sinitek-lowcode-advanced/materials'
import { addCtxMaterials, registry } from 'sinitek-lowcode-shared'
addCtxMaterials(materials)
addCtxMaterials(advancedMaterials)
import SinitekUI from 'sinitek-ui'
import { http } from 'sinitek-util'

import * as echarts from 'echarts'
window.echarts = echarts
window.__LCShowAI__ = true
// 添加低代码例子
// import './lowcode/lowcode.js'

registry(SinitekLowcodeAdvanced)
registry({
  install(simulator) {
    simulator.addUtil('test', () => {})
    simulator.addUtil('test2', () => {})
  },
})
Vue.use(render)
Vue.use(SinitekLowcodeRender)
Vue.use(ElementUI)
Vue.use(SinitekUI, { http })
// Vue.config.productionTip = false
window.LCVue = Vue
new Vue({
  render: (h) => h(App),
}).$mount('#app')
