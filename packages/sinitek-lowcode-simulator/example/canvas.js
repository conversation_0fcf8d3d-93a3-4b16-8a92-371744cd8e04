import App from '@/canvas/index.vue'
// import materials from 'sinitek-lowcode-materials'
// import advancedMaterials from 'sinitek-lowcode-advanced/materials'
// import { addCtxMaterials, addCtxComponent } from 'sinitek-lowcode-shared'
// addCtxMaterials(materials)
// addCtxMaterials(advancedMaterials)
// import { components } from 'sinitek-lowcode-advanced'
import Vue from 'vue'
import ElementUI from 'element-ui'
import SinitekUI from 'sinitek-ui'
import { http } from 'sinitek-util'
// Object.entries(components).forEach(([name, component]) => {
//   addCtxComponent(name, component)
// })

Vue.use(SinitekUI, { http })
Vue.use(ElementUI)
import 'sinitek-css/dist/icons/iconfont.css'
import 'sinitek-css/dist/theme/default.css'
import 'sinitek-css/dist/theme/mars.css'
import 'sinitek-css/dist/theme/earth.css'
import 'sinitek-lowcode-advanced/dist/sinitek-lowcode-advanced.css'
import 'sinitek-lowcode-render/dist/sinitek-lowcode-render.css'
import * as echarts from 'echarts'
window.echarts = echarts

// TODO 复用外部的组件
new Vue({
  render: (h) => h(App),
  mounted() {
    const svg = window.parent.document.querySelector('#__SVG_SPRITE_NODE__')
    if (svg) {
      const clone = svg.cloneNode(true)
      document.body.appendChild(clone)
    }
    Array.from(window.parent.document.head.children).forEach((e) => {
      if (e.tagName === 'SCRIPT') return
      document.head.appendChild(e.cloneNode())
    })
  },
}).$mount('#canvas')
