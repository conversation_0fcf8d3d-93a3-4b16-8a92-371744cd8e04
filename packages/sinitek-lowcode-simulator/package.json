{"name": "sinitek-lowcode-simulator", "version": "1.0.0-SNAPSHOT.135", "description": "> TODO: description", "author": "wei.peng <<EMAIL>>", "homepage": "", "license": "ISC", "main": "dist/sinitek-lowcode-simulator.umd.min.js", "private": false, "files": ["dist"], "repository": {"url": "http://*************:8088/repository/sinitek-npm/"}, "scripts": {"dev": "rspack serve", "dev:bun": "bun run rspack serve", "build": "rspack build", "build2": "vue-cli-service build --target lib --formats umd-min --name sinitek-lowcode-simulator --dest dist ./index.js", "lint": "eslint . --fix", "format": "prettier --write \"{src,example}/**/*.{vue,js}\"", "test": "jest", "doctor": "set RSDOCTOR=true && rspack build "}, "devDependencies": {"@babel/core": "catalog:", "@babel/eslint-parser": "catalog:", "@babel/generator": "catalog:", "@babel/parser": "catalog:", "@babel/plugin-transform-nullish-coalescing-operator": "^7.26.6", "@babel/plugin-transform-optional-chaining": "^7.25.9", "@iconify-json/icon-park-outline": "^1.2.2", "@iconify-json/lucide": "^1.2.43", "@iconify-json/material-symbols-light": "^1.2.16", "@iconify-json/mdi": "^1.2.3", "@iconify-json/mingcute": "^1.2.3", "@iconify-json/tabler": "^1.2.17", "@iconify-json/vscode-icons": "^1.2.16", "@iconify/utils": "^2.3.0", "@rspack/cli": "catalog:", "@rspack/core": "catalog:", "@unocss/eslint-config": "catalog:", "@unocss/postcss": "catalog:", "@unocss/preset-icons": "catalog:", "@unocss/preset-legacy-compat": "catalog:", "@unocss/preset-rem-to-px": "catalog:", "@unocss/reset": "catalog:", "@unocss/transformer-attributify-jsx": "catalog:", "@unocss/webpack": "catalog:", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/babel-preset-jsx": "^1.4.0", "acorn": "8.14.0", "acorn-walk": "8.3.4", "eslint": "catalog:", "eslint-config-prettier": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-vue": "catalog:", "jest": "29.7.0", "lint-staged": "catalog:", "monaco-editor-webpack-plugin": "7.1.0", "node-polyfill-webpack-plugin": "4.0.0", "prettier": "3.3.3", "sinitek-lowcode-flow": "workspace:^", "unocss": "catalog:", "unplugin": "1.15.0", "vue": "catalog:", "vue-template-compiler": "catalog:"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "1.4.0", "@atlaskit/pragmatic-drag-and-drop-hitbox": "1.0.3", "@floating-ui/dom": "^1.6.13", "@vojtechlanka/vue-tags-input": "^2", "core-js": "3.39.0", "lodash": "catalog:", "sirmapp": "catalog:"}, "gitHooks": {"pre-commit": "lint-staged"}, "volta": {"node": "20.12.2"}, "peerDependencies": {"@iconify/vue2": "catalog:", "echarts": "catalog:", "element-ui": "catalog:", "lodash": "catalog:", "monaco-editor": "catalog:", "sinitek-css": "catalog:", "sinitek-lowcode-advanced": "workspace:^", "sinitek-lowcode-flow-execute": "workspace:^", "sinitek-lowcode-materials": "workspace:^", "sinitek-lowcode-render": "workspace:^", "sinitek-lowcode-shared": "workspace:^", "sinitek-ui": "catalog:", "sinitek-util": "catalog:", "vue": "catalog:"}}