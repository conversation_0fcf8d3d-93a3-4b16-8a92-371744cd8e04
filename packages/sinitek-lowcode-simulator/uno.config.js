import theme from './src/theme'
import config from '../../config/uno.config.js'
import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders'
export default config(theme, {
  isSimulator: true,
  presetIconsOptions: {
    collections: {
      custom: FileSystemIconLoader('./src/assets/svg', (svg) =>
        svg.replace(/fill="[^"]*"/g, (match) => {
          // 排除 fill="none" 的情况
          if (match === 'fill="none"') return match
          return 'fill="currentColor"'
        })
      ),
    },
  },
})
