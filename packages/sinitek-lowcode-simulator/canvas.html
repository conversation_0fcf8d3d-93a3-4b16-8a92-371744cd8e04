<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  </head>
  <body>
    
      <style type="text/css">
      @keyframes ldio-yzaezf3dcmj {
        0% { opacity: 1 }
        100% { opacity: 0 }
      }
      .ldio-yzaezf3dcmj div {
        left: 96px;
        top: 52px;
        position: absolute;
        animation: ldio-yzaezf3dcmj linear 1s infinite;
        background: #000000;
        width: 8px;
        height: 24px;
        border-radius: 0px / 0px;
        transform-origin: 4px 48px;
      }.ldio-yzaezf3dcmj div:nth-child(1) {
        transform: rotate(0deg);
        animation-delay: -0.9411764705882353s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(2) {
        transform: rotate(21.176470588235293deg);
        animation-delay: -0.8823529411764706s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(3) {
        transform: rotate(42.35294117647059deg);
        animation-delay: -0.8235294117647058s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(4) {
        transform: rotate(63.529411764705884deg);
        animation-delay: -0.7647058823529411s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(5) {
        transform: rotate(84.70588235294117deg);
        animation-delay: -0.7058823529411765s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(6) {
        transform: rotate(105.88235294117646deg);
        animation-delay: -0.6470588235294118s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(7) {
        transform: rotate(127.05882352941177deg);
        animation-delay: -0.5882352941176471s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(8) {
        transform: rotate(148.23529411764707deg);
        animation-delay: -0.5294117647058824s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(9) {
        transform: rotate(169.41176470588235deg);
        animation-delay: -0.47058823529411764s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(10) {
        transform: rotate(190.58823529411765deg);
        animation-delay: -0.4117647058823529s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(11) {
        transform: rotate(211.76470588235293deg);
        animation-delay: -0.35294117647058826s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(12) {
        transform: rotate(232.94117647058823deg);
        animation-delay: -0.29411764705882354s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(13) {
        transform: rotate(254.11764705882354deg);
        animation-delay: -0.23529411764705882s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(14) {
        transform: rotate(275.29411764705884deg);
        animation-delay: -0.17647058823529413s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(15) {
        transform: rotate(296.47058823529414deg);
        animation-delay: -0.11764705882352941s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(16) {
        transform: rotate(317.6470588235294deg);
        animation-delay: -0.058823529411764705s;
        background: #000000;
      }.ldio-yzaezf3dcmj div:nth-child(17) {
        transform: rotate(338.8235294117647deg);
        animation-delay: 0s;
        background: #000000;
      }
      .loadingio-spinner-spinner-2by998twmg8 {
        width: 200px;
        height: 200px;
        display: inline-block;
        overflow: hidden;
        background: none;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
      .ldio-yzaezf3dcmj {
        width: 100%;
        height: 100%;
        position: relative;
        transform: translateZ(0) scale(0.4);
        backface-visibility: hidden;
        transform-origin: center; /* see note above */
      }
      .ldio-yzaezf3dcmj div { box-sizing: content-box; }
      /* [ldio] generated by https://loading.io */
      </style>
    <div id="canvas">
      <div class="loadingio-spinner-spinner-2by998twmg8"><div class="ldio-yzaezf3dcmj">
        <div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div>
        </div></div>
    </div>
    <!-- <script type="module" src="/example/canvas.js"></script> -->
  </body>
</html>
