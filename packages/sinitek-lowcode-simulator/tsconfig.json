{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "moduleDetection": "force", "useDefineForClassFields": true, "baseUrl": "./", "module": "ESNext", "moduleResolution": "Node", "allowJs": true, "strict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "declaration": true, "importHelpers": true, "checkJs": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.d.ts"]}