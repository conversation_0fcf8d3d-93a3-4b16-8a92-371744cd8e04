<template>
  <div class="absolute wh-full">
    <LCResizeBorder v-show="direction.top || direction.y" direction="top" />
    <LCResizeBorder v-show="direction.right || direction.x" direction="right" />
    <LCResizeBorder v-show="direction.bottom" direction="bottom" />
    <LCResizeBorder v-show="direction.left || direction.x" direction="left" />
  </div>
</template>

<script>
import { isRow, isCol } from './cut.vue'
import LCResizeBorder from './resize-border.vue'
export default {
  name: 'LCResize',
  components: {
    LCResizeBorder,
  },
  inject: ['$doc'],
  props: {
    current: {
      type: Object,
    },
  },
  data() {
    return {}
  },
  computed: {
    direction() {
      const dir = {
        top: false,
        right: false,
        x: false, //左右2边都可以
        bottom: false,
        left: false,
        y: false,
      }
      if (!this?.current?.id) return dir
      const { parent } = this.$doc.node.getNode(this.current.id, true)
      const index = parent.children.findIndex((e) => e.id === this.current.id)
      if (isRow(parent.componentName)) {
        // 行元素，只能左右拖动
        dir.right = index !== parent.children.length - 1
        dir.x = index > 0 && index < parent.children.length - 1
        dir.left = !dir.right
      } else if (isCol(parent.componentName)) {
        // 列元素，只能上下拖动
        dir.bottom = index !== parent.children.length - 1
        dir.y = index > 0 && index < parent.children.length - 1
        dir.top = !dir.bottom
      }
      return dir
    },
  },
}
</script>
