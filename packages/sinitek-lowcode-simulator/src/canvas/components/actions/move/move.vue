<template>
  <div class="flex" flex-shrink-0>
    <div
      float="向上移动"
      class="i-lucide-arrow-up h-4 w-4"
      @click.stop="moveUp"
    ></div>
    <div
      class="i-lucide-arrow-down h-4 w-4"
      float="向下移动"
      @click.stop="moveDown"
    ></div>
  </div>
</template>

<script>
export default {
  name: 'LCActionParent',
  inject: ['$doc'],
  props: {
    selectState: Object,
  },
  methods: {
    moveUp() {
      const { parent, schema } = this.$doc.getCurrent()
      this.moveChild(parent?.children, schema, -1)
    },
    moveDown() {
      const { parent, schema } = this.$doc.getCurrent()
      this.moveChild(parent?.children, schema, 1)
    },
    moveChild(list, selected, addend) {
      if (!list || list.length < 2) {
        return
      }

      const index = list.indexOf(selected)

      if (index > -1) {
        const toIndex = index + addend

        if (toIndex > -1 && toIndex < list.length) {
          const temp = list[index]
          list[index] = list[toIndex]
          list[toIndex] = temp
          // ;[list[index], list[toIndex]] = [list[toIndex], list[index]]
        }
      }
      // fix： 移动后不更新问题
      const { parent } = this.$doc.getCurrent()
      parent.children = list.slice()
      this.$doc.schema.update()
      this.$doc.history.addHistory()
      this.$nextTick(() => {
        this.$doc.select.reselect()
      })
    },
  },
}
</script>
