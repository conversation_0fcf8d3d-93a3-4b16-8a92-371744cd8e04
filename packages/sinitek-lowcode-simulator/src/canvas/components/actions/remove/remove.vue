<template>
  <div class="i-lucide-trash h-4 w-4" float="删除" @click.stop="remove"></div>
</template>

<script>
export default {
  name: 'LCActionParent',
  inject: ['$doc'],
  props: {
    selectState: Object,
  },
  methods: {
    remove() {
      const { schema, parent } = this.$doc.getCurrent()
      // 重新渲染父元素
      if (parent.componentName !== 'Page') {
        parent.__key__ = Date.now()
      }
      this.$doc.node.removeNodeById(schema?.id)
    },
  },
}
</script>
