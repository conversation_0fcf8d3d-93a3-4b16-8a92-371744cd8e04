<template>
  <div>
    <div
      class="flex-shrink-0 bg-primary px-1 text-white"
      @mousemove="showParentList = true"
    >
      {{ getTitle() }}
    </div>
    <div
      class="absolute left-0 z-50 w-full -top-full"
      :style="computedStyle"
      @mousemove="showParentList = true"
      @mouseleave="showParentList = false"
    >
      <div
        v-for="(item, i) of parentListForRender"
        :key="item.id"
        class="absolute left-0 h-5 bg-primary px-1 transition-bottom-2 duration-300"
        :class="[showParentList ? 'visible' : 'invisible']"
        :style="{
          bottom: showParentList ? i * ITEM_HEIGHT + 'px' : 0,
        }"
        @click="selectNode(item.id)"
      >
        {{ item.title }}
      </div>
    </div>
  </div>
</template>

<script>
const ITEM_HEIGHT = 25
export default {
  name: 'LCActionParent',
  inject: ['$doc'],
  props: {
    selectState: Object,
  },
  data() {
    return {
      showParentList: false,
      parentList: [],
      popupDir: 'bottom',
      ITEM_HEIGHT,
    }
  },
  computed: {
    computedStyle() {
      let showParentList = this.showParentList
      let parentList = this.parentList
      let result = {
        height: showParentList ? parentList.length * ITEM_HEIGHT + 'px' : 0,
      }
      if (this.popupDir === 'top') {
        result.transform = `translateY(${showParentList ? -((parentList.length - 1) * ITEM_HEIGHT) - 10 + 'px' : 0})`
      } else {
        result.transform = `translateY(${showParentList ? '40px' : 0})`
      }
      return result
    },
    parentListForRender() {
      if (this.popupDir === 'bottom') {
        return [...this.parentList].reverse()
      }
      return this.parentList
    },
  },
  watch: {
    parentList() {
      this.updatePopupDir()
    },
  },
  mounted() {
    if (this.selectState.parent) {
      this.getParentList()
    }
    this.$doc.select.onChange(() => {
      if (this.selectState.parent) {
        this.showParentList = false
        this.getParentList()
      } else {
        this.parentList = []
      }
    })
    this.updatePopupDir()
  },
  methods: {
    getParentList() {
      const parentList = []
      const { parent } = this.$doc.getCurrent()
      if (!parent) return
      let _parent = this.$doc.node.getNode(parent.id)
      while (_parent) {
        parentList.push({
          title: this.getTitle(_parent),
          id: _parent.id,
        })
        _parent = this.$doc.node.getNode(_parent.id, true).parent
      }
      this.parentList = parentList
    },
    getTitle(schema = this.$doc.getCurrent()?.schema) {
      let componentName = schema?.componentName
      return (
        schema?.title ||
        (this.$doc.getMaterial(componentName)?.title ?? componentName)
      )
    },
    selectNode(id) {
      this.$doc.selectNode(id)
      this.showParentList = false
    },
    updatePopupDir() {
      // 判断下方空间是否足够，不足时向上弹出，否则向下弹出
      const rect = this.$el.getBoundingClientRect()
      const triggerTop = rect.top
      const triggerHeight = rect.height
      const popupHeight = this.parentList.length * ITEM_HEIGHT
      const spaceBelow = window.innerHeight - (triggerTop + triggerHeight)
      this.popupDir = spaceBelow < popupHeight ? 'top' : 'bottom'
    },
  },
}
</script>
