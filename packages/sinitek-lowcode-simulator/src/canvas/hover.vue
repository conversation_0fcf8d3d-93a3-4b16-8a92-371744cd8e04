<template>
  <div
    v-show="hoverState.height && hoverState.width"
    class="hover z-9998 fs-14 canvas-rect"
    :style="computedStyle"
    :class="
      lineState.forbidden ? 'bg-red bg-op-50 w-full h-full left-0 top-0' : ''
    "
  >
    <div class="absolute -top-5">
      {{ hoverState.componentName }}
    </div>
    <div
      v-show="isContainer"
      class="absolute whitespace-nowrap b-1 b-bColor rounded bg-[#ccc] px-1 text-#f5f5f5 -bottom-6 -right-1px"
    >
      拖放元素到容器内
    </div>
  </div>
</template>

<script>
export default {
  name: 'LCHover',
  props: ['hoverState', 'lineState'],
  computed: {
    isContainer() {
      return this.hoverState.configure?.component?.isContainer
    },
    computedStyle() {
      const state = this.hoverState
      return {
        top: (state?.top ?? 0) + 'px',
        left: (state?.left ?? 0) + 'px',
        height: (state?.height ?? 0) + 'px',
        width: (state?.width ?? 0) - 1 + 'px',
      }
    },
  },
}
</script>
