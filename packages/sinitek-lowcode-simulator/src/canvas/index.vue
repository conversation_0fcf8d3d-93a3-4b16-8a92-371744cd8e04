<template>
  <div v-if="loaded" class="canvas-wrap wh-full overflow-hidden">
    <!-- 修改滚动条样式 -->
    <div
      class="canvas-box relative mx-a wh-full overflow-x-hidden overflow-y-auto"
      @scroll="onScroll"
    >
      <!-- 主体容器 -->
      <!-- click改为mousedown只在鼠标按下时触发拖动 -->
      <div
        class="LCCanvas relative h-full min-w-xs p-1px"
        @mousedown.capture.right="onRightClick"
      >
        <SinitekLowcodeRender
          v-if="Object.keys(config).length"
          :config="config"
          :mode="DesignMode.DESIGN"
          @renderNode="setNode"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { deepClone, findParent, getElement, bind, combine } from '../utils'
import SinitekLowcodeRender from 'sinitek-lowcode-render'
import {
  Design,
  DesignMode,
  addCtxComponent,
  addCtxComponents,
} from 'sinitek-lowcode-shared'

export default {
  name: 'LCCanvas',
  components: {
    SinitekLowcodeRender,
  },
  provide() {
    return {
      $doc: window.parent.__LCDoc__,
    }
  },
  data() {
    return {
      target: null,
      config: {},
      doc: window.parent.__LCDoc__,
      DesignMode,
      loaded: false,
    }
  },
  beforeDestroy() {
    this.clean?.()
  },
  mounted() {
    // 加载完成后，先设置一次config。防止change没监听到
    const doc = this.doc
    doc.read(() => {
      this.loaded = true
      this.config = doc.getSchema()
      doc.schema.onChange((arg) => {
        this.config = arg
      })

      this.$nextTick(() => {
        // 确保在dom渲染后触发
        window.parent.dispatchEvent(
          new CustomEvent('mounted', {
            detail: {
              addCtxComponent,
              addCtxComponents,
            },
          })
        )
      })
      this.clean = combine(
        bind(document, 'keydown', (e) => {
          this.doc.hotkey.setTarget('canvas')
          window.parent.dispatchEvent(new KeyboardEvent('keydown', e))
        }),
        bind(document, 'mousemove', (e) => {
          window.parent.document.dispatchEvent(new MouseEvent('mousemove', e))
        }),
        bind(document, 'mouseup', (e) => {
          window.parent.document.dispatchEvent(new MouseEvent('mouseup', e))
        }),
        bind(document.body, 'mousedown', this.onClick, {
          capture: true,
        }),
        bind(document.body, 'mousedown', this.onClick, {
          capture: true,
        }),
        bind(document, 'paste', (event) => {
          event.preventDefault()
          event.stopPropagation()
          // 获取粘贴的内容
          const clipboardData = event.clipboardData
          const pastedData = clipboardData.getData('text')
          try {
            const cd = JSON.parse(pastedData)
            // 如果当前元素不是容器则提示错误
            const { schema, parent } = this.doc.getCurrent()
            if (!cd || !schema || cd.type !== 'schema' || !cd.node) return
            const copyData = cd.node
            const cleanIdData = this.doc.schema.cloneSchema(copyData)
            const isModal = this.doc.materials.getConfigure(
              schema.componentName
            )?.component?.isModal
            if (isModal) {
              this.doc.getSchema().children.push(cleanIdData)
              this.doc.schema.update()
              this.doc.history.addHistory()
              return
            }
            if (copyData.componentName === 'Page') {
              if (this.config?.message?.confirm) {
                this.config?.message
                  ?.confirm('整个页面都会被替换，是否继续？', '提示')
                  .then(() => {
                    this.setSchema(copyData)
                  })
              } else {
                // eslint-disable-next-line no-alert
                if (window.confirm('整个页面都会被替换，是否继续？')) {
                  this.setSchema(copyData)
                }
              }
              this.doc.select.clean()
              return
            }
            this.doc.node.copyNode({ data: copyData, node: schema, parent })
          } catch (e) {
            console.error(e)
          }
        })
      )
    })
  },
  methods: {
    setNode({ schema, parent, scope }) {
      // setNode(schema, parent)
      this.doc?.node?.setNode(schema, parent, scope)
    },
    onClick($event) {
      // 给clickOutside使用
      // 用来触发隐藏插件面板
      window.parent.document.dispatchEvent(new KeyboardEvent('click', $event))
      // getCtx().api.hidePanel()
      const el = getElement($event.target)
      // 给输入框添加禁用属性
      if (['INPUT', 'TEXTAREA'].includes($event.target.tagName)) {
        $event.target.setAttribute('readonly', 'true')
        $event.target.blur()
      }
      // 如果点击的内容是select组件里的则不清空选中
      if (findParent($event.target, '.canvas-rect.select')) return
      // 如果input-number正在设置元素属性时，切换了元素或者清空选中
      // 导致input-number输入的值无法设置,所以延迟清空和切换
      setTimeout(() => {
        if (!el) {
          this.doc.select.clean()
          return
        }
        const id = el.getAttribute(Design.NODE_UID)
        const node = this.doc.node.selectNode(id)
        if (node) {
          // 给选中的元素添加拖拽数据
          this.doc.drag.dragStart(deepClone(node))
        }
      })
    },
    onRightClick(e) {
      e.preventDefault()
    },
    onScroll() {
      this.doc.hover.clean()
      this.doc.select.reselect()
    },
  },
}
</script>

<style lang="scss">
html,
body {
  height: 100%;
  // user-select: none;
}
.canvas-box {
  &::-webkit-scrollbar {
    width: 8px;
    height: 4px;
    margin-right: 20px;
  }
  &::-webkit-scrollbar-thumb {
    background: #f2f2f2;
    border-radius: 6px;
  }
}
// .LCCanvas {
//   [class*='xn-input--width'] {
//     width: 100% !important;
//   }
// }
</style>
