<template>
  <div
    v-show="selectState.height && selectState.width"
    ref="select"
    class="select z-9999 b-2px b-solid fs-14 canvas-rect"
    :style="computedStyle"
  >
    <template v-if="selectState.height && selectState.width">
      <div
        class="shadow-log pointer-events-auto absolute z-2 flex cursor-pointer items-center whitespace-nowrap bg-primary pl-1 pr-2 text-white -bottom-22px"
        :class="actionPosition"
      >
        <template v-for="item of actions">
          <component
            :is="item.component"
            :key="item.id"
            :select-state="selectState"
            class="ml-1 flex-shrink-0"
            :style="{ order: item.order || 1 }"
          />
        </template>
        <template v-if="isModal">
          <EyeIcon :hidden="hidden" @click.native.stop="onClickHidden" />
        </template>
      </div>
    </template>
    <LCCut v-if="isCutComponent" />
    <LCResize
      v-if="isResizeComponent"
      :current="selectState && selectState.current"
    />
  </div>
</template>

<script>
import LCCut from './cut.vue'
import LCResize from './resize.vue'
import EyeIcon from '@common/eye-icon.vue'
export default {
  name: 'LCSelect',
  components: {
    LCCut,
    LCResize,
    EyeIcon,
  },
  inject: ['$doc'],
  props: ['selectState'],
  data() {
    return {
      hidden: false,
      actions: [],
      originActions: [],
    }
  },
  computed: {
    isCutComponent() {
      return ['flex', 'lcflex'].includes(
        this.selectState?.componentName?.toLowerCase?.()
      )
    },
    isResizeComponent() {
      return ['lcflex', 'lcrow', 'lccol'].includes(
        this.selectState?.componentName?.toLowerCase?.()
      )
    },
    computedStyle() {
      const state = this.selectState
      return {
        top: (state?.top ?? 0) - 1 + 'px',
        left: (state?.left ?? 0) - 1 + 'px',
        height: (state?.height ?? 0) + 1 + 'px',
        width: (state?.width ?? 0) + 1 + 'px',
      }
    },
    isModal() {
      return this.selectState?.configure?.component?.isModal
    },
    actionPosition() {
      if (this.selectState.left < 20 && this.selectState.width < 100) {
        return '-left-2px'
      }
      return '-right-2px'
    },
  },
  watch: {
    selectState: {
      immediate: true,
      deep: true,
      handler(v) {
        this.hidden = v?.state?.hidden
        if (
          v.configure?.component?.disableBehaviors?.includes('*') ||
          v.isChoiceComponent
        ) {
          this.actions = this.originActions.filter((e) => e.id === 'parent')
          return
        }
        this.actions = this.originActions.filter(
          (e) =>
            !(v.configure?.component?.disableBehaviors || []).includes(e.id)
        )
      },
    },
  },
  created() {
    this.$doc.getPlugins('selectActionArea', (plugins) => {
      this.originActions = plugins.sort((a, b) => a.index - b.index)
    })
  },
  methods: {
    onClickHidden() {
      this.hidden = !this.hidden

      const schema = this.$doc.getCurrent().schema
      if (schema.hidden == void 0) {
        this.$set(schema, 'hidden', this.hidden)
      } else {
        schema.hidden = this.hidden
      }
      this.$doc.select.clean()
      this.$doc.hover.clean()
    },
  },
}
</script>
