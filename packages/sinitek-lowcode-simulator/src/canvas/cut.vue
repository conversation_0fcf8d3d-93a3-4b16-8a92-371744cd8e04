<template>
  <div class="absolute wh-full">
    <div
      class="cut-btn pointer-events-auto absolute left-0 top-1/2 z-2 h-6 w-6 f-c-c rounded-full bg-white shadow -mt-3"
      @click="onClick('LCCol')"
    >
      <div class="i-lucide-scissors h-4 w-4 bg-black"></div>
    </div>
    <div
      class="absolute left-0 right-0 top-1/2 hidden b-1 b-dashed -mt-1px important-b-primary"
    ></div>
    <div
      class="cut-btn pointer-events-auto absolute left-1/2 top-0 z-2 h-6 w-6 f-c-c rotate-90 rounded-full bg-white shadow -ml-2"
      @click="onClick('LCRow')"
    >
      <div class="i-lucide-scissors h-4 w-4 bg-black"></div>
    </div>
    <div
      class="absolute bottom-0 left-1/2 top-0 ml-3px hidden b-1 b-primary b-dashed"
    ></div>
  </div>
</template>

<script>
export const isRow = (name) => {
  return ['lcrow'].includes(name.toLowerCase())
}
export const isCol = (name) => {
  return ['lccol'].includes(name.toLowerCase())
}
export const isFlex = (name) => {
  return ['lcflex'].includes(name.toLowerCase())
}
import cloneDeep from 'lodash/cloneDeep'
export default {
  name: 'LCCut',
  inject: ['$doc'],
  methods: {
    onClick(type) {
      const { schema, parent } = this.$doc.getCurrent()
      const parentComponent = parent.componentName

      const newSchema = {
        componentName: 'LCFlex',
        props: {},
        children: [],
      }
      let sid = schema.id
      if (isRow(parentComponent)) {
        // 如果父级是row，切割的是row
        // 在当前切割的后面添加一个新的flex
        if (isRow(type)) {
          newSchema.componentName = 'LCFlex'
          const index = parent.children.findIndex((e) => e.id === schema.id)
          parent.children.splice(index + 1, 0, newSchema)
        } else {
          // 如果父级是row，切割的是col
          const first = cloneDeep(schema)
          delete schema.id
          schema.componentName = 'LCCol'
          schema.children = [first, newSchema]
        }
      } else if (isCol(parentComponent)) {
        // 如果父级是col，那么切割的是row
        if (isCol(type)) {
          newSchema.componentName = 'LCFlex'
          const index = parent.children.findIndex((e) => e.id === schema.id)
          parent.children.splice(index + 1, 0, newSchema)
        } else {
          // 如果父级是row，切割的是col
          const first = cloneDeep(schema)
          delete schema.id
          schema.componentName = 'LCRow'
          schema.children = [first, newSchema]
        }
      } else {
        // 没有子元素的情况，使用哪种切割，就替换成那种元素
        const first = cloneDeep(schema)
        schema.componentName = type
        delete schema.id
        sid = first.id
        schema.children = [first, newSchema]
      }
      this.$nextTick(() => {
        this.$doc.node.selectNode(sid)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.cut-btn {
  cursor: pointer;
  &:hover {
    + .hidden {
      display: block;
    }
  }
}
</style>
