<template>
  <div
    :class="[
      'resize-border b-1 b-solid important-b-primary',
      'border-' + direction,
    ]"
    @mousedown.stop="onMouseDown"
  ></div>
</template>

<script>
import { Design } from 'sinitek-lowcode-shared'
// import { isFlex, isRow, isCol } from './cut.vue'

export default {
  name: 'LCResizeBorder',
  inject: ['$doc'],
  props: {
    direction: {
      type: String,
      default: 'top',
    },
  },
  methods: {
    onMouseDown() {
      const minWidth = 30
      const minHeight = 30
      const currentDocument = this.$doc.frameDocument
      const { schema, parent } = this.$doc.getCurrent()
      if (!schema) return

      const id = schema.id
      const el = currentDocument.body.querySelector(
        `[${Design.NODE_UID}='${id}']`
      )
      const elRect = el.getBoundingClientRect()
      const parentRect = el.parentElement.getBoundingClientRect()

      // 统一维度计算
      const dimension = this.calculateDimensions(elRect, parentRect)
      this.setGlobalCursor()

      const componentName = schema.componentName
      const setPropKey = this.getSetPropKey(componentName, parent)
      const props = {}
      let mousemove = null

      if (['right', 'left'].includes(this.direction)) {
        mousemove = this.handleHorizontalMove(
          dimension,
          minWidth,
          setPropKey,
          props
        )
      } else {
        mousemove = this.handleVerticalMove(
          dimension,
          minHeight,
          setPropKey,
          props
        )
      }

      const mouseup = () => {
        this.$doc.select.clean()
        schema.props = { ...schema.props, ...props }

        // 重置兄弟元素basis属性
        parent.children.forEach((e) => {
          if (e.id !== schema.id) e.props.basis = 'auto'
        })

        currentDocument.body.style.cursor = 'auto'
        this.scheduleReSelect(id)

        document.removeEventListener('mousemove', mousemove)
        document.removeEventListener('mouseup', mouseup)
      }

      document.addEventListener('mousemove', mousemove)
      document.addEventListener('mouseup', mouseup)
    },

    calculateDimensions(elRect, parentRect) {
      return {
        width: this.$doc.select.state.width,
        height: this.$doc.select.state.height,
        left: this.$doc.select.state.left,
        top: this.$doc.select.state.top,
        maxWidth: parentRect.width,
        maxHeight: parentRect.height,
        x: elRect.left - parentRect.left,
        y: elRect.top - parentRect.top,
      }
    },

    setGlobalCursor() {
      const currentDocument = this.$doc.frameDocument
      currentDocument.body.style.cursor = ['left', 'right'].includes(
        this.direction
      )
        ? 'e-resize'
        : 'n-resize'
    },

    getSetPropKey() {
      // if (!isFlex(componentName)) {
      //   if (isRow(parent.componentName)) return 'width'
      //   if (isCol(parent.componentName)) return 'height'
      // }
      return 'basis'
    },

    handleHorizontalMove(dimension, minWidth, setPropKey, props) {
      const { x, left, width, maxWidth } = dimension
      const selectMinLeft = left - x + minWidth
      const selectMaxLeft = left + width - minWidth
      return ({ movementX }) => {
        const state = this.$doc.select.state
        let newLeft = state.left
        let newWidth = state.width

        if (this.direction === 'left') {
          newWidth = Math.max(
            minWidth,
            Math.min(state.width - movementX, x + width - minWidth)
          )
          newLeft = Math.max(
            Math.min(state.left + movementX, selectMaxLeft),
            selectMinLeft
          )
        } else {
          newWidth = Math.min(state.width + movementX, maxWidth - x - minWidth)
          newWidth = Math.max(newWidth, minWidth)
        }

        this.updateDimensions(
          { width: newWidth, left: newLeft },
          setPropKey,
          props,
          newWidth
        )
      }
    },

    handleVerticalMove(dimension, minHeight, setPropKey, props) {
      const { y, maxHeight } = dimension
      const minTop = this.$doc.select.state.top - y + minHeight
      return ({ movementY }) => {
        const state = this.$doc.select.state
        let newTop = state.top
        let newHeight = state.height

        if (this.direction === 'top') {
          newHeight = Math.max(
            minHeight,
            Math.min(state.height - movementY, y + dimension.height - minHeight)
          )
          newTop = Math.max(
            Math.min(
              state.top + movementY,
              dimension.top + dimension.height - minHeight
            ),
            minTop
          )
        } else {
          newHeight = Math.min(
            state.height + movementY,
            maxHeight - y - minHeight
          )
          newHeight = Math.max(newHeight, minHeight)
        }

        this.updateDimensions(
          { height: newHeight, top: newTop },
          setPropKey,
          props,
          newHeight
        )
      }
    },

    updateDimensions(dimensions, setPropKey, props, value) {
      this.$doc.select.set(dimensions)
      this.$doc.select.update()
      props[setPropKey] = `${value}px`
    },

    scheduleReSelect(id) {
      this.$nextTick(() =>
        setTimeout(() => {
          this.$doc.node.selectNode(id)
        }, 150)
      )
    },
  },
}
</script>

<style lang="scss" scoped>
$size: 3px;
.resize-border {
  @apply absolute z-3 pointer-events-auto;

  &.border-top {
    @apply -top-1 left-1/2 h-1 w-1/2;
    border-radius: $size $size 0 0;
    transform: translateX(-50%);
    cursor: n-resize;
  }
  &.border-bottom {
    @apply -bottom-1 left-1/2 h-1 w-1/2;
    border-radius: 0 0 $size $size;
    transform: translateX(-50%);
    cursor: n-resize;
  }
  &.border-left {
    @apply -left-1 top-1/2 w-1 h-1/2;
    border-radius: $size 0 0 $size;
    transform: translateY(-50%);
    cursor: e-resize;
  }
  &.border-right {
    @apply -right-1 top-1/2 w-1 h-1/2;
    border-radius: 0 $size $size 0;
    transform: translateY(-50%);
    cursor: e-resize;
  }
}
</style>
