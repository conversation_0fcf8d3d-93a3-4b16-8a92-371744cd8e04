<template>
  <div
    v-show="
      lineState && lineState.height && lineState.width && !lineState.forbidden
    "
    class="line pointer-events-none z-9999 b-transparent canvas-rect"
    :style="computedStyle"
  >
    <div :class="['absolute pointer-events-none', position]">
      <div
        v-if="lineState.position === 'in' && hoverState && hoverState.configure"
        class="flex-center h-full w-full"
      >
        <div
          v-if="!lineState.forbidden"
          class="insert-indicator rounded-sm px-2 py-1 text-center text-gray-500"
        >
          <i class="el-icon-plus mr-1"></i>插入到容器
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LCLine',
  props: ['lineState', 'hoverState'],
  computed: {
    computedStyle() {
      const state = this.lineState || {}
      return {
        top: (state.top ?? 0) + 'px',
        left: Math.max(state.left ?? 0, 2) + 'px',
        height: (state.height ?? 2) + 'px',
        width: (state.width ?? 2) + 'px',
        zIndex: 9999,
      }
    },
    position() {
      if (!this.lineState || !this.lineState.position) return ''

      const map = {
        top: 'w-full h-1 bg-primary -top-1',
        right: 'w-1 h-full bg-primary -right-1',
        bottom: 'w-full h-1 bg-primary -bottom-1',
        left: 'w-1 h-full bg-primary -left-1',
        in: 'bg-green bg-op-10 w-full h-full left-0 top-0',
        forbid: 'bg-red bg-op-50 w-full h-full left-0 top-0',
      }
      return map[this.lineState?.position]
    },
  },
}
</script>
