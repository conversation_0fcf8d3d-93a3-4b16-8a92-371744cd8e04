<template>
  <div class="absolute left-43% f-c-c">
    <!-- 切换模拟器大小 -->
    <div class="i-custom-desktop hidden"></div>
    <div class="i-custom-mobile hidden"></div>
    <div class="lc-button-group">
      <div
        v-for="(item, i) of platformList"
        :key="i"
        :class="{
          'lc-button-item--active': platform === item.key,
        }"
        class="lc-button-item"
        @click="changePlatform(item)"
      >
        <div :class="item.icon" class="h-4 w-4"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LCHistory',
  inject: ['$doc'],
  data() {
    return {
      platformList: [
        {
          icon: 'i-custom-desktop',
          float: '电脑端',
          key: 'desktop',
        },
        {
          icon: 'i-custom-mobile',
          float: '手机端',
          key: 'phone',
        },
      ],
      platform: 'desktop',
    }
  },
  methods: {
    changePlatform(item) {
      this.platform = item.key
      this.$doc.container.setDevice(item.key)
    },
  },
}
</script>
