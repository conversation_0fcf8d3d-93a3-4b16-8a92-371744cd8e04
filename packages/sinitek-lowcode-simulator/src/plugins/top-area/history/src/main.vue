<template>
  <div class="flex flex-shrink-0">
    <div
      class="lc-plugin-top-button"
      :class="[![3, 4].includes(state) ? 'text-#999' : 'icon-hover text-#666']"
      @click="undo"
    >
      <div
        class="i-custom-undo h-5 w-5"
        :float="![3, 4].includes(state) ? '没有要撤销的' : '撤销'"
      ></div>
    </div>
    <div
      class="lc-plugin-top-button"
      :class="[![1, 4].includes(state) ? 'text-#999' : 'icon-hover text-#666']"
      @click="redo"
    >
      <div
        class="i-custom-redo h-5 w-5"
        :float="![1, 4].includes(state) ? '没有要恢复的' : '恢复'"
      ></div>
    </div>
  </div>
</template>

<script>
import { HistoryState } from '@doc/history'
export default {
  name: 'LCHistory',
  inject: ['$doc'],
  data() {
    return {
      state: HistoryState.NONE,
      HistoryState,
    }
  },
  mounted() {
    this.$doc.history.onChange((state) => {
      this.state = state
    })
  },
  methods: {
    redo() {
      this.$doc.history.forward()
    },
    undo() {
      this.$doc.history.back()
    },
  },
}
</script>
