import cloneDeep from 'lodash/cloneDeep'

export const getId = (properties) => {
  return getPropValue(properties, 'id')
}
export const getPropValue = (properties = [], prop) => {
  return properties.find((item) => item.key.value === prop)?.value.value
}

export const findParentComponent = (schema, componentName, doc) => {
  let _schema = cloneDeep(schema)
  const _find = (_componentName) => {
    const _parent = doc.node.getNode(_schema.id, true)?.parent
    if (!_parent) return null
    _schema = _parent
    if (_parent.componentName === _componentName) {
      return _parent
    }
    return _find(_componentName)
  }
  return _find(componentName)
}
