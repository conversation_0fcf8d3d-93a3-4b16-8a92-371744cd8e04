import { noRepeatId } from './no-repeat-id'
import { noRepeatRef } from './no-repeat-ref'
import { colDataTypeDateAlignCenter } from './col-dataType-date-align-center'
import { dialogNestTableHasHeight } from './dialog-nest-table-has-height'
import { linkHrefClick } from './link-href-click'
import { formItemProp } from './form-item-prop'
import { tableDataSource } from './table-datasource'
import { flexNestTable } from './flex-nest-table'
import { collapse } from './collapse'
import { inputInXnFormItem } from './input-in-xn-form-item'
import { dialogNoRepeatValue } from './dialog-no-repeat-value'
export default [
  noRepeatId,
  noRepeatRef,
  colDataTypeDateAlignCenter,
  dialogNestTableHasHeight,
  linkHrefClick,
  formItemProp,
  tableDataSource,
  flexNestTable,
  collapse,
  inputInXnFormItem,
  dialogNoRepeatValue,
]
