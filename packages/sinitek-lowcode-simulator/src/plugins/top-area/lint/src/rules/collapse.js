/**
 * 校验折叠面板项名称和标题唯一性
 * 1. 检测重复的name标识符
 * 2. 检测重复的title标题
 * 3. 检测缺少标题的折叠项
 */
import { getId } from './utils'

// 支持的折叠面板组件名称
const COLLAPSE_COMPONENT_NAMES = ['el-collapse', 'ElCollapse']

export function collapse({ node, parent, error, $doc }) {
  // 仅处理折叠面板组件
  if (
    node.key.value === 'componentName' &&
    COLLAPSE_COMPONENT_NAMES.includes(node.value.value)
  ) {
    const collapseId = getId(parent.properties)
    if (!collapseId) return

    const collapseNode = $doc.node.getNode(collapseId)
    if (!collapseNode?.children?.length) return

    // 创建重复检测器
    const createDuplicateChecker = (errorMsg, level) => {
      const values = new Set()
      return (value, index) => {
        if (values.has(value)) {
          error.push({
            id: collapseNode.id,
            message: `折叠面板，下标为${index}的${errorMsg}【${value}】重复`,
            level,
          })
        }
        values.add(value)
      }
    }

    const checkNameDuplicate = createDuplicateChecker('唯一标识符', 'error')
    const checkTitleDuplicate = createDuplicateChecker('标题', 'warning')

    // 遍历子节点
    for (const [index, childNode] of collapseNode.children.entries()) {
      const { props, children = [] } = childNode

      // 检测缺少标题的情况
      if (!props?.title && children.length === 0) {
        error.push({
          id: collapseNode.id,
          message: `折叠面板项 #${index + 1} 缺少标题`,
          level: 'warning',
        })
        continue
      }

      // 检查标题和名称重复
      if (props?.title) checkTitleDuplicate(props.title, index)
      if (props?.name) checkNameDuplicate(props.name, index)
    }
  }
}
