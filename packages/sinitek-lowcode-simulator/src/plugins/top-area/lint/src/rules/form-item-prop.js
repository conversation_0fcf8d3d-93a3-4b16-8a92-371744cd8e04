import { getId } from './utils'
//检查表单的子元素prop是否正常设置
export function formItemProp({ node, parent, error, $doc }) {
  if (
    node.key.value === 'componentName' &&
    ['xn-form-item', 'XnFormItem', 'LAFormItem'].includes(node.value.value)
  ) {
    const id = getId(parent.properties)
    if (!id) return
    const _node = $doc.node.getNode(id)
    if (!_node) return
    if (node.value.value === 'LAFormItem') {
      if (!_node.props?.itemProps?.prop) {
        error.push({
          value: '',
          id: _node.id,
          loc: parent.loc,
          path: ['itemProps', 'prop'],
          message: 'LAFormItem组件必须设置itemProps.prop',
          level: 'warning',
        })
      }
    } else if (_node?.children?.length <= 1 && !_node.props?.prop) {
      error.push({
        value: '',
        id: _node.id,
        loc: parent.loc,
        path: ['prop'],
        message: 'XnFormItem组件必须设置prop',
        level: 'warning',
      })
    }
  }
}
