import { getId } from './utils'

// table数据源检查
export function tableDataSource({ node, parent, error, $doc }) {
  if (node.key.value === 'componentName' && node.value.value === 'xn-table') {
    const id = getId(parent.properties)
    if (!id) return
    const _node = $doc.node.getNode(id)
    if (!_node) return
    // 检查是否同时使用两个及以上数据源属性
    const hasDataSource = !!_node.props?.datasource
    const hasURL = !!_node.props?.url
    const hasData = !!_node.props?.data

    if (
      (hasDataSource && hasURL) ||
      (hasDataSource && hasData) ||
      (hasURL && hasData)
    ) {
      error.push({
        value: '',
        id: _node.id,
        loc: parent.loc,
        message: 'xn-table组件只能设置datasource、url、data其中一种',
        level: 'warning',
      })
    }
  }
}
