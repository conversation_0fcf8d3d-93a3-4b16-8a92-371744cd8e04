// dialog嵌套表格，表格必须设置高度
import { getId, findParentComponent } from './utils'
export function dialogNestTableHasHeight({ node, parent, error, $doc }) {
  if (
    node.key.value === 'componentName' &&
    ['la-table', 'LATable'].includes(node.value.value)
  ) {
    const id = getId(parent.properties)
    if (!id) return
    const _node = $doc.node.getNode(id)
    if (!_node || _node.props?.height) return
    const _parent = findParentComponent(_node, 'LADialog', $doc)
    if (_parent) {
      error.push({
        value: '',
        id: _node.id,
        loc: node.loc,
        message: '在弹框中使用的表格必须设置高度',
        level: 'error',
      })
    }
  }
}
