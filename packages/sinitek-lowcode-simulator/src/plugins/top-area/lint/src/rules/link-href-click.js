import { getId } from './utils'

// 检查link组件的href属性和点击事件不能同时存在
export function linkHrefClick({ node, parent, error, $doc }) {
  if (
    node.key.value === 'componentName' &&
    ['el-link', 'ElLink'].includes(node.value.value)
  ) {
    const id = getId(parent.properties)
    const _node = $doc.node.getNode(id)
    if (_node.props?.href && _node.events?.click) {
      error.push({
        value: '',
        id: _node.id,
        loc: node.loc,
        message: '链接组件的“链接地址”属性和点击事件不能同时存在',
        level: 'error',
      })
    }
  }
}
