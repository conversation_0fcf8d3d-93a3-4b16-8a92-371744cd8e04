import { getId } from './utils'
// 检查xn-col组件的数据类型是date，列必须居中
export function colDataTypeDateAlignCenter({ node, parent, error, $doc }) {
  if (
    node.key.value === 'componentName' &&
    ['xn-col', 'XnColAdvanced'].includes(node.value.value)
  ) {
    const id = getId(parent.properties)
    if (!id) return
    const { node: _node, parent: _parent } = $doc.node.getNode(id, true) ?? {}
    if (!_node) return
    if (_node.props?.dataType === 'date' && _node.props?.align !== 'center') {
      error.push({
        value: _node.props?.align,
        id: _parent.id,
        loc: parent.loc,
        message: `表格组件下数据列【${_node.props?.label}】的数据类型是日期，列对齐必须居中，当前值为【${_node.props?.align ?? '--'}】`,
        level: 'warning',
      })
    }
  }
}
