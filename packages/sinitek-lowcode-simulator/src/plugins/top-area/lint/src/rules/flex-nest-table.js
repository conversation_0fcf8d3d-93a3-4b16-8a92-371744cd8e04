// 布局组件下有table，basis不能是auto
import { getId, findParentComponent } from './utils'
export function flexNestTable({ node, parent, error, $doc }) {
  if (
    node.key.value === 'componentName' &&
    ['la-table', 'LATable'].includes(node.value.value)
  ) {
    const id = getId(parent.properties)
    if (!id) return
    const _node = $doc.node.getNode(id)
    if (!_node || node.props?.height) return
    const _parent = findParentComponent(_node, 'LCFlex', $doc)
    if (_parent) {
      if (_node.props?.height) return
      error.push({
        value: '',
        id: _node.id,
        loc: node.loc,
        message: '布局组件下有table，必须设置表格高度',
        level: 'error',
      })
    }
  }
}
