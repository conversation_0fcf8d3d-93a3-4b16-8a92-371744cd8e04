// 高级表单子元素长度为1，需要判断列数是否为1列

import { getId } from './utils'
export function laFormChildOne({ node, parent, $doc, error }) {
  if (
    node.key.value === 'componentName' &&
    ['la-form', 'LAForm'].includes(node.value.value)
  ) {
    const id = getId(parent.properties)
    const _node = $doc.node.getNode(id)
    if (_node.children.length === 1 && _node.props?.column !== 1) {
      error.push({
        id: _node.id,
        loc: node.loc,
        message: '高级表单只有一个子元素，列数最好保持为1列',
        level: 'warning',
      })
    }
  }
}
