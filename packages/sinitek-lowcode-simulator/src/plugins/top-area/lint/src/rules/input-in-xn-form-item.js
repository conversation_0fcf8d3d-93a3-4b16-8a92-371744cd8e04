// 输入组件需要在xn-form-item组件中使用
import { getId, findParentComponent } from './utils'
const InputComponentNames = [
  'LAInput',
  'ElInput',
  'ElInputNumber',
  'ElSelect',
  'ElDatePicker',
  'ElTimeSelect',
  'ElDateRangePicker',
  'ElTimePicker',
  'ElTimeRangePicker',
  'XnUpload',
  'XnCascader',
  'ElCascader',
  'ElRate',
]
export function inputInXnFormItem({ node, parent, error, $doc }) {
  if (
    node.key.value === 'componentName' &&
    InputComponentNames.includes(node.value.value)
  ) {
    const id = getId(parent.properties)
    if (!id) return
    const _node = $doc.node.getNode(id)
    if (!_node) return
    const _parent = findParentComponent(_node, 'XnFormItem', $doc)

    if (!_parent) {
      error.push({
        id: node.id,
        message: `${node.value.value}输入组件需要在“表单元素容器”组件中使用`,
        level: 'warning',
      })
    }
  }
}
