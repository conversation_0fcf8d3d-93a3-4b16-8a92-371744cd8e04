import { getId } from './utils'
// 多个弹框是否绑定同一个值，警告
export function dialogNoRepeatValue({ node, map, error, $doc }) {
  if (
    node.key.value === 'componentName' &&
    ['la-dialog', 'LADialog'].includes(node.value.value)
  ) {
    const id = getId(parent.properties)
    if (!id) return
    const _node = $doc.node.getNode(id)
    if (!_node) return
    if (map.has(_node.props.value)) {
      error.push({
        id: node.id,
        value: node.value.value,
        loc: node.loc,
        message: `多个弹框绑定同一个值，重复值为${node.value.value}`,
        level: 'warning',
      })
    } else {
      map.set(_node.props.value, 1)
    }
  }
}
