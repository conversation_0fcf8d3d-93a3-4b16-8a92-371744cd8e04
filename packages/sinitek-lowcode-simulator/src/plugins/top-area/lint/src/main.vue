<template>
  <div>
    <div
      v-show="error.length === 0"
      class="lc-plugin-top-button"
      float="检查"
      @click="onLint"
    >
      <div class="i-mdi:check-circle-outline h-5 w-5 bg-green-600"></div>
      <div v-show="isChecking" class="i-mdi:loading h-5 w-5"></div>
    </div>

    <el-popover width="320" trigger="hover" placement="bottom">
      <div class="overflow-hidden">
        <div class="h-7 flex items-center b-b-1 fs-14">
          错误: <span class="text-red-600">{{ errorLen }}</span
          >， 警告: <span class="text-yellow-600">{{ warningLen }}</span>
        </div>
        <div class="max-h-50 overflow-x-hidden overflow-y-auto">
          <div
            v-for="(item, index) in error"
            :key="index"
            class="flex cursor-pointer items-center b-b-1 p-y-1 fs-14"
            @click="selectComponent(item)"
          >
            <div
              class="mr-2 h-4 w-4 flex-shrink-0"
              :class="[
                item.level === 'warning'
                  ? 'bg-yellow-600 i-mdi:error-outline'
                  : 'bg-red-600 i-mdi:close-circle-outline',
              ]"
            ></div>
            {{ item.message }}
          </div>
        </div>
      </div>
      <div
        v-show="error.length"
        slot="reference"
        class="lc-plugin-top-button"
        @click="onLint"
      >
        <div
          class="h-5 w-5"
          :class="[
            !errorLen
              ? 'bg-yellow-600 i-mdi:error-outline'
              : 'bg-red-600 i-mdi:close-circle-outline',
          ]"
        ></div>
        <div v-show="isChecking" class="i-mdi:loading h-5 w-5"></div>
      </div>
    </el-popover>
  </div>
</template>

<script>
import { string2AST } from '@/utils'
import rules from './rules'
import { getAPI } from 'sinitek-lowcode-shared'
import { on, off } from '@/utils/emitter'

const walk = (ast, options) => {
  const _run = (nodes, parent) => {
    nodes.forEach((node) => {
      const keys = Object.keys(options)
      keys.forEach((key) => {
        if (node.type === key) {
          options[key](node, parent)
        }
      })
      if (
        node.type === 'ObjectProperty' &&
        node.value.type === 'ArrayExpression'
      ) {
        _run(node.value.elements, node)
      }
      if (node.type === 'ObjectExpression' && node?.properties?.length) {
        _run(node.properties, node)
      }
    })
  }
  _run(ast)
}

export default {
  name: 'LCLint',
  components: {},
  inject: ['$doc'],
  data() {
    return {
      isShow: false,
      isChecking: false,
      error: [],
      visible: false,
    }
  },
  computed: {
    errorLen() {
      return this.error.filter((item) => item.level === 'error').length
    },
    warningLen() {
      return this.error.filter((item) => item.level === 'warning').length
    },
  },
  created() {
    on(['lint', 'props:change', 'node:remove', 'node:insertNode'], () => {
      this.onLint()
    })
  },
  beforeDestroy() {
    off(['lint', 'props:change', 'node:remove', 'node:insertNode'])
  },
  mounted() {
    let queue = []
    const run = () => {
      if (queue.length) {
        const schema = queue.pop()
        queue = []
        if (!schema || this.isChecking) return
        let error = []
        const kvMap = new WeakMap()
        const code = `export default ${schema}`
        const ast = string2AST(code)
        this.isChecking = true
        try {
          walk(ast.program.body[0].declaration.properties, {
            ObjectProperty: (node, parent) => {
              for (const rule of rules) {
                let map = kvMap.get(rule)
                if (!map) {
                  map = new Map()
                  kvMap.set(rule, map)
                }
                if (!this.$doc) return
                rule({ node, parent, map, error, $doc: this.$doc })
              }
            },
          })
        } catch (e) {
          console.error(e)
        } finally {
          this.isChecking = false
        }
        this.error = error
      }
    }
    const _run = (v) => {
      if (v) {
        queue.push(JSON.stringify(v, null, 2))
        setTimeout(() => {
          run()
        })
      }
    }
    _run(this.$doc.schema.schema)
    this.$doc.schema.onChange((arg) => {
      _run(arg)
    })

    this.onLint = () => {
      queue = [JSON.stringify(this.$doc.schema.schema, null, 2)]
      run()
    }
  },
  methods: {
    onLint() {},
    selectComponent(item) {
      if (item.type === 'openSchema') {
        const loc = item.loc
        getAPI().openPanel('Schema', {
          selection: {
            startLineNumber: loc.start.line,
            startColumn: loc.start.column + 1,
            endLineNumber: loc.end.line,
            endColumn: loc.end.column + 1,
          },
        })
      } else {
        if (!item.id) return
        this.$doc.node.selectNode(item.id)
      }
    },
  },
}
</script>
