<template>
  <div
    class="relative z-2000 h-50px flex shrink-0 items-center overflow-hidden bg-white shadow-[0px_2px_4px_0px_rgba(0,0,0,0.1)]"
  >
    <!-- 工具栏 -->
    <div v-if="left.length" class="left-btns w-full flex">
      <component :is="item.component" v-for="item of left" :key="item.id" />
    </div>
    <div v-if="right.length" class="right-btns ml-a flex">
      <component
        :is="item.component"
        v-for="item of right"
        :key="item.id"
        :style="{ order: item.order || 1 }"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'LCTopArea',
  inject: ['$doc'],
  data() {
    return {
      components: {},
      right: [],
      left: [],
    }
  },
  created() {
    this.$doc.getPlugins('topArea', (plugins) => {
      this.right = [...plugins.filter((e) => e.align === 'right')]
      this.left = [...plugins.filter((e) => e.align === 'left')]
    })
  },
}
</script>
