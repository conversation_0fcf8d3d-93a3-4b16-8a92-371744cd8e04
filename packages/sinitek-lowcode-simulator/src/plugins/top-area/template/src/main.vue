<template>
  <div class="ml-2">
    <div
      class="i-mdi:application-import h-6 w-5"
      float="导入模板"
      @click="open"
    ></div>
    <xn-dialog
      title="模板列表"
      :show.sync="show"
      :show-footer="false"
      :width="`${dialogWidth}px`"
      :height="`${dialogHeight}px`"
    >
      <el-container :style="`height:${dialogHeight - 20}px`">
        <el-header class="flex flex-row items-center"
          ><div class="flex basis-1/6 justify-start">
            <el-radio-group v-model="templateType" @input="changeTab">
              <el-radio-button
                v-for="item in radioGroupList"
                :key="item.value"
                :label="item.label"
              ></el-radio-button>
            </el-radio-group>
          </div>
          <div class="basis-1/4">
            <xn-input
              v-model.trim="queryParams.name"
              placeholder="请输入模板名称"
              clearable
            >
              <el-button
                slot="append"
                icon="el-icon-search"
                @click="search"
              ></el-button>
            </xn-input>
          </div>
        </el-header>
        <el-main v-loading="loading">
          <div
            v-show="pageTotal"
            class="grid auto-rows-auto grid-cols-5 h-full gap-2"
            :style="{ overflow: loading ? 'hidden' : 'auto' }"
          >
            <div
              v-for="item in templateList"
              :key="item.id"
              class="template border-1 border-gray-500/10 border-solid shadow"
              :style="`height:${(dialogHeight - 148) / 2}px`"
            >
              <div class="overlay">
                <el-button type="primary" @click="loadTemplate(item)"
                  >加载模板</el-button
                >
              </div>
              <div class="h-full flex flex-col">
                <div class="h-5/6">
                  <el-empty
                    v-if="!item.thumbnailBase64"
                    description=" "
                  ></el-empty>
                  <el-image
                    v-else
                    class="h-full w-full"
                    :src="item.thumbnailBase64"
                    fit="cover"
                  ></el-image>
                </div>
                <div class="title h-1/18 flex items-center justify-start px-2">
                  {{ item.name }}
                </div>
                <div
                  class="describe h-1/9 flex items-center justify-start px-2"
                >
                  {{ item.description }}
                </div>
              </div>
            </div>
          </div>
          <el-empty v-show="!pageTotal"></el-empty>
        </el-main>
        <el-footer class="flex items-center justify-end">
          <el-pagination
            background
            :total="pageTotal"
            :current-page="queryParams.pageIndex"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="queryParams.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </el-footer>
      </el-container>
    </xn-dialog>
  </div>
</template>

<script>
import { TABLIST } from '../../../../../../sinitek-lowcode-manager/src/constant/templateMarket'
export default {
  name: 'LCTemplate',
  inject: ['$doc', 'fetcher'],
  // 通过fetcher.getTemplate获取模板列表
  // 具体查看simulator/examples/app.vue
  // 例子: this.fetcher.getTemplate({xxxx}).then(res => xxx)
  data() {
    return {
      show: false,
      queryParams: {
        name: '',
        sourceType: undefined,
        createBy: undefined,
        pageSize: 10,
        pageIndex: 1,
      },
      loading: false,
      pageTotal: 0,
      dialogHeight: 700,
      dialogWidth: 1200,
      templateList: [
        {
          id: '1793943633083371522',
          sourceType: 1,
          name: '无图片模板',
          description:
            '无图片模板无图片模板无图片模板无图片模板无图片模板无图片模板无图片模板无图片模板',
          createdOrgId: '999000001',
          createdName: '管理员',
          thumbnailBase64: null,
          createTimeStamp: 1716544467000,
          updateTimeStamp: 1716544467000,
          sort: -1,
          pcFlag: 1,
          mobileFlag: 1,
        },
        {
          id: '17939436330833715423',
          sourceType: 1,
          name: '无图片模板42525',
          description:
            '无图片模板无图片模板无图片模板无图片模板无图片模板无图片模板无图片模板无图片模板',
          createdOrgId: '999000001',
          createdName: '管理员',
          thumbnailBase64: null,
          createTimeStamp: 1716544467000,
          updateTimeStamp: 1716544467000,
          sort: -1,
          pcFlag: 1,
          mobileFlag: 1,
        },
        {
          id: '179394363308337432423',
          sourceType: 1,
          name: '无图片模板333',
          description:
            '无图片模板无图片模板无图片模板无图片模板无图片模板无图片模板无图片模板无图片模板',
          createdOrgId: '999000001',
          createdName: '管理员',
          thumbnailBase64: null,
          createTimeStamp: 1716544467000,
          updateTimeStamp: 1716544467000,
          sort: -1,
          pcFlag: 1,
          mobileFlag: 1,
        },
      ],
      radioGroupList: TABLIST,
      templateType: '全部',
    }
  },
  methods: {
    open() {
      this.show = true
      this.getTemplateList()
    },
    async getTemplateList() {
      this.loading = true
      await this.fetcher
        .getTemplate(this.queryParams)
        .then(({ data, total }) => {
          if (data) {
            this.templateList = data
            this.pageTotal = total
            this.loading = false
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleSizeChange(size) {
      this.queryParams.pageSize = size
      this.getTemplateList()
    },
    handleCurrentChange(index) {
      this.queryParams.pageIndex = index
      this.getTemplateList()
    },
    search() {
      this.queryParams.pageIndex = 1
      this.getTemplateList()
    },
    changeTab(e) {
      const tabParam = this.radioGroupList.filter((item) => {
        return item.label === e
      })[0].value
      this.queryParams.sourceType = tabParam
      this.queryParams.pageIndex = 1
      this.getTemplateList()
    },
    async loadTemplate(item) {
      await this.fetcher
        .setSchema(item.id)
        .then((res) => {
          if (res) {
            this.$doc.schema.replace(res)
            this.show = false
          }
        })
        .catch(() => {
          this.show = false
        })
    },
  },
}
</script>
<style scoped>
.el-main {
  padding: 0 20px;
}
.template {
  position: relative;
}
.overlay {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.template:hover .overlay {
  display: flex;
}
.title {
  color: #3773d2;
}
.describe {
  font-size: 10px;
  color: gray;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 3em;
}
</style>
