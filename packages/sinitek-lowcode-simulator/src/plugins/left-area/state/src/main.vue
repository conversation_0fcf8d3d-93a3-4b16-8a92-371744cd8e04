<template>
  <div class="wh-full flex">
    <div class="w-250px">
      <div class="h-11 flex shrink-0 items-center justify-between px-3 fs-14">
        <span class="i-custom-state-fill h-4 w-4 text-primary"></span>
        <span class="ml-2 inline-block w-full font-bold">状态管理</span>
        <div class="flex items-center">
          <div class="h-5 w-5 icon-hover">
            <div
              class="i-custom-plus h-4 w-4"
              float="新增"
              @click="openEditPane('')"
            ></div>
          </div>
          <div class="ml-2 h-5 w-5 icon-hover">
            <div class="i-custom-x h-5 w-5" float="关闭" @click="onClose"></div>
          </div>
        </div>
      </div>
      <div flex items-center justify-between class="p-[10px]">
        <div class="flex items-center">
          <span>展示字段</span>
          <el-tooltip
            effect="dark"
            content="这里切换的是js面板里，给state设置的注释。"
            placement="bottom"
            class="ml-1"
          >
            <div class="i-custom:question h-4 w-4 text-#999"></div>
          </el-tooltip>
        </div>
        <ElSwitch v-model="showStateComment" size="mini" />
      </div>
      <div
        v-for="item of stateKeys"
        :key="item"
        class="h-30px flex items-center px-2 hover:bg-hover"
        :class="{ 'bg-primary-100': item === editName }"
      >
        <span class="text-primary fs-12">state.</span>
        <span :float="getStateLabel(item, true)" ellipsis cursor-pointer>{{
          getStateLabel(item)
        }}</span>

        <div
          class="ml-a h-5 w-5 flex-shrink-0 icon-hover hover:(text-primary bg-primary-200)"
          float="编辑"
          @click="openEditPane(item)"
        >
          <div class="i-custom-edit h-5 w-5"></div>
        </div>
        <StateRemove
          v-if="item !== 'main'"
          :label="item"
          @remove="removeEditPane"
        />
      </div>
    </div>
    <div
      v-show="showEdit"
      class="edit-pane h-full b-l-1"
      :style="{ width: editWidth + 'px' }"
    >
      <div class="h-12 flex shrink-0 items-center justify-between px-4 fs-16">
        <span>状态编辑</span>
        <div class="flex items-center">
          <div class="h-5 w-5 icon-hover" float="保存" @click="save">
            <div class="i-custom:save h-5 w-5"></div>
          </div>
          <div class="mx-10px h-5 w-5 icon-hover" @click="onFullScreen">
            <div
              v-show="!isFullScreen"
              class="i-custom:fullscreen h-4 w-4"
            ></div>
            <div
              v-show="isFullScreen"
              class="i-mdi:fullscreen-exit h-4 w-4"
            ></div>
          </div>
          <div class="h-5 w-5 icon-hover">
            <div
              class="i-custom:x h-5 w-5"
              float="关闭"
              @click="closeEditPane"
            ></div>
          </div>
        </div>
      </div>
      <div ref="editPane" class="p-4">
        <ElForm ref="form" :model="params">
          <ElFormItem prop="name" :rules="varRule" label="变量名">
            <ElInput
              v-model="params.name"
              :readonly="!isNewAdd && params.name === 'main'"
              placeholder="变量名"
              size="mini"
            />
          </ElFormItem>
          <ElFormItem prop="desc" label="描述">
            <ElInput
              v-model="params.desc"
              size="mini"
              placeholder="添加状态描述"
            />
          </ElFormItem>
          <ElFormItem prop="code" label="初始值">
            <MonacoEditor ref="monaco" class="mt-8 b h-100!" language="json" />
          </ElFormItem>
        </ElForm>
      </div>
    </div>
  </div>
</template>

<script>
import { ElInput, ElForm, ElFormItem, ElSwitch } from 'adaptor/element-ui'
import MonacoEditor from '@common/monaco-editor/view.vue'
import { formatJson } from '@utils'
import StateRemove from './remove.vue'
import Plugin from '../index'
import { isValidVariableName, setObserver } from 'sinitek-lowcode-shared'
export default {
  name: 'LCState',
  components: {
    ElInput,
    ElForm,
    ElFormItem,
    ElSwitch,
    MonacoEditor,
    StateRemove,
  },
  inject: ['$doc', 'message'],
  data() {
    return {
      showEdit: false,
      params: {
        name: '',
        code: '',
        desc: '',
      },
      editName: '',
      isNewAdd: false,
      stateKeys: [],
      stateComment: {},
      showStateComment: false,
      editWidth: 500,
      isFullScreen: false,
    }
  },
  computed: {
    varRule() {
      var validate = (rule, value, callback) => {
        if (!isValidVariableName(value, this.isNewAdd ? 'main' : null)) {
          callback(new Error('变量名不合法!'))
        } else if (this.isNewAdd && this.stateKeys.includes(value)) {
          callback(new Error('变量名重名!'))
        } else {
          callback()
        }
      }
      return [{ validator: validate, trigger: ['blur', 'change'] }]
    },
  },
  mounted() {
    const schema = this.$doc.getSchema()
    if (!schema.state) {
      this.$set(schema, 'state', {})
    }
    this.stateKeys = Object.keys(schema.state)
    this.stateComment = schema.stateComment || {}
  },
  methods: {
    getStateLabel(key, reverse = false) {
      let v = this.showStateComment
      if (reverse) {
        v = !v
      }
      if (v) {
        return this.stateComment[key] || key
      }
      return key
    },
    closeEditPane() {
      this.$doc.hotkey.setTarget(null)
      this.$refs.form.resetFields()
      this.editName = ''
      this.showEdit = false
    },
    openEditPane(key = '') {
      this.isNewAdd = !key
      this.editName = key
      this.params.name = key
      this.params.desc = this.$doc.getSchema()?.stateComment?.[key] ?? ''
      this.params.code = this.$doc.getSchema()?.state?.[key] ?? ''
      this.$nextTick(() => {
        this.$refs.monaco.refresh()
        formatJson(JSON.stringify(this.params.code, null, 2)).then((code) => {
          this.$refs.monaco.editor.setValue(code)
        })
      })
      this.$doc.hotkey.setTarget(Plugin.id)
      this.$doc.hotkey.bind(Plugin.id, 'Ctrl+s', () => {
        this.save()
      })
      this.showEdit = true
      if (this.isFullScreen) {
        this.onFullScreen()
      }
    },
    removeEditPane(key) {
      const schema = this.$doc.getSchema()
      this.$delete(schema.state, key)
      this.stateKeys = Object.keys(schema.state)
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          try {
            this.params.code = JSON.parse(this.$refs.monaco.editor.getValue())
            // 检查值是否是一个正确值
            const schema = this.$doc.getSchema()
            if (this.isNewAdd) {
              setObserver(schema.state, this.params.name, this.params.code)
              this.stateKeys.push(this.params.name)
            } else {
              delete schema.state[this.editName]
              setObserver(schema.state, this.params.name, this.params.code)
              this.stateKeys = Object.keys(schema.state)
            }
            if (!schema.stateComment) {
              setObserver(schema, 'stateComment', {
                [this.params.name]: this.params.desc,
              })
            } else {
              setObserver(
                schema.stateComment,
                this.params.name,
                this.params.desc
              )
            }

            this.message?.success('保存成功！')
            this.closeEditPane()
          } catch (e) {
            this.message?.error('初始值不是一个正确的值！')
            console.error(e)
          }
        } else {
          return false
        }
      })
    },
    onClose() {
      this.$emit('close')
      this.closeEditPane()
    },
    hide() {
      this.showEdit = false
    },
    onFullScreen() {
      this.isFullScreen = !this.isFullScreen
      const rect = this.$refs.editPane.getBoundingClientRect()
      this.editWidth = this.isFullScreen
        ? document.body.offsetWidth - rect.x
        : 500
      this.$nextTick(() => {
        this.$refs.monaco.refresh()
      })
    },
  },
}
</script>
