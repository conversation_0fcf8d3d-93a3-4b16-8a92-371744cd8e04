<template>
  <div
    class="h-5 w-5 flex-shrink-0 icon-hover hover:(text-primary bg-primary-200)"
    float="删除"
  >
    <ElPopover v-model="visible">
      <p>确定删除{{ label }}?</p>
      <div style="text-align: right; margin: 0">
        <el-button click-outside type="primary" size="mini" @click="onRemove"
          >确定</el-button
        >
        <el-button
          click-outside
          size="mini"
          type="text"
          @click="visible = false"
          >取消</el-button
        >
      </div>
      <div slot="reference" class="i-custom-remove h-5 w-5"></div>
    </ElPopover>
  </div>
</template>

<script>
import { ElPopover, ElButton } from 'adaptor/element-ui'
export default {
  name: 'StateRemove',
  components: {
    ElPopover,
    ElButton,
  },
  props: {
    label: String,
  },
  data() {
    return {
      visible: false,
    }
  },
  methods: {
    onRemove() {
      this.visible = false
      this.$emit('remove', this.label)
    },
  },
}
</script>
