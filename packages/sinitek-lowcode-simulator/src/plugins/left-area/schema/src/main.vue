<template>
  <div class="h-full flex flex-col">
    <div class="h-12 flex shrink-0 items-center justify-between px-4 fs-16">
      <span class="flex items-center">页面 Schema </span>
      <div class="flex items-center">
        <div class="h-5 w-5 icon-hover" float="保存" @click="onSave">
          <div class="i-custom:save h-5 w-5"></div>
        </div>
        <div class="ml-2 h-5 w-5 icon-hover">
          <div
            class="i-custom-x h-5 w-5"
            float="关闭"
            @click="$emit('close')"
          ></div>
        </div>
      </div>
    </div>
    <MonacoEditor
      ref="monaco"
      class="mt-1 h-full b w-800px!"
      language="json"
      @input="onChange"
      @focus="setTarget"
    />
  </div>
</template>

<script>
import MonacoEditor from '@common/monaco-editor/view.vue'
const id = 'Schema'
export default {
  name: 'LCSchema',
  components: {
    MonacoEditor,
  },
  inject: ['$doc', 'message'],
  data() {
    return {
      isChanged: false,
      isSet: false,
    }
  },
  mounted() {
    this.show()
    this.$doc.hotkey.bind(id, 'Ctrl+s', () => {
      this.onSave()
    })
  },
  methods: {
    // 获得焦点的时候，设置快捷键的目标
    // 防止修改了未保存后，点击其他地方，target清空
    // 再次点击编辑器，快捷键无效
    setTarget() {
      this.$doc.hotkey.setTarget(id)
    },
    onChange() {
      if (this.isSet) return
      this.isChanged = true
    },
    onSave() {
      if (!this.isChanged) {
        this.$emit('close')
        return
      }
      try {
        const value = this.$refs.monaco.editor.getValue()
        this.$doc.setSchema(JSON.parse(value))
        this.$doc.select.clean()
        this.$doc.hover.clean()
        this.message?.success('保存成功！')
        this.isChanged = false
        this.$emit('close')
      } catch (error) {
        this.message?.error('schema 格式错误')
        console.error(error)
      }
    },
    show(opts) {
      this.setTarget()
      this.$nextTick(() => {
        this.$refs.monaco.refresh()
        this.isSet = true
        this.$refs.monaco.editor.setValue(
          JSON.stringify(this.$doc.getSchema(), null, 2)
        )
        this.$nextTick(() => {
          this.isSet = false
          // 支持选中选项
          if (opts?.selection) {
            setTimeout(() => {
              const position = {
                lineNumber: opts.selection.startLineNumber,
                column: opts.selection.startColumn,
              }
              this.$refs.monaco.editor.setPosition(position)
              this.$refs.monaco.editor.revealPositionInCenter(position)
              this.$refs.monaco.editor.setSelection(opts.selection)
            })
          }
        })
      })
    },
  },
}
</script>
