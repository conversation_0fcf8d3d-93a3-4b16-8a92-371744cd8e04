<template>
  <div>
    <div v-for="(item, i) of currentValue" :key="i" class="mb-2 f-c-c">
      <ElSelect
        v-model="item.orderName"
        placeholder="请选择"
        style="width: 135px"
        shrink-0
        size="mini"
      >
        <ElOption
          v-for="item2 in modelFields"
          :key="item2.orderType"
          :label="item2.label"
          :value="item2.value"
          :disabled="getItemDisable(item2)"
        >
        </ElOption>
      </ElSelect>
      <span>：</span>
      <SelectExpression
        v-model="item.componentIndex"
        mr-2
        shrink-0
        @change="(v) => onChange(v, item)"
      />
      <div class="mr-2">
        <ExpressionEditor
          v-if="item.componentIndex === Status.expression"
          v-model="item.orderType"
          placeholder="输入表达式"
        >
        </ExpressionEditor>
        <ElSelect
          v-else
          v-model="item.orderType"
          placeholder="请选择"
          style="width: 80px"
          size="mini"
        >
          <ElOption label="升序" value="asc"> </ElOption>
          <ElOption label="降序" value="desc"> </ElOption>
        </ElSelect>
      </div>
      <div class="ml-auto flex">
        <!-- 外部有clickOutside事件，需要阻止冒泡 -->
        <!-- 防止元素删除后，冒泡到父元素，导致支持outside里的回调 -->
        <div @click.stop="onDelete(i)">
          <div
            class="i-material-symbols-light:delete-outline-rounded h-6 w-6"
          ></div>
        </div>
      </div>
    </div>
    <xn-button
      size="mini"
      :show-loading="false"
      class="h-8 w-25 f-c-c"
      icon="el-icon-plus"
      @click="onAdd"
    >
      添加一项
    </xn-button>
  </div>
</template>

<script>
import { ElInput, ElSwitch, ElSelect, ElOption } from 'adaptor/element-ui'
import { createComponent } from '@utils/createComponent'
import { JSExpression, ExpressionEditor } from 'sinitek-lowcode-shared'
import SelectExpression, { Status } from '@common/select-expression.vue'
export default createComponent({
  props: {
    modelValue: Array,
    modelFields: Array,
  },
  components: {
    ElInput,
    ElSwitch,
    ElSelect,
    ElOption,
    SelectExpression,
    ExpressionEditor,
  },
  data() {
    return {
      currentValue: this.modelValue || [],
      Status,
    }
  },
  methods: {
    getItemDisable(item) {
      return this.currentValue.map((e) => e.orderName).includes(item.value)
    },
    onAdd() {
      this.currentValue.push({
        orderName: '',
        orderType: 'asc',
        componentIndex: Status.static,
      })
    },
    onDelete(index) {
      this.currentValue.splice(index, 1)
    },
    getValue() {
      const result = []
      this.currentValue.forEach((item) => {
        if (!item.orderName) return
        const _item = {
          orderName: item.orderName,
          orderType: item.orderType,
        }
        if (item.componentIndex === Status.expression) {
          _item.orderType = {
            type: JSExpression,
            value: item.orderType,
          }
        }
        result.push(_item)
      })
      return result
    },
    onChange(v, item) {
      if (v === Status.expression) {
        item.orderType = ''
      } else {
        item.orderType = 'asc'
      }
    },
  },
  watch: {
    modelValue(mv = []) {
      this.refreshFlag = true
      this.currentValue = mv.map((e) => {
        if (e.type === JSExpression) {
          return {
            ...e,
            componentIndex: Status.expression,
          }
        }
        return {
          ...e,
          componentIndex: Status.static,
        }
      })
      this.$nextTick(() => {
        this.refreshFlag = false
      })
    },
  },
})
</script>
