<template>
  <div class="datasource-plugin h-full flex">
    <div class="w-250px">
      <div class="h-11 flex shrink-0 items-center justify-between px-3 fs-14">
        <span class="i-custom-datasource-fill h-4 w-4 text-primary"></span>
        <span class="ml-2 inline-block w-full font-bold">数据源</span>
        <div class="flex items-center">
          <div class="h-5 w-5 icon-hover" @click="openEditPane()">
            <div class="i-custom-plus h-4 w-4" float="新增"></div>
          </div>
          <div class="ml-2 h-5 w-5 icon-hover">
            <div class="i-custom-x h-5 w-5" float="关闭" @click="close"></div>
          </div>
        </div>
      </div>
      <div
        v-for="(item, i) of datasource"
        :key="item.id"
        class="h-8 flex items-center px-2"
      >
        <span
          flex="~ shrink-0 items-center justify-center"
          mr-2
          w-5
          rounded
          text-white
          uppercase
          :class="[
            (item.type || 'c').slice(0, 1) === 'c'
              ? 'bg-orange'
              : (item.type || 'c').slice(0, 1) === 'e'
                ? 'bg-green'
                : 'bg-primary',
          ]"
          >{{ (item.type || 'C').slice(0, 1) }}</span
        >
        <span
          class="relative"
          ellipsis
          :float="item.desc || item.name || item.id"
          >{{ item.name || item.id }}
          <span
            v-if="item.auto"
            class="absolute left-0 top-0 h-1 w-1 rounded-full bg-red-500"
          ></span>
        </span>
        <div
          class="ml-a h-5 w-5 flex-shrink-0 icon-hover hover:(text-primary bg-primary-200)"
          :float="canEdit(item) ? '编辑' : '查看'"
          @click="openEditPane(item, i)"
        >
          <div v-if="canEdit(item)" class="i-custom-edit h-5 w-5"></div>
          <div v-else class="i-custom-eye h-5 w-5"></div>
        </div>
        <StateRemove
          v-if="canDelete(item)"
          :label="item.name || item.id"
          @remove="removeEditPane(i)"
        />
      </div>
    </div>
    <div v-show="showEdit" class="edit-pane w-600px flex flex-col b-l-1">
      <div
        class="h-11 flex shrink-0 items-center justify-between b-b-1 px-4 fs-14"
      >
        <span class="font-bold">数据源编辑</span>
        <div class="flex items-center">
          <div class="h-5 w-5 icon-hover" float="保存" @click="save">
            <div class="i-custom:save h-5 w-5"></div>
          </div>
          <div class="ml-2 h-5 w-5 icon-hover">
            <div class="i-custom-x h-5 w-5" float="关闭" @click="hide"></div>
          </div>
        </div>
      </div>
      <div class="h-full overflow-auto pr-5 pt-4">
        <ElForm ref="form" :model="params" :rules="rules" label-width="10em">
          <ElFormItem prop="id" label="数据源ID">
            <ElInput
              v-model="params.id"
              size="mini"
              placeholder="数据源ID"
              :disabled="params.type === DatasourceType.ENUM || isMain"
            >
            </ElInput>
          </ElFormItem>
          <ElFormItem prop="desc" label="描述">
            <ElInput
              v-model="params.desc"
              size="mini"
              placeholder="添加数据源说明"
            >
            </ElInput>
          </ElFormItem>
          <ElFormItem prop="auto" label="请求类型">
            <ElRadioGroup
              v-model="params.type"
              :disabled="isMain"
              size="mini"
              @change="typeChange"
            >
              <ElRadioButton :label="DatasourceType.MODEL">模型</ElRadioButton>
              <ElRadioButton :label="DatasourceType.ENUM">枚举</ElRadioButton>
              <ElRadioButton :label="DatasourceType.CUSTOM"
                >自定义</ElRadioButton
              >
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem prop="auto" label="是否自动请求">
            <ElSwitch v-model="params.auto" size="mini" />
          </ElFormItem>
          <template v-if="params.type === DatasourceType.MODEL">
            <ElFormItem
              prop="modelCode"
              :rules="[
                {
                  required: true,
                  message: '请选择模型',
                  trigger: 'blur',
                },
              ]"
              required
              label="选择模型"
            >
              <ElSelect
                v-model="params.modelCode"
                placeholder="请选择"
                :disabled="isMain"
                filterable
                size="mini"
                @change="modelChange"
              >
                <ElOption
                  v-for="item in modelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </ElOption>
              </ElSelect>
            </ElFormItem>
            <template v-if="params.modelCode">
              <ElFormItem prop="modelCode" label="筛选条件" class="form-top">
                <LCCondition
                  ref="condition"
                  v-model="params.condition"
                  :fields="modelFields"
                />
              </ElFormItem>
              <ElFormItem label="显示的字段" prop="data">
                <ElSelect
                  v-model="params.data.filterFields"
                  placeholder="不选择全部展示"
                  multiple
                  size="mini"
                  @change="modelChange"
                >
                  <ElOption
                    v-for="item in modelFields"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </ElOption>
                </ElSelect>
              </ElFormItem>
              <ElFormItem label="排序" prop="data">
                <Orders
                  ref="orders"
                  v-model="params.data.orders"
                  :model-fields="modelFields"
                />
              </ElFormItem>
            </template>
          </template>
          <template v-else-if="params.type === DatasourceType.ENUM">
            <ElFormItem prop="enum" required label="选择枚举">
              <el-cascader
                v-model="params.enum"
                placeholder="请选择"
                filterable
                clearable
                :options="enumOptions"
                @change="enumChange"
              ></el-cascader>
            </ElFormItem>
          </template>
          <template v-else>
            <ElFormItem prop="url" required label="请求地址">
              <ElInput
                v-model="params.url"
                size="mini"
                placeholder="请求地址"
              />
            </ElFormItem>
            <ElFormItem prop="data" label="请求参数">
              <!-- <ElInput v-model="params.data" placeholder="json对象" /> -->
              <Params ref="p1" v-model="params.data" />
            </ElFormItem>
            <ElFormItem prop="method" required label="请求方法">
              <ElSelect v-model="params.method" size="mini">
                <ElOption label="GET" value="get"></ElOption>
                <ElOption label="POST" value="post"></ElOption>
              </ElSelect>
            </ElFormItem>
            <ElFormItem prop="headers" label="请求头信息">
              <Params ref="p2" v-model="params.headers" />
            </ElFormItem>
          </template>
        </ElForm>
        <div class="lc-line-x ml-4 b-t-1"></div>
        <ElForm
          v-if="params.type !== DatasourceType.ENUM"
          ref="form2"
          :model="params"
          :rules="rules"
          label-position="top"
          label-width="10em"
          class="px-4"
        >
          <ElFormItem>
            <div class="mb-2 font-medium fs-14">添加数据处理函数</div>
            <div class="fs-14">是否发起请求</div>
            <MonacoEditor
              ref="monaco"
              class="mt-1 b h-50!"
              language="javascript"
            />
            <div class="fs-14">参数预处理</div>
            <MonacoEditor
              ref="monaco2"
              class="mt-1 b h-50!"
              language="javascript"
            />
            <div class="fs-14">成功处理</div>
            <MonacoEditor
              ref="monaco3"
              class="mt-1 b h-50!"
              language="javascript"
            />
            <div class="fs-14">失败处理</div>
            <MonacoEditor
              ref="monaco4"
              class="mt-1 b h-50!"
              language="javascript"
            />
          </ElFormItem>
        </ElForm>
      </div>
    </div>
  </div>
</template>

<script>
import MonacoEditor from '@common/monaco-editor/view.vue'
import StateRemove from '../../state/src/remove.vue'
import { arrToUnderlineVariable, deepClone, formatScript } from '@utils'
import { getContext } from 'sinitek-lowcode-render'
import { DatasourceType, isValidVariableName } from 'sinitek-lowcode-shared'
import Params from './params.vue'
import Orders from './orders.vue'
import LCCondition from '@common/condition/condition'
import Plugin from '../index'
import {
  ElForm,
  ElFormItem,
  ElRadioGroup,
  ElRadioButton,
  ElSelect,
  ElOption,
} from 'adaptor/element-ui'

const initParams = Object.freeze({
  id: '',
  name: '',
  auto: false,
  data: {},
  url: '',
  headers: {},
  method: 'get',
  type: DatasourceType.MODEL,
  modelCode: '',
  condition: { and: [] },
  enum: '',
  shouldFetch: `function shouldFetch() { 
    return true; 
  }`,
  errorHandler: 'function errorHandler(err) {}',
  dataHandler: `function dataHandler(res) { return res }`,
  willFetch: `function willFetch(options) { return options; }`,
  // 可修改
  // 主模型生成的枚举无法修改
  editable: true,
  deletable: true,
})

export default {
  name: 'LCDatasource',
  components: {
    MonacoEditor,
    StateRemove,
    Params,
    ElForm,
    ElFormItem,
    ElRadioGroup,
    ElRadioButton,
    ElSelect,
    ElOption,
    LCCondition,
    Orders,
  },
  inject: ['$doc', 'fetcher', 'message'],
  data() {
    return {
      showEdit: false,
      editIndex: -1,
      params: deepClone(initParams),
      modelOptions: [],
      enumOptions: [],
      modelFields: [],
      DatasourceType: DatasourceType,
      updateFlag: 1,
      datasource: [],
    }
  },

  computed: {
    rules() {
      var validate = (rule, value, callback) => {
        if (!isValidVariableName(value, 'main') && !this.isMain) {
          callback(new Error('数据源ID不合法!'))
        } else {
          callback()
        }
      }
      return {
        id: [
          {
            required: true,
            message: '模型ID不能为空',
            trigger: 'blur',
          },
          {
            validator: validate,
            trigger: 'blur',
          },
        ],
        url: [
          { required: true, message: '请输入请求地址', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value.match(/(https?:)?\/\//)) {
                callback(new Error('只能使用相对路径，不能使用完整URL。'))
              } else {
                callback()
              }
            },
            trigger: 'blur',
          },
        ],
      }
    },
    isMain() {
      return this.params.id === 'main' && !this.params.deletable
    },
  },
  created() {
    this.getModelData()
  },
  methods: {
    async getModelData() {
      this.modelOptions = await this.fetcher.getModelList()
      this.enumOptions = await this.fetcher.getEnumList()
    },
    async modelChange(v) {
      this.modelFields = await this.fetcher.getModelFields(v)
      if (!this.params.id) {
        this.params.id = v
      }
    },
    enumChange(v) {
      // 其他符号转成下划线
      this.params.id = arrToUnderlineVariable(v)
    },
    save() {
      const done = () => {
        const page = this.$doc.getSchema()
        if (this.editIndex === -1) {
          page.datasource.push(deepClone(this.params))
        } else {
          this.$set(page.datasource, this.editIndex, deepClone(this.params))
          this.$nextTick(() => {
            getContext().datasource[this.params.id].send()
          })
          this.datasource = [...page.datasource]
        }
        this.message.success('保存成功！')
        this.hide()
        this.editIndex = -1
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.params.type === DatasourceType.CUSTOM) {
            this.params.data = this.$refs.p1.getValue()
            this.params.headers = this.$refs.p2.getValue()
          }
          if (this.params.type !== DatasourceType.ENUM) {
            this.params.shouldFetch = this.$refs.monaco.editor.getValue()
            this.params.willFetch = this.$refs.monaco2.editor.getValue()
            this.params.dataHandler = this.$refs.monaco3.editor.getValue()
            this.params.errorHandler = this.$refs.monaco4.editor.getValue()
          }
          if (this.params.type === DatasourceType.MODEL) {
            this.params.data.orders = this.$refs.orders.getValue()
            this.$refs.condition.validate((v) => {
              if (v) {
                done()
              }
            })
            return
          }
          done()
        } else {
          this.message.error('请填写完整信息')
        }
      })
    },
    close() {
      this.$emit('close')
    },
    openEditPane(item = initParams, i = -1) {
      this.$refs.form.resetFields()
      this.showEdit = true
      this.editIndex = i
      this.params = deepClone(item)
      if (i > -1 && item.type === DatasourceType.MODEL && item.modelCode) {
        this.modelChange(item.modelCode)
      }
      this.$doc.hotkey.setTarget(Plugin.id)
      this.$doc.hotkey.bind(Plugin.id, 'Ctrl+s', () => {
        this.save()
      })
      this.typeChange()
    },
    removeEditPane(i) {
      this.datasource.splice(i, 1)
      this.showEdit = false
    },
    typeChange() {
      this.$nextTick(() => {
        if (this.params.type !== DatasourceType.ENUM) {
          this.$refs.monaco.refresh()
          this.$refs.monaco2.refresh()
          this.$refs.monaco3.refresh()
          this.$refs.monaco4.refresh()
          formatScript(this.params.shouldFetch || initParams.shouldFetch).then(
            (code) => {
              this.$refs.monaco.editor.setValue(code)
            }
          )
          formatScript(this.params.willFetch || initParams.willFetch).then(
            (code) => {
              this.$refs.monaco2.editor.setValue(code)
            }
          )
          formatScript(this.params.dataHandler || initParams.dataHandler).then(
            (code) => {
              this.$refs.monaco3.editor.setValue(code)
            }
          )
          formatScript(
            this.params.errorHandler || initParams.errorHandler
          ).then((code) => {
            this.$refs.monaco4.editor.setValue(code)
          })
        }
        this.$refs.form.clearValidate()
      })
    },
    hide() {
      this.$doc.hotkey.setTarget(null)
      this.showEdit = false
    },
    // 是否可以删除
    canDelete(item) {
      // 兼容不存在的值
      return item.deletable || item.deletable === void 0
    },
    canEdit(item) {
      // 兼容不存在的值
      return item.editable || item.editable === void 0
    },
    show() {
      // 这个值是用作js查询方法
      this.updateFlag++
      const page = this.$doc.getSchema()
      const ds = page?.datasource ?? []
      // updateFlag 在show中更新，保证每次打开都获取最新数据
      if (
        !ds.find((e) => e.id === 'main') &&
        page.mainModel?.modelCode &&
        this.updateFlag
      ) {
        ds.push({
          ...deepClone(initParams),
          deletable: false,
          id: 'main',
          name: '主模型',
          type: DatasourceType.MODEL,
          modelCode: page.mainModel.modelCode,
          condition: page.mainModel?.condition ?? { and: [] },
        })
      }
      this.datasource = page.datasource
    },
  },
}
</script>
<style lang="scss">
.form-top {
  display: flex;
  flex-direction: column;
  .el-form-item__content {
    margin-left: 0 !important;
  }
}
</style>
