<template>
  <div>
    <div v-for="(item, i) of list" :key="i" class="mb-2 f-c-c">
      <ElInput
        v-model="item.key"
        class="w-16 flex-shrink-0"
        style="width: 135px"
        shrink-0
        placeholder="name"
        size="mini"
      />
      <span>：</span>
      <SelectExpression
        v-model="item.componentIndex"
        is-params
        mr-2
        shrink-0
        @change="(v) => onChange(v, item)"
      />
      <div class="mr-2">
        <ElInput
          v-if="item.componentIndex === Status.static"
          v-model="item.value"
          size="mini"
        />
        <ElSwitch
          v-if="item.componentIndex === Status.bool"
          v-model="item.value"
        />
        <ExpressionEditor
          v-if="item.componentIndex === Status.expression"
          v-model="item.value"
          placeholder="输入表达式"
        >
        </ExpressionEditor>
      </div>
      <div class="ml-auto flex">
        <!-- 外部有clickOutside事件，需要阻止冒泡 -->
        <!-- 防止元素删除后，冒泡到父元素，导致支持outside里的回调 -->
        <div @click.stop="onDelete(i)">
          <div class="i-lucide-trash h-6 w-6"></div>
        </div>
      </div>
    </div>
    <xn-button
      size="mini"
      :show-loading="false"
      class="h-8 w-25"
      icon="el-icon-plus"
      @click="onAdd"
    >
      添加一项
    </xn-button>
  </div>
</template>

<script>
import { ElInput, ElSwitch } from 'adaptor/element-ui'
import { createComponent } from '@utils/createComponent'
import { JSExpression, ExpressionEditor } from 'sinitek-lowcode-shared'
import SelectExpression, { Status } from '@common/select-expression.vue'
export default createComponent({
  props: {
    modelValue: {
      type: Object,
    },
  },
  components: {
    ElInput,
    ElSwitch,
    SelectExpression,
    ExpressionEditor,
  },
  data() {
    return {
      currentValue: this.modelValue,
      list: [],
      Status,
    }
  },
  methods: {
    onAdd() {
      this.list.push({
        key: '',
        value: '',
        componentIndex: Status.static,
      })
    },
    onDelete(index) {
      this.list.splice(index, 1)
    },
    getValue() {
      const result = {}
      this.list.forEach((item) => {
        if (!item.key) return
        if (item.componentIndex === Status.expression) {
          result[item.key] = {
            type: JSExpression,
            value: item.value,
          }
          return
        }
        result[item.key] = item.value
      })
      return result
    },
    onChange(v, item) {
      if (v === Status.bool) {
        item.value = false
      } else {
        item.value = ''
      }
    },
  },
  watch: {
    modelValue(mv = {}) {
      this.refreshFlag = true
      this.list = Object.keys(mv).map((key) => {
        if (mv[key].type === JSExpression) {
          return {
            key,
            value: mv[key].value,
            componentIndex: Status.expression,
          }
        } else if (typeof mv[key] === 'boolean') {
          return {
            key,
            value: mv[key],
            componentIndex: Status.bool,
          }
        }
        return {
          key,
          value: mv[key],
          componentIndex: Status.static,
        }
      })
      this.$nextTick(() => {
        this.refreshFlag = false
      })
    },
  },
})
</script>
