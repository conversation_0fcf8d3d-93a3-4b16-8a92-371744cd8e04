<template>
  <div
    ref="el"
    class="tree-item px-4"
    :draggable="!disableDrag"
    @dragstart.stop="setDragData"
  >
    <div
      class="tree-item-header relative h-8 flex items-center"
      :class="[disableDrag ? 'cursor-default' : 'cursor-move']"
    >
      <div
        v-if="item && item.children && item.children.length"
        class="i-lucide-chevron-right h-4 w-4"
        :class="{
          'transform rotate-90': isExpanded,
        }"
        @click="isExpanded = !isExpanded"
      ></div>
      <span>{{ item.label }}</span>
      <span class="ml-2 text-gray fs-12">{{
        ComponentTypeLabel[item.data.componentType]
      }}</span>
    </div>
    <div
      v-if="item && item.children && item.children.length"
      class="relative"
      :class="{
        hidden: !isExpanded,
      }"
    >
      <TreeItem
        v-for="child of item.children"
        :key="child.id"
        is-children
        :item="child"
      />
    </div>
  </div>
</template>

<script>
import {
  ComponentTypeLabel,
  ComponentName,
  isEnumType,
  DatasourceType,
  JSExpression,
} from 'sinitek-lowcode-shared'
import { arrToUnderlineVariable } from '@utils'

export default {
  name: 'TreeItem',
  inject: ['$doc', 'message'],
  props: {
    item: Object,
    isChildren: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isExpanded: true,
      ComponentTypeLabel,
    }
  },
  computed: {
    disableDrag() {
      return ['id', 'version', 'createtimestamp', 'updatetimestamp'].includes(
        this.item.value
      )
    },
  },
  beforeDestroy() {
    this.clean?.()
  },
  methods: {
    setDragData() {
      let data = this.getFormItemData(this.item)
      if (this.isChildren) {
        data.parentWhiteList = ['LAChildForm']
      }
      if (this.item.data.componentType === ComponentName.CHILDREN_FORM) {
        data = {
          componentName: 'LAChildForm',
          props: {
            status: 'edit',
            addLabel: '添加一项',
            addButtonType: 'text',
            addIcon: 'el-icon-plus',
          },
          LCBindState: this.item.value,
          children: [],
        }
        // 过滤内置字段
        this.item.children
          .filter(
            (e) =>
              !['id', 'version', 'createtimestamp', 'updatetimestamp'].includes(
                e.value
              )
          )
          .forEach((e) => {
            const item = this.getFormItemData(e)
            data.children.push(item)
          })
      } else if (this.item.data.componentType === ComponentName.RELA_FORM) {
        data = this.getRelaFormData(this.item)
      }
      data.source = 'model'
      this.$doc.drag.dragStart(JSON.parse(JSON.stringify(data)))
      this.clean = this.$doc.event.on('drag:end', () => {
        setTimeout(() => {
          const pname = this.$doc.getCurrent()?.parent?.componentName
          // 给高级表单的数据绑定添加main前缀
          if (pname === 'LAForm') {
            this.$doc.getCurrent().schema.LCBindState = `main.${this.item.value}`
            const id = this.$doc.getCurrent().schema.id
            // 设置LCBindState状态没变化，需要清除后在选中
            this.$doc.select.clean()
            this.$nextTick(() => {
              this.$doc.node.selectNode(id)
            })
          }

          this?.clean?.()
        })
      })
    },
    checkEnum(item) {
      if (!isEnumType(item.data.type)) return
      const s = this.$doc.getSchema()
      s.datasource = s.datasource || []
      const enumParams = [item.data.enumCatalog, item.data.enumType]
      const enumId = arrToUnderlineVariable(enumParams)
      const index = s.datasource.findIndex(
        (e) => e.type === DatasourceType.ENUM && enumId === e.id
      )
      if (index > -1) return enumId
      s.datasource.push({
        type: DatasourceType.ENUM,
        id: enumId,
        enum: enumParams,
        auto: true,
        method: 'get',
        editable: false,
      })
      return enumId
    },
    getRelaFormData(item) {
      const configJson = JSON.parse(item.data?.configJson || '{}')
      return {
        props: {
          field: item.data.componentType,
          itemProps: {
            label: item.label,
            prop: item.value,
          },
          multiple: configJson?.association === 'ONE2MANY',
          modelAndCondition: {
            modelCode: item.data.relaModelCode,
            condition: {
              and: [],
            },
          },
          display: {
            primaryKey: configJson?.displayName || 'id',
          },
        },
        LCBindState: `${item.value}`,
        componentName: 'LARelaForm',
        children: [],
      }
    },
    getFormItemData(item) {
      if (item.data.componentType === ComponentName.RELA_FORM) {
        return this.getRelaFormData(item)
      }
      const result = {
        props: {
          field: item.data.componentType,
          itemProps: {
            label: item.label,
            prop: item.value,
          },
        },
        LCBindState: `${item.value}`,
        componentName: 'LAFormItem',
        children: [],
      }
      if (isEnumType(item.data.type)) {
        result.props.fieldProps = {
          options: {
            type: JSExpression,
            value: `this.datasource.${this.checkEnum(item)}`,
          },
        }
      }
      if (item.data.componentType === ComponentName.FILE) {
        const configJson = JSON.parse(item.data?.configJson || '{}')
        result.props.fieldProps = {
          sourceEntity: configJson?.attachmentSourceName ?? '',
          sourceId: configJson?.attachmentSourceIdPropName ?? 'id',
          type: `${configJson?.attachmentType ?? '0'}`,
        }
      }
      return result
    },
  },
}
</script>
<style lang="scss" scoped>
.tree-item {
  .tree-item {
    padding-left: 20px;
  }
  .tree-item-header {
    .actionHidden {
      display: none;
    }
    &:hover {
      .actionHidden {
        display: block;
      }
    }
    .block,
    .lc-block {
      display: block;
    }
  }
}
</style>
