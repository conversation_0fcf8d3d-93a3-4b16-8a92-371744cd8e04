<template>
  <div class="h-full w-250px flex flex-col overflow-y-auto">
    <div class="px-2">
      <!-- <ModelSetter v-model="mainModel" @input="onInput" /> -->
      <xn-select
        v-model="modelCode"
        :options="models"
        size="mini"
        placeholder="请选择主模型"
        clearable
        filterable
        class="mt-2 w-full"
        @change="onInput"
      ></xn-select>
    </div>
    <div
      v-if="!list.length"
      v-loading="loading"
      class="mt-20 w-full text-center text-gray fs-14"
    >
      请选择主模型
    </div>
    <template v-if="list.length">
      <div bg-bgGrayLighter mt-2 h-8 px-4 flex="~ shrink-0 items-center">
        主模型字段
      </div>
      <TreeItem v-for="tree of list" :key="tree.id" :item="tree" />
    </template>
  </div>
</template>

<script>
import TreeItem from './tree-item.vue'
// import ModelSetter from '@settings/props/src/setter/model-setter'
import { DatasourceType } from 'sinitek-lowcode-shared'
export default {
  name: 'LCModelTree',
  components: {
    TreeItem,
    // ModelSetter,
  },
  inject: ['$doc', 'fetcher'],
  data() {
    return {
      list: [],
      models: [],
      modelCode: '',
      loading: false,
    }
  },
  mounted() {
    this.getModels()
    this.modelCode = this.$doc.getSchema()?.mainModel?.modelCode || ''
  },
  methods: {
    async getModels() {
      this.models = await this.fetcher.getModelList()
    },
    show() {
      this.getData()
    },
    onInput(v) {
      const s = this.$doc.getSchema()

      const deleteStateMain = () => {
        s?.state?.main && delete s.state.main
        const findIndex = s.datasource.findIndex((item) => item.id === 'main')
        if (findIndex !== -1) {
          s.datasource.splice(findIndex, 1)
        }
      }

      if (v) {
        // model不一样时，清空state.main
        if (s?.mainModel?.modelCode && v !== s.mainModel.modelCode) {
          deleteStateMain()
        }
        s.mainModel = {
          modelCode: v,
        }
        s.datasource.unshift({
          condition: { and: [] },
          modelCode: v,
          id: 'main',
          name: '主模型',
          type: DatasourceType.MODEL,
          data: {},
          shouldFetch: `function shouldFetch() { 
            return true; 
          }`,
          errorHandler: 'function errorHandler(err) {}',
          dataHandler: `function dataHandler(res) { return res }`,
          willFetch: `function willFetch(options) { return options; }`,
          deletable: false,
          // auto:true 是为了兼容之前的
          auto: true,
        })
      } else {
        delete s.mainModel
        deleteStateMain()
      }
      this.getData()
    },
    getData() {
      // 获取模型数据
      const schema = this.$doc.getSchema()
      if (!schema?.mainModel?.modelCode) {
        this.list = []
        return
      }
      const setStateMainVariable = (list) => {
        const result = list.reduce((acc, cur) => {
          acc[cur.data.name] = null
          return acc
        }, {})
        if (!schema.state) {
          this.$set(schema, 'state', { main: result })
        } else if (!schema.state?.main) {
          this.$set(schema.state, 'main', result)
        }
      }
      this.loading = true
      this.fetcher
        .getModelFields(schema.mainModel.modelCode)
        .then((res) => {
          this.list = res
          setStateMainVariable(res)
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>
<style lang="scss" scoped>
.custom-tree-node {
  .action {
    display: none;
  }
  &:hover {
    .action {
      display: block;
    }
  }
  .action.actionHidden {
    display: block;
  }
}
</style>
