<template>
  <div class="material-plugin w-300px flex flex-col">
    <div class="search px-2 py-4">
      <div class="search-wrap h-8 f-c-c b-1 rounded-2 hover:b-primary">
        <div class="icon i-lucide-search mx-1 h-4 w-4 text-textSecondary"></div>
        <input
          type="search"
          placeholder="请输入关键字搜索"
          class="wh-full rounded-2 border-none pr-2 outline-none"
          @input="handleSearch"
        />
      </div>
    </div>
    <el-tabs v-model="activeKey">
      <el-tab-pane label="原子组件" name="原子组件">
        <CollapseItem
          v-for="(items, title) of categories"
          :key="title"
          :title="title"
          :is-mini-title="true"
        >
          <div class="grid grid-cols-2 gap-2 px-2">
            <template v-for="item of items">
              <DragItem
                v-for="snippet of item.snippets"
                :key="`${snippet.title}`"
                class="h-8 flex items-center b-1 b-[#D8D8D8] rounded-4px b-solid text-#595959 hover:(b-primary b-dashed text-primary)"
                :data="snippet.schema"
                :page-data="snippet.pageData"
                :pre-preview="snippet.prePreview"
                :pre-preview-schema="snippet.prePreviewSchema"
                @dragstart="onDragstart"
              >
                <div class="component-item-component mx-2 flex items-center">
                  <LoadSvg :svg-url="snippet.screenshot" />
                </div>
                <div class="fs-13">
                  {{ snippet.title }}
                </div>
              </DragItem>
            </template>
          </div>
        </CollapseItem>
      </el-tab-pane>
      <el-tab-pane label="模型驱动组件" name="模型驱动组件"
        ><div class="grid grid-cols-2 gap-2 px-2 pt-2">
          <template v-for="(item, i) of modelList">
            <DragItem
              v-for="(snippet, si) of item.snippets"
              :key="`${i}_${si}`"
              class="h-8 flex items-center b-1 b-[#D8D8D8] rounded-4px b-solid text-#595959 hover:(b-primary b-dashed text-primary)"
              :data="snippet.schema"
              :page-data="snippet.pageData"
              :pre-preview="snippet.prePreview"
              :pre-preview-schema="snippet.prePreviewSchema"
              @dragstart="onDragstart"
            >
              <div
                class="component-item-component mx-2 h-7 w-7 flex items-center"
              >
                <LoadSvg :svg-url="snippet.screenshot" />
              </div>
              <div class="text-#595959 fs-14">
                {{ snippet.title }}
              </div>
            </DragItem>
          </template>
        </div></el-tab-pane
      >
      <el-tab-pane
        v-if="lowcodeList.length > 0"
        label="低代码组件"
        name="低代码组件"
        ><div class="flex flex-wrap">
          <template v-for="(item, i) of lowcodeList">
            <DragItem
              v-for="(snippet, si) of item.snippets"
              :key="`${i}_${si}`"
              class="h-10 w-1/2 flex items-center b-b-1 b-r-1 transition-all duration-300"
              :data="{ ...snippet.schema, title: snippet.title }"
              :pre-preview="snippet.prePreview"
              :pre-preview-schema="snippet.prePreviewSchema"
              @dragstart="onDragstart"
            >
              <div
                class="component-item-component mx-2 h-7 w-7 flex items-center"
              >
                <LoadSvg :svg-url="snippet.screenshot" />
              </div>
              <div class="text-#595959 fs-14">
                {{ snippet.title }}
              </div>
            </DragItem>
          </template>
        </div></el-tab-pane
      >
    </el-tabs>
  </div>
</template>

<script>
import DragItem from '@common/drag-item.vue'
import { getAPI, CollapseItem } from 'sinitek-lowcode-shared'
import LoadSvg from './load-svg.vue'
export default {
  name: 'LCMaterials',
  components: {
    DragItem,
    CollapseItem,
    LoadSvg,
  },
  inject: ['$doc'],
  data() {
    return {
      materials: [],
      activeKey: '原子组件',
    }
  },
  computed: {
    categories() {
      // const _ = ['基础', '容器', '表单', '信息输入', '信息展示', '工作流程']
      const result = {}
      const groupBy = Object.groupBy(
        this.materials
          .filter(
            (e) =>
              (e.snippets && !e.modelComponent) ||
              (e.snippets && e.componentName === 'LAChildForm')
          )
          .sort((a, b) => (b?.priority ?? 1) - (a?.priority ?? 1)),
        (item) => item.category
      )
      Object.keys(groupBy).forEach((key) => {
        if (groupBy[key] && groupBy[key].length) {
          result[key] = groupBy[key]
        }
      })
      return result
    },
    lowcodeList() {
      return this.materials.filter((e) => e.lowcodeComponent)
    },
    modelList() {
      return this.materials.filter((e) => e.modelComponent)
    },
  },

  mounted() {
    this.materials = this.$doc.getMaterials()
    this.$doc.materials.onChange(() => {
      this.materials = this.$doc.getMaterials()
    })
  },
  methods: {
    handleSearch($event) {
      const keyword = $event.target.value
      const original = this.$doc.getMaterials()
      if (!$event.target.value) {
        this.materials = original
        return
      }

      this.materials = original.filter(
        (e) =>
          e.componentName.toLowerCase().indexOf(keyword.toLowerCase()) > -1 ||
          e.title.indexOf(keyword) > -1
      )
    },
    onDragstart() {
      setTimeout(() => {
        getAPI().hidePanel()
      }, 20)
    },
    tabChange(item) {
      this.activeKey = item.value
    },
  },
}
</script>

<style lang="scss">
.search:focus-within {
  .search-wrap {
    @apply b-primary;
  }
}
.material-plugin {
  height: 100%;
  overflow-y: auto;
  .el-tabs__header {
    margin-bottom: 0;
  }
  .el-tabs--top .el-tabs__item:nth-child(2),
  .el-tabs--top .el-tabs__item:last-child {
    padding: 0 10px;
  }
}
</style>
