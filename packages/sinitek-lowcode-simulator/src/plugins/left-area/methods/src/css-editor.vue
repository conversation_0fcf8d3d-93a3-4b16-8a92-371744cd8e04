<template>
  <MonacoEditorView
    ref="monaco"
    :style="{ width: editWidth + 'px' }"
    :language="'css'"
    :is-full-screen="isFullScreen"
    @input="onChange"
    @focus="setTarget"
  />
</template>

<script>
import MonacoEditorView from '@common/monaco-editor/view.vue'
import Plugin from '../index'
import { formatCss } from '@/utils'
export default {
  name: 'CssEditor',
  components: { MonacoEditorView },
  inject: ['$doc', 'message'],
  props: {
    editWidth: {
      type: Number,
      default: 800,
    },
    isFullScreen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isChanged: false,
      isSaved: true,
      isSet: false,
      isError: false,
    }
  },
  mounted() {
    this.show()
  },
  methods: {
    setTarget() {
      this.$doc.hotkey.setTarget(Plugin.id)
    },
    show() {
      const page = this.$doc.getSchema()
      this.$nextTick(() => {
        this.$refs.monaco.refresh()
      })
      if (!page.css) return
      formatCss(page.css).then((res) => {
        this.isSet = true
        this.$refs.monaco.editor.setValue(res)
        this.$nextTick(() => {
          this.isSet = false
        })
      })
    },
    onChange() {
      if (this.isSet) return
      this.isChanged = true
      this.isSaved = !this.isChanged
      this.$emit('change')
    },
    save(callback) {
      try {
        this.isError = false
        const css = this.$refs.monaco.editor.getValue()
        this.$doc.getSchema().css = css
        this.isSaved = true
        this.message?.success('保存成功！')
        this.isChanged = false
        this.isError = false
        callback?.()
        this.$emit('saved')
      } catch (_) {
        this.isSaved = false
        this.isError = true
        this.message?.error('CSS 格式错误，无法自动保存!')
      }
    },
    refresh() {
      this.$nextTick(() => {
        this.$refs.monaco.refresh()
      })
    },
  },
}
</script>
