<template>
  <div ref="editPane" class="h-full flex flex-col" @click.stop>
    <div class="h-11 flex shrink-0 items-center justify-between px-4 fs-16">
      <span class="i-custom-datasource-fill h-4 w-4 text-primary"></span>
      <span class="ml-2 w-full flex items-center font-bold"
        >代码
        <ElPopover trigger="hover">
          <div class="w-60 bg-white">
            <div>可以通过this获取</div>
            <div><b>this.refs </b>获取所有的元素所有</div>
            <div><b>this.fetcher </b>调用请求数据对象</div>
            <div><b>this.state </b>获取状态字段</div>
            <div><b>this.datasource </b>获取请求的数据对象</div>
            <div><b>this.methods </b>调用其他函数</div>
            <div><b>this.scope </b>获取当前作用域，主要获取组件插槽数据</div>
            <br />
            <div>
              <b>this.vm </b>获取组件实例，主要用来调用挂载在vue上的功能。<b
                class="text-red"
                >不推荐</b
              >
            </div>
            <br />
            <div>
              注意不能在编辑器里，直接使用this.xxx调用函数。需要在methods属性下调用，例如<b
                >this.methods.xxx()</b
              >
            </div>
          </div>
          <div
            slot="reference"
            class="i-custom:question ml-2 h-4 w-4 text-[#666]"
          ></div>
        </ElPopover>
      </span>

      <div class="flex items-center">
        <div class="h-5 w-5 icon-hover" float="保存" @click="onSave">
          <div class="i-custom:save h-5 w-5"></div>
        </div>
        <div class="mx-2 h-5 w-5 icon-hover" @click="onFullScreen">
          <div v-show="!isFullScreen" class="i-custom:fullscreen h-3 w-3"></div>
          <div
            v-show="isFullScreen"
            class="i-mdi:fullscreen-exit h-4 w-4"
          ></div>
        </div>
        <div class="h-5 w-5 icon-hover">
          <div
            class="i-custom:x h-5 w-5"
            float="关闭"
            @click="$emit('close')"
          ></div>
        </div>
      </div>
    </div>
    <el-tabs
      v-model="activeTab"
      class="method-tabs h-full"
      type="card"
      size="mini"
      @change="onTabChange"
    >
      <el-tab-pane :label="jsTabLabel" name="js" class="h-full">
        <js-editor
          v-show="activeTab === 'js'"
          ref="jsEditor"
          :edit-width="editWidth"
          :is-full-screen="isFullScreen"
          @change="isJSChange = true"
          @saved="isJSChange = false"
        />
      </el-tab-pane>
      <el-tab-pane lazy :label="cssTabLabel" name="css" class="h-full">
        <css-editor
          v-show="activeTab === 'css'"
          ref="cssEditor"
          :edit-width="editWidth"
          :is-full-screen="isFullScreen"
          @change="isCSSChange = true"
          @saved="isCSSChange = false"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import JsEditor from './js-editor.vue'
import CssEditor from './css-editor.vue'
import { combine } from '@utils'
import { ElPopover } from 'adaptor/element-ui'
import Plugin from '../index'
export default {
  name: 'LCMethods',
  components: {
    JsEditor,
    CssEditor,
    ElPopover,
  },
  inject: ['$doc', 'message'],
  data() {
    return {
      activeTab: 'js',
      editWidth: 800,
      isFullScreen: false,
      isJSChange: false,
      isCSSChange: false,
    }
  },
  computed: {
    jsTabLabel() {
      return this.isJSChange ? 'JS *' : 'JS'
    },
    cssTabLabel() {
      return this.isCSSChange ? 'CSS *' : 'CSS'
    },
  },
  mounted() {
    this.clean = combine(
      this.$doc.hotkey.bind(Plugin.id, 'Ctrl+s', () => {
        this.onSave()
      }),
      this.$doc.event.on('highlight:method', (name) => {
        this.activeTab = 'js'
      })
    )
  },
  beforeDestroy() {
    this.clean?.()
  },
  methods: {
    // 获得焦点的时候，设置快捷键的目标
    // 防止修改了未保存后，点击其他地方，target清空
    // 再次点击编辑器，快捷键无效
    setTarget() {
      this.$doc.hotkey.setTarget(Plugin.id)
    },
    show() {
      this.setTarget()
      if (this.isFullScreen) {
        this.onFullScreen()
      }
      this.$nextTick(() => {
        if (this.activeTab === 'js') {
          this.$refs.jsEditor?.show?.()
        } else {
          this.$refs.cssEditor?.show?.()
        }
      })
    },
    onSave() {
      this.$refs.jsEditor?.isChanged && this.$refs.jsEditor?.save?.()
      this.$refs.cssEditor?.isChanged && this.$refs.cssEditor?.save?.()
    },
    onFullScreen() {
      this.isFullScreen = !this.isFullScreen
      const rect = this.$refs.editPane.getBoundingClientRect()
      this.editWidth = this.isFullScreen
        ? document.body.offsetWidth - rect.x
        : 800
      // 通知子组件刷新
      this.$nextTick(() => {
        if (this.activeTab === 'js') {
          this.$refs.jsEditor?.refresh?.()
        } else {
          this.$refs.cssEditor?.refresh?.()
        }
      })
    },
    onTabChange() {
      this.$nextTick(() => {
        if (this.activeTab === 'js') {
          this.$refs.jsEditor?.refresh?.()
        } else {
          this.$refs.cssEditor?.refresh?.()
        }
      })
    },
  },
}
</script>
<style lang="sass">
.method-tabs {
  .el-tabs__content {
    height: calc(100% - 60px);
  }
}
</style>
