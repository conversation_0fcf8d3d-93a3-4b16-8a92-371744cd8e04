<template>
  <MonacoEditorView
    ref="monaco"
    v-model="currentValue"
    :style="{ width: editWidth + 'px' }"
    :is-full-screen="isFullScreen"
    @input="onChange"
    @focus="setTarget"
  />
</template>

<script>
import MonacoEditorView from '@common/monaco-editor/js.vue'
import { formatScript, string2AST, AST2string, combine } from '@utils'
import { JSFunction } from 'sinitek-lowcode-shared'
import Plugin from '../index'

// 如果classmethod里，使用其他函数，需要改成this.methods.xxx
function replaceFnNameAddMethods(fnNames, str) {
  fnNames.forEach((e) => {
    str = str.replace(new RegExp(`this.\\b${e}\\b`, 'g'), `this.methods.${e}`)
  })
  return str
}
// 讲字符串里的this.methods.xxx 替换成 this.xxx
function replaceFnNameDropMethods(str) {
  return str.replace(new RegExp('this\\.methods\\.\\b(.*)\\b', 'g'), `this.$1`)
}
let highlightMethodName = null

function generateState(state = {}, stateComment = {}) {
  const result = ['{']
  Object.keys(state).forEach((key) => {
    if (stateComment[key]) {
      result.push(`  /* ${stateComment[key]} */`)
    }
    result.push(`  ${key}: ${JSON.stringify(state[key], null, 2)},`)
  })
  result.push('}')
  return result.join('\n')
}

export default {
  name: 'JsEditor',
  components: { MonacoEditorView },
  inject: ['$doc', 'message'],
  props: {
    editWidth: {
      type: Number,
      default: 800,
    },
    isFullScreen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currentValue: '',
      isChanged: false,
      isSaved: true,
      isSet: false,
      isError: false,
    }
  },
  mounted() {
    this.clean = combine(
      this.$doc.event.on('highlight:method', (name) => {
        highlightMethodName = name
      })
    )
    this.show()
  },
  beforeDestroy() {
    this.clean?.()
  },
  methods: {
    setTarget() {
      this.$doc.hotkey.setTarget(Plugin.id)
    },
    highlightMethod(code) {
      if (!highlightMethodName) return
      string2AST(code).program.body.forEach((declaration) => {
        if (declaration.type === 'ClassDeclaration') {
          const find = declaration.body.body.find(
            (e) =>
              e.type === 'ClassMethod' && e.key.name === highlightMethodName
          )
          if (!find) {
            highlightMethodName = null
            return
          }
          const editor = this.$refs.monaco.editor
          editor.focus()
          if (find.loc) {
            editor.setSelection({
              startColumn: find.loc.start.column + 1,
              startLineNumber: find.loc.start.line,
              endColumn: find.loc.end.column + 1,
              endLineNumber: find.loc.end.line,
            })
            const top = editor.revealLineInCenter(find.loc.start.line)
            editor.setScrollPosition(
              {
                scrollLeft: 0,
                scrollTop: top,
              },
              0
            )
          }
        }
      })
    },
    show() {
      this.setTarget()

      const page = this.$doc.getSchema()
      if (!page.methods) {
        this.$set(page, 'methods', {})
      }
      const methods = page.methods
      let code = [
        `/**\n         * 常用页面事件，设计时不会执行\n         * created 页面创建完成后触发\n         * mounted 页面加载完成后触发\n         * activated 页面激活时触发\n         * deactivated 页面失活时触发\n         * beforeDestroy 页面销毁前触发\n         * destroyed 页面销毁后触发\n         * datasourceLoaded 自动请求全部加载完成后触发。可以通过this.datasource获取对应的数据源 \n         */`,
        'class LowcodePage extends Page {',
        `/** 页面状态 */`,
        `  state = ${generateState(page?.state, page?.stateComment)}\n`,
      ]
      Object.keys(methods).forEach((e) => {
        const _ast = string2AST(
          replaceFnNameDropMethods(methods[e]?.original ?? methods[e]?.value)
        )
        _ast.program.body[0].type = 'ClassMethod'
        _ast.program.body[0].id.name = e
        _ast.program.body[0].id.loc.identifierName = e
        _ast.program.body[0].key = _ast.program.body[0].id
        code.push(` ${AST2string(_ast.program.body[0])}\n`)
      })
      code.push('}')
      formatScript(code.join('\n')).then((res) => {
        this.isSet = true
        this.$refs.monaco.editor.setValue(res)
        this.highlightMethod(res)
        this.$nextTick(() => {
          this.isSet = false
        })
        highlightMethodName = null
      })
    },
    onChange() {
      if (this.isSet) return
      this.isChanged = true
      this.isSaved = !this.isChanged
      this.$emit('change')
    },
    save(callback) {
      try {
        this.isError = false
        const code = this.$refs.monaco.editor.getValue()
        const ast = string2AST(code)
        const methods = {}
        let state = {}
        let stateComment = {}
        ast.program.body.forEach((declaration) => {
          if (declaration.type === 'ClassDeclaration') {
            const fnNames = declaration.body.body
              .filter((e) => e.type === 'ClassMethod')
              .map((e) => e.key.name)

            declaration.body.body.map((d, index) => {
              if (d.type === 'ClassMethod') {
                const name = d.key.name
                // 前一个方法的尾部注释和后一个方法的头部注释指向相同引用时，删除尾部注释, 解决注释重复生成问题
                if (
                  declaration.body?.body?.[index + 1]?.leadingComments &&
                  d.trailingComments ===
                    declaration.body.body[index + 1].leadingComments
                ) {
                  delete d.trailingComments
                }
                // 给函数添加function保存
                // 原始代码
                const content = AST2string({
                  ...d,
                  type: 'FunctionDeclaration',
                  id: {
                    type: 'Identifier',
                    name,
                  },
                }).trim()
                // 转换代码
                const transformCode = AST2string(
                  {
                    ...d,
                    type: 'FunctionDeclaration',
                    id: {
                      type: 'Identifier',
                      name,
                    },
                  },
                  {
                    targets: ' > 1%, last 2 versions',
                    usePresetEnv: true,
                    generatorOpts: {
                      comments: false,
                      compact: true,
                    },
                  }
                ).trim()
                if (name) {
                  methods[name] = {
                    type: JSFunction,
                    // 原始的函数代码
                    original: replaceFnNameAddMethods(fnNames, content),
                    // 转换后的函数
                    value: transformCode,
                  }
                }
              } else if (d.type === 'ClassProperty' && d.key.name === 'state') {
                // 只有state属性才能被复制到状态里
                if (d.value.type === 'ObjectExpression') {
                  d.value.properties.forEach((e) => {
                    stateComment[e.key.name] = e.leadingComments
                      ?.map((e) => e.value)
                      .join('\n')
                  })
                  state = Function(`return ${AST2string(d.value).trim()}`)()
                } else {
                  this.message?.error('state格式错误!')
                  this.isError = true
                }
              } else {
                // 不是类属性和方法
                this.message?.error(
                  '方法格式错误!类型：' +
                    d.type +
                    '，字段:' +
                    (d.key?.name ?? d.key?.id?.name)
                )
                this.isError = true
              }
            })
          }
        })
        if (this.isError) return
        this.$doc.getSchema().methods = methods
        this.$doc.getSchema().state = state
        this.$doc.getSchema().stateComment = stateComment
        this.isSaved = true
        this.message?.success('保存成功！')
        this.isChanged = false
        this.isError = false
        callback?.()
        this.$emit('saved')
      } catch (e) {
        console.warn(e)
        this.isSaved = false
        this.isError = true
        this.message?.error('格式错误，无法自动保存!')
      }
    },
    refresh() {
      this.$nextTick(() => {
        this.$refs.monaco.refresh()
      })
    },
  },
}
</script>
