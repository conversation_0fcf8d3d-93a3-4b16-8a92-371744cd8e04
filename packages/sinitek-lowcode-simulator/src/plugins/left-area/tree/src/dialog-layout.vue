<template>
  <div class="p-2">
    <!-- 头部 -->
    <div class="b-1 rounded-1">
      <div class="h-8 flex items-center px-2">
        <span>模态视图层</span>
      </div>
      <div class="b-t-1 px-1">
        <TreeItem
          v-for="item of list"
          :key="item.id"
          :item="item"
          :active-id="activeId"
          :actives="actives"
          :expanded="false"
        >
        </TreeItem>
      </div>
    </div>
  </div>
</template>

<script>
import TreeItem from './tree-item.vue'
export default {
  name: 'LCTreeDialogLayout',
  components: {
    TreeItem,
  },
  props: ['list', 'activeId', 'actives'],
}
</script>
