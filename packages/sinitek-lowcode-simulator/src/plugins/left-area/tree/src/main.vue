<template>
  <div class="h-full w-100 flex flex-col overflow-y-auto">
    <div
      class="h-8 flex cursor-pointer items-center bg-bgGrayLighter px-4"
      @click="selectPage"
    >
      页面
    </div>
    <div ref="el" class="h-full overflow-y-auto">
      <DialogLayout
        v-if="modalList.length"
        :list="modalList"
        :active-id="activeId"
        :actives="actives"
      />
      <div class="overflow h-full px-1">
        <TreeItem
          v-for="tree of list"
          :key="tree.id"
          :active-id="activeId"
          :item="tree"
          :actives="actives"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { deepClone } from '@utils'
import DialogLayout from './dialog-layout.vue'
import TreeItem from './tree-item.vue'
export default {
  name: 'LCTree',
  components: {
    DialogLayout,
    TreeItem,
  },
  inject: ['$doc'],
  provide() {
    return {
      setActiveId: this.setActiveId,
      setHiddenComponent: this.setHiddenComponent,
    }
  },
  data() {
    return {
      currentValue: '',
      pageChildren: [],
      activeId: this.$doc.getCurrent()?.schema?.id,
      actives: [],
    }
  },

  computed: {
    modalList() {
      return (this.pageChildren || [])?.filter((e) => {
        const config = this.$doc.getConfigure(e.componentName)
        return config?.component?.isModal
      })
    },
    list() {
      return (this.pageChildren || [])?.filter((e) => {
        const config = this.$doc.getConfigure(e.componentName)
        return !config?.component?.isModal
      })
    },
  },
  mounted() {
    const fn = () => {
      this.$nextTick(() => {
        this.pageChildren = this.$doc.getSchema(true).children
      })
    }
    this.clean = this.$doc.event.combine(
      this.$doc.select.onChange((arg) => {
        this.activeId = arg?.current?.id
      }),
      this.$doc.event.on('insertNode', fn),
      this.$doc.event.on('removeNode', fn),
      this.$doc.event.on('history:change', fn)
    )
  },
  beforeDestroy() {
    this?.clean?.()
  },
  methods: {
    setActiveId(id) {
      this.activeId = id
      const node = this.$doc.node.selectNode(id)
      if (node) {
        this.$doc.drag.dragStart(deepClone(node))
      }
    },
    setHiddenComponent(node, hidden) {
      let _node = this.$doc.node.getNode(node.id)
      if (!hidden) {
        this.$nextTick(() => {
          this.$nextTick(() => {
            this.$doc.node.selectNode(node.id)
          })
        })
      } else {
        // 清空select和hover
        this.$doc.select.clean()
        this.$doc.hover.clean()
      }
      if (_node.hidden == void 0) {
        this.$set(_node, 'hidden', hidden)
      } else {
        _node.hidden = hidden
      }
    },
    selectPage() {
      const id = this.$doc.getSchema().id
      this.$doc.node.selectNode(id)
    },
    show(v = []) {
      // 这个值是用作js查询方法
      this.actives = v
      this.pageChildren = this.$doc.getSchema(true).children
      setTimeout(() => {
        this.$nextTick(() => {
          const el = this.$refs?.el
          const primary = el?.querySelector('.text-primary')
          if (!el || !primary) return
          const rect = primary.getBoundingClientRect()
          const elRect = el.getBoundingClientRect()
          let top = Math.max(rect.top - elRect.top - elRect.height / 2, 0)
          this.$refs.el.scrollTo({ top })
        })
      }, 16)
    },
    hide() {
      this.$refs?.el?.scrollTo?.({ top: 0 })
      this.actives = []
    },
  },
}
</script>
<style lang="scss" scoped>
.custom-tree-node {
  .action {
    display: none;
  }
  &:hover {
    .action {
      display: block;
    }
  }
  .action.actionHidden {
    display: block;
  }
}
</style>
