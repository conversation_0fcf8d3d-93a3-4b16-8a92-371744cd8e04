<template>
  <div class="tree-item fs-12">
    <div
      ref="el"
      class="tree-item-header relative h-30px flex cursor-pointer items-center justify-between hover:bg-hover"
      :class="{
        'bg-primary-100 text-primary': activeId === item.id,
      }"
      @click="setActive(item.id)"
    >
      <div
        v-if="item && item.children && item.children.length"
        class="i-custom-right h-2 w-2 flex-shrink-0 text-#999"
        :class="{
          'transform rotate-90': isExpanded,
          'text-primary': activeId === item.id,
        }"
        @click.stop="isExpanded = !isExpanded"
      ></div>
      <input
        v-if="isEdit"
        v-model="materialTitle"
        click-outside
        b-1
        rounded-1
        px-1
        text-text
        maxlength="10"
        @input="onInput"
        @blur="onBlur"
      />
      <span v-else mr-a ellipsis overflow-hidden px-1 :float="materialTitle">{{
        materialTitle
      }}</span>
      <div class="actionHidden ml-a items-center">
        <div
          class="h-5 w-5 icon-hover hover:(text-primary bg-primary-200)"
          float="编辑名称"
          @click.stop="onEdit"
        >
          <div class="i-custom-edit h-4 w-4"></div>
        </div>
        <div
          class="h-5 w-5 icon-hover hover:(text-primary bg-primary-200)"
          float="隐藏"
          @click.stop="onClickHidden"
        >
          <EyeIcon :hidden="hidden" size="4" />
        </div>
        <div
          v-show="hasBehavior('copy')"
          class="h-5 w-5 icon-hover hover:(text-primary bg-primary-200)"
          float="复制"
          @click.stop="onCopy"
        >
          <div class="i-custom-copy h-4 w-4"></div>
        </div>
        <div
          v-show="hasBehavior('remove')"
          class="h-5 w-5 icon-hover hover:(text-primary bg-primary-200)"
          float="删除"
          @click.stop="onRemove"
        >
          <div class="i-custom-remove h-4 w-4"></div>
        </div>
      </div>
      <DndIndicator v-show="edge" :edge="edge" />
      <div
        v-if="actives.includes(item.id)"
        class="i-lucide-target size-6"
      ></div>
    </div>
    <template v-if="item && item.children && item.children.length">
      <div
        class="relative"
        :class="{
          hidden: !isExpanded,
        }"
      >
        <div
          class="line absolute bottom-0 left-0 top-0 b-l-1"
          :class="{
            'b-l-primary': activeId === item.id,
          }"
        ></div>
        <TreeItem
          v-for="child of item.children"
          :key="child.id"
          :active-id="activeId"
          :item="child"
          :actives="actives"
        />
      </div>
    </template>
  </div>
</template>

<script>
import EyeIcon from '@common/eye-icon.vue'
import {
  draggable,
  dropTargetForElements,
} from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine'
import {
  attachInstruction,
  extractInstruction,
} from '@atlaskit/pragmatic-drag-and-drop-hitbox/tree-item'
import DndIndicator from '@common/dnd/indicator.vue'
import { clickOutside } from '@utils'
import get from 'lodash/get'
export default {
  name: 'TreeItem',
  components: {
    EyeIcon,
    DndIndicator,
  },
  inject: ['setActiveId', 'setHiddenComponent', '$doc'],
  props: {
    item: Object,
    activeId: String,
    actives: Array,
    expanded: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isExpanded: this.expanded,
      hidden: this.item.hidden,
      edge: '',
      isEdit: false,
      materialTitle: '',
    }
  },
  watch: {
    item: {
      handler() {
        this.setDefaultValue()
      },
      deep: true,
    },
  },
  mounted() {
    this.setDefaultValue()
    const el = this.$refs.el
    const positionMap = {
      'reorder-above': 'top',
      'reorder-below': 'bottom',
      'make-child': 'in',
    }
    const getPosition = (instruction) => {
      return positionMap[instruction?.desired?.type ?? instruction.type]
    }
    const config = this.$doc.getConfigure(this.item.componentName)
    if (config?.component?.isModal) {
      return
    }
    this.cleanup = combine(
      draggable({
        element: el,
        getInitialData: () => ({ item: this.item }),
      }),
      dropTargetForElements({
        element: el,
        getData: (args) => {
          const { input, element } = args
          return attachInstruction(this.item, {
            input,
            element,
            mode: 'standard',
          })
        },
        canDrop: (args) => {
          return args.source.data.item.id !== this.item.id
        },
        onDrop: ({ location, source, self }) => {
          const target = location.current.dropTargets[0]
          if (!target || this.edge === 'forbid') {
            this.edge = ''
            return
          }
          const sourceData = source.data
          const targetData = self.data
          const { parent, node } = this.$doc.node.getNode(targetData.id, true)
          const targetNode = { parent, node, data: { ...sourceData.item } }
          this.$doc.node.removeNode(
            this.$doc.node.getNode(source.data.item.id, true)
          )
          this.$doc.node.insertNode(targetNode, this.edge)
          this.edge = ''
        },
        onDrag: (arg) => {
          this.edge = getPosition(extractInstruction(arg.self.data))
          const config = this.$doc.getConfigure(arg.self.data.componentName)
          if (this.edge === 'in' && !config?.component?.isContainer) {
            this.edge = 'forbid'
          }
        },
        onDragEnter: (arg) => {
          this.edge = getPosition(extractInstruction(arg.self.data))
        },
        onDragLeave: () => {
          this.edge = ''
        },
      })
    )
  },
  beforeDestroy() {
    this?.cleanup?.()
    this?.cleanOutside?.()
  },
  methods: {
    setDefaultValue() {
      const material = this.$doc.getMaterial(this.item?.componentName)
      let dynamicTitle
      if (material.treeKey) {
        let keys = material.treeKey
        if (typeof keys === 'string') {
          keys = [`${keys}.value`, keys]
        }
        if (Array.isArray(keys)) {
          for (let index = 0; index < keys.length && !dynamicTitle; index++) {
            const key = keys[index]
            dynamicTitle = get(this.item.props, key)
            dynamicTitle = dynamicTitle
              ? `${material.title}(${dynamicTitle})`
              : null
          }
        }
      }
      this.materialTitle =
        this.item?.title ||
        dynamicTitle ||
        `${material?.title}${this.item.props?.title ? `(${this.item.props?.title?.value ?? this.item.props?.title})` : ''}` ||
        this.item.componentName
    },
    setActive(id) {
      this?.setActiveId(id)
      setTimeout(() => {
        // 防止outside事件清除target
        this.$doc.hotkey.setTarget('canvas')
      })
    },
    onClickHidden() {
      this.hidden = !this.hidden
      this.setHiddenComponent(this.item, this.hidden)
    },
    onEdit($event) {
      this.isEdit = true
      this.cleanOutside = clickOutside($event.target, () => {
        this.isEdit = false
        this?.cleanOutside?.()
      })
    },
    onRemove() {
      this.$doc.node.removeNodeById(this.item.id)
    },
    onCopy() {
      this.$doc.node.copyNodeById(this.item.id)
    },
    onBlur() {
      if (!this.materialTitle) {
        this.setDefaultValue()
      }
    },
    onInput() {
      let v = this.materialTitle
      if (!v) {
        return
      }
      const schema = this.$doc.getCurrent().schema
      schema.title = v
      this.$nextTick(() => {
        this.$doc.select.reselect()
      })
    },
    hasBehavior(name) {
      const config = this.$doc.materials.getConfigure(this.item.componentName)
      if (config?.component?.disableBehaviors?.includes('*')) return false
      return !config?.component?.disableBehaviors?.includes(name)
    },
  },
}
</script>
<style lang="scss" scoped>
.tree-item {
  .tree-item .tree-item-header {
    padding-left: 20px;
  }
  .tree-item-header {
    .actionHidden {
      display: none;
    }
    &:hover {
      .actionHidden {
        display: flex;
      }
    }
    .block,
    .lc-block {
      display: block;
    }
  }
}
</style>
