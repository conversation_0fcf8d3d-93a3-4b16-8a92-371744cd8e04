<template>
  <div class="h-full">
    <div w-250px>
      <div class="h-12 flex shrink-0 items-center justify-between px-4 fs-16">
        <span class="i-custom-logic-fill h-4 w-4 text-primary"></span>
        <span class="ml-2 inline-block w-full font-bold">逻辑编排</span>
        <div class="flex items-center">
          <div class="mr-2 h-5 w-5 icon-hover">
            <div
              class="i-custom-plus h-4 w-4"
              float="新增"
              @click="openEditPane('')"
            ></div>
          </div>
          <div class="h-5 w-5 icon-hover">
            <div
              class="i-mingcute:close-line h-4 w-4"
              float="关闭"
              @click="onClose"
            ></div>
          </div>
        </div>
      </div>
      <div
        v-for="item of actions"
        :key="item"
        class="h-8 flex items-center px-2"
      >
        <span cursor-pointer overflow-hidden text-ellipsis :float="item">{{
          item
        }}</span>
        <div
          class="ml-a h-5 w-5 flex-shrink-0 icon-hover hover:(text-primary bg-primary-200)"
          float="复制"
          @click="copy(item)"
        >
          <div class="i-custom-copy h-5 w-5"></div>
        </div>
        <div
          class="h-5 w-5 flex-shrink-0 icon-hover hover:(text-primary bg-primary-200)"
          float="编辑"
          @click="openEditPane(item)"
        >
          <div class="i-custom-edit h-5 w-5"></div>
        </div>
        <StateRemove
          v-if="item !== 'main'"
          :label="item"
          flex-shrink-0
          @remove="removeEditPane"
        />
      </div>
    </div>
    <div
      v-show="showEdit"
      class="edit-pane w-300 b-l bg-white"
      flex="~ col"
      click-outside
      absolute
      left-0
      top-0
      z-9
      h-full
      b-r
    >
      <div class="h-12 flex shrink-0 items-center justify-between px-4 fs-14">
        <span class="flex items-center">逻辑编排编辑</span>
        <div class="flex items-center">
          <div class="mr-2 h-5 w-5 icon-hover" float="保存" @click="onSave">
            <div class="i-custom:save h-5 w-5"></div>
          </div>
          <div class="h-5 w-5 icon-hover">
            <div
              class="i-mingcute:close-line h-4 w-4"
              float="关闭"
              @click="closeEdit"
            ></div>
          </div>
        </div>
      </div>
      <ElForm ref="form" :model="params" ml-4 inline>
        <ElFormItem prop="name" :rules="varRule" label="名称">
          <ElInput v-model="params.name" placeholder="名称" />
        </ElFormItem>
      </ElForm>
      <LogicFlow
        v-if="showEdit"
        ref="flow"
        :value="params.code"
        :config="config"
        @hook:mounted="onMounted"
      />
    </div>
  </div>
</template>

<script>
import Plugin from '../index'
import { ElInput, ElForm, ElFormItem } from 'adaptor/element-ui'
import StateRemove from '../../state/src/remove.vue'
import { deepClone } from '@utils'
import isPlainObject from 'lodash/isPlainObject'
const cacheMethods = {}
let editLogicId = null
function setEditLogicId(id) {
  editLogicId = id
}
export default {
  name: 'LCSchema',
  components: {
    LogicFlow: () => import('sinitek-lowcode-flow'),
    ElInput,
    ElForm,
    ElFormItem,
    StateRemove,
  },
  inject: ['$doc', 'message'],
  data() {
    return {
      actions: [],
      showEdit: false,
      isNewAdd: false,
      params: {
        name: '',
      },
      isChanged: false,
      editName: '',
      isFlowLoaded: false,
    }
  },
  computed: {
    config() {
      return {
        getFunction: () => {
          return Object.keys(this.$doc.getSchema().methods).map((e) => {
            return { label: e, value: e }
          })
        },
        getState: () => {
          const state = this.$doc.getSchema().state
          // 输出state里面对象的所有key，不处理数组
          const getKeys = (obj, prefix = '') => {
            let result = []
            Object.keys(obj).map((e) => {
              result.push({ label: `${prefix}${e}`, value: `${prefix}${e}` })
              if (isPlainObject(obj[e])) {
                result.push(...getKeys(obj[e], `${prefix}${e}.`))
              }
            })
            return result
          }
          return getKeys(state)
        },
        getDatasource: () => {
          const schema = this.$doc.getSchema()
          const ds = []
          // 兼容之前的, 后面添加的主模型都会进入datasource
          if (schema.mainModel) {
            const main =
              schema?.datasource?.filter((e) => e.id === 'main') ?? []
            if (!main.length) {
              ds.push({ ...schema.mainModel, type: 'model', id: 'main' })
            }
          }
          ds.push(
            ...schema.datasource.map((e) => {
              return e
            })
          )
          return ds
        },
        getAction: () => {
          return Object.keys(this.$doc.getSchema()?.actions ?? {})
            .map((e) => {
              return { label: e, value: e }
            })
            .filter((e) => e.value !== this.params.name)
        },
        getComponents: () => {
          return Object.values(this.$doc.node.nodes)
            .filter((e) => {
              const material = this.$doc.getMaterial(e.node.componentName)
              const { methods = [], states = [] } =
                material?.configure?.supports ?? {}
              return !![...methods, ...states].length
            })
            .map((e) => {
              return { label: e.node.title || e.node.ref, value: e.node.ref }
            })
        },
        getComponentMethods: (refKey) => {
          if (cacheMethods[refKey]) {
            return cacheMethods[refKey]?.configure?.supports?.methods || []
          }

          const s = Object.values(this.$doc.node.nodes).find(
            (e) => e.node.ref === refKey
          ).node
          const material = this.$doc.getMaterial(s.componentName)
          cacheMethods[refKey] = material
          return material?.configure?.supports?.methods || []
        },
      }
    },
    varRule() {
      var validate = (rule, value, callback) => {
        if (this.isNewAdd && this.actions.includes(value)) {
          callback(new Error('名称重名!'))
        } else if (!value) {
          callback(new Error('名称不能为空!'))
        } else {
          callback()
        }
      }
      return [{ validator: validate, trigger: ['blur', 'change'] }]
    },
  },
  mounted() {
    const schema = this.$doc.getSchema()
    if (!schema.actions) {
      this.$set(schema, 'actions', {})
    }
    this.actions = Object.keys(schema.actions)
    this.clean = this.$doc.event.on('logic:locate', (id) => {
      setEditLogicId(id)
    })
  },
  beforeCreate() {
    this.clean?.()
  },
  methods: {
    onSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 检查值是否是一个正确值
          const schema = this.$doc.getSchema()
          const data = this.$refs.flow.getGraphData()
          // 有错误也可以保存
          // if (this.$refs.flow.validate()) {
          //   this.message?.error('请检查节点是否有错误！')
          //   return
          // }
          if (!schema.actions) {
            this.$set(schema, 'actions', {})
          }
          if (this.isNewAdd) {
            this.$set(schema.actions, this.params.name, data)
            this.actions.push(this.params.name)
          } else {
            delete schema.actions[this.editName]
            schema.actions[this.params.name] = data
            this.actions = Object.keys(schema.actions)
          }
          this.message?.success('保存成功！')
          this.isChanged = false
          this.closeEditPane()
        } else {
          return false
        }
      })
    },
    show() {
      this.$doc.hotkey.setTarget(Plugin.id)
      this.$nextTick(() => {
        this.isSet = false
        if (editLogicId) {
          this.openEditPane(editLogicId)
          setEditLogicId(null)
        }
      })
    },
    copy(item) {
      const schema = this.$doc.getSchema()
      const data = schema.actions[item]
      if (data) {
        // 复制自动生成key，要检验是否冲突，自动生成不冲突的key
        let i = 1
        let key = `copy_${item}_${i}`
        while (schema.actions[key]) {
          key = `copy_${item}_${++i}`
        }
        this.$set(schema.actions, key, deepClone(data))
        this.actions = Object.keys(schema.actions)
      }
    },
    openEditPane(key = '') {
      this.isNewAdd = !key
      this.params.name = key
      if (key) {
        this.editName = key
      }
      this.params.code = this.$doc.getSchema()?.actions?.[key]
      this.$doc.hotkey.setTarget(Plugin.id)
      this.$doc.hotkey.bind(Plugin.id, 'Ctrl+s', () => {
        this.onSave()
      })
      this.showEdit = true
      setTimeout(async () => {
        if (this.isFlowLoaded) {
          await this.$refs.flow.render(this.params.code)
          this.$refs.flow.lf.on('history:change', () => {
            this.isChanged = true
          })
        }
      }, 100)
    },
    onMounted() {
      this.$nextTick(async () => {
        await this.$refs.flow.render(this.params.code)
        this.$refs.flow.lf.on('history:change', () => {
          this.isChanged = true
        })
        this.isFlowLoaded = true
      })
    },
    removeEditPane(key) {
      const schema = this.$doc.getSchema()
      this.$delete(schema.actions, key)
      this.actions = Object.keys(schema.actions)
    },
    closeEdit() {
      this.showEdit = false
      this.editName = ''
      this.isChanged = false
    },
    onClose() {
      this.$emit('close')
      this.closeEditPane()
    },
    hide() {
      if (this.isChanged) return
      this.showEdit = false
    },
    closeEditPane() {
      this.$doc.hotkey.setTarget(null)
      this.$refs.form.resetFields()
      this.showEdit = false
      this.editName = ''
    },
  },
}
</script>
