<template>
  <div>
    <RenderConfigItem :list="list" :model-value="model" @change="onChange" />
  </div>
</template>

<script>
import { setObserver } from 'sinitek-lowcode-shared'
import RenderConfigItem from '../../props/src/render-config-item.jsx'
export default {
  name: 'SettingAdvanced',
  components: {
    RenderConfigItem,
  },
  inject: ['$doc'],
  props: {
    material: Object,
  },
  data() {
    return {
      model: {},
    }
  },
  computed: {
    list() {
      const result = []
      if (this.material.configure.supports.condition !== false) {
        result.push({
          title: {
            label: '是否渲染',
            tip: '设计时，不会生效。',
          },
          name: 'condition',
          setter: 'BoolSetter',
        })
      }
      result.push({
        title: '埋点代码',
        name: 'trackCode',
        setter: 'StringSetter',
      })
      result.push({
        title: '加载状态',
        name: 'directiveLoading',
        setter: 'BoolSetter',
      })
      return result
    },
  },
  mounted() {
    this.getModel()
  },
  methods: {
    updateModel() {
      this.$nextTick(() => {
        this.getModel()
      })
    },
    getModel() {
      // fix: 改变了对象索引问题
      const schema = this.$doc.getCurrent(true).schema
      if (schema.condition === void 0) {
        schema.condition = true
      }
      this.model = schema
    },
    onChange(name, v) {
      const schema = this.$doc.getCurrent().schema
      // fix: Page组件没有父级
      if (schema.componentName === 'Page') {
        schema[name] = v
        return
      }
      setObserver(schema, name, v)
    },
  },
}
</script>
