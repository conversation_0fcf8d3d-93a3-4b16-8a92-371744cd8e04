<template>
  <xn-dialog
    :show.sync="isShow"
    title="事件绑定"
    :buttons="buttons"
    @save="onConfirm"
    @cancel="close"
  >
    <div class="overflow-hidden py-3">
      <div
        class="w-full flex overflow-hidden border-1 border-[#E0E3E5] rounded-lg border-solid fs-14 h-125!"
      >
        <div
          flex="~ col shrink-0 "
          class="b-r-1 border-[#E0E3E5] border-solid"
          w-58
        >
          <Tab
            :tabs="tabs"
            theme="tab"
            :default-key="defaultKey"
            @change="tabChange"
          />
          <div v-show="defaultKey" class="w-full overflow-hidden" mt-2>
            <div class="h-full overflow-y-auto p-2">
              <div class="b-1 border rounded-1 b-solid">
                <ul v-show="defaultKey === JSFunction">
                  <li
                    v-for="event of events"
                    :key="`${event.value}_${event.type}`"
                    class="h-9 flex cursor-pointer items-center justify-between px-4 hover:bg-gray-100"
                    :class="{
                      'bg-gray-100':
                        event.value === methodName && event.type === JSFunction,
                    }"
                    @click="onEventNameChange(event)"
                  >
                    <span>{{ event.value }}</span>
                    <div
                      v-show="
                        event.value === methodName && event.type === JSFunction
                      "
                      class="i-material-symbols-light:check h-6 w-6 text-primary"
                    ></div>
                  </li>
                  <li v-if="!events.length">暂无数据</li>
                </ul>
                <ul v-show="defaultKey !== JSFunction">
                  <li
                    v-for="event of actions"
                    :key="`${event.value}_${event.type}`"
                    class="h-9 flex cursor-pointer items-center justify-between px-4 hover:bg-gray-100"
                    :class="{
                      'bg-gray-100':
                        event.value === actionName && event.type === JSAction,
                    }"
                    @click="onActionChange(event)"
                  >
                    <span>{{ event.value }}</span>
                    <div
                      v-show="
                        event.value === actionName && event.type === JSAction
                      "
                      class="i-material-symbols-light:check h-6 w-6 text-primary"
                    ></div>
                  </li>
                  <li v-if="!actions.length">暂无数据</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div class="wh-full p-5">
          <template v-if="defaultKey === JSFunction">
            <div class="m-b-2 font-bold">事件名称</div>
            <xn-form ref="form" :model="{ methodName: methodName }">
              <xn-form-item prop="methodName" :rules="rules">
                <el-autocomplete
                  v-model="methodName"
                  :fetch-suggestions="querySearch"
                  placeholder="请输入事件名称"
                  class="w-full"
                  clearable
                ></el-autocomplete>
              </xn-form-item>
            </xn-form>
            <div class="my-2 flex justify-between">
              <span class="m-b-2 font-bold">扩展参数设置：</span>
              <ElSwitch v-model="useExtParams" />
            </div>
            <div class="relative h-80" :class="{ 'b-red-5': !isValid }">
              <!-- mask -->
              <div
                v-show="!useExtParams"
                class="absolute inset-0 z-1 h-full w-full bg-bgGray opacity-30"
              ></div>
              <MonacoEditorView
                ref="monaco"
                class="h-90% w-full"
                :language="'json'"
                @error="onError"
                @input="onCodeInput"
              />
            </div>
            <div class="error h-5 text-red-5 fs-14">
              <span v-show="!isValid">请输入正确的JSON格式数据。</span>
            </div>
          </template>
          <template v-else>
            <LogicFlow ref="flow" readonly />
          </template>
        </div>
      </div>
    </div>
  </xn-dialog>
</template>

<script>
import { createComponent } from '@utils/createComponent'
import {
  JSFunction,
  JSAction,
  generateBindMethodTemplate,
  elVariableValid,
} from 'sinitek-lowcode-shared'
import { ElInput, ElSwitch } from 'adaptor/element-ui'
import MonacoEditorView from '@common/monaco-editor/view.vue'
import Tab from '@common/tab.vue'
export default createComponent({
  name: 'BindEvent',
  components: {
    ElInput,
    ElSwitch,
    MonacoEditorView,
    Tab,
    LogicFlow: () => import('sinitek-lowcode-flow'),
  },
  props: {
    confirm: Function,
    bindEvent: Object,
  },
  data() {
    return {
      isShow: this.modelValue,
      methodName: this.bindEvent?.methodName || '',
      actionName: this.bindEvent?.methodName || '',
      errors: '',
      events: [],
      actions: [],
      useExtParams: !!this.bindEvent?.params || false,
      isValid: true,
      extParams: '',
      JSFunction,
      JSAction,
      tabs: Object.freeze([
        {
          label: '全局函数',
          value: JSFunction,
        },
        {
          label: '逻辑编排',
          value: JSAction,
        },
      ]),
      defaultKey: this.bindEvent.type || JSFunction,
      msg: '',
      rules: [
        {
          required: true,
          message: '函数名不能为空',
          trigger: ['blur', 'change'],
        },
        {
          validator: elVariableValid('函数名不合规'),
          trigger: ['blur', 'change'],
        },
      ],
    }
  },
  computed: {
    saveDisabled() {
      return (
        !this.isValid ||
        (this.useExtParams && !this.extParams) ||
        (!this.methodName && !this.actionName)
      )
    },
    buttons() {
      return [
        // 由于sinitek-ui没有安装vue-i18n，所以此处传入了label来显示按钮的文字
        // 实际项目中不需要传入，会根据action去i18n中寻找对应文字
        {
          label: '保存',
          type: this.saveDisabled ? 'info' : 'primary',
          action: 'save',
          event: 'save',
          showLoading: false,
        },
        { label: '取消', type: 'info', action: 'cancel', event: 'cancel' },
      ]
    },
  },
  methods: {
    tabChange(v) {
      this.defaultKey = v.value
      this.getEvents()
    },
    getEvents() {
      const doc = this.$doc.getSchema()
      const methods = doc?.methods ?? {}
      this.events = Object.keys(methods).map((e) => ({
        type: JSFunction,
        value: e,
      }))
      this.actions = Object.keys(doc?.actions ?? {}).map((e) => ({
        type: JSAction,
        value: e,
      }))
    },
    init() {
      this.getEvents()
      this.methodName = this.bindEvent?.methodName ?? ''
      this.actionName = this.bindEvent?.methodName ?? ''
      this.useExtParams = !!this.bindEvent?.params || false
      if (this.defaultKey === JSAction && this.actionName) {
        this.renderFlow()
      }
    },
    renderFlow() {
      setTimeout(async () => {
        const doc = this.$doc.getSchema()
        this.$refs.flow.render(doc?.actions[this.actionName])
        // this.$refs.flow.layout()
      }, 200)
    },
    async onConfirm() {
      if (this.saveDisabled) {
        return
      }
      try {
        if (this.defaultKey === JSFunction) {
          await this.$refs.form.validate()
        }
        let eventObj = {
          type: JSFunction,
          value: generateBindMethodTemplate(this.methodName),
        }
        if (this.defaultKey === JSAction) {
          eventObj = {
            type: JSAction,
            value: this.actionName,
          }
        } else {
          if (this.useExtParams) {
            eventObj.params = this.extParams
            eventObj.value = generateBindMethodTemplate(
              this.methodName,
              this.extParams
            )
          }
          // TODO 修改methods 代码入参
          this.addMethod()
        }
        this.$emit('confirm', eventObj)
        this.close()
      } catch (_) {
        // 错误信息提示
      }
    },
    close() {
      this.isShow = false
    },
    onEventNameChange(event) {
      this.methodName = event.value
      this.actionName = ''
    },
    onActionChange(event) {
      this.actionName = event.value
      this.methodName = ''
      this.renderFlow()
    },
    // 组件名称不存在，添加新的函数插入到methods中
    addMethod() {
      const doc = this.$doc.getSchema()
      if (!doc.methods) {
        doc.methods = {}
      }
      const methods = doc.methods
      const { schema } = this.$doc.getCurrent()
      if (!methods[this.methodName]) {
        const config = this.$doc.getConfigure(schema.componentName)
        // 低代码组件配置修改函数回调时，没有配置支持的事件
        let event = null
        if (config.supports.events) {
          event = config.supports.events.find(
            (a) => a.name === this.bindEvent.name
          )
        }
        methods[this.methodName] = {
          type: JSFunction,
          value:
            this.bindEvent?.template ??
            event?.template ??
            `function ${this.methodName}(${this.useExtParams ? 'extParams' : ''}) {}`,
        }
      }
    },
    onError(e) {
      this.isValid = e.length === 0
    },
    onCodeInput(v) {
      this.extParams = JSON.parse(v)
    },
    querySearch(queryString, cb) {
      const doc = this.$doc.getSchema()
      const methods = doc?.methods ?? {}
      const events = Object.keys(methods)
      cb(
        events
          .filter((e) => e.includes(queryString))
          .map((e) => ({
            value: e,
          }))
      )
    },
  },
  watch: {
    isShow(v) {
      this._emit(v)
      if (v) {
        this.init()
        this.$nextTick(() => {
          if (this.$refs.monaco) {
            this.$refs.monaco.refresh()
            this.$refs.monaco.editor.setValue(
              JSON.stringify(this.bindEvent?.params ?? {}) ?? '{}'
            )
          }
        })
        this.isValid = true
      } else {
        this.methodName = ''
        this.actionName = ''
        this.useExtParams = false
      }
    },
    modelValue(v) {
      this.isShow = v
    },
    'bindEvent.type'(v) {
      this.defaultKey = v

      this.init()
    },
  },
})
</script>
