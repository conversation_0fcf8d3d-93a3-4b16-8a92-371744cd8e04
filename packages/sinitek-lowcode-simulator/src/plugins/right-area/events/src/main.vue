<template>
  <div>
    <div class="flex justify-between px-4">
      <div class="font-bold fs-14">事件绑定</div>
      <ElDropdown size="medium" placement="bottom" @command="addEvent">
        <div class="h-5 w-5 icon-hover">
          <div class="i-custom-plus h-10px w-10px text-#666"></div>
        </div>
        <ElDropdownMenu slot="dropdown" class="max-h-75 overflow-y-auto">
          <ElDropdownItem
            v-for="event1 of events"
            :key="event1.name"
            :command="event1.name"
            :disabled="getBindEventsDisabled(event1.name)"
          >
            <div
              class="max-w-250px overflow-hidden text-ellipsis text-nowrap fs-12"
            >
              {{ event1.name }}
              {{ event1.description }}
            </div>
          </ElDropdownItem>
        </ElDropdownMenu>
      </ElDropdown>
    </div>
    <div v-if="!bindEvents.length" class="px-4">
      <div class="flex justify-center text-#999 fs-12">
        点击 <span class="mx-2">+</span> 绑定事件
      </div>
    </div>

    <!-- 绑定的事件列表 -->
    <div v-if="bindEvents.length" class="px-2 pt-4 fs-14">
      <EventItem
        v-for="event2 of bindEvents"
        :key="event2.name"
        :event="event2"
        @edit="onEdit"
        @remove="onRemoveEvent"
      />
    </div>
    <BindEvent v-model="isShow" :bind-event="event" @confirm="onConfirm" />
  </div>
</template>

<script>
import EventItem from '@common/event-item.vue'
import { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'adaptor/element-ui'
import BindEvent from './bind-event.vue'
import { isJSAction } from 'sinitek-lowcode-shared'
export default {
  name: 'SettingsEvents',
  components: {
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem,
    BindEvent,
    EventItem,
  },
  inject: ['$doc'],
  data() {
    return {
      events: [],
      isShow: false,
      event: {},
      bindEvents: [],
      isJSAction,
    }
  },
  mounted() {
    this.updateModel()
  },
  methods: {
    updateModel() {
      this.$nextTick(() => {
        const { schema } = this.$doc.getCurrent()
        const config = this.$doc.getConfigure(schema.componentName)
        this.events = (config?.supports?.events ?? []).filter(
          (e) => !e.condition || e.condition(schema.props)
        )
        this.bindEvents = this.getBindEvents()
      })
    },
    addEvent(command) {
      this.isShow = true
      this.event = { name: command }
    },
    getBindEventsDisabled(eventName) {
      const { schema } = this.$doc.getCurrent()
      const events = schema.events || {}
      return !!events[eventName]
    },
    getBindEvents() {
      const { schema } = this.$doc.getCurrent()
      if (!schema.events) return []
      return Object.keys(schema.events).map((e) => {
        const config = this.events.find((a) => a.name === e)
        const value = schema.events[e].value
        let code = /this.methods\.(.*?)(?=\.)/g.exec(value)?.[1]
        if (isJSAction(schema.events[e])) {
          code = value
        }
        return {
          name: e,
          description: config?.description || '',
          methodName: code,
          type: schema.events[e]?.type,
          params: schema.events[e]?.params,
        }
      })
    },
    onConfirm(eventObj) {
      const { schema } = this.$doc.getCurrent()
      const events = schema.events || {}
      events[this.event.name] = eventObj
      Object.assign(schema, { events })
      this.bindEvents = this.getBindEvents()
    },
    onRemoveEvent(eventName) {
      const { schema } = this.$doc.getCurrent()
      delete schema.events[eventName]
      this.bindEvents = this.getBindEvents()
    },
    onEdit(event) {
      this.event = event
      this.isShow = true
    },
  },
}
</script>
