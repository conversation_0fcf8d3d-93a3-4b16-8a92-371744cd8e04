<template>
  <div class="border-module fs-12">
    <div class="mb-2 font-bold fs-12">边框</div>
    <div class="flex">
      <!-- 边框选择区域 -->
      <div class="relative size-25 f-c-c b-1 rounded-md b-solid">
        <!-- 上边框 -->
        <div
          float="上边框"
          class="group absolute left-3.5 top-0 h-5 w-18 f-c-c cursor-pointer hover:bg-primary-100"
          :class="{ 'bg-primary-300': isActiveType('top') }"
          @click="setType('top')"
        >
          <div
            class="w-full b-t-2 b-gray b-solid group-hover:b-primary"
            :class="{ 'b-primary': isActiveType('top') }"
          ></div>
        </div>
        <!-- 右边框 -->
        <div
          float="右边框"
          class="group absolute right-0 top-3.5 h-18 w-5 f-c-c cursor-pointer hover:bg-primary-100"
          :class="{ 'bg-primary-300': isActiveType('right') }"
          @click="setType('right')"
        >
          <div
            class="h-full b-l-2 b-gray b-solid group-hover:b-primary"
            :class="{ 'b-primary': isActiveType('right') }"
          ></div>
        </div>
        <!-- 下边框 -->
        <div
          float="下边框"
          class="group absolute bottom-0 left-3.5 h-5 w-18 f-c-c cursor-pointer hover:bg-primary-100"
          :class="{ 'bg-primary-300': isActiveType('bottom') }"
          @click="setType('bottom')"
        >
          <div
            class="w-full b-t-2 b-gray b-solid group-hover:b-primary"
            :class="{ 'b-primary': isActiveType('bottom') }"
          ></div>
        </div>
        <!-- 左边框 -->
        <div
          float="左边框"
          class="group absolute left-0 top-3.5 h-18 w-5 f-c-c cursor-pointer hover:bg-primary-100"
          :class="{ 'bg-primary-300': isActiveType('left') }"
          @click="setType('left')"
        >
          <div
            class="h-full b-l-2 b-gray b-solid group-hover:b-primary"
            :class="{ 'b-primary': isActiveType('left') }"
          ></div>
        </div>
        <!-- 四边框 -->
        <div
          class="group absolute size-10 f-c-c cursor-pointer rounded hover:bg-primary-100"
          float="统一边框"
          :class="{ 'bg-primary-300': type === 'all' }"
          @click="setType('all')"
        >
          <div
            class="size-6 b-2 b-gray b-solid group-hover:b-primary"
            :class="{ 'b-primary': type === 'all' }"
          ></div>
        </div>
      </div>

      <!-- 边框属性设置 -->
      <div class="grid grid-cols-[30px_1fr] ml-2 flex-1 gap-2">
        <span class="self-center">样式</span>
        <el-select
          v-model="borderStyle"
          size="mini"
          @change="updateBorderStyle"
        >
          <el-option value="none" label="无"></el-option>
          <el-option value="solid" label="实线"></el-option>
          <el-option value="dashed" label="虚线"></el-option>
          <el-option value="dotted" label="点线"></el-option>
          <el-option value="double" label="双线"></el-option>
        </el-select>

        <span class="self-center">宽度</span>
        <el-input
          v-model.number="borderWidth"
          size="mini"
          type="number"
          min="0"
          max="100"
          @change="updateBorderWidth"
        >
          <template slot="append">px</template>
        </el-input>

        <span class="self-center">颜色</span>
        <div class="flex items-center">
          <el-input
            v-model="borderColor"
            size="mini"
            class="mr-2"
            placeholder="#000000"
            @change="updateBorderColor"
          ></el-input>
          <el-color-picker
            v-model="borderColor"
            size="mini"
            show-alpha
            color-format="hex"
            @change="updateBorderColor"
          ></el-color-picker>
        </div>
      </div>
    </div>

    <!-- 圆角设置 -->
    <div class="my-2 font-bold fs-12">圆角</div>
    <div class="radius-group flex items-center">
      <div class="radius-group-item">
        <div
          class="radius-group-item-all"
          :class="{ 'radius-group-item--active': isActiveRadiusType('all') }"
          @click="setRadiusType('all')"
        ></div>
        <div
          class="radius-group-item-custom"
          :class="{ 'radius-group-item--active': !isActiveRadiusType('all') }"
          @click="setRadiusType('custom')"
        ></div>
      </div>
      <div class="radius-group-item-input ml-2">
        <el-input
          v-model.number="radiusValue"
          size="mini"
          type="number"
          min="0"
          max="100"
          :disabled="isActiveRadiusType('custom')"
          @change="() => updateRadius()"
        >
          <template slot="append">px</template>
        </el-input>
      </div>
    </div>
    <div
      v-show="isActiveRadiusType('custom')"
      class="grid grid-cols-4 mt-2 gap-2"
    >
      <div class="radius-group-setting" float="左上角">
        <el-input
          v-model.number="radiusTopValue"
          size="mini"
          type="number"
          min="0"
          max="100"
          @change="() => updateRadius('top-left')"
        ></el-input>
        <div
          class="radius-group-setting-item radius-group-setting-top-left"
        ></div>
      </div>
      <div class="radius-group-setting" float="右上角">
        <el-input
          v-model.number="radiusRightValue"
          size="mini"
          type="number"
          min="0"
          max="100"
          @change="() => updateRadius('top-right')"
        ></el-input>
        <div
          class="radius-group-setting-item radius-group-setting-top-right"
        ></div>
      </div>
      <div class="radius-group-setting" float="右下角">
        <el-input
          v-model.number="radiusBottomValue"
          size="mini"
          type="number"
          min="0"
          max="100"
          @change="() => updateRadius('bottom-right')"
        ></el-input>
        <div
          class="radius-group-setting-item radius-group-setting-bottom-right"
        ></div>
      </div>
      <div class="radius-group-setting" float="左下角">
        <el-input
          v-model.number="radiusLeftValue"
          size="mini"
          type="number"
          min="0"
          max="100"
          @change="() => updateRadius('bottom-left')"
        ></el-input>
        <div
          class="radius-group-setting-item radius-group-setting-bottom-left"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import { createComponent } from '@utils/createComponent'

const BORDER_EDGES = ['top', 'right', 'bottom', 'left']

const RADIUS_CORNERS_MAP = {
  'top-left': 'radiusTopValue',
  'top-right': 'radiusRightValue',
  'bottom-right': 'radiusBottomValue',
  'bottom-left': 'radiusLeftValue',
}

export default createComponent({
  name: 'BorderModule',
  props: {
    styleData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      activeTab: 'radius',

      // 当前选中的边框类型
      type: 'all',

      // 边框属性
      borderColor: '',
      borderWidth: '',
      borderStyle: 'none',

      // 圆角类型控制
      radiusType: 'all',

      // 圆角属性
      radiusValue: '',
      radiusTopValue: '',
      radiusRightValue: '',
      radiusBottomValue: '',
      radiusLeftValue: '',
      radiusUnit: 'px',
    }
  },

  watch: {
    styleData: {
      handler(val) {
        this.parseStyleData(val)
      },
      immediate: true,
      deep: true,
    },
  },

  methods: {
    /**
     * 设置边框类型
     */
    setType(type) {
      this.type = type
      this.parseBorderData()
    },

    /**
     * 设置圆角类型
     */
    setRadiusType(type) {
      this.radiusType = type
      this.parseRadiusData()
    },

    /**
     * 判断是否为激活的边框类型
     */
    isActiveType(edgeType) {
      return this.type === edgeType
    },

    /**
     * 判断是否为激活的圆角类型
     */
    isActiveRadiusType(cornerType) {
      return this.radiusType === cornerType
    },

    /**
     * 解析样式数据
     */
    parseStyleData(styleData) {
      if (!styleData) {
        this.resetData()
        return
      }

      this.parseBorderData()
      this.parseRadiusData()
    },

    /**
     * 解析边框数据
     */
    parseBorderData() {
      const { styleData } = this

      // 处理border简写属性
      let rootWidth, rootStyle, rootColor
      if (styleData.border) {
        const parts = styleData.border.trim().split(/\s+/)
        if (parts.length >= 1) rootWidth = this.parseNumber(parts[0]) || ''
        if (parts.length >= 2) rootStyle = parts[1] || 'none'
        if (parts.length >= 3) rootColor = parts[2] || ''
      }

      // 处理具体边的属性
      if (BORDER_EDGES.includes(this.type)) {
        const prefix = `border-${this.type}`
        let width =
          styleData[`${prefix}-width`] || styleData[`border-width`] || rootWidth
        let style =
          styleData[`${prefix}-style`] || styleData[`border-style`] || rootStyle
        let color =
          styleData[`${prefix}-color`] || styleData[`border-color`] || rootColor
        this.borderWidth = width ? this.parseNumber(width) : ''
        this.borderStyle = style || 'none'
        this.borderColor = color || ''
      } else {
        // 处理all情况，取第一个有值的边
        const width = this.parseNumber(styleData[`border-width`]) || rootWidth
        const style = styleData[`border-style`] || rootStyle
        const color = styleData[`border-color`] || rootColor

        this.borderWidth = width || ''
        this.borderStyle = style || 'none'
        this.borderColor = color || ''
      }
    },

    /**
     * 解析圆角数据
     */
    parseRadiusData() {
      const { styleData } = this
      // 处理具体角的属性
      if (this.radiusType === 'custom') {
        this.radiusTopValue = this.parseValueWithUnit(
          styleData['border-top-left-radius']
        ).value
        this.radiusRightValue = this.parseValueWithUnit(
          styleData['border-top-right-radius']
        ).value
        this.radiusBottomValue = this.parseValueWithUnit(
          styleData['border-bottom-right-radius']
        ).value
        this.radiusLeftValue = this.parseValueWithUnit(
          styleData['border-bottom-left-radius']
        ).value
      } else {
        // 处理all情况
        const value = styleData['border-radius']

        if (value) {
          const parsed = this.parseValueWithUnit(value)
          this.radiusValue = parsed.value
          this.radiusUnit = parsed.unit
        } else {
          this.radiusValue = ''
          this.radiusUnit = 'px'
        }
      }
    },

    /**
     * 解析带单位的值
     */
    parseValueWithUnit(value) {
      if (!value) return { value: '', unit: 'px' }

      const match = String(value).match(/^([\d.]+)(\w+|%)$/)
      if (match) {
        return { value: match[1], unit: match[2] }
      }

      const numValue = this.parseNumber(value)
      if (numValue !== null) {
        return { value: String(numValue), unit: 'px' }
      }

      return { value: '', unit: 'px' }
    },

    /**
     * 解析数字值
     */
    parseNumber(value) {
      if (value === null || value === undefined || value === '') return null
      const num = parseFloat(String(value).replace(/[^\d.-]/g, ''))
      return isNaN(num) ? null : num
    },

    /**
     * 重置数据
     */
    resetData() {
      this.type = 'all'
      this.borderWidth = ''
      this.borderStyle = 'none'
      this.borderColor = ''
      this.radiusType = 'all'
      this.radiusValue = ''
      this.radiusUnit = 'px'
    },

    /**
     * 更新边框宽度
     */
    updateBorderWidth() {
      const width = this.validateNumber(this.borderWidth, 0, 100)

      const property =
        this.type === 'all' ? 'border-width' : `border-${this.type}-width`
      const value = width ? `${width}px` : ''
      this.$emit('update', property, value)
    },

    /**
     * 更新边框样式
     */
    updateBorderStyle() {
      const property =
        this.type === 'all' ? 'border-style' : `border-${this.type}-style`
      this.$emit('update', property, this.borderStyle)
    },

    /**
     * 更新边框颜色
     */
    updateBorderColor() {
      const property =
        this.type === 'all' ? 'border-color' : `border-${this.type}-color`
      this.$emit('update', property, this.borderColor)
    },

    /**
     * 更新圆角半径
     */
    updateRadius(type) {
      let radius = this.validateNumber(this.radiusValue, 0)
      if (type && type !== 'all') {
        radius = this.validateNumber(this[RADIUS_CORNERS_MAP[type]], 0)
      }

      const property = !type ? 'border-radius' : `border-${type}-radius`
      const value = radius ? `${radius}${this.radiusUnit}` : ''
      this.$emit('update', property, value)
      if (type) {
        this.$emit('update', 'border-radius', '')
      } else {
        this.$emit('update', 'border-top-left-radius', '')
        this.$emit('update', 'border-top-right-radius', '')
        this.$emit('update', 'border-bottom-right-radius', '')
        this.$emit('update', 'border-bottom-left-radius', '')
      }
    },

    /**
     * 验证数字范围
     */
    validateNumber(value, min = null, max = null) {
      const num = this.parseNumber(value)
      if (num === null) return null

      let result = num
      if (min !== null && result < min) result = min
      if (max !== null && result > max) result = max

      return result
    },
  },
})
</script>

<style lang="scss" scoped>
.border-module {
  padding: 0 15px 10px;
  .radius-group {
    &-item {
      background: #f7f7f9;
      display: flex;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;

      &-all {
        align-items: center;
        background: #f7f7f9;
        display: inline-flex;
        height: 2rem;
        justify-content: center;
        width: 2rem;
      }
      &-all,
      &-custom {
        &:before {
          border: 2px solid #5c5f66;
          border-radius: 2px;
          content: '';
          display: block;
          height: 0.875rem;
          width: 0.875rem;
        }
      }
      &--active:before {
        @apply b-primary;
      }
      &-custom {
        background: #f7f7f9;
        border-left: 1px solid #e8e9eb;
        height: 2rem;
        position: relative;
        width: 2rem;
        &:before,
        &:after {
          display: block;
          left: 50%;
          position: absolute;
          top: 50%;
          transform: translate(-50%, -50%);
        }
        &:after {
          color: #f7f7f9;
          content: '＋';
          font-size: 1.875rem;
          font-weight: 900;
        }
      }
    }
    &-setting {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      &-item {
        border: 0 solid #5c5f66;
        border-radius: 2px;
        height: 0.625rem;
        margin: 0.5rem auto;
        width: 0.625rem;
      }
      &-top-left {
        border-left-width: 2px;
        border-top-width: 2px;
      }
      &-top-right {
        border-right-width: 2px;
        border-top-width: 2px;
      }
      &-bottom-right {
        border-bottom-width: 2px;
        border-right-width: 2px;
      }
      &-bottom-left {
        border-left-width: 2px;
        border-bottom-width: 2px;
      }
    }
  }
}
</style>
