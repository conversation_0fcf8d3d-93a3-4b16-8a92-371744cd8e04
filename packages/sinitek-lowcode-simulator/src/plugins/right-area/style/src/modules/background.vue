<template>
  <div class="background-module">
    <StyleTabs v-model="activeTab" :tabs="tabs" />
    <div class="h-2"></div>
    <div v-if="activeTab === 'color'" class="background-color-setting">
      <div class="color-row">
        <span class="label">背景色:</span>
        <el-color-picker
          v-model="backgroundColor"
          show-alpha
          @change="updateBackgroundColor"
        ></el-color-picker>
        <el-button
          v-if="backgroundColor"
          type="text"
          icon="el-icon-delete"
          @click="clearBackgroundColor"
        ></el-button>
      </div>
    </div>

    <!-- 背景渐变设置 -->
    <div v-if="activeTab === 'gradient'" class="background-gradient-setting">
      <div class="gradient-type-row">
        <span class="label">渐变类型:</span>
        <el-select v-model="gradientType" size="mini" @change="updateGradient">
          <el-option value="linear" label="线性渐变"></el-option>
          <el-option value="radial" label="径向渐变"></el-option>
        </el-select>
      </div>

      <!-- 线性渐变方向 -->
      <div v-if="gradientType === 'linear'" class="gradient-direction-row">
        <span class="label">渐变方向:</span>
        <el-select
          v-model="linearDirection"
          size="mini"
          @change="updateGradient"
        >
          <el-option value="to right" label="从左到右"></el-option>
          <el-option value="to left" label="从右到左"></el-option>
          <el-option value="to bottom" label="从上到下"></el-option>
          <el-option value="to top" label="从下到上"></el-option>
          <el-option value="to bottom right" label="左上到右下"></el-option>
          <el-option value="to bottom left" label="右上到左下"></el-option>
          <el-option value="to top right" label="左下到右上"></el-option>
          <el-option value="to top left" label="右下到左上"></el-option>
        </el-select>
      </div>

      <!-- 径向渐变形状 -->
      <div v-if="gradientType === 'radial'" class="gradient-shape-row">
        <span class="label">渐变形状:</span>
        <el-select v-model="radialShape" size="mini" @change="updateGradient">
          <el-option value="circle" label="圆形"></el-option>
          <el-option value="ellipse" label="椭圆形"></el-option>
        </el-select>
      </div>

      <!-- 渐变色标 -->
      <div class="gradient-stops">
        <div class="section-title">渐变色标</div>
        <div
          v-for="(stop, index) in gradientStops"
          :key="index"
          class="gradient-stop-item"
        >
          <el-color-picker
            v-model="stop.color"
            show-alpha
            size="mini"
            @change="updateGradient"
          ></el-color-picker>
          <el-input
            v-model="stop.position"
            placeholder="位置"
            size="mini"
            @input="updateGradient"
          >
            <template slot="append">%</template>
          </el-input>
          <el-button
            type="text"
            icon="el-icon-delete"
            :disabled="gradientStops.length <= 2"
            @click="removeGradientStop(index)"
          ></el-button>
        </div>
        <el-button type="text" icon="el-icon-plus" @click="addGradientStop">
          添加色标
        </el-button>
      </div>

      <div class="gradient-preview">
        <div class="section-title">预览</div>
        <div class="preview-box" :style="{ background: gradientPreview }"></div>
      </div>

      <div class="gradient-actions">
        <el-button size="mini" @click="applyGradient">应用渐变</el-button>
        <el-button size="mini" @click="clearGradient">清除渐变</el-button>
      </div>
    </div>

    <!-- 背景图像设置 -->
    <div v-if="activeTab === 'image'" class="background-image-setting">
      <div class="image-url-row">
        <span class="label">图像URL:</span>
        <el-input
          v-model="backgroundImage"
          placeholder="输入图像URL"
          size="mini"
          @input="updateBackgroundImage"
        ></el-input>
      </div>

      <div class="image-size-row">
        <span class="label">背景尺寸:</span>
        <el-select
          v-model="backgroundSize"
          size="mini"
          @change="updateBackgroundSize"
        >
          <el-option value="auto" label="自动"></el-option>
          <el-option value="cover" label="覆盖"></el-option>
          <el-option value="contain" label="包含"></el-option>
          <el-option value="custom" label="自定义"></el-option>
        </el-select>

        <template v-if="backgroundSize === 'custom'">
          <el-input
            v-model="customBackgroundWidth"
            placeholder="宽度"
            size="mini"
            @input="updateCustomBackgroundSize"
          >
            <template slot="append">
              <el-select
                v-model="customBackgroundWidthUnit"
                size="mini"
                @change="updateCustomBackgroundSize"
              >
                <el-option value="px" label="px"></el-option>
                <el-option value="%" label="%"></el-option>
              </el-select>
            </template>
          </el-input>

          <el-input
            v-model="customBackgroundHeight"
            placeholder="高度"
            size="mini"
            @input="updateCustomBackgroundSize"
          >
            <template slot="append">
              <el-select
                v-model="customBackgroundHeightUnit"
                size="mini"
                @change="updateCustomBackgroundSize"
              >
                <el-option value="px" label="px"></el-option>
                <el-option value="%" label="%"></el-option>
              </el-select>
            </template>
          </el-input>
        </template>
      </div>

      <div class="image-repeat-row">
        <span class="label">背景重复:</span>
        <el-select
          v-model="backgroundRepeat"
          size="mini"
          @change="updateBackgroundRepeat"
        >
          <el-option value="repeat" label="重复"></el-option>
          <el-option value="repeat-x" label="水平重复"></el-option>
          <el-option value="repeat-y" label="垂直重复"></el-option>
          <el-option value="no-repeat" label="不重复"></el-option>
        </el-select>
      </div>

      <div class="image-position-row">
        <span class="label">背景位置:</span>
        <el-select
          v-model="backgroundPosition"
          size="mini"
          @change="updateBackgroundPosition"
        >
          <el-option value="center" label="居中"></el-option>
          <el-option value="top" label="上"></el-option>
          <el-option value="bottom" label="下"></el-option>
          <el-option value="left" label="左"></el-option>
          <el-option value="right" label="右"></el-option>
          <el-option value="top left" label="左上"></el-option>
          <el-option value="top right" label="右上"></el-option>
          <el-option value="bottom left" label="左下"></el-option>
          <el-option value="bottom right" label="右下"></el-option>
          <el-option value="custom" label="自定义"></el-option>
        </el-select>

        <template v-if="backgroundPosition === 'custom'">
          <el-input
            v-model="customBackgroundX"
            placeholder="X位置"
            size="mini"
            @input="updateCustomBackgroundPosition"
          >
            <template slot="append">
              <el-select
                v-model="customBackgroundXUnit"
                size="mini"
                @change="updateCustomBackgroundPosition"
              >
                <el-option value="px" label="px"></el-option>
                <el-option value="%" label="%"></el-option>
              </el-select>
            </template>
          </el-input>

          <el-input
            v-model="customBackgroundY"
            placeholder="Y位置"
            size="mini"
            @input="updateCustomBackgroundPosition"
          >
            <template slot="append">
              <el-select
                v-model="customBackgroundYUnit"
                size="mini"
                @change="updateCustomBackgroundPosition"
              >
                <el-option value="px" label="px"></el-option>
                <el-option value="%" label="%"></el-option>
              </el-select>
            </template>
          </el-input>
        </template>
      </div>

      <div class="image-actions">
        <el-button size="mini" @click="clearBackgroundImage">
          清除背景图像
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { createComponent } from '@utils/createComponent'
import StyleTabs from '../tabs.vue'
export default createComponent({
  name: 'BackgroundModule',
  components: {
    StyleTabs,
  },
  props: {
    styleData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      tabs: [
        { name: 'color', label: '背景颜色' },
        { name: 'gradient', label: '渐变' },
        { name: 'image', label: '背景图像' },
      ],

      activeTab: 'color',

      // 背景颜色
      backgroundColor: '',

      // 渐变相关
      gradientType: 'linear',
      linearDirection: 'to right',
      radialShape: 'circle',
      gradientStops: [
        { color: 'rgba(255, 255, 255, 1)', position: '0' },
        { color: 'rgba(0, 0, 0, 1)', position: '100' },
      ],
      gradientPreview: '',

      // 背景图像相关
      backgroundImage: '',
      backgroundSize: 'auto',
      backgroundRepeat: 'repeat',
      backgroundPosition: 'center',

      // 自定义背景尺寸
      customBackgroundWidth: '',
      customBackgroundWidthUnit: '%',
      customBackgroundHeight: '',
      customBackgroundHeightUnit: '%',

      // 自定义背景位置
      customBackgroundX: '',
      customBackgroundXUnit: '%',
      customBackgroundY: '',
      customBackgroundYUnit: '%',
    }
  },

  watch: {
    styleData: {
      handler(val) {
        this.initFromStyleData(val)
      },
      immediate: true,
      deep: true,
    },
  },

  methods: {
    // 初始化数据
    initFromStyleData(styleData) {
      // 背景颜色
      this.backgroundColor = styleData['background-color'] || ''

      // 背景图像
      if (styleData['background-image']) {
        // 检查是否为渐变
        if (styleData['background-image'].includes('gradient')) {
          this.parseGradient(styleData['background-image'])
        } else {
          // 解析背景图像URL
          const urlMatch = styleData['background-image'].match(
            /url\(['"]?(.*?)['"]?\)/
          )
          this.backgroundImage = urlMatch ? urlMatch[1] : ''
        }
      } else {
        this.backgroundImage = ''
      }

      // 背景尺寸
      if (styleData['background-size']) {
        if (
          ['auto', 'cover', 'contain'].includes(styleData['background-size'])
        ) {
          this.backgroundSize = styleData['background-size']
        } else {
          this.backgroundSize = 'custom'
          const sizeValues = styleData['background-size'].split(' ')
          if (sizeValues.length >= 1) {
            const widthMatch = sizeValues[0].match(/^([\d.]+)(\w+|%)$/)
            if (widthMatch) {
              this.customBackgroundWidth = widthMatch[1]
              this.customBackgroundWidthUnit = widthMatch[2]
            }
          }
          if (sizeValues.length >= 2) {
            const heightMatch = sizeValues[1].match(/^([\d.]+)(\w+|%)$/)
            if (heightMatch) {
              this.customBackgroundHeight = heightMatch[1]
              this.customBackgroundHeightUnit = heightMatch[2]
            }
          }
        }
      } else {
        this.backgroundSize = 'auto'
      }

      // 背景重复
      this.backgroundRepeat = styleData['background-repeat'] || 'repeat'

      // 背景位置
      if (styleData['background-position']) {
        const standardPositions = [
          'center',
          'top',
          'bottom',
          'left',
          'right',
          'top left',
          'top right',
          'bottom left',
          'bottom right',
        ]

        if (standardPositions.includes(styleData['background-position'])) {
          this.backgroundPosition = styleData['background-position']
        } else {
          this.backgroundPosition = 'custom'
          const posValues = styleData['background-position'].split(' ')
          if (posValues.length >= 1) {
            const xMatch = posValues[0].match(/^([\d.]+)(\w+|%)$/)
            if (xMatch) {
              this.customBackgroundX = xMatch[1]
              this.customBackgroundXUnit = xMatch[2]
            }
          }
          if (posValues.length >= 2) {
            const yMatch = posValues[1].match(/^([\d.]+)(\w+|%)$/)
            if (yMatch) {
              this.customBackgroundY = yMatch[1]
              this.customBackgroundYUnit = yMatch[2]
            }
          }
        }
      } else {
        this.backgroundPosition = 'center'
      }
    },

    // 解析渐变
    parseGradient(gradientStr) {
      // 判断渐变类型
      if (gradientStr.includes('linear-gradient')) {
        this.gradientType = 'linear'

        // 解析线性渐变方向
        const directionMatch = gradientStr.match(/linear-gradient\(\s*([^,]+),/)
        if (directionMatch) {
          this.linearDirection = directionMatch[1].trim()
        }

        // 解析渐变色标
        this.parseGradientStops(gradientStr)
      } else if (gradientStr.includes('radial-gradient')) {
        this.gradientType = 'radial'

        // 解析径向渐变形状
        if (gradientStr.includes('circle')) {
          this.radialShape = 'circle'
        } else {
          this.radialShape = 'ellipse'
        }

        // 解析渐变色标
        this.parseGradientStops(gradientStr)
      }

      // 更新渐变预览
      this.updateGradientPreview()
    },

    // 解析渐变色标
    parseGradientStops(gradientStr) {
      // 提取所有色标
      const stopsPattern = /rgba?\([^)]+\)|\\#[0-9a-f]{3,8}|[a-z]+/gi
      const stops = gradientStr.match(stopsPattern) || []

      // 提取位置百分比
      const posPattern = /\s+(\d+)%/g
      const positions = []
      let posMatch
      while ((posMatch = posPattern.exec(gradientStr)) !== null) {
        positions.push(posMatch[1])
      }

      // 重建色标数组
      this.gradientStops = []
      stops.forEach((color, index) => {
        this.gradientStops.push({
          color,
          position:
            positions[index] ||
            (index === 0 ? '0' : index === stops.length - 1 ? '100' : '50'),
        })
      })

      // 确保至少有两个色标
      if (this.gradientStops.length < 2) {
        this.gradientStops = [
          { color: 'rgba(255, 255, 255, 1)', position: '0' },
          { color: 'rgba(0, 0, 0, 1)', position: '100' },
        ]
      }
    },

    // 更新背景颜色
    updateBackgroundColor() {
      this.$emit('update', 'background-color', this.backgroundColor)
    },

    // 清除背景颜色
    clearBackgroundColor() {
      this.backgroundColor = ''
      this.$emit('update', 'background-color', '')
    },

    // 更新渐变
    updateGradient() {
      this.updateGradientPreview()
    },

    // 更新渐变预览
    updateGradientPreview() {
      if (this.gradientType === 'linear') {
        this.gradientPreview = this.generateLinearGradient()
      } else {
        this.gradientPreview = this.generateRadialGradient()
      }
    },

    // 生成线性渐变
    generateLinearGradient() {
      const stopsStr = this.gradientStops
        .map((stop) => `${stop.color} ${stop.position}%`)
        .join(', ')

      return `linear-gradient(${this.linearDirection}, ${stopsStr})`
    },

    // 生成径向渐变
    generateRadialGradient() {
      const stopsStr = this.gradientStops
        .map((stop) => `${stop.color} ${stop.position}%`)
        .join(', ')

      return `radial-gradient(${this.radialShape}, ${stopsStr})`
    },

    // 添加渐变色标
    addGradientStop() {
      // 计算新色标的位置
      let newPosition = '50'
      if (this.gradientStops.length >= 2) {
        // 找到最大位置
        const maxPosition = Math.max(
          ...this.gradientStops.map((s) => parseInt(s.position))
        )
        // 在最大位置和100之间添加
        newPosition = Math.min(parseInt(maxPosition) + 20, 100).toString()
      }

      this.gradientStops.push({
        color: 'rgba(128, 128, 128, 1)',
        position: newPosition,
      })

      this.updateGradient()
    },

    // 移除渐变色标
    removeGradientStop(index) {
      if (this.gradientStops.length > 2) {
        this.gradientStops.splice(index, 1)
        this.updateGradient()
      }
    },

    // 应用渐变
    applyGradient() {
      const gradientValue = this.gradientPreview
      this.$emit('update', 'background-image', gradientValue)
    },

    // 清除渐变
    clearGradient() {
      this.$emit('update', 'background-image', '')

      // 重置渐变设置
      this.gradientStops = [
        { color: 'rgba(255, 255, 255, 1)', position: '0' },
        { color: 'rgba(0, 0, 0, 1)', position: '100' },
      ]
      this.linearDirection = 'to right'
      this.radialShape = 'circle'
      this.updateGradient()
    },

    // 更新背景图像
    updateBackgroundImage() {
      if (this.backgroundImage) {
        this.$emit('update', 'background-image', `url(${this.backgroundImage})`)
      } else {
        this.$emit('update', 'background-image', '')
      }
    },

    // 更新背景尺寸
    updateBackgroundSize() {
      if (this.backgroundSize === 'custom') {
        this.updateCustomBackgroundSize()
      } else {
        this.$emit('update', 'background-size', this.backgroundSize)
      }
    },

    // 更新自定义背景尺寸
    updateCustomBackgroundSize() {
      if (this.customBackgroundWidth && this.customBackgroundHeight) {
        const size = `${this.customBackgroundWidth}${this.customBackgroundWidthUnit} ${this.customBackgroundHeight}${this.customBackgroundHeightUnit}`
        this.$emit('update', 'background-size', size)
      } else if (this.customBackgroundWidth) {
        const size = `${this.customBackgroundWidth}${this.customBackgroundWidthUnit}`
        this.$emit('update', 'background-size', size)
      }
    },

    // 更新背景重复
    updateBackgroundRepeat() {
      this.$emit('update', 'background-repeat', this.backgroundRepeat)
    },

    // 更新背景位置
    updateBackgroundPosition() {
      if (this.backgroundPosition === 'custom') {
        this.updateCustomBackgroundPosition()
      } else {
        this.$emit('update', 'background-position', this.backgroundPosition)
      }
    },

    // 更新自定义背景位置
    updateCustomBackgroundPosition() {
      if (this.customBackgroundX && this.customBackgroundY) {
        const position = `${this.customBackgroundX}${this.customBackgroundXUnit} ${this.customBackgroundY}${this.customBackgroundYUnit}`
        this.$emit('update', 'background-position', position)
      }
    },

    // 清除背景图像
    clearBackgroundImage() {
      this.backgroundImage = ''
      this.$emit('update', 'background-image', '')
    },
  },
})
</script>

<style lang="scss" scoped>
.background-module {
  padding: 0 15px 10px;

  .section-title {
    font-weight: bold;
    margin: 10px 0;
    font-size: 12px;
  }

  .color-row,
  .gradient-type-row,
  .gradient-direction-row,
  .gradient-shape-row,
  .image-url-row,
  .image-size-row,
  .image-repeat-row,
  .image-position-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .label {
      width: 80px;
      flex-shrink: 0;
    }
  }

  .gradient-preview {
    margin: 10px 0;

    .preview-box {
      height: 80px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  }

  .gradient-stop-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .el-color-picker {
      margin-right: 10px;
    }

    .el-input {
      width: 120px;
      margin-right: 10px;
    }
  }

  .gradient-actions,
  .image-actions {
    margin-top: 10px;
  }
}
</style>
