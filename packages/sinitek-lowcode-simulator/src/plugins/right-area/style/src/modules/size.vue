<template>
  <div class="size-module">
    <el-form label-position="top" class="grid grid-cols-2 gap-2" size="mini">
      <!-- 宽度设置 -->
      <el-form-item>
        <div class="control-row">
          <span class="property-label w-16">宽度</span>
          <el-input
            v-model="width"
            placeholder="未定义"
            size="mini"
            class="mr-2 flex-1"
            @input="handleWidthChange"
            @blur="validateWidth"
          >
            <template slot="append">
              <el-select
                v-model="widthUnit"
                size="mini"
                @change="handleWidthChange"
              >
                <el-option value="px" label="px"></el-option>
                <el-option value="%" label="%"></el-option>
                <el-option value="vw" label="vw"></el-option>
                <el-option value="rem" label="rem"></el-option>
              </el-select>
            </template>
          </el-input>
        </div>
      </el-form-item>

      <!-- 高度设置 -->
      <el-form-item>
        <div class="control-row">
          <span class="property-label w-16">高度</span>
          <el-input
            v-model="height"
            placeholder="未定义"
            size="mini"
            class="mr-2 flex-1"
            @input="handleHeightChange"
            @blur="validateHeight"
          >
            <template slot="append">
              <el-select
                v-model="heightUnit"
                size="mini"
                @change="handleHeightChange"
              >
                <el-option value="px" label="px"></el-option>
                <el-option value="%" label="%"></el-option>
                <el-option value="vh" label="vh"></el-option>
                <el-option value="rem" label="rem"></el-option>
              </el-select>
            </template>
          </el-input>
        </div>
      </el-form-item>

      <!-- 锁定比例按钮 -->
      <el-form-item v-if="!hasAutoWidth && !hasAutoHeight">
        <div class="flex justify-center">
          <el-tooltip content="锁定宽高比例" placement="top">
            <el-button
              type="text"
              :class="{ 'text-primary': aspectRatioLocked }"
              @click="toggleAspectRatio"
            >
              <i
                :class="[aspectRatioLocked ? 'el-icon-lock' : 'el-icon-unlock']"
              ></i>
            </el-button>
          </el-tooltip>
        </div>
      </el-form-item>

      <!-- 最小最大尺寸设置 -->
      <template v-if="showAdvancedOptions">
        <!-- 最小宽度 -->
        <el-form-item>
          <div class="control-row">
            <span class="property-label w-16">最小宽度</span>
            <el-input
              v-model="minWidth"
              placeholder="未设置"
              size="mini"
              class="flex-1"
              @input="handleMinWidthChange"
            >
              <template slot="append">
                <el-select
                  v-model="minWidthUnit"
                  size="mini"
                  @change="handleMinWidthChange"
                >
                  <el-option value="px" label="px"></el-option>
                  <el-option value="%" label="%"></el-option>
                </el-select>
              </template>
            </el-input>
          </div>
        </el-form-item>

        <!-- 最大宽度 -->
        <el-form-item>
          <div class="control-row">
            <span class="property-label w-16">最大宽度</span>
            <el-input
              v-model="maxWidth"
              placeholder="未设置"
              size="mini"
              class="flex-1"
              @input="handleMaxWidthChange"
            >
              <template slot="append">
                <el-select
                  v-model="maxWidthUnit"
                  size="mini"
                  @change="handleMaxWidthChange"
                >
                  <el-option value="px" label="px"></el-option>
                  <el-option value="%" label="%"></el-option>
                </el-select>
              </template>
            </el-input>
          </div>
        </el-form-item>

        <!-- 最小高度 -->
        <el-form-item>
          <div class="control-row">
            <span class="property-label w-16">最小高度</span>
            <el-input
              v-model="minHeight"
              placeholder="未设置"
              size="mini"
              class="flex-1"
              @input="handleMinHeightChange"
            >
              <template slot="append">
                <el-select
                  v-model="minHeightUnit"
                  size="mini"
                  @change="handleMinHeightChange"
                >
                  <el-option value="px" label="px"></el-option>
                  <el-option value="%" label="%"></el-option>
                </el-select>
              </template>
            </el-input>
          </div>
        </el-form-item>

        <!-- 最大高度 -->
        <el-form-item>
          <div class="control-row">
            <span class="property-label w-16">最大高度</span>
            <el-input
              v-model="maxHeight"
              placeholder="未设置"
              size="mini"
              class="flex-1"
              @input="handleMaxHeightChange"
            >
              <template slot="append">
                <el-select
                  v-model="maxHeightUnit"
                  size="mini"
                  @change="handleMaxHeightChange"
                >
                  <el-option value="px" label="px"></el-option>
                  <el-option value="%" label="%"></el-option>
                </el-select>
              </template>
            </el-input>
          </div>
        </el-form-item>
      </template>

      <!-- 显示/隐藏高级选项 -->
      <div class="col-span-2 mt-2 text-center">
        <el-button type="text" @click="toggleAdvancedOptions">
          {{ showAdvancedOptions ? '收起高级选项' : '显示高级选项' }}
          <i
            :class="[
              showAdvancedOptions ? 'el-icon-arrow-up' : 'el-icon-arrow-down',
            ]"
          ></i>
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'SizeModule',
  props: {
    styleData: {
      type: Object,
      required: true,
    },
    componentType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      width: '',
      height: '',
      widthUnit: 'px',
      heightUnit: 'px',
      minWidth: '',
      maxWidth: '',
      minHeight: '',
      maxHeight: '',
      minWidthUnit: 'px',
      maxWidthUnit: 'px',
      minHeightUnit: 'px',
      maxHeightUnit: 'px',
      hasAutoWidth: false,
      hasAutoHeight: false,
      aspectRatioLocked: false,
      aspectRatio: 1,
      showAdvancedOptions: false,
    }
  },
  watch: {
    styleData: {
      handler: 'initFromStyleData',
      immediate: true,
      deep: true,
    },
  },
  methods: {
    initFromStyleData() {
      // 解析宽度
      if (this.styleData.width) {
        this.hasAutoWidth = false
        const widthMatch = this.styleData.width.match(/^([\d.]+)(.*)$/)
        if (widthMatch) {
          this.width = widthMatch[1]
          this.widthUnit = widthMatch[2] || 'px'
        }
      } else {
        this.width = ''
        this.hasAutoWidth = true
      }

      // 解析高度
      if (this.styleData.height) {
        this.hasAutoHeight = false
        const heightMatch = this.styleData.height.match(/^([\d.]+)(.*)$/)
        if (heightMatch) {
          this.height = heightMatch[1]
          this.heightUnit = heightMatch[2] || 'px'
        }
      } else {
        this.height = ''
        this.hasAutoHeight = true
      }

      // 计算当前宽高比例
      if (this.width && this.height) {
        this.aspectRatio = parseFloat(this.width) / parseFloat(this.height)
      }

      // 解析最小宽度
      if (this.styleData['min-width']) {
        const minWidthMatch =
          this.styleData['min-width'].match(/^([\d.]+)(.*)$/)
        if (minWidthMatch) {
          this.minWidth = minWidthMatch[1]
          this.minWidthUnit = minWidthMatch[2] || 'px'
        }
      } else {
        this.minWidth = ''
      }

      // 解析最大宽度
      if (this.styleData['max-width']) {
        const maxWidthMatch =
          this.styleData['max-width'].match(/^([\d.]+)(.*)$/)
        if (maxWidthMatch) {
          this.maxWidth = maxWidthMatch[1]
          this.maxWidthUnit = maxWidthMatch[2] || 'px'
        }
      } else {
        this.maxWidth = ''
      }

      // 解析最小高度
      if (this.styleData['min-height']) {
        const minHeightMatch =
          this.styleData['min-height'].match(/^([\d.]+)(.*)$/)
        if (minHeightMatch) {
          this.minHeight = minHeightMatch[1]
          this.minHeightUnit = minHeightMatch[2] || 'px'
        }
      } else {
        this.minHeight = ''
      }

      // 解析最大高度
      if (this.styleData['max-height']) {
        const maxHeightMatch =
          this.styleData['max-height'].match(/^([\d.]+)(.*)$/)
        if (maxHeightMatch) {
          this.maxHeight = maxHeightMatch[1]
          this.maxHeightUnit = maxHeightMatch[2] || 'px'
        }
      } else {
        this.maxHeight = ''
      }
    },
    validateWidth() {
      if (this.width && isNaN(parseFloat(this.width))) {
        this.width = ''
        this.$message.error('宽度必须是数字')
      }
    },
    validateHeight() {
      if (this.height && isNaN(parseFloat(this.height))) {
        this.height = ''
        this.$message.error('高度必须是数字')
      }
    },
    handleWidthChange() {
      if (!this.width) {
        this.$emit('update', 'width', undefined)
        return
      }

      const numWidth = parseFloat(this.width)
      if (isNaN(numWidth)) return

      this.$emit('update', 'width', `${numWidth}${this.widthUnit}`)

      // 如果锁定了宽高比，则同步更新高度
      if (this.aspectRatioLocked && this.aspectRatio && !this.hasAutoHeight) {
        const newHeight = (numWidth / this.aspectRatio).toFixed(2)
        this.height = newHeight
        this.$emit('update', 'height', `${newHeight}${this.heightUnit}`)
      }
    },
    handleHeightChange() {
      if (!this.height) {
        this.$emit('update', 'height', undefined)
        return
      }

      const numHeight = parseFloat(this.height)
      if (isNaN(numHeight)) return

      this.$emit('update', 'height', `${numHeight}${this.heightUnit}`)

      // 如果锁定了宽高比，则同步更新宽度
      if (this.aspectRatioLocked && this.aspectRatio && !this.hasAutoWidth) {
        const newWidth = (numHeight * this.aspectRatio).toFixed(2)
        this.width = newWidth
        this.$emit('update', 'width', `${newWidth}${this.widthUnit}`)
      }
    },
    toggleAutoWidth() {
      this.hasAutoWidth = !this.hasAutoWidth
      if (this.hasAutoWidth) {
        this.width = ''
        this.$emit('update', 'width', undefined)
      } else {
        this.width = '100'
        this.$emit('update', 'width', `100${this.widthUnit}`)
      }
    },
    toggleAutoHeight() {
      this.hasAutoHeight = !this.hasAutoHeight
      if (this.hasAutoHeight) {
        this.height = ''
        this.$emit('update', 'height', undefined)
      } else {
        this.height = '100'
        this.$emit('update', 'height', `100${this.heightUnit}`)
      }
    },
    toggleAspectRatio() {
      this.aspectRatioLocked = !this.aspectRatioLocked
      // 计算当前宽高比
      if (this.width && this.height) {
        this.aspectRatio = parseFloat(this.width) / parseFloat(this.height)
      }
    },
    toggleAdvancedOptions() {
      this.showAdvancedOptions = !this.showAdvancedOptions
    },
    handleMinWidthChange() {
      if (!this.minWidth) {
        this.$emit('update', 'min-width', undefined)
        return
      }
      this.$emit('update', 'min-width', `${this.minWidth}${this.minWidthUnit}`)
    },
    handleMaxWidthChange() {
      if (!this.maxWidth) {
        this.$emit('update', 'max-width', undefined)
        return
      }
      this.$emit('update', 'max-width', `${this.maxWidth}${this.maxWidthUnit}`)
    },
    handleMinHeightChange() {
      if (!this.minHeight) {
        this.$emit('update', 'min-height', undefined)
        return
      }
      this.$emit(
        'update',
        'min-height',
        `${this.minHeight}${this.minHeightUnit}`
      )
    },
    handleMaxHeightChange() {
      if (!this.maxHeight) {
        this.$emit('update', 'max-height', undefined)
        return
      }
      this.$emit(
        'update',
        'max-height',
        `${this.maxHeight}${this.maxHeightUnit}`
      )
    },
  },
}
</script>

<style lang="scss">
.size-module {
  padding: 0 15px 10px;

  .el-form-item {
    margin-bottom: 10px;
  }
}
</style>
