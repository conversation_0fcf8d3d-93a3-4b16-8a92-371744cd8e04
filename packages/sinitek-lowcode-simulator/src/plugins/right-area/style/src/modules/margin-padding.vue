<template>
  <div class="margin-padding-module">
    <div class="spacing-container">
      <!-- margin -->
      <div class="spacing-box margin-box">
        <!-- margin-top -->
        <div class="spacing-edge spacing-edge-top margin-edge-top">
          <input
            v-model="marginTop"
            class="spacing-input spacing-input-horizontal"
            placeholder="0"
            maxlength="3"
            @input="() => updateSpacing('margin', 'top')"
          />
        </div>
        <!-- margin-right -->
        <div class="spacing-edge spacing-edge-right margin-edge-right">
          <input
            v-model="marginRight"
            class="spacing-input spacing-input-vertical"
            placeholder="0"
            maxlength="3"
            @input="() => updateSpacing('margin', 'right')"
          />
        </div>
        <!-- margin-bottom -->
        <div class="spacing-edge spacing-edge-bottom margin-edge-bottom">
          <input
            v-model="marginBottom"
            class="spacing-input spacing-input-horizontal"
            placeholder="0"
            maxlength="3"
            @input="() => updateSpacing('margin', 'bottom')"
          />
        </div>
        <!-- margin-left -->
        <div class="spacing-edge spacing-edge-left margin-edge-left">
          <input
            v-model="marginLeft"
            class="spacing-input spacing-input-vertical"
            placeholder="0"
            maxlength="3"
            @input="() => updateSpacing('margin', 'left')"
          />
        </div>
        <div class="spacing-label">外边距</div>
      </div>
      <!-- padding -->
      <div class="spacing-box padding-box">
        <!-- padding-top -->
        <div class="spacing-edge spacing-edge-top padding-edge-top">
          <input
            v-model="paddingTop"
            class="spacing-input spacing-input-horizontal"
            placeholder="0"
            maxlength="3"
            @input="() => updateSpacing('padding', 'top')"
          />
        </div>
        <!-- padding-right -->
        <div class="spacing-edge spacing-edge-right padding-edge-right">
          <input
            v-model="paddingRight"
            class="spacing-input spacing-input-vertical"
            placeholder="0"
            maxlength="3"
            @input="() => updateSpacing('padding', 'right')"
          />
        </div>
        <!-- padding-bottom -->
        <div class="spacing-edge spacing-edge-bottom padding-edge-bottom">
          <input
            v-model="paddingBottom"
            class="spacing-input spacing-input-horizontal"
            placeholder="0"
            maxlength="3"
            @input="() => updateSpacing('padding', 'bottom')"
          />
        </div>
        <!-- padding-left -->
        <div class="spacing-edge spacing-edge-left padding-edge-left">
          <input
            v-model="paddingLeft"
            class="spacing-input spacing-input-vertical"
            placeholder="0"
            maxlength="3"
            @input="() => updateSpacing('padding', 'left')"
          />
        </div>
        <div class="spacing-label">内边距</div>
      </div>
    </div>
  </div>
</template>

<script>
// 支持的CSS单位
const SUPPORTED_UNITS = ['px', 'em', 'rem', '%', 'vh', 'vw']
const DEFAULT_UNIT = 'px'

// 间距配置
const SPACING_CONFIG = {
  margin: ['top', 'right', 'bottom', 'left'],
  padding: ['top', 'right', 'bottom', 'left'],
}

export default {
  name: 'MarginPaddingModule',
  props: {
    styleData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      activeTab: 'margin',
      // 动态创建数据属性
      ...this.createSpacingData(),
    }
  },

  watch: {
    styleData: {
      handler(val) {
        this.initFromStyleData(val)
      },
      immediate: true,
      deep: true,
    },
  },

  methods: {
    /**
     * 创建间距数据属性
     */
    createSpacingData() {
      const data = {}

      Object.keys(SPACING_CONFIG).forEach((type) => {
        SPACING_CONFIG[type].forEach((direction) => {
          const capitalizedDirection = this.capitalizeFirst(direction)

          // 创建值和单位属性
          data[`${type}${capitalizedDirection}`] = ''
          data[`${type}${capitalizedDirection}Unit`] = DEFAULT_UNIT
        })
      })

      return data
    },

    /**
     * 从样式数据初始化
     */
    initFromStyleData(styleData) {
      // 首先重置所有间距值为默认状态
      this.resetAllSpacingValues()

      // 如果没有有效的样式数据，保持重置状态
      if (!styleData || typeof styleData !== 'object') return

      Object.keys(SPACING_CONFIG).forEach((type) => {
        // 处理简写属性
        if (styleData[type]) {
          this.parseSpacing(styleData[type], type, 'all')
        }

        // 处理单独方向的属性（会覆盖简写属性）
        SPACING_CONFIG[type].forEach((direction) => {
          const property = `${type}-${direction}`
          if (styleData[property]) {
            this.parseSpacing(styleData[property], type, direction)
          }
        })
      })
    },

    /**
     * 重置所有间距值为默认状态
     */
    resetAllSpacingValues() {
      Object.keys(SPACING_CONFIG).forEach((type) => {
        SPACING_CONFIG[type].forEach((direction) => {
          this.setSpacingValue(type, direction, '', DEFAULT_UNIT)
        })
      })
    },

    /**
     * 解析间距值
     */
    parseSpacing(value, type, direction) {
      if (!value) return

      try {
        if (direction === 'all') {
          this.parseShorthandSpacing(value, type)
        } else {
          this.parseDirectionalSpacing(value, type, direction)
        }
      } catch (error) {
        console.warn(`解析${type}间距失败:`, error)
      }
    },

    /**
     * 解析简写间距
     */
    parseShorthandSpacing(value, type) {
      const values = String(value).trim().split(/\s+/)
      const directions = SPACING_CONFIG[type]

      const mapping = {
        1: [0, 0, 0, 0], // 四边相同
        2: [0, 1, 0, 1], // 上下相同，左右相同
        3: [0, 1, 2, 1], // 上、左右相同、下
        4: [0, 1, 2, 3], // 上右下左
      }

      const indices = mapping[values.length]
      if (!indices) return

      directions.forEach((direction, index) => {
        this.parseDirectionalSpacing(values[indices[index]], type, direction)
      })
    },

    /**
     * 解析单方向间距
     */
    parseDirectionalSpacing(value, type, direction) {
      const cleanValue = String(value).trim()

      // 处理auto值
      if (cleanValue === 'auto') {
        this.setSpacingValue(type, direction, 'auto', DEFAULT_UNIT)
        return
      }

      // 解析数字+单位格式
      const match = cleanValue.match(/^([\d.-]+)(\w+|%)$/)
      if (match) {
        const [, numValue, unit] = match
        if (this.isValidNumber(numValue) && this.isValidUnit(unit)) {
          this.setSpacingValue(type, direction, numValue, unit)
          return
        }
      }

      // 纯数字，默认px单位
      if (this.isValidNumber(cleanValue)) {
        this.setSpacingValue(type, direction, cleanValue, DEFAULT_UNIT)
        return
      }

      // 无效值，清空
      this.setSpacingValue(type, direction, '', DEFAULT_UNIT)
    },

    /**
     * 设置间距值
     */
    setSpacingValue(type, direction, value, unit) {
      const capitalizedDirection = this.capitalizeFirst(direction)

      this[`${type}${capitalizedDirection}`] = value
      this[`${type}${capitalizedDirection}Unit`] = unit
    },

    /**
     * 通用的间距更新方法
     */
    updateSpacing(type, direction) {
      const capitalizedDirection = this.capitalizeFirst(direction)

      const value = this[`${type}${capitalizedDirection}`]
      const unit = this[`${type}${capitalizedDirection}Unit`]
      const property = `${type}-${direction}`

      // 输入验证
      if (!this.validateSpacingInput(value)) {
        this.$emit('update', property, '')
        return
      }

      // 生成CSS值
      const cssValue = this.generateCSSValue(value, unit)
      this.$emit('update', property, cssValue)
    },

    /**
     * 验证间距输入
     */
    validateSpacingInput(value) {
      if (!value || value === '') return false
      if (value === 'auto') return true

      return this.isValidNumber(value)
    },

    /**
     * 生成CSS值
     */
    generateCSSValue(value, unit) {
      if (value === 'auto') return 'auto'
      if (!value || value === '') return ''

      return `${value}${unit}`
    },

    /**
     * 验证数字
     */
    isValidNumber(value) {
      const num = parseFloat(value)
      return !isNaN(num) && isFinite(num)
    },

    /**
     * 验证CSS单位
     */
    isValidUnit(unit) {
      return SUPPORTED_UNITS.includes(unit)
    },

    /**
     * 首字母大写
     */
    capitalizeFirst(str) {
      return str.charAt(0).toUpperCase() + str.slice(1)
    },
  },
}
</script>

<style lang="scss" scoped>
.margin-padding-module {
  padding: 0 15px 10px;
}

/* 容器样式 */
.spacing-container {
  @apply relative h-30 w-68;
}

/* 间距盒子基础样式 */
.spacing-box {
  @apply relative;
}

.margin-box {
  @apply h-30 w-full;
}

.padding-box {
  @apply absolute left-7 top-7 h-16 w-54;
}

/* 边缘样式基础类 */
.spacing-edge {
  @apply absolute border-24 border-solid border-blue-100;

  &:hover {
    @apply transition-colors duration-200;
  }
}

/* 上边缘 */
.spacing-edge-top {
  @apply top-0 h-0 w-full border-b-0 border-l-transparent border-r-transparent;
}

.margin-edge-top:hover {
  @apply border-t-blue-300;
}

.padding-edge-top {
  @apply border-t-blue-100;

  &:hover {
    @apply border-t-blue-300;
  }
}

/* 右边缘 */
.spacing-edge-right {
  @apply right-0 w-0 border-l-0 border-b-transparent border-t-transparent;
}

.margin-edge-right {
  @apply top-1 h-28 border-l-transparent;

  &:hover {
    @apply border-r-blue-300;
  }
}

.padding-edge-right {
  @apply top-1 h-14;

  &:hover {
    @apply border-r-blue-300;
  }
}

/* 下边缘 */
.spacing-edge-bottom {
  @apply bottom-0 h-0 w-full border-t-0 border-l-transparent border-r-transparent;

  &:hover {
    @apply border-b-blue-300;
  }
}

/* 左边缘 */
.spacing-edge-left {
  @apply left-0 w-0 border-r-0 border-b-transparent border-t-transparent;
}

.margin-edge-left {
  @apply top-1 h-28;

  &:hover {
    @apply border-l-blue-300;
  }
}

.padding-edge-left {
  @apply top-1 h-14;

  &:hover {
    @apply border-l-blue-300;
  }
}

/* 输入框样式 */
.spacing-input {
  @apply absolute bg-transparent text-center text-xs;
}

.spacing-input-horizontal {
  @apply h-6 w-full;
}

.spacing-input-vertical {
  @apply w-6;
  top: 50%;
  transform: translateY(-50%);
}

.margin-edge-right .spacing-input-vertical {
  @apply -right-6;
}

.margin-edge-left .spacing-input-vertical {
  @apply -left-6;
}

.padding-edge-right .spacing-input-vertical {
  @apply -right-6;
}

.padding-edge-left .spacing-input-vertical {
  @apply -left-6;
}

.spacing-edge-bottom .spacing-input-horizontal {
  @apply -bottom-6;
}
.spacing-edge-top .spacing-input-horizontal {
  @apply -top-6;
}

/* 标签样式 */
.spacing-label {
  @apply absolute bottom-0 left-4 text-gray-700 uppercase text-xs;
}
</style>
