<template>
  <div class="layout-module">
    <!-- 显示属性 -->
    <div class="display-section">
      <!-- 显示方式 -->
      <div class="display-type-row">
        <span class="label">显示方式:</span>
        <el-select
          v-model="displayType"
          placeholder="选择显示方式"
          size="mini"
          @change="updateDisplayType"
        >
          <el-option value="block" label="块级(block)"></el-option>
          <el-option value="inline" label="内联(inline)"></el-option>
          <el-option
            value="inline-block"
            label="内联块(inline-block)"
          ></el-option>
          <el-option value="flex" label="弹性布局(flex)"></el-option>
          <el-option value="grid" label="网格布局(grid)"></el-option>
          <el-option value="none" label="隐藏(none)"></el-option>
        </el-select>
      </div>

      <!-- 溢出处理 -->
      <div class="overflow-row">
        <span class="label">溢出处理:</span>
        <el-select
          v-model="overflow"
          placeholder="选择溢出处理方式"
          size="mini"
          @change="updateOverflow"
        >
          <el-option value="visible" label="可见(visible)"></el-option>
          <el-option value="hidden" label="隐藏(hidden)"></el-option>
          <el-option value="scroll" label="滚动(scroll)"></el-option>
          <el-option value="auto" label="自动(auto)"></el-option>
        </el-select>
      </div>
    </div>

    <!-- Flex布局设置 -->
    <div v-show="displayType === 'flex'" class="flex-section">
      <!-- 主轴方向 -->
      <div class="flex-direction-row">
        <span class="label">主轴方向:</span>
        <el-select
          v-model="flexDirection"
          placeholder="选择方向"
          size="mini"
          @change="updateFlexDirection"
        >
          <el-option value="row" label="水平(row)"></el-option>
          <el-option
            value="row-reverse"
            label="水平反向(row-reverse)"
          ></el-option>
          <el-option value="column" label="垂直(column)"></el-option>
          <el-option
            value="column-reverse"
            label="垂直反向(column-reverse)"
          ></el-option>
        </el-select>
      </div>

      <!-- 主轴对齐 -->
      <div class="justify-content-row">
        <span class="label">主轴对齐:</span>
        <el-select
          v-model="justifyContent"
          placeholder="选择对齐方式"
          size="mini"
          @change="updateJustifyContent"
        >
          <el-option
            value="flex-start"
            label="起点对齐(flex-start)"
          ></el-option>
          <el-option value="flex-end" label="终点对齐(flex-end)"></el-option>
          <el-option value="center" label="居中对齐(center)"></el-option>
          <el-option
            value="space-between"
            label="两端对齐(space-between)"
          ></el-option>
          <el-option
            value="space-around"
            label="环绕对齐(space-around)"
          ></el-option>
          <el-option
            value="space-evenly"
            label="均匀对齐(space-evenly)"
          ></el-option>
        </el-select>
      </div>

      <!-- 交叉轴对齐 -->
      <div class="align-items-row">
        <span class="label">交叉轴对齐:</span>
        <el-select
          v-model="alignItems"
          placeholder="选择对齐方式"
          size="mini"
          @change="updateAlignItems"
        >
          <el-option
            value="flex-start"
            label="起点对齐(flex-start)"
          ></el-option>
          <el-option value="flex-end" label="终点对齐(flex-end)"></el-option>
          <el-option value="center" label="居中对齐(center)"></el-option>
          <el-option value="baseline" label="基线对齐(baseline)"></el-option>
          <el-option value="stretch" label="拉伸对齐(stretch)"></el-option>
        </el-select>
      </div>

      <!-- 换行方式 -->
      <div class="flex-wrap-row">
        <span class="label">换行方式:</span>
        <el-select
          v-model="flexWrap"
          placeholder="选择换行方式"
          size="mini"
          @change="updateFlexWrap"
        >
          <el-option value="nowrap" label="不换行(nowrap)"></el-option>
          <el-option value="wrap" label="换行(wrap)"></el-option>
          <el-option
            value="wrap-reverse"
            label="反向换行(wrap-reverse)"
          ></el-option>
        </el-select>
      </div>

      <!-- 多行对齐 -->
      <div class="align-content-row">
        <span class="label">多行对齐:</span>
        <el-select
          v-model="alignContent"
          placeholder="选择对齐方式"
          size="mini"
          @change="updateAlignContent"
        >
          <el-option
            value="flex-start"
            label="起点对齐(flex-start)"
          ></el-option>
          <el-option value="flex-end" label="终点对齐(flex-end)"></el-option>
          <el-option value="center" label="居中对齐(center)"></el-option>
          <el-option
            value="space-between"
            label="两端对齐(space-between)"
          ></el-option>
          <el-option
            value="space-around"
            label="环绕对齐(space-around)"
          ></el-option>
          <el-option value="stretch" label="拉伸对齐(stretch)"></el-option>
        </el-select>
      </div>

      <!-- 子元素属性 -->
      <div
        v-if="
          parentContainerType === 'Flex' || parentContainerType === 'Layout'
        "
        class="flex-child-section"
      >
        <div class="section-title">作为Flex子元素</div>

        <!-- 伸缩比例 -->
        <div class="flex-grow-row">
          <span class="label">伸缩比例:</span>
          <el-input-number
            v-model="flexGrow"
            :min="0"
            :step="1"
            size="mini"
            @change="updateFlexGrow"
          ></el-input-number>
        </div>

        <!-- 收缩比例 -->
        <div class="flex-shrink-row">
          <span class="label">收缩比例:</span>
          <el-input-number
            v-model="flexShrink"
            :min="0"
            :step="1"
            size="mini"
            @change="updateFlexShrink"
          ></el-input-number>
        </div>

        <!-- 基准尺寸 -->
        <div class="flex-basis-row">
          <span class="label">基准尺寸:</span>
          <el-input
            v-model="flexBasis"
            placeholder="未设置"
            size="mini"
            @input="updateFlexBasis"
          >
            <template slot="append">
              <el-select
                v-model="flexBasisUnit"
                size="mini"
                @change="updateFlexBasis"
              >
                <el-option value="auto" label="auto"></el-option>
                <el-option value="px" label="px"></el-option>
                <el-option value="%" label="%"></el-option>
              </el-select>
            </template>
          </el-input>
        </div>

        <!-- 自身对齐 -->
        <div class="align-self-row">
          <span class="label">自身对齐:</span>
          <el-select
            v-model="alignSelf"
            placeholder="选择对齐方式"
            size="mini"
            @change="updateAlignSelf"
          >
            <el-option value="auto" label="继承父元素(auto)"></el-option>
            <el-option
              value="flex-start"
              label="起点对齐(flex-start)"
            ></el-option>
            <el-option value="flex-end" label="终点对齐(flex-end)"></el-option>
            <el-option value="center" label="居中对齐(center)"></el-option>
            <el-option value="baseline" label="基线对齐(baseline)"></el-option>
            <el-option value="stretch" label="拉伸对齐(stretch)"></el-option>
          </el-select>
        </div>
      </div>
    </div>

    <!-- Grid布局设置 -->
    <div v-show="displayType === 'grid'" class="grid-section">
      <div class="grid-container-section">
        <div class="section-title">网格容器</div>

        <!-- 网格模板 - 列 -->
        <div class="grid-template-columns-row">
          <span class="label">列模板:</span>
          <el-input
            v-model="gridTemplateColumns"
            placeholder="如：1fr 2fr 1fr"
            size="mini"
            @input="updateGridTemplateColumns"
          ></el-input>
        </div>

        <!-- 网格模板 - 行 -->
        <div class="grid-template-rows-row">
          <span class="label">行模板:</span>
          <el-input
            v-model="gridTemplateRows"
            placeholder="如：100px auto 100px"
            size="mini"
            @input="updateGridTemplateRows"
          ></el-input>
        </div>

        <!-- 列间距 -->
        <div class="grid-column-gap-row">
          <span class="label">列间距:</span>
          <el-input
            v-model="gridColumnGap"
            placeholder="未设置"
            size="mini"
            @input="updateGridColumnGap"
          >
            <template slot="append">
              <el-select
                v-model="gridColumnGapUnit"
                size="mini"
                @change="updateGridColumnGap"
              >
                <el-option value="px" label="px"></el-option>
                <el-option value="%" label="%"></el-option>
                <el-option value="em" label="em"></el-option>
                <el-option value="rem" label="rem"></el-option>
              </el-select>
            </template>
          </el-input>
        </div>

        <!-- 行间距 -->
        <div class="grid-row-gap-row">
          <span class="label">行间距:</span>
          <el-input
            v-model="gridRowGap"
            placeholder="未设置"
            size="mini"
            @input="updateGridRowGap"
          >
            <template slot="append">
              <el-select
                v-model="gridRowGapUnit"
                size="mini"
                @change="updateGridRowGap"
              >
                <el-option value="px" label="px"></el-option>
                <el-option value="%" label="%"></el-option>
                <el-option value="em" label="em"></el-option>
                <el-option value="rem" label="rem"></el-option>
              </el-select>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 网格项目设置 -->
      <div v-if="parentContainerType === 'Grid'" class="grid-item-section">
        <div class="section-title">作为Grid子元素</div>

        <!-- 列起始位置 -->
        <div class="grid-column-start-row">
          <span class="label">列起始:</span>
          <el-input-number
            v-model="gridColumnStart"
            :min="1"
            :step="1"
            size="mini"
            @change="updateGridColumnStart"
          ></el-input-number>
        </div>

        <!-- 列结束位置 -->
        <div class="grid-column-end-row">
          <span class="label">列结束:</span>
          <el-input-number
            v-model="gridColumnEnd"
            :min="1"
            :step="1"
            size="mini"
            @change="updateGridColumnEnd"
          ></el-input-number>
        </div>

        <!-- 行起始位置 -->
        <div class="grid-row-start-row">
          <span class="label">行起始:</span>
          <el-input-number
            v-model="gridRowStart"
            :min="1"
            :step="1"
            size="mini"
            @change="updateGridRowStart"
          ></el-input-number>
        </div>

        <!-- 行结束位置 -->
        <div class="grid-row-end-row">
          <span class="label">行结束:</span>
          <el-input-number
            v-model="gridRowEnd"
            :min="1"
            :step="1"
            size="mini"
            @change="updateGridRowEnd"
          ></el-input-number>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { createComponent } from '@utils/createComponent'

export default createComponent({
  name: 'LayoutModule',
  props: {
    styleData: {
      type: Object,
      default: () => ({}),
    },
    parentContainerType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      activeTab: 'display',

      // 显示属性
      displayType: 'block',
      overflow: 'visible',

      // Flex容器属性
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignItems: 'stretch',
      flexWrap: 'nowrap',
      alignContent: 'stretch',

      // Flex子项属性
      flexGrow: 0,
      flexShrink: 1,
      flexBasis: 'auto',
      flexBasisUnit: 'auto',
      alignSelf: 'auto',

      // Grid容器属性
      gridTemplateColumns: '',
      gridTemplateRows: '',
      gridColumnGap: '',
      gridColumnGapUnit: 'px',
      gridRowGap: '',
      gridRowGapUnit: 'px',

      // Grid子项属性
      gridColumnStart: 1,
      gridColumnEnd: 2,
      gridRowStart: 1,
      gridRowEnd: 2,
    }
  },

  watch: {
    styleData: {
      handler(val) {
        this.initFromStyleData(val)
      },
      immediate: true,
      deep: true,
    },

    displayType(val) {
      // 当显示类型改变时，更新活动标签
      if (val === 'flex') {
        this.activeTab = 'flex'
      } else if (val === 'grid') {
        this.activeTab = 'grid'
      }
    },
  },

  methods: {
    // 初始化数据
    initFromStyleData(styleData) {
      // 显示属性
      this.displayType = styleData.display || 'block'
      this.overflow = styleData.overflow || 'visible'

      // Flex容器属性
      this.flexDirection = styleData['flex-direction'] || 'row'
      this.justifyContent = styleData['justify-content'] || 'flex-start'
      this.alignItems = styleData['align-items'] || 'stretch'
      this.flexWrap = styleData['flex-wrap'] || 'nowrap'
      this.alignContent = styleData['align-content'] || 'stretch'

      // Flex子项属性
      this.flexGrow =
        styleData['flex-grow'] !== undefined
          ? parseFloat(styleData['flex-grow'])
          : 0
      this.flexShrink =
        styleData['flex-shrink'] !== undefined
          ? parseFloat(styleData['flex-shrink'])
          : 1
      this.parseFlexBasis(styleData['flex-basis'])
      this.alignSelf = styleData['align-self'] || 'auto'

      // Grid容器属性
      this.gridTemplateColumns = styleData['grid-template-columns'] || ''
      this.gridTemplateRows = styleData['grid-template-rows'] || ''
      this.parseGridGap(
        styleData['grid-column-gap'] || styleData['column-gap'],
        'column'
      )
      this.parseGridGap(
        styleData['grid-row-gap'] || styleData['row-gap'],
        'row'
      )

      // Grid子项属性
      this.gridColumnStart =
        styleData['grid-column-start'] !== undefined
          ? parseInt(styleData['grid-column-start'])
          : 1
      this.gridColumnEnd =
        styleData['grid-column-end'] !== undefined
          ? parseInt(styleData['grid-column-end'])
          : 2
      this.gridRowStart =
        styleData['grid-row-start'] !== undefined
          ? parseInt(styleData['grid-row-start'])
          : 1
      this.gridRowEnd =
        styleData['grid-row-end'] !== undefined
          ? parseInt(styleData['grid-row-end'])
          : 2
    },

    // 解析flex-basis
    parseFlexBasis(value) {
      if (!value || value === 'auto') {
        this.flexBasis = 'auto'
        this.flexBasisUnit = 'auto'
        return
      }

      const match = value.match(/^([\d.]+)(\w+|%)$/)
      if (match) {
        this.flexBasis = match[1]
        this.flexBasisUnit = match[2]
      } else {
        this.flexBasis = 'auto'
        this.flexBasisUnit = 'auto'
      }
    },

    // 解析网格间隙
    parseGridGap(value, type) {
      if (!value) {
        this[`grid${this.capitalizeFirst(type)}Gap`] = ''
        this[`grid${this.capitalizeFirst(type)}GapUnit`] = 'px'
        return
      }

      const match = value.match(/^([\d.]+)(\w+|%)$/)
      if (match) {
        this[`grid${this.capitalizeFirst(type)}Gap`] = match[1]
        this[`grid${this.capitalizeFirst(type)}GapUnit`] = match[2]
      }
    },

    // 首字母大写
    capitalizeFirst(str) {
      return str.charAt(0).toUpperCase() + str.slice(1)
    },

    // 更新显示方式
    updateDisplayType() {
      this.$emit('update', 'display', this.displayType)
    },

    // 更新溢出处理
    updateOverflow() {
      this.$emit('update', 'overflow', this.overflow)
    },

    // 更新Flex容器属性 - 主轴方向
    updateFlexDirection() {
      this.$emit('update', 'flex-direction', this.flexDirection)
    },

    // 更新Flex容器属性 - 主轴对齐
    updateJustifyContent() {
      this.$emit('update', 'justify-content', this.justifyContent)
    },

    // 更新Flex容器属性 - 交叉轴对齐
    updateAlignItems() {
      this.$emit('update', 'align-items', this.alignItems)
    },

    // 更新Flex容器属性 - 换行方式
    updateFlexWrap() {
      this.$emit('update', 'flex-wrap', this.flexWrap)
    },

    // 更新Flex容器属性 - 多行对齐
    updateAlignContent() {
      this.$emit('update', 'align-content', this.alignContent)
    },

    // 更新Flex子项属性 - 伸缩比例
    updateFlexGrow() {
      this.$emit('update', 'flex-grow', this.flexGrow.toString())
    },

    // 更新Flex子项属性 - 收缩比例
    updateFlexShrink() {
      this.$emit('update', 'flex-shrink', this.flexShrink.toString())
    },

    // 更新Flex子项属性 - 基准尺寸
    updateFlexBasis() {
      if (this.flexBasisUnit === 'auto') {
        this.$emit('update', 'flex-basis', 'auto')
      } else if (this.flexBasis) {
        this.$emit(
          'update',
          'flex-basis',
          `${this.flexBasis}${this.flexBasisUnit}`
        )
      } else {
        this.$emit('update', 'flex-basis', '')
      }
    },

    // 更新Flex子项属性 - 自身对齐
    updateAlignSelf() {
      this.$emit('update', 'align-self', this.alignSelf)
    },

    // 更新Grid容器属性 - 列模板
    updateGridTemplateColumns() {
      this.$emit('update', 'grid-template-columns', this.gridTemplateColumns)
    },

    // 更新Grid容器属性 - 行模板
    updateGridTemplateRows() {
      this.$emit('update', 'grid-template-rows', this.gridTemplateRows)
    },

    // 更新Grid容器属性 - 列间距
    updateGridColumnGap() {
      if (this.gridColumnGap) {
        const value = `${this.gridColumnGap}${this.gridColumnGapUnit}`
        this.$emit('update', 'column-gap', value)
      } else {
        this.$emit('update', 'column-gap', '')
      }
    },

    // 更新Grid容器属性 - 行间距
    updateGridRowGap() {
      if (this.gridRowGap) {
        const value = `${this.gridRowGap}${this.gridRowGapUnit}`
        this.$emit('update', 'row-gap', value)
      } else {
        this.$emit('update', 'row-gap', '')
      }
    },

    // 更新Grid子项属性 - 列起始位置
    updateGridColumnStart() {
      this.$emit('update', 'grid-column-start', this.gridColumnStart.toString())
    },

    // 更新Grid子项属性 - 列结束位置
    updateGridColumnEnd() {
      this.$emit('update', 'grid-column-end', this.gridColumnEnd.toString())
    },

    // 更新Grid子项属性 - 行起始位置
    updateGridRowStart() {
      this.$emit('update', 'grid-row-start', this.gridRowStart.toString())
    },

    // 更新Grid子项属性 - 行结束位置
    updateGridRowEnd() {
      this.$emit('update', 'grid-row-end', this.gridRowEnd.toString())
    },
  },
})
</script>

<style lang="scss" scoped>
.layout-module {
  padding: 0 15px 10px;

  .section-title {
    font-weight: bold;
    margin: 15px 0 10px;
  }

  .display-type-row,
  .overflow-row,
  .flex-direction-row,
  .justify-content-row,
  .align-items-row,
  .flex-wrap-row,
  .align-content-row,
  .flex-grow-row,
  .flex-shrink-row,
  .flex-basis-row,
  .align-self-row,
  .grid-template-columns-row,
  .grid-template-rows-row,
  .grid-column-gap-row,
  .grid-row-gap-row,
  .grid-column-start-row,
  .grid-column-end-row,
  .grid-row-start-row,
  .grid-row-end-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .label {
      width: 80px;
      flex-shrink: 0;
    }
  }

  .flex-child-section,
  .grid-container-section,
  .grid-item-section {
    margin-top: 15px;
  }
}
</style>
