<template>
  <div class="position-module">
    <div class="position-type-row w-full flex items-center">
      <span class="label mr-2 flex-shrink-0">定位方式:</span>
      <el-select
        v-model="positionType"
        placeholder="选择定位方式"
        size="mini"
        @change="updatePositionType"
      >
        <el-option value="static" label="默认(static)"></el-option>
        <el-option value="relative" label="相对定位(relative)"></el-option>
        <el-option value="absolute" label="绝对定位(absolute)"></el-option>
        <el-option value="fixed" label="固定定位(fixed)"></el-option>
        <el-option value="sticky" label="粘性定位(sticky)"></el-option>
      </el-select>
    </div>

    <div v-if="positionType !== 'static'" class="grid grid-cols-2 mt-2 gap-2">
      <div class="section-title col-span-2">位置偏移量</div>

      <div class="offset-row">
        <span class="label">上:</span>
        <el-input v-model="topValue" size="mini" @input="updateTopValue">
          <template slot="append">
            <el-select v-model="topUnit" size="mini" @change="updateTopValue">
              <el-option value="px" label="px"></el-option>
              <el-option value="%" label="%"></el-option>
              <el-option value="em" label="em"></el-option>
              <el-option value="rem" label="rem"></el-option>
            </el-select>
          </template>
        </el-input>
      </div>

      <div class="offset-row">
        <span class="label">右:</span>
        <el-input v-model="rightValue" size="mini" @input="updateRightValue">
          <template slot="append">
            <el-select
              v-model="rightUnit"
              size="mini"
              @change="updateRightValue"
            >
              <el-option value="px" label="px"></el-option>
              <el-option value="%" label="%"></el-option>
              <el-option value="em" label="em"></el-option>
              <el-option value="rem" label="rem"></el-option>
            </el-select>
          </template>
        </el-input>
      </div>

      <div class="offset-row">
        <span class="label">下:</span>
        <el-input v-model="bottomValue" size="mini" @input="updateBottomValue">
          <template slot="append">
            <el-select
              v-model="bottomUnit"
              size="mini"
              @change="updateBottomValue"
            >
              <el-option value="px" label="px"></el-option>
              <el-option value="%" label="%"></el-option>
              <el-option value="em" label="em"></el-option>
              <el-option value="rem" label="rem"></el-option>
            </el-select>
          </template>
        </el-input>
      </div>

      <div class="offset-row">
        <span class="label">左:</span>
        <el-input v-model="leftValue" size="mini" @input="updateLeftValue">
          <template slot="append">
            <el-select v-model="leftUnit" size="mini" @change="updateLeftValue">
              <el-option value="px" label="px"></el-option>
              <el-option value="%" label="%"></el-option>
              <el-option value="em" label="em"></el-option>
              <el-option value="rem" label="rem"></el-option>
            </el-select>
          </template>
        </el-input>
      </div>
    </div>

    <div v-if="positionType !== 'static'" class="z-index-row col-span-2 mt-3">
      <span class="label mr-2">层级(z-index):</span>
      <el-input-number
        v-model="zIndex"
        :min="-999"
        :max="9999"
        size="mini"
        @change="updateZIndex"
      ></el-input-number>
    </div>

    <div
      v-if="positionType === 'absolute' || positionType === 'fixed'"
      class="helpful-tips"
    >
      <el-alert
        type="info"
        :closable="false"
        title="提示"
        description="绝对定位和固定定位会使元素脱离正常文档流，可能会覆盖其他元素。使用上、右、下、左属性控制元素位置。"
        show-icon
      ></el-alert>
    </div>

    <div v-if="positionType === 'sticky'" class="helpful-tips">
      <el-alert
        type="info"
        :closable="false"
        title="提示"
        description="粘性定位在滚动到指定阈值前为相对定位，滚动到阈值后变为固定定位。常用于固定导航栏。"
        show-icon
      ></el-alert>
    </div>
  </div>
</template>

<script>
import { createComponent } from '@utils/createComponent'

export default createComponent({
  name: 'PositionModule',
  props: {
    styleData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      // 定位方式
      positionType: 'static',

      // 位置偏移量
      topValue: '',
      topUnit: 'px',
      rightValue: '',
      rightUnit: 'px',
      bottomValue: '',
      bottomUnit: 'px',
      leftValue: '',
      leftUnit: 'px',

      // 层级
      zIndex: 0,
    }
  },

  watch: {
    styleData: {
      handler(val) {
        this.initFromStyleData(val)
      },
      immediate: true,
      deep: true,
    },
  },

  methods: {
    // 初始化数据
    initFromStyleData(styleData) {
      // 定位方式
      this.positionType = styleData.position || 'static'

      // 位置偏移量
      this.parseOffsetValue(styleData.top, 'top')
      this.parseOffsetValue(styleData.right, 'right')
      this.parseOffsetValue(styleData.bottom, 'bottom')
      this.parseOffsetValue(styleData.left, 'left')

      // 层级
      this.zIndex =
        styleData['z-index'] !== undefined ? parseInt(styleData['z-index']) : 0
    },

    // 解析偏移量
    parseOffsetValue(value, position) {
      if (!value) {
        this[`${position}Value`] = ''
        this[`${position}Unit`] = 'px'
        return
      }

      const match = value.match(/^([\d.-]+)(\w+|%)$/)
      if (match) {
        this[`${position}Value`] = match[1]
        this[`${position}Unit`] = match[2]
      }
    },

    // 更新定位方式
    updatePositionType() {
      this.$emit('update', 'position', this.positionType)

      // 如果切换到静态定位，清除所有偏移量和层级
      if (this.positionType === 'static') {
        this.$emit('update', 'top', '')
        this.$emit('update', 'right', '')
        this.$emit('update', 'bottom', '')
        this.$emit('update', 'left', '')
        this.$emit('update', 'z-index', '')
      }
    },

    // 更新位置偏移量
    updateTopValue() {
      if (this.topValue) {
        this.$emit('update', 'top', `${this.topValue}${this.topUnit}`)
      } else {
        this.$emit('update', 'top', '')
      }
    },

    updateRightValue() {
      if (this.rightValue) {
        this.$emit('update', 'right', `${this.rightValue}${this.rightUnit}`)
      } else {
        this.$emit('update', 'right', '')
      }
    },

    updateBottomValue() {
      if (this.bottomValue) {
        this.$emit('update', 'bottom', `${this.bottomValue}${this.bottomUnit}`)
      } else {
        this.$emit('update', 'bottom', '')
      }
    },

    updateLeftValue() {
      if (this.leftValue) {
        this.$emit('update', 'left', `${this.leftValue}${this.leftUnit}`)
      } else {
        this.$emit('update', 'left', '')
      }
    },

    // 更新层级
    updateZIndex() {
      if (this.zIndex !== 0) {
        this.$emit('update', 'z-index', this.zIndex.toString())
      } else {
        this.$emit('update', 'z-index', '')
      }
    },
  },
})
</script>

<style lang="scss" scoped>
.position-module {
  padding: 0 15px 10px;

  .section-title {
    font-weight: bold;
    font-size: 12px;
  }

  .helpful-tips {
    margin-top: 10px;
  }
}
</style>
