<template>
  <div class="text-module">
    <StyleTabs v-model="activeTab" :tabs="tabs" class="mb-2" />
    <!-- 字体与颜色 -->
    <div v-if="activeTab === 'font'" class="font-section">
      <!-- 字体系列 -->
      <div class="font-family-row">
        <span class="label">字体系列:</span>
        <el-select
          v-model="fontFamily"
          placeholder="选择字体"
          size="mini"
          style="width: 100%"
          @change="updateFontFamily"
        >
          <el-option value="'Arial', sans-serif" label="Arial"></el-option>
          <el-option
            value="'Helvetica', sans-serif"
            label="Helvetica"
          ></el-option>
          <el-option
            value="'Times New Roman', serif"
            label="Times New Roman"
          ></el-option>
          <el-option
            value="'Courier New', monospace"
            label="Courier New"
          ></el-option>
          <el-option value="'Georgia', serif" label="Georgia"></el-option>
          <el-option value="'Verdana', sans-serif" label="Verdana"></el-option>
          <el-option
            value="'微软雅黑', 'Microsoft YaHei', sans-serif"
            label="微软雅黑"
          ></el-option>
          <el-option value="'宋体', 'SimSun', serif" label="宋体"></el-option>
          <el-option
            value="'黑体', 'SimHei', sans-serif"
            label="黑体"
          ></el-option>
          <el-option value="'楷体', 'KaiTi', serif" label="楷体"></el-option>
        </el-select>
      </div>

      <!-- 字体大小 -->
      <div class="font-size-row">
        <span class="label">字体大小:</span>
        <el-input
          v-model="fontSize"
          placeholder="未设置"
          size="mini"
          @input="updateFontSize"
        >
          <template slot="append">
            <el-select
              v-model="fontSizeUnit"
              size="mini"
              @change="updateFontSize"
            >
              <el-option value="px" label="px"></el-option>
              <el-option value="%" label="%"></el-option>
              <el-option value="em" label="em"></el-option>
              <el-option value="rem" label="rem"></el-option>
            </el-select>
          </template>
        </el-input>
      </div>

      <!-- 字体粗细 -->
      <div class="font-weight-row">
        <span class="label">字体粗细:</span>
        <el-select
          v-model="fontWeight"
          placeholder="选择粗细"
          size="mini"
          @change="updateFontWeight"
        >
          <el-option value="normal" label="正常"></el-option>
          <el-option value="bold" label="粗体"></el-option>
          <el-option value="lighter" label="细体"></el-option>
          <el-option value="100" label="100"></el-option>
          <el-option value="200" label="200"></el-option>
          <el-option value="300" label="300"></el-option>
          <el-option value="400" label="400 (normal)"></el-option>
          <el-option value="500" label="500"></el-option>
          <el-option value="600" label="600"></el-option>
          <el-option value="700" label="700 (bold)"></el-option>
          <el-option value="800" label="800"></el-option>
          <el-option value="900" label="900"></el-option>
        </el-select>
      </div>

      <!-- 字体风格 -->
      <div class="font-style-row">
        <span class="label">字体风格:</span>
        <el-select
          v-model="fontStyle"
          placeholder="选择风格"
          size="mini"
          @change="updateFontStyle"
        >
          <el-option value="normal" label="正常"></el-option>
          <el-option value="italic" label="斜体"></el-option>
          <el-option value="oblique" label="倾斜"></el-option>
        </el-select>
      </div>

      <!-- 文本颜色 -->
      <div class="color-row">
        <span class="label">文本颜色:</span>
        <el-color-picker
          v-model="color"
          show-alpha
          @change="updateColor"
        ></el-color-picker>
      </div>
    </div>

    <!-- 文本对齐与装饰 -->
    <div v-if="activeTab === 'align'" class="align-section">
      <!-- 文本对齐 -->
      <div class="text-align-row">
        <span class="label">文本对齐:</span>
        <el-radio-group
          v-model="textAlign"
          size="mini"
          @change="updateTextAlign"
        >
          <el-radio-button float="left" label="left">
            <i class="el-icon-s-fold"></i>
          </el-radio-button>
          <el-radio-button float="center" label="center">
            <i class="el-icon-s-operation"></i>
          </el-radio-button>
          <el-radio-button float="right" label="right">
            <i class="el-icon-s-unfold"></i>
          </el-radio-button>
          <el-radio-button float="justify" label="justify">
            <i class="el-icon-menu"></i>
          </el-radio-button>
        </el-radio-group>
      </div>

      <!-- 垂直对齐 -->
      <div class="vertical-align-row">
        <span class="label">垂直对齐:</span>
        <el-select
          v-model="verticalAlign"
          placeholder="选择对齐方式"
          size="mini"
          @change="updateVerticalAlign"
        >
          <el-option value="baseline" label="基线"></el-option>
          <el-option value="top" label="顶部"></el-option>
          <el-option value="middle" label="居中"></el-option>
          <el-option value="bottom" label="底部"></el-option>
          <el-option value="text-top" label="文本顶部"></el-option>
          <el-option value="text-bottom" label="文本底部"></el-option>
        </el-select>
      </div>

      <!-- 行高 -->
      <div class="line-height-row">
        <span class="label">行高:</span>
        <el-input
          v-model="lineHeight"
          placeholder="未设置"
          size="mini"
          @input="updateLineHeight"
        >
          <template slot="append">
            <el-select
              v-model="lineHeightUnit"
              size="mini"
              @change="updateLineHeight"
            >
              <el-option value="" label="无单位"></el-option>
              <el-option value="px" label="px"></el-option>
              <el-option value="%" label="%"></el-option>
              <el-option value="em" label="em"></el-option>
            </el-select>
          </template>
        </el-input>
      </div>

      <!-- 字间距 -->
      <div class="letter-spacing-row">
        <span class="label">字间距:</span>
        <el-input
          v-model="letterSpacing"
          placeholder="未设置"
          size="mini"
          @input="updateLetterSpacing"
        >
          <template slot="append">
            <el-select
              v-model="letterSpacingUnit"
              size="mini"
              @change="updateLetterSpacing"
            >
              <el-option value="px" label="px"></el-option>
              <el-option value="em" label="em"></el-option>
            </el-select>
          </template>
        </el-input>
      </div>

      <!-- 单词间距 -->
      <div class="word-spacing-row">
        <span class="label">单词间距:</span>
        <el-input
          v-model="wordSpacing"
          placeholder="未设置"
          size="mini"
          @input="updateWordSpacing"
        >
          <template slot="append">
            <el-select
              v-model="wordSpacingUnit"
              size="mini"
              @change="updateWordSpacing"
            >
              <el-option value="px" label="px"></el-option>
              <el-option value="em" label="em"></el-option>
            </el-select>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 文本修饰 -->
    <div v-if="activeTab === 'decoration'" class="decoration-section">
      <!-- 文本装饰 -->
      <div class="text-decoration-row">
        <span class="label">文本装饰:</span>
        <el-checkbox-group
          v-model="textDecorations"
          @change="updateTextDecoration"
        >
          <el-checkbox label="underline">下划线</el-checkbox>
          <el-checkbox label="line-through">删除线</el-checkbox>
          <el-checkbox label="overline">上划线</el-checkbox>
        </el-checkbox-group>
      </div>

      <!-- 文本转换 -->
      <div class="text-transform-row">
        <span class="label">文本转换:</span>
        <el-select
          v-model="textTransform"
          placeholder="选择转换方式"
          size="mini"
          @change="updateTextTransform"
        >
          <el-option value="none" label="无"></el-option>
          <el-option value="capitalize" label="首字母大写"></el-option>
          <el-option value="uppercase" label="全部大写"></el-option>
          <el-option value="lowercase" label="全部小写"></el-option>
        </el-select>
      </div>

      <!-- 文本缩进 -->
      <div class="text-indent-row">
        <span class="label">文本缩进:</span>
        <el-input
          v-model="textIndent"
          placeholder="未设置"
          size="mini"
          @input="updateTextIndent"
        >
          <template slot="append">
            <el-select
              v-model="textIndentUnit"
              size="mini"
              @change="updateTextIndent"
            >
              <el-option value="px" label="px"></el-option>
              <el-option value="em" label="em"></el-option>
              <el-option value="%" label="%"></el-option>
            </el-select>
          </template>
        </el-input>
      </div>

      <!-- 文本阴影 -->
      <div class="text-shadow-section">
        <div class="section-title mb-1">文本阴影</div>

        <div class="shadow-control-row">
          <span class="shadow-label">水平偏移:</span>
          <el-input
            v-model="shadowX"
            placeholder="0"
            size="mini"
            @input="updateTextShadow"
          >
            <template slot="append">px</template>
          </el-input>
        </div>

        <div class="shadow-control-row">
          <span class="shadow-label">垂直偏移:</span>
          <el-input
            v-model="shadowY"
            placeholder="0"
            size="mini"
            @input="updateTextShadow"
          >
            <template slot="append">px</template>
          </el-input>
        </div>

        <div class="shadow-control-row">
          <span class="shadow-label">模糊半径:</span>
          <el-input
            v-model="shadowBlur"
            placeholder="0"
            size="mini"
            @input="updateTextShadow"
          >
            <template slot="append">px</template>
          </el-input>
        </div>

        <div class="shadow-control-row">
          <span class="shadow-label">阴影颜色:</span>
          <el-color-picker
            v-model="shadowColor"
            show-alpha
            @change="updateTextShadow"
          ></el-color-picker>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { createComponent } from '@utils/createComponent'
import StyleTabs from '../tabs.vue'

export default createComponent({
  name: 'TextModule',
  props: {
    styleData: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    StyleTabs,
  },
  data() {
    return {
      activeTab: 'font',
      tabs: [
        { name: 'font', label: '字体' },
        { name: 'align', label: '对齐' },
        { name: 'decoration', label: '修饰' },
      ],

      // 字体与颜色
      fontFamily: '',
      fontSize: '',
      fontSizeUnit: 'px',
      fontWeight: 'normal',
      fontStyle: 'normal',
      color: '',

      // 对齐与行高
      textAlign: 'left',
      verticalAlign: 'baseline',
      lineHeight: '',
      lineHeightUnit: '',
      letterSpacing: '',
      letterSpacingUnit: 'px',
      wordSpacing: '',
      wordSpacingUnit: 'px',

      // 文本修饰
      textDecorations: [],
      textTransform: 'none',
      textIndent: '',
      textIndentUnit: 'px',

      // 文本阴影
      shadowX: '0',
      shadowY: '0',
      shadowBlur: '0',
      shadowColor: 'rgba(0, 0, 0, 0.5)',
    }
  },

  watch: {
    styleData: {
      handler(val) {
        this.initFromStyleData(val)
      },
      immediate: true,
      deep: true,
    },
  },

  methods: {
    // 初始化数据
    initFromStyleData(styleData) {
      // 字体与颜色
      this.fontFamily = styleData['font-family'] || ''
      this.parseFontSize(styleData['font-size'])
      this.fontWeight = styleData['font-weight'] || 'normal'
      this.fontStyle = styleData['font-style'] || 'normal'
      this.color = styleData.color || ''

      // 对齐与行高
      this.textAlign = styleData['text-align'] || 'left'
      this.verticalAlign = styleData['vertical-align'] || 'baseline'
      this.parseLineHeight(styleData['line-height'])
      this.parseSpacing(styleData['letter-spacing'], 'letterSpacing')
      this.parseSpacing(styleData['word-spacing'], 'wordSpacing')

      // 文本修饰
      this.parseTextDecoration(styleData['text-decoration'])
      this.textTransform = styleData['text-transform'] || 'none'
      this.parseTextIndent(styleData['text-indent'])

      // 文本阴影
      this.parseTextShadow(styleData['text-shadow'])
    },

    // 解析字体大小
    parseFontSize(value) {
      if (!value) {
        this.fontSize = ''
        this.fontSizeUnit = 'px'
        return
      }

      const match = value.match(/^([\d.]+)(\w+|%)$/)
      if (match) {
        this.fontSize = match[1]
        this.fontSizeUnit = match[2]
      }
    },

    // 解析行高
    parseLineHeight(value) {
      if (!value) {
        this.lineHeight = ''
        this.lineHeightUnit = ''
        return
      }

      // 处理无单位行高
      if (!isNaN(value)) {
        this.lineHeight = value
        this.lineHeightUnit = ''
        return
      }

      const match = value.match(/^([\d.]+)(\w+|%)$/)
      if (match) {
        this.lineHeight = match[1]
        this.lineHeightUnit = match[2]
      }
    },

    // 解析间距
    parseSpacing(value, property) {
      if (!value) {
        this[property] = ''
        this[`${property}Unit`] = 'px'
        return
      }

      const match = value.match(/^([\d.-]+)(\w+)$/)
      if (match) {
        this[property] = match[1]
        this[`${property}Unit`] = match[2]
      }
    },

    // 解析文本装饰
    parseTextDecoration(value) {
      if (!value || value === 'none') {
        this.textDecorations = []
        return
      }

      this.textDecorations = []
      const decorations = ['underline', 'line-through', 'overline']

      decorations.forEach((decoration) => {
        if (value.includes(decoration)) {
          this.textDecorations.push(decoration)
        }
      })
    },

    // 解析文本缩进
    parseTextIndent(value) {
      if (!value) {
        this.textIndent = ''
        this.textIndentUnit = 'px'
        return
      }

      const match = value.match(/^([\d.-]+)(\w+|%)$/)
      if (match) {
        this.textIndent = match[1]
        this.textIndentUnit = match[2]
      }
    },

    // 解析文本阴影
    parseTextShadow(value) {
      if (!value || value === 'none') {
        this.shadowX = '0'
        this.shadowY = '0'
        this.shadowBlur = '0'
        this.shadowColor = 'rgba(0, 0, 0, 0.5)'
        return
      }

      // 尝试匹配 "Xpx Ypx Blurpx Color"
      const match = value.match(/(-?\d+)px\s+(-?\d+)px\s+(-?\d+)px\s+([^,]+)/)
      if (match) {
        this.shadowX = match[1]
        this.shadowY = match[2]
        this.shadowBlur = match[3]
        this.shadowColor = match[4].trim()
      }
    },

    // 更新字体系列
    updateFontFamily() {
      this.$emit('update', 'font-family', this.fontFamily)
    },

    // 更新字体大小
    updateFontSize() {
      if (this.fontSize) {
        this.$emit(
          'update',
          'font-size',
          `${this.fontSize}${this.fontSizeUnit}`
        )
      } else {
        this.$emit('update', 'font-size', '')
      }
    },

    // 更新字体粗细
    updateFontWeight() {
      this.$emit('update', 'font-weight', this.fontWeight)
    },

    // 更新字体风格
    updateFontStyle() {
      this.$emit('update', 'font-style', this.fontStyle)
    },

    // 更新文本颜色
    updateColor() {
      this.$emit('update', 'color', this.color)
    },

    // 更新文本对齐
    updateTextAlign() {
      this.$emit('update', 'text-align', this.textAlign)
    },

    // 更新垂直对齐
    updateVerticalAlign() {
      this.$emit('update', 'vertical-align', this.verticalAlign)
    },

    // 更新行高
    updateLineHeight() {
      if (this.lineHeight) {
        const value = this.lineHeightUnit
          ? `${this.lineHeight}${this.lineHeightUnit}`
          : this.lineHeight
        this.$emit('update', 'line-height', value)
      } else {
        this.$emit('update', 'line-height', '')
      }
    },

    // 更新字间距
    updateLetterSpacing() {
      if (this.letterSpacing) {
        this.$emit(
          'update',
          'letter-spacing',
          `${this.letterSpacing}${this.letterSpacingUnit}`
        )
      } else {
        this.$emit('update', 'letter-spacing', '')
      }
    },

    // 更新单词间距
    updateWordSpacing() {
      if (this.wordSpacing) {
        this.$emit(
          'update',
          'word-spacing',
          `${this.wordSpacing}${this.wordSpacingUnit}`
        )
      } else {
        this.$emit('update', 'word-spacing', '')
      }
    },

    // 更新文本装饰
    updateTextDecoration() {
      if (this.textDecorations.length === 0) {
        this.$emit('update', 'text-decoration', 'none')
      } else {
        this.$emit('update', 'text-decoration', this.textDecorations.join(' '))
      }
    },

    // 更新文本转换
    updateTextTransform() {
      this.$emit('update', 'text-transform', this.textTransform)
    },

    // 更新文本缩进
    updateTextIndent() {
      if (this.textIndent) {
        this.$emit(
          'update',
          'text-indent',
          `${this.textIndent}${this.textIndentUnit}`
        )
      } else {
        this.$emit('update', 'text-indent', '')
      }
    },

    // 更新文本阴影
    updateTextShadow() {
      if (
        this.shadowX === '0' &&
        this.shadowY === '0' &&
        this.shadowBlur === '0'
      ) {
        this.$emit('update', 'text-shadow', 'none')
      } else {
        const shadow = `${this.shadowX}px ${this.shadowY}px ${this.shadowBlur}px ${this.shadowColor}`
        this.$emit('update', 'text-shadow', shadow)
      }
    },
  },
})
</script>

<style lang="scss" scoped>
.text-module {
  padding: 0 15px 10px;

  .section-title {
    font-weight: bold;
    margin: 10px 0 0;
    font-size: 12px;
  }

  .font-family-row,
  .font-size-row,
  .font-weight-row,
  .font-style-row,
  .color-row,
  .text-align-row,
  .vertical-align-row,
  .line-height-row,
  .letter-spacing-row,
  .word-spacing-row,
  .text-decoration-row,
  .text-transform-row,
  .text-indent-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .label {
      width: 80px;
      flex-shrink: 0;
    }
  }

  .shadow-control-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .shadow-label {
      width: 80px;
      flex-shrink: 0;
    }
  }
}
</style>
