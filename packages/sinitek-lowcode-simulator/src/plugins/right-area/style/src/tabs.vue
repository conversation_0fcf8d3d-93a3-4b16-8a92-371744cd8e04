<template>
  <div class="style-tabs flex">
    <div
      v-for="tab in tabs"
      :key="tab.name"
      class="style-tab-item cursor-pointer"
      :class="{ active: activeTab === tab.name }"
      @click="handleTabChange(tab.name)"
    >
      {{ tab.label }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'StyleTabs',
  props: {
    value: {
      type: String,
      default: '',
    },
    tabs: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      activeTab: 'color',
    }
  },
  watch: {
    value: {
      handler(val) {
        this.activeTab = val
      },
    },
  },
  methods: {
    handleTabChange(tab) {
      this.activeTab = tab
      this.$emit('input', tab)
    },
  },
}
</script>

<style lang="scss" scoped>
.style-tab-item {
  border: 1px solid #d8d8d8;
  width: 72px;
  color: #999;
  @apply f-c-c;
  &:hover,
  &.active {
    @apply text-primary b-primary;
  }
  &:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  &:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}
</style>
