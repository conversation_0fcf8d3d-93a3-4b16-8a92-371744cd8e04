<template>
  <div class="style-setting-panel">
    <template v-if="isSelectPage">
      <div class="flex items-center justify-between p-2 fs-14">
        全局样式
        <div
          class="i-custom:fullscreen h-4 w-4"
          @click="setPageFullScreen(true)"
        />
      </div>
      <MonacoEditor
        ref="page"
        class="b h-40!"
        language="css"
        @input="onPageCssChange"
      />
    </template>
    <template v-else>
      <!-- 样式控制面板 -->
      <div class="style-modules">
        <!-- 高级样式编辑器 -->
        <collapse-item ref="advancedModule" :border="true" title="高级">
          <div class="flex items-center justify-between p-2 fs-14">
            行内样式
            <div
              class="i-custom:fullscreen h-4 w-4"
              @click="setFullScreen(true)"
            />
          </div>
          <MonacoEditor
            ref="monaco"
            class="h-40!"
            language="css"
            @input="onMonacoChange"
          />
        </collapse-item>
        <LCStyle :value="css" @input="updateStyle" />
      </div>
    </template>
  </div>
</template>

<script>
import { createComponent } from '@utils/createComponent'
import MonacoEditor from '@common/monaco-editor/view.vue'
import { formatCss } from '../../../../utils'
import { setObserver, CollapseItem } from 'sinitek-lowcode-shared'
import LCStyle from './style'

export default createComponent({
  name: 'SettingStyle',
  components: {
    MonacoEditor,
    CollapseItem,
    LCStyle,
  },
  data() {
    return {
      css: '',
    }
  },
  computed: {
    isSelectPage() {
      return this.$doc.getCurrent()?.schema?.componentName === 'Page'
    },
  },
  mounted() {
    this.updateModel()
  },
  methods: {
    updateModel() {
      this.$nextTick(() => {
        this.getStyleProps()
      })
    },
    getStyleProps() {
      if (this.isSelectPage) {
        const _css = this.$doc.getSchema(true).css || ''
        formatCss(_css).then((code) => {
          if (this.$refs.page) {
            this.$refs.page.editor.setValue(code)
          }
        })
      } else {
        const schema = this.$doc.getCurrent(true)?.schema
        if (schema) {
          const _style = schema.style || ''
          // 解析样式字符串到对象
          this.css = _style
          // 更新Monaco编辑器
          formatCss(`:root{${_style}\n}`).then((code) => {
            if (this.$refs.monaco) {
              this.$refs.monaco.editor.setValue(code)
            }
          })
        }
      }
    },
    updateStyle(v) {
      this.css = v
      this.applyStyleChanges()
    },
    applyStyleChanges() {
      const styleStr = this.css
      const schema = this.$doc.getCurrent().schema
      // 修改canvas的响应式对象

      setObserver(schema, 'style', styleStr)
      // 更新Monaco编辑器
      formatCss(`:root{${styleStr}\n}`).then((code) => {
        if (this.$refs.monaco) {
          this.$refs.monaco.editor.setValue(code)
        }
      })
      this._research()
    },
    onMonacoChange(v) {
      const schema = this.$doc.getCurrent().schema
      try {
        const style = v
          .match(/:root\s*{([\s\S]*)}/)[1]
          .replace(/\n/g, '')
          .trim()

        // 解析新的样式到styleData对象
        this.css = style
        setObserver(schema, 'style', style)
        this._research()
      } catch (error) {
        console.error('Invalid CSS format', error)
      }
    },
    onPageCssChange(v) {
      const schema = this.$doc.getSchema()
      if (!schema.css) {
        this.$set(schema, 'css', v)
      } else {
        schema.css = v
      }
      this._research()
    },
    setFullScreen(fullScreen) {
      this.$refs.monaco.setFullScreen(fullScreen)
    },
    setPageFullScreen(fullScreen) {
      this.$refs.page.setFullScreen(fullScreen)
    },
  },
})
</script>

<style lang="scss">
.style-setting-panel {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .el-input-group__append .el-select {
    width: 75px !important;
    margin: -10px -30px;
    .el-input__inner {
      margin-left: 5px;
    }
    .el-input__suffix {
      margin-right: 10px;
    }
  }
}

.style-modules {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 4px;
  margin-right: -4px; /* 防止滚动条占用空间 */

  /* 美化滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}
</style>
