<template>
  <div class="style-setting-panel">
    <!-- 样式控制面板 -->
    <div class="style-modules">
      <!-- 尺寸设置模块 -->
      <collapse-item ref="sizeModule" :border="true" title="尺寸">
        <size-module
          :style-data="styleData"
          :component-type="componentType"
          @update="updateStyle"
        />
      </collapse-item>

      <!-- 内外边距模块 -->
      <collapse-item ref="marginPaddingModule" :border="true" title="边距">
        <margin-padding-module :style-data="styleData" @update="updateStyle" />
      </collapse-item>

      <!-- 边框样式模块 -->
      <collapse-item ref="borderModule" :border="true" title="边框">
        <border-module :style-data="styleData" @update="updateStyle" />
      </collapse-item>

      <!-- 背景样式模块 -->
      <collapse-item ref="backgroundModule" :border="true" title="背景">
        <background-module :style-data="styleData" @update="updateStyle" />
      </collapse-item>

      <!-- 定位模块(仅容器组件显示) -->
      <collapse-item ref="positionModule" :border="true" title="定位">
        <position-module :style-data="styleData" @update="updateStyle" />
      </collapse-item>

      <!-- 文本样式模块(仅文本节点显示) -->
      <collapse-item ref="textModule" :border="true" title="文本">
        <text-module :style-data="styleData" @update="updateStyle" />
      </collapse-item>

      <!-- 布局模块 -->
      <collapse-item ref="layoutModule" :border="true" title="布局">
        <layout-module
          :style-data="styleData"
          :parent-container-type="parentContainerType"
          @update="updateStyle"
        />
      </collapse-item>
    </div>
  </div>
</template>

<script>
import SizeModule from './modules/size.vue'
import MarginPaddingModule from './modules/margin-padding.vue'
import BorderModule from './modules/border.vue'
import BackgroundModule from './modules/background.vue'
import PositionModule from './modules/position.vue'
import TextModule from './modules/text.vue'
import LayoutModule from './modules/layout.vue'
import debounce from 'lodash/debounce'
import { CollapseItem } from 'sinitek-lowcode-shared'

export default {
  name: 'LCStyle',
  components: {
    CollapseItem,
    SizeModule,
    MarginPaddingModule,
    BorderModule,
    BackgroundModule,
    PositionModule,
    TextModule,
    LayoutModule,
  },
  props: {
    value: [String, Object],
  },
  data() {
    return {
      styleData: {},
      componentType: '',
      parentContainerType: '',
      debouncedUpdate: null,
      currentStyle: '',
    }
  },
  computed: {
    isContainer() {
      // 判断是否为容器类组件
      const containerTypes = [
        'Container',
        'Layout',
        'Grid',
        'Flex',
        'Row',
        'Column',
      ]
      return containerTypes.includes(this.componentType)
    },
    hasTextContent() {
      // 判断是否包含文本内容
      const textTypes = [
        'Text',
        'Button',
        'Link',
        'Label',
        'Title',
        'Paragraph',
      ]
      if (textTypes.includes(this.componentType)) return true

      // 检查组件是否有textContent属性
      const schema = this.$doc.getCurrent(true)?.schema
      return (
        schema && (schema.textContent || (schema.props && schema.props.text))
      )
    },
  },
  watch: {
    value: {
      handler(v) {
        if (!v) {
          this.styleData = {}
          return
        }
        if (typeof v === 'string') {
          this.styleData = this.parseStyleToObject(v)
        } else {
          this.styleData = v
        }
      },
      immediate: true,
    },
  },
  created() {
    this.debouncedUpdate = debounce(this.applyStyleChanges, 500)
  },
  methods: {
    parseStyleToObject(styleStr) {
      if (!styleStr) return {}

      const styleObj = {}
      const styles = styleStr.split(';')

      styles.forEach((style) => {
        const [property, value] = style.split(':').map((s) => s.trim())
        if (property && value) {
          styleObj[property] = value
        }
      })

      return styleObj
    },
    objectToStyleString(styleObj) {
      if (!styleObj || Object.keys(styleObj).length === 0) return ''

      return Object.entries(styleObj)
        .filter(([, value]) => value !== undefined && value !== '')
        .map(([prop, value]) => `${prop}: ${value}`)
        .join('; ')
    },
    updateStyle(propertyName, value) {
      // 更新styleData对象
      if (value === undefined || value === '') {
        this.$delete(this.styleData, propertyName)
      } else {
        this.$set(this.styleData, propertyName, value)
      }
      // 使用防抖处理更新
      this.debouncedUpdate()
    },
    applyStyleChanges() {
      const styleStr = this.objectToStyleString(this.styleData)
      this.$emit('input', styleStr)
    },
  },
}
</script>

<style lang="scss">
.style-setting-panel {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .el-input-group__append .el-select {
    width: 75px !important;
    margin: -10px -30px;
    .el-input__inner {
      margin-left: 5px;
    }
    .el-input__suffix {
      margin-right: 10px;
    }
  }
}

.style-modules {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 4px;
  margin-right: -4px; /* 防止滚动条占用空间 */

  /* 美化滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}
</style>
