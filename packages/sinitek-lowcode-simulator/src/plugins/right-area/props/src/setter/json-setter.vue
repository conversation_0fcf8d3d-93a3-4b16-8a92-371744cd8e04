<template>
  <div class="wh-full">
    <xn-button
      :show-loading="false"
      :type="isEdit ? 'primary' : 'default'"
      size="mini"
      @click="show"
    >
      {{ isEdit ? '编辑数据' : '绑定数据' }}
    </xn-button>
    <i
      v-if="config && config.documentUrl"
      class="el-icon-question ml-2 color-primary"
      @click="showDoc"
    ></i>
  </div>
</template>

<script>
import { createComponent } from '@utils/createComponent'
export default createComponent({
  name: 'JSONSetter',
  props: ['defaultValue', 'config'],
  inject: ['showMED'],
  computed: {
    isEdit() {
      let content = this.modelValue ?? this?.defaultValue ?? ''
      return !!content
    },
  },
  methods: {
    show() {
      // this.isShow = true
      this.showMED({
        title: this.isEdit ? '编辑数据' : '绑定数据',
        value: JSON.stringify(this.modelValue ?? this?.defaultValue, null, 2),
        language: 'json',
        onConfirm: this.onConfirm,
      })
    },
    onConfirm(val) {
      this._emit(val ? JSON.parse(val) : void 0)
    },
    showDoc() {
      window.open(this.config?.documentUrl, '_blank')
    },
  },
})
</script>
