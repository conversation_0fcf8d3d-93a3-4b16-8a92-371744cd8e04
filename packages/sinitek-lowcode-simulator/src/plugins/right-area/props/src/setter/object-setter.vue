<template>
  <div>
    <template v-if="visible">
      <LCPanel
        ref="panel"
        placement="right"
        :offset="[300, 0]"
        :hide-pin="true"
        :title="`${index === void 0 ? '' : index}`"
        :modal="true"
        @hide="$emit('hide')"
      >
        <div class="w-300px">
          <!-- array嵌套object 就是config.items
          object嵌套array 就是config.setter.props.config.items -->
          <RenderConfigItem
            :list="list"
            :model-value="currentValue"
            @change="onInput"
          />
        </div>
      </LCPanel>
    </template>
    <!-- <template v-if="parentIsArray">
      <div class="i-material-symbols-light:add h-6 w-6" @click="add"></div>
    </template> -->
    <template v-if="!parentIsArray">
      <!-- // 编辑对象 -->
      <xn-button size="mini" w-full :show-loading="false" @click="show"
        >编辑对象</xn-button
      >
    </template>
  </div>
</template>

<script>
// import { ElInput } from 'adaptor/element-ui'
import { createComponent } from '@utils/createComponent'
import LCPanel from '@common/panel.vue'
import { getSetter } from './get-setter'
export default createComponent({
  name: 'ObjectSetter',
  props: ['defaultValue', 'config', 'index'],
  data() {
    return {
      currentValue: this.modelValue || this.defaultValue || {},
      visible: false,
    }
  },
  // arraySetter 会添加一个 parent 属性，指向父组件
  // 用来判断是怎么包裹的，object 嵌套array 还是 array 嵌套object
  // inject: ['parentIsArray'],
  inject: {
    parentIsArray: { default: false },
    getEdit: { default: () => void 0 },
    $doc: { default: null },
  },
  provide() {
    return {
      parentIsArray: false,
      hideObjectPanel: this.hide,
    }
  },
  computed: {
    list() {
      let items
      // 只有array嵌套object时才会去设置auto
      if (this.parentIsArray && this.config.auto) {
        // 根据组件自动去materials找对应组件的配置
        const node = this.$doc.node.getNode(this.getEdit().__id__)
        items = this.$doc.getMaterial(node.componentName)?.props
      }
      // array嵌套object 就是config.items
      // object嵌套object 就是config.setter.props.config.items
      return items || this.config.items || this.config.setter.props.config.items
    },
  },
  components: {
    // ElInput,
    LCPanel,
  },
  methods: {
    getSetter,
    show() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.panel.show()
      })
    },
    hide() {
      this.visible = false
    },
    onInput(name, v) {
      this.currentValue[name] = v
      this._emit(this.currentValue)
    },
  },
  watch: {
    modelValue(v) {
      this.currentValue = v
    },
  },
})
</script>
