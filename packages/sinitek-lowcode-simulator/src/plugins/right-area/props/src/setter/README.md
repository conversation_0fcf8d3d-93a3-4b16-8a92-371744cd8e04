# setter

对setter的一些说明

## 组件开发

// 使用createComponent 创建元素，在不使用vue3组合开发特性的情况下，兼容vue3和vue2。
属性的定义

```js
import {createComponent} from 'sinitek-lowcode-simulator/utils/createComponent'
export default createComponent({
    // 自动inject $doc
    // 使用 _emit(val) input修改值
    // 使用 _change(val) input修改值,并修改选中框
    // 使用 _research() 重绘选中框
    props: {
        //标准的有config,是当前prop元素
        config: Object
        // 动态的
        ...other: 是当前setter的props属性解构
    },
    data() {
        return {
            currentValue: this.modelValue || this.defaultValue,
        }
    },
})
```

## StringSetter

**TextareaSetter**和**StringSetter**一致

使用`element-ui`的`el-input`组件,兼容`element-plus`。

| 属性         | 说明                                     |
| ------------ | ---------------------------------------- |
| placeholder  | 输入框提示                               |
| defaultValue | 默认值                                   |
| removeProp   | 将当前值设置为undefined,使用组件的默认值 |
| suggestions  | 异步函数,(target) => {label,value}[]     |

### suggestions 函数

```js
(target) => {label,value}[]
```

target说明查看materials的target文档
多了一个getModelFields方法，可以获取模型字段

## NumberSetter

使用`element-ui`的`el-input-number`组件,兼容`element-plus`。

| 属性         | 说明                |
| ------------ | ------------------- |
| min          | 指定最小值          |
| max          | 指定最大值          |
| defaultValue | 默认值              |
| step         | 指定步长 number     |
| units        | 指定单位 string     |
| precision    | 设置小数位数 number |

## BoolSetter

使用`element-ui`的`el-switch`组件,兼容`element-plus`。

| 属性         | 说明     |
| ------------ | -------- |
| disabled     | 是否可选 |
| defaultValue | 默认值   |

## ColorSetter

使用`element-ui`的`el-color-picker`组件,兼容`element-plus`。

| 属性         | 说明   |
| ------------ | ------ |
| defaultValue | 默认值 |

## RadioGroupSetter

是枚举时使用，必须有一个选中，其他情况使用`SelectSetter`。
使用`element-ui`的`el-radio-group`和`el-radio-button`组件,兼容`element-plus`。

| 属性         | 说明                                                                            |
| ------------ | ------------------------------------------------------------------------------- |
| defaultValue | 默认值                                                                          |
| options      | 传入的数据源，参数格式: [{img: 'url', value: 'text', label/title: 'text'}, ...] |

## SelectSetter

使用`element-ui`的`el-select`和`el-option`组件,兼容`element-plus`。

| 属性         | 说明                                                                            |
| ------------ | ------------------------------------------------------------------------------- |
| defaultValue | 默认值                                                                          |
| mode         | 选择器模式 可选值: 'single', 'multiple', 'tag'                                  |
| options      | 传入的数据源，参数格式: [{img: 'url', value: 'text', label/title: 'text'}, ...] |

## IconSetter

| 属性         | 说明      |
| ------------ | --------- | ------------------------------------ |
| defaultValue | 默认值    |
| type         | 'iconify' | 'element-ui' \|'svg-icon'\|'iconify' |

查看iconify对应的图标https://icon-sets.iconify.design/

## ArraySetter

### 配置示例

```json
"setter": {
    "componentName": "ArraySetter",
    "props": {
        "itemSetter": {
            "componentName": "ObjectSetter",
            "props": {
                "config": {
                    "items": [{
                            "name": "title",
                            "title": "标题",
                            "setter": "StringSetter"
                        },
                        {
                            "name": "callback",
                            "title": {"label":"callback", "tip":"回调函数"},
                            "setter": {
                                "componentName": "FunctionSetter"
                            }
                        }
                    ]
                }
            },
            "initialValue": {
              "title": "I am title",
              "callback": null
            }
        }
    }
}
```

| 属性         | 说明       |
| ------------ | ---------- |
| defaultValue | 默认值     |
| icons        | [图标名称] |

### ArraySetter 配置

| 属性名     | 类型         | 说明                                             |
| ---------- | ------------ | ------------------------------------------------ |
| itemSetter | ObjectSetter | ArraySetter 的子节点内容必须用 ObjectSetter 包裹 |

### itemSetter 配置

| 属性名        | 类型             | 说明             |
| ------------- | ---------------- | ---------------- |
| componentName | String           |                  |
| props         |                  |                  |
| initialValue  | Object\|(length) => Object | 新增一项的初始值 |

### ObjectSetter 配置

| 属性名                | 类型                | 说明                                   |
| --------------------- | ------------------- | -------------------------------------- |
| textField      | String              | 显示字段                               |
| config                | Object              | 配置项                                 |
| config.auto           | Boolean             | 自动获取物料数据                       |
| config.items          | Array               | 子属性列表数据                         |
| config.items[].name   | String              | 子属性名称                             |
| config.items[].title  | String\|{label,tip} | 标题                                   |
| config.items[].setter | Object \| String    | 子属性setter配置 \| 子属性setter组件名 |
| textField | String | 使用那个字段来展示 |

如果子组件有多种或者不想设置items，可以使用auto:true自动从物料库获取

## FunctionSetter

| 属性名   | 类型   | 说明 |
| -------- | ------ | ---- |
| template | String | 模板 |

## ModelSetter

模型设置

| 属性名       | 类型    | 说明                                                       |
| ------------ | ------- | ---------------------------------------------------------- |
| selectFields | Boolean | 选择模型是否选择字段, 会给children添加字段对应的输入组件。 |
| refMain      | Boolean | 有主模型时，限制当前模型列表                               |

## SlotSetter

插槽设置

## BindStateSetter

字段绑定

| 属性名        | 类型    | 说明                                |
| ------------- | ------- | ----------------------------------- |
| useExpression | Boolean | 选中后使用表达式绑定，false是字符串 |

## RichTextSetter

富文本

## DsSetter

数据源

| 属性名        | 类型    | 说明                                |
| ------------- | ------- | ----------------------------------- |
| filter | function | 过滤方法 |