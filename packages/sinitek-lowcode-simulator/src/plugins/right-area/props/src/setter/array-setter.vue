<template>
  <div>
    <div class="array-container">
      <ArrayItem
        v-for="(c, i) of currentValue"
        :key="`array-item_${i}`"
        :text="c[textField]"
        :index="i"
        :class="{ 'bg-gray-50': editIndex === i }"
        @edit="onEdit(c, i)"
        @remove="onRemove(c, i)"
        @move="onMove"
      />
    </div>
    <div v-if="itemSetter.initialValue" mt-2 flex items-center>
      <span
        f-c-c
        cursor-pointer
        text-primary
        class="add-item-btn rounded px-2 transition-colors duration-200 hover:bg-blue-50"
        @click="add"
      >
        添加一项
        <span inline-block class="i-material-symbols-light:add h-6 w-6"></span>
      </span>
    </div>
    <ObjectSetter
      v-bind="itemSetter.props"
      ref="obj"
      :key="editIndex"
      :model-value="osValue"
      :index="editIndex"
      class="w-full"
      @input="onInput"
      @hide="editIndex = -1"
    />
  </div>
</template>

<script>
import { ElInput } from 'adaptor/element-ui'
import { isFunction, deepClone } from '@utils'
import { createComponent } from '@utils/createComponent'
import ArrayItem from './array-item.vue'

export default createComponent({
  name: 'ArraySetter',
  inject: {
    message: { default: null },
    isRenderChildren: { default: false },
    getParentConfig: { default: null },
  },
  props: ['defaultValue', 'placeholder', 'itemSetter', 'initialValue'],
  data() {
    return {
      currentValue: this.modelValue || this.defaultValue || [],
      osValue: {},
      editIndex: -1,
      // 用于存储预览状态，不修改实际数据
      previewList: null,
      sourceIndex: -1,
      targetIndex: -1,
    }
  },
  provide() {
    return {
      parentIsArray: true,
      getEdit: () => {
        return this.osValue
      },
      getEditIndex: () => {
        return this.editIndex
      },
    }
  },
  components: {
    ElInput,
    ArrayItem,
  },
  computed: {
    // 取第一列来展示
    textField() {
      return (
        this.itemSetter.props?.textField ??
        this.itemSetter.props.config.items[0]?.name
      )
    },
  },
  methods: {
    __emit() {
      // TODO: 这里的的对象需要深拷贝，否则会导致数据不稳定。目前不知道是哪里的数据操作导致了这个数据的引用丢失
      this.$emit('input', deepClone(this.currentValue))
    },
    add() {
      if (this.itemSetter?.initialValue) {
        if (isFunction(this.itemSetter.initialValue)) {
          this.currentValue.push(
            this.itemSetter.initialValue(this.currentValue.length + 1)
          )
        } else {
          this.currentValue.push(deepClone(this.itemSetter.initialValue))
        }
        this.__emit()
      } else {
        this?.message?.error('物料ObjectSetter缺少initialValue!')
      }
    },
    onEdit(v, i) {
      this.osValue = v
      this.editIndex = i
      // 切换后，等待重新渲染弹框后在打开弹框
      this.$nextTick(() => {
        this.$refs.obj.show()
      })
    },
    onRemove(_, i) {
      this.currentValue.splice(i, 1)
      this.__emit()
      // 删除setterIndex,防止新增的复用当前的setterIndex
      if (this.getParentConfig) {
        const schema = this.$doc.getCurrent().schema
        if (schema.setterIndex) {
          const path = this.getSetterPath()
          if (!path) return
          // 删除对应的数据
          const keys = Object.keys(schema.setterIndex)
          keys.forEach((key) => {
            if (key.startsWith(path)) {
              delete schema.setterIndex[key]
            }
          })
        }
      }

      // 打开的是当前的弹框，需要关闭
      if (i === this.editIndex) {
        this.editIndex = -1
      }
    },
    getSetterPath() {
      const arr = this.getParentConfig?.()
      let path = ''
      if (arr?.length) {
        arr.forEach((e) => {
          if (e.name) {
            if (!path) {
              path += e.name
            } else {
              path += `.${e.name}`
            }
          } else if (!isNaN(e)) {
            path += `[${e}]`
          }
        })
      }
      if (path) {
        return path
      }
      return ''
    },
    // 获取真实索引
    // 因为children里面可能有slot组件，会被filter过滤掉，所以需要通过id来获取真实索引
    getRealIndex(index) {
      const id = this.modelValue?.[index]?.__id__
      if (!id) return -1
      const children = this.$doc.getCurrent().schema?.children ?? []
      return children.findIndex((e) => e.id === id)
    },
    onMove({ source, target }) {
      // 只有在此时才真正移动数据
      const sourceIdx = source.index
      const targetIdx = target.index

      if (sourceIdx !== targetIdx) {
        // 对实际数据进行操作
        const temp = this.currentValue[sourceIdx]
        this.currentValue.splice(sourceIdx, 1)
        this.currentValue.splice(targetIdx, 0, temp)
        // 如果是渲染子组件，更新schema
        if (this.isRenderChildren) {
          const schema = this.$doc.getCurrent().schema
          const sourceIndex = this.getRealIndex(sourceIdx)
          const temp = schema.children[sourceIndex]
          const targetIndex = this.getRealIndex(targetIdx)
          if (sourceIndex > -1 && targetIndex > -1) {
            schema.children.splice(sourceIndex, 1)
            schema.children.splice(targetIndex, 0, temp)
          }
        }
        this.__emit()
      }

      // 清除预览状态
      this.previewList = null
      this.sourceIndex = -1
      this.targetIndex = -1
    },
    onInput(v) {
      if (this.editIndex > -1) {
        Object.assign(this.currentValue[this.editIndex], v)
        this.__emit()
      }
    },
  },
  watch: {
    modelValue(v = []) {
      if (JSON.stringify(v) === JSON.stringify(this.currentValue)) return
      this.currentValue = v
      // 通过物料的extraProps.setValue来设置值后，重新赋值当前编辑的值，触发页面回显
      v.forEach((e, i) => {
        if (this.editIndex !== -1 && this.editIndex === i) {
          this.osValue = e
        }
      })
    },
  },
})
</script>

<style lang="scss" scoped>
.array-container {
  border-radius: 4px;
  border: 1px solid #ebeef5;
  overflow: hidden;
}

.add-item-btn {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}
</style>
