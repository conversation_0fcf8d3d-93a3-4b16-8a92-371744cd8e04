<template>
  <xn-richbox
    ref="editor"
    v-model="currentValue"
    :class="{ 'fixed inset-0 bg-white z-999': fullScreen }"
  >
  </xn-richbox>
</template>

<script>
import { createComponent } from '@utils/createComponent'
export default createComponent({
  name: 'RichTextSetter',
  props: ['defaultValue'],
  data() {
    return {
      currentValue: this.modelValue || this.defaultValue,
      fullScreen: false,
    }
  },
  mounted() {
    const fullEl = document.createElement('span')
    fullEl.classList.add('ql-formats')
    fullEl.innerHTML =
      '<button type="button" class="ql-full-screen ql-button"><div class="i-custom:fullscreen  w-4 h-4"></div></button>'
    fullEl.onclick = () => {
      this.fullScreen = !this.fullScreen
    }
    this.$refs.editor.$el.querySelector('.ql-toolbar').appendChild(fullEl)
  },
  watch: {
    currentValue(v) {
      this._emit(v)
    },
    modelValue(v) {
      this.currentValue = v
    },
  },
})
</script>
