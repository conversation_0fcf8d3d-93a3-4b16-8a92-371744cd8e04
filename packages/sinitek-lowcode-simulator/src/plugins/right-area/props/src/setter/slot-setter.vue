<template>
  <ElSwitch :value="currentValue" @input="onInput" />
</template>

<script>
import { ElSwitch } from 'adaptor/element-ui'
import { createComponent } from '@utils/createComponent'
export default createComponent({
  name: 'SlotSetter',
  props: ['options', 'defaultValue', 'config'],
  data() {
    return {
      currentValue: false,
    }
  },
  components: {
    ElSwitch,
  },
  mounted() {
    this.setCurrentValue()
  },
  inject: {
    // ArraySetter 编辑时，获取编辑的数据
    getEdit: { default: null },
    $doc: { default: null },
  },
  methods: {
    getChildren() {
      const schema = this.$doc.getCurrent()?.schema
      let children = schema.children || []
      const isArraySetter = !!this.getEdit
      if (isArraySetter) {
        const edit = this.getEdit()
        const child = children.find((e) => e.id === edit.__id__)
        if (child) {
          children = child.children || []
        }
      }
      return children
    },
    setCurrentValue() {
      const children = this.getChildren()
      this.currentValue = children.some(
        (e) => e.componentName === 'Slot' && e.props.name === this.config.name
      )
    },
    onInput(v) {
      let children = this.getChildren()
      if (v) {
        // children添加
        children.push({
          componentName: 'Slot',
          props: {
            name: this.config.name,
          },
          children: [],
          id: Math.random().toString(36).slice(2),
        })
      } else {
        // children移除
        const index = children.findIndex(
          (e) =>
            e.slot === this.config.name ||
            (e.componentName === 'Slot' && e.props.name === this.config.name)
        )
        if (index > -1) {
          children.splice(index, 1)
        }
      }
      const node = this.$doc.getCurrent().schema
      // 修改key来触发更新
      node.__key__ = Math.random().toString(36).slice(2)
      this.currentValue = v
      this._research()
    },
  },
})
</script>
