<template>
  <ElInput v-model="currentValue" type="textarea" :placeholder="placeholder" />
</template>

<script>
import { ElInput } from 'adaptor/element-ui'
import { createComponent } from '@utils/createComponent'
export default createComponent({
  name: 'TextareaSetter',
  props: ['defaultValue', 'placeholder'],
  data() {
    return {
      currentValue: this.modelValue || this.defaultValue,
    }
  },
  components: {
    ElInput,
  },
  watch: {
    currentValue(v) {
      this._emit(v)
    },
    modelValue(v) {
      this.currentValue = v
    },
  },
})
</script>
