<template>
  <div>
    <xn-select
      v-model="currentValue.modelCode"
      size="mini"
      placeholder="请选择"
      clearable
      filterable
      class="my-2 w-full"
      :options="options"
      @change="onModelChange"
    >
    </xn-select>
    <template v-if="currentValue.modelCode">
      <xn-dialog
        :show.sync="showSelectProp"
        :buttons="buttons"
        title="选择字段"
        @save="onSelectPropConfirm"
        @cancel="showSelectProp = false"
      >
        <el-table
          ref="table"
          :data="fields"
          stripe
          style="width: 100%"
          @selection-change="onSelectionChange"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column prop="label" label="label" width="180">
          </el-table-column>
          <el-table-column prop="value" label="组件"> </el-table-column>
        </el-table>
      </xn-dialog>
      <xn-dialog
        :show.sync="isShow"
        title="查询条件设置"
        :buttons="buttons2"
        @save="onConfirm"
        @cancel="isShow = false"
      >
        <div class="h-sm w-2xl flex">
          <LCCondition ref="condition" v-model="condition" :fields="fields" />
        </div>
      </xn-dialog>
      <xn-button
        size="mini"
        :show-loading="false"
        class="mt-2"
        @click="openCondition"
        >编辑查询条件</xn-button
      >
    </template>
  </div>
</template>

<script>
import { createComponent } from '@utils/createComponent'
import LCCondition from '@common/condition/condition'
import { deepClone } from '@utils'
export default createComponent({
  components: {
    LCCondition,
  },
  name: 'ModelSetter',
  // defaultValue
  // {modelCode: '',condition: {and: []}}
  props: ['defaultValue', 'modelValue', 'config'],
  inject: ['fetcher', '$doc'],
  data() {
    return {
      isShow: false,
      options: [],
      fields: [],
      currentValue: this.modelValue ||
        this.defaultValue || { modelCode: '', condition: { and: [] } },
      condition: this?.modelValue?.condition ??
        this?.defaultValue?.condition ?? { and: [] },
      showSelectProp: false,
      multipleSelection: [],
      buttons2: [
        {
          label: '保存',
          type: 'primary',
          action: 'save',
          event: 'save',
          showLoading: false,
        },
        { label: '取消', type: 'info', action: 'cancel', event: 'cancel' },
      ],
    }
  },
  computed: {
    buttons() {
      return [
        {
          label: '保存',
          type: this.multipleSelection.length ? 'primary' : 'info',
          action: 'save',
          event: 'save',
          showLoading: false,
        },
        { label: '取消', type: 'info', action: 'cancel', event: 'cancel' },
      ]
    },
  },
  created() {
    this.getData()
  },
  methods: {
    async getData() {
      let modelCode = this.$doc.getSchema()?.mainModel?.modelCode
      if (this.config?.setter?.props?.refMain && modelCode) {
        this.options = await this.fetcher.getRefModelList(modelCode)
        return
      }
      this.options = await this.fetcher.getModelList()
    },
    async getModelFields(v) {
      if (!v) return Promise.resolve([])
      return await this.fetcher.getModelFields(v).then((res) => {
        this.fields = res
        return Promise.resolve(res)
      })
    },
    emit() {
      this._emit(this.currentValue)
    },
    onConfirm() {
      this.onConfirmBefore().then(() => {
        this.isShow = false
        this.currentValue.condition = deepClone(this.condition)
        this.emit()
      })
    },
    onConfirmBefore() {
      return new Promise((resolve, reject) => {
        this.$refs.condition.validate((valid) => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    onModelChange(v) {
      this.currentValue.condition = { and: [] }
      this.condition = this.currentValue.condition

      this.getModelFields(v).then((res) => {
        if (this.config?.setter?.props?.selectFields) {
          this.showSelectProp = true
          this.multipleSelection = res
          this.$refs?.table?.toggleAllSelection?.()
        }
      })
      this.emit()
    },
    onSelectPropConfirm() {
      if (!this.multipleSelection.length) return
      const schema = this.$doc.getCurrent().schema
      const children = []
      this.multipleSelection.forEach((e) => {
        if (e.data.componentType === 'RELA_FORM') {
          children.push({
            componentName: 'LARelaForm',
            props: {
              field: 'RELA_FORM',
              itemProps: {
                label: e.label,
                prop: e.value,
              },
              modelAndCondition: {
                modelCode: e.data.relaModelCode,
                condition: {
                  and: [],
                },
              },
            },
          })
          return
        }
        // 添加组件
        children.push({
          componentName: 'LAFormItem',
          props: {
            field: e.data.componentType,
            itemProps: {
              label: e.label,
              prop: e.value,
            },
          },
        })
      })
      schema.children = children
      this.showSelectProp = false
    },
    onSelectionChange(val) {
      this.multipleSelection = val
    },
    openCondition() {
      this.isShow = true
      this.condition = deepClone(this.currentValue.condition)
    },
  },
  watch: {
    modelValue: {
      immediate: true,
      handler(v) {
        if (!v) return
        this.currentValue = v
        if (this.currentValue?.modelCode) {
          this.getModelFields(this.currentValue.modelCode)
        }
        this.condition = v.condition
      },
    },
  },
})
</script>
