<template>
  <div class="wh-full">
    <!-- :class="{ 'bg-primary text-white b-bgGrayLight': showBindState }" -->
    <xn-button
      v-if="!showBindState"
      :type="showBindState ? 'primary' : 'default'"
      size="mini"
      :show-loading="false"
      @click="show"
    >
      绑定函数
    </xn-button>
    <EventItem v-else :event="event" @edit="show" @remove="onRemoveEvent" />
    <BindEvent v-model="isShow" :bind-event="event" @confirm="onConfirm" />
  </div>
</template>

<script>
import { createComponent } from '@utils/createComponent'
import BindEvent from '../../../events/src/bind-event.vue'
import { isJSAction, isJSFunction } from 'sinitek-lowcode-shared'
import EventItem from '@common/event-item.vue'
export default createComponent({
  name: 'FunctionSetter',
  props: ['defaultValue', 'template', 'config'],
  computed: {
    isEdit() {
      let content = this.modelValue ?? this?.defaultValue ?? ''
      return !!content
    },
    showBindState() {
      const value = this.currentValue
      return isJSFunction(value) || isJSAction(value)
    },
  },
  data() {
    return {
      currentValue: this.modelValue || this.defaultValue || {},
      isShow: false,
      event: {
        name: '',
        methodName: '',
      },
    }
  },
  components: {
    BindEvent,
    EventItem,
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.event.name = this.config.name
      if (this.currentValue) {
        let code = /this.methods\.(.*?)(?=\.)/g.exec(
          this.currentValue.value
        )?.[1]
        if (isJSAction(this.currentValue)) {
          code = this.currentValue.value
        }
        this.event.methodName = code
        this.event.params = this.currentValue.params
        this.event.type = this.currentValue.type
        this.event.template = this.template
      }
    },
    show() {
      this.isShow = true
      this.init()
    },
    onConfirm(val) {
      this.currentValue = val
      this.init()
      this._emit(val)
    },
    onRemoveEvent() {
      this.currentValue = null
      this.event.name = ''
      this.event.methodName = ''
      this.event.params = '{}'
      this.event.template = null
      this.event.type = null
      this._emit(null)
    },
  },
  watch: {
    modelValue(v) {
      this.currentValue = v || {}
    },
  },
})
</script>
