<template>
  <div>
    <el-select
      v-model="currentValue"
      size="mini"
      placeholder="请选择或输入新字段"
      :float="currentValue"
      clearable
      filterable
      default-first-option
      w-full
      @visible-change="onVisibleChange"
    >
      <el-option
        v-for="(item, i) in stateList"
        :key="i"
        :label="item.label || item"
        :value="item.value || item"
        :title="item.value || item"
      >
        <div class="flex items-center justify-between">
          <span>{{ item.value || item }}</span>
          <span
            v-if="stateComment[item.value || item]"
            class="text-xs text-gray-4"
          >
            {{ stateComment[item.value || item] }}
          </span>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { createComponent } from '@utils/createComponent'
import { ElSelect, ElOption } from 'adaptor/element-ui'
import isPlainObject from 'lodash/isPlainObject'
import {
  DatasourceType,
  isJSExpression,
  JSExpression,
  getObjectType,
  getPageCtx,
} from 'sinitek-lowcode-shared'

export default createComponent({
  name: 'BindStateSetter',
  props: [
    'modelValue',
    'defaultValue',
    'useExpression',
    'useDatasource',
    'defaultLabel',
    'hideTip',
    'emptyText',
    'filter',
  ],
  inject: ['$doc'],
  components: {
    ElSelect,
    ElOption,
  },
  data() {
    return {
      currentValue: this.modelValue,
      stateList: [],
      stateComment: {},
      pathMap: new Map(),
    }
  },
  computed: {
    filterFn() {
      const fn = this.filter || ((_, list) => list)
      return fn.bind(null, this.$doc.materials.getTarget())
    },
  },
  created() {
    this.onVisibleChange(true)
  },
  methods: {
    onVisibleChange(visible) {
      this.pathMap.clear()
      if (visible) {
        const schema = this.$doc.getSchema(true)
        if (this.useDatasource) {
          const ds = schema?.datasource ?? []
          if (schema.mainModel) {
            const main =
              schema?.datasource?.filter((e) => e.id === 'main') ?? []
            if (!main.length) {
              ds.push({ ...schema.mainModel, type: 'model', id: 'main' })
            }
          }
          this.stateList = this.filterFn(
            ds.filter((e) => e.type === DatasourceType.MODEL).map((e) => e.id)
          )
          this.stateComment = {
            main: '主模型',
          }
          return
        }
        const state = schema?.state ?? {}
        this.stateComment = schema?.stateComment ?? {}
        const currentSchema = this.$doc.getCurrent(true).schema
        const scope =
          getPageCtx()?.refs[currentSchema.ref]?.$parent?.scope?.scope
        const stateList = []
        const depth = 5

        if (scope) {
          extractObjectPaths(scope, depth, 0, 'scope', true).forEach((e) => {
            this.pathMap.set(e.value, { ...e, isState: false })
            stateList.push(e)
          })
        }

        const extractedPaths = extractObjectPaths(state, depth, 0, '', true)
        this.filterFn(extractedPaths).forEach((e) => {
          this.pathMap.set(e.value, { ...e, isState: true })
          stateList.push(e)
        })
        this.stateList = stateList
      }
    },
  },
  watch: {
    currentValue(v) {
      // 表达式用在高级表单的model上
      if (this.useExpression) {
        if (!v) {
          this._emit(null)
        } else {
          this._emit({
            type: JSExpression,
            value: `this.state.${v}`,
            // 标识是状态
            isState: true,
          })
        }
      } else {
        const path = this.pathMap.get(v)
        if (path) {
          this._emit(path)
        } else {
          this._emit(v || '')
        }
      }
    },
    modelValue: {
      handler(v) {
        if (isJSExpression(v)) {
          this.currentValue = v.value.replace('this.state.', '').trim()
        } else {
          if (typeof v === 'string') {
            // 兼容是字符串的情况
            this.currentValue = v
          } else {
            // 新的是个对象
            this.currentValue = v?.value ?? ''
          }
        }
      },
    },
  },
})

// 提取遍历对象层级的方法
/**
 * 递归提取对象所有层级的路径
 * @param {Object} obj - 要遍历的对象
 * @param {Number} maxDepth - 最大递归深度，-1表示无限制
 * @param {Number} currentDepth - 当前递归深度，内部使用
 * @param {String} parentPath - 父路径，内部使用
 * @param {Boolean} fullObject - 是否返回完整对象，false则只返回路径字符串
 * @returns {Array} - 路径数组
 */
function extractObjectPaths(
  obj,
  maxDepth = 1,
  currentDepth = 0,
  parentPath = '',
  fullObject = true
) {
  if (
    !obj ||
    typeof obj !== 'object' ||
    Array.isArray(obj) ||
    (currentDepth > maxDepth && maxDepth !== -1)
  ) {
    return []
  }

  let result = []

  // 遍历当前对象的所有键
  Object.keys(obj).forEach((key) => {
    const currentPath = parentPath ? `${parentPath}.${key}` : key

    // 根据参数决定返回对象还是字符串
    if (fullObject) {
      // 添加当前属性
      result.push({
        type: getObjectType(obj[key]),
        value: currentPath,
        label: currentPath,
      })
    } else {
      result.push(currentPath)
    }

    // 如果是对象且未达到最大深度或深度无限制，则递归处理
    if (
      isPlainObject(obj[key]) &&
      (currentDepth < maxDepth || maxDepth === -1)
    ) {
      // 递归处理子对象
      const childPaths = extractObjectPaths(
        obj[key],
        maxDepth,
        currentDepth + 1,
        currentPath,
        fullObject
      )
      result = result.concat(childPaths)
    }
  })

  return result
}
</script>
