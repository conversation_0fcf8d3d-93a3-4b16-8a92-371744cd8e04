<template>
  <ElColorPicker v-model="currentValue" />
</template>

<script>
import { ElColorPicker } from 'adaptor/element-ui'
import { createComponent } from '@utils/createComponent'
export default createComponent({
  name: 'ColorSetter',
  props: ['options', 'defaultValue', 'disabled'],
  data() {
    return {
      currentValue: this.modelValue || this.defaultValue,
    }
  },
  components: {
    ElColorPicker,
  },
  watch: {
    currentValue(v) {
      this._emit(v)
    },
    modelValue(v) {
      this.currentValue = v
    },
  },
})
</script>
