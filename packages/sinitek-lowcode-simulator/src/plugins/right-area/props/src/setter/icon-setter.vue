<template>
  <ElAutocomplete
    v-model="currentValue"
    size="mini"
    :fetch-suggestions="suggestions"
    clearable
    @select="onSelect"
    @clear="onClear"
  >
    <template #prepend>
      <Icon v-if="isIconify" :icon="currentValue" />
      <SvgIcon v-else-if="isSvgIcon" :icon-class="currentValue" />
      <i v-else :class="currentValue" />
    </template>
    <template #default="{ item }">
      <div class="w-200px flex items-center">
        <Icon
          v-if="isIconify"
          :icon="item"
          class="flex-shrink-0"
          :width="12"
          :height="12"
        />
        <SvgIcon v-else-if="isSvgIcon" :icon-class="item" />
        <i v-else :class="item" />
        <span class="ml-1">{{ item }}</span>
      </div>
    </template>
  </ElAutocomplete>
</template>

<script>
import { ElAutocomplete } from 'adaptor/element-ui'
import { createComponent } from '@utils/createComponent'
import { loadIcons, Icon } from '@iconify/vue2'
import elementUIIcons from './element-icon.json'
import SinitekUI from 'sinitek-ui'
export default createComponent({
  components: {
    ElAutocomplete,
    Icon,
    SvgIcon: SinitekUI.SvgIcon,
  },
  props: ['config'],
  inject: {
    getIcons: { default: () => () => [] },
  },
  data() {
    return {
      currentValue: this.modelValue || this.defaultValue,
    }
  },
  computed: {
    iconType() {
      return this.config?.setter?.props?.type ?? 'element-ui'
    },
    isIconify() {
      return this.iconType === 'iconify'
    },
    isElementUI() {
      return this.iconType === 'element-ui'
    },
    isSvgIcon() {
      return this.iconType === 'svg-icon'
    },
  },
  mounted() {
    if (this.isSvgIcon) {
      this.icons = Object.freeze(
        [].concat(this.getIcons(), SinitekUI.getIcons()).map((e) => e.name)
      )
    }
  },
  methods: {
    suggestions(k, fn) {
      if (this.isIconify) {
        // TODO: 从 iconify 获取图标
        window
          .fetch(`https://api.iconify.design/search?query=${k}&limit=10`)
          .then((res) => {
            return res.json()
          })
          .then((res) => {
            loadIcons(res.icons)
            fn(res.icons)
          })
      } else if (this.isElementUI) {
        fn(
          elementUIIcons
            .map((e) => 'el-icon-' + e)
            .filter((i) => i.indexOf(k) > -1)
        )
      } else if (this.isSvgIcon) {
        fn(this.icons.filter((i) => i.indexOf(k) > -1))
      }
    },
    onSelect(v) {
      this.currentValue = v
      this._emit(v)
    },
    onClear() {
      this.currentValue = ''
      this._emit('')
    },
  },
  watch: {
    modelValue(v) {
      this.currentValue = v
    },
  },
})
</script>
