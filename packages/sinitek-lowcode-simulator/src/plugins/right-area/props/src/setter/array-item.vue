<template>
  <div
    ref="el"
    class="relative flex items-center b-b-1"
    :class="{ 'is-dragging': isDragging, 'drop-preview': isDropPreview }"
  >
    <div
      ref="move"
      class="i-material-symbols-light:more-vert h-6 w-6 important-cursor-move"
    ></div>
    <div class="mr-a break-all">{{ text }}</div>
    <div
      class="i-custom:edit h-5 w-5 flex-shrink-0 transition-colors hover:text-primary"
      @click="onEdit"
    ></div>
    <div
      class="i-custom:remove h-5 w-5 flex-shrink-0 transition-colors hover:text-primary"
      @click="onRemove"
    ></div>
    <DndIndicator v-if="dragPosition" :edge="dragPosition" />
  </div>
</template>

<script>
import {
  draggable,
  dropTargetForElements,
  monitorForElements,
} from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine'
import DndIndicator from '@common/dnd/indicator.vue'

export default {
  name: 'ArrayItem',
  components: {
    DndIndicator,
  },
  props: ['text', 'index'],
  data() {
    return {
      dragPosition: '',
      isDragging: false,
      isDropPreview: false, // 标记是否是拖拽预览目标
      dragTimeout: null,
    }
  },
  mounted() {
    const el = this.$refs.el
    const move = this.$refs.move

    // 监听拖拽状态
    this.monitorCleanup = monitorForElements({
      onDragStart: ({ source }) => {
        if (source.element === el) {
          this.isDragging = true
          el.classList.add('dragging')

          // 添加拖拽预览效果
          document.body.classList.add('dragging-active')
        }
      },
      onDrop: () => {
        this.isDragging = false
        this.dragPosition = ''
        this.isDropPreview = false
        el.classList.remove('dragging')
        document.body.classList.remove('dragging-active')

        // 清除所有可能的延迟操作
        if (this.dragTimeout) {
          clearTimeout(this.dragTimeout)
          this.dragTimeout = null
        }
      },
    })

    this.cleanup = combine(
      draggable({
        dragHandle: move,
        element: el,
        getInitialData: () => ({ index: this.index, text: this.text }),
      }),
      dropTargetForElements({
        element: el,
        getData: () => ({ index: this.index }),
        getIsSticky: () => true,
        onDragEnter: (arg) => {
          const sourceIndex = arg.source.data.index
          const targetIndex = arg.self.data.index

          // 如果是相同索引，不显示指示器
          if (sourceIndex === targetIndex) {
            this.dragPosition = ''
            return
          }

          // 根据拖动方向确定放置位置
          if (sourceIndex < targetIndex) {
            this.dragPosition = 'bottom'
          } else if (sourceIndex > targetIndex) {
            this.dragPosition = 'top'
          }
          // 标记这个元素为预览目标
          this.isDropPreview = true
        },
        onDragLeave: () => {
          this.dragPosition = ''
          this.isDropPreview = false
        },
        onDrop: ({ location, source }) => {
          const target = location.current.dropTargets[0]
          if (!target) {
            return
          }
          const sourceData = source.data
          const targetData = target.data
          if (sourceData.index === targetData.index) {
            return
          }

          // 发送实际移动事件，真正改变数据
          this.$emit('move', { source: sourceData, target: targetData })
          this.dragPosition = ''
          this.isDropPreview = false
        },
      })
    )
  },
  beforeDestroy() {
    this.cleanup()
    this.monitorCleanup && this.monitorCleanup()
  },
  methods: {
    onEdit() {
      this.$emit('edit')
    },
    onRemove() {
      this.$emit('remove')
    },
  },
}
</script>

<style lang="scss" scoped>
.dragging {
  opacity: 0.5;
  background-color: #f5f7fa;
}

.is-dragging {
  opacity: 0.6;
  background-color: #f5f7fa;
}

.drop-preview {
  background-color: rgba(12, 102, 228, 0.05);
  transition: background-color 0.2s;
}
</style>
