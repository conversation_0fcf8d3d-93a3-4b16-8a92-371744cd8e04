<template>
  <div class="flex flex-wrap gap-2">
    <div
      v-for="option in options"
      :key="option.value"
      :value="option.value"
      class="cursor-pointer border rounded-md px-2 py-1"
      :class="{
        'bg-gray-100': currentValue === option.value,
        [option.value]: true,
      }"
      @click="handleClick(option)"
    >
      {{ option.label || option.title }}
    </div>
  </div>
</template>

<script>
import { createComponent } from '@utils/createComponent'
export default createComponent({
  name: 'SelectStyleSetter',
  props: ['options', 'defaultValue', 'mode'],
  data() {
    return {
      currentValue: this.modelValue || this.defaultValue,
    }
  },
  watch: {
    currentValue(v) {
      this._emit(v)
    },
    modelValue(v) {
      this.currentValue = v
    },
  },
  methods: {
    handleClick(option) {
      this._emit(option.value)
    },
  },
})
</script>
