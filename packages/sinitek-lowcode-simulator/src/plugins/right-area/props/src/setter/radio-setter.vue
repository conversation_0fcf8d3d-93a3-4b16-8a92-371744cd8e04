<template>
  <ElRadioGroup v-model="currentValue" class="zd-cgs w-full" size="mini">
    <ElRadioButton
      v-for="item of options"
      :key="item.value"
      :label="item.value"
      :float="item.value"
    >
      {{ item.label || item.title }}
    </ElRadioButton>
  </ElRadioGroup>
</template>

<script>
import { ElRadioGroup, ElRadioButton } from 'adaptor/element-ui'
import { createComponent } from '@utils/createComponent'
export default createComponent({
  name: 'RadioSetter',
  components: {
    ElRadioGroup,
    ElRadioButton,
  },
  props: ['options', 'defaultValue'],
  data() {
    return {
      currentValue: this.modelValue || this.defaultValue,
    }
  },
  watch: {
    currentValue(v) {
      this._emit(v)
    },
    modelValue(v) {
      this.currentValue = v
    },
  },
})
</script>
<style lang="scss">
.zd-cgs {
  display: flex !important;
  label,
  span {
    padding: 0 !important;
    display: flex !important;
    @apply items-center justify-center h-7 w-full;
  }
}
</style>
