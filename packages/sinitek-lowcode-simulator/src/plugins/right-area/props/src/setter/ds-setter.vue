<template>
  <el-select
    v-model="currentValue"
    size="mini"
    placeholder="请选择"
    clearable
    filterable
    @visible-change="onVisibleChange"
  >
    <el-option
      v-for="option in options"
      :key="option.value"
      :label="option.label || option.title"
      :value="option.value"
      class="ds-option"
    >
      <div>{{ option.label }}</div>
      <div v-if="option.desc" class="mt-1 text-gray-400 fs-12">
        {{ option.desc }}
      </div>
    </el-option>
  </el-select>
</template>

<script>
import { ElSelect, ElOption } from 'adaptor/element-ui'
import { createComponent } from '@utils/createComponent'
export default createComponent({
  name: 'DsSetter',
  props: ['defaultValue', 'mode'],
  data() {
    return {
      currentValue: this.modelValue || this.defaultValue,
      options: [],
    }
  },
  components: {
    ElSelect,
    ElOption,
  },
  created() {
    this.onVisibleChange(true)
  },
  methods: {
    onVisibleChange(visible) {
      if (visible) {
        const schema = this.$doc.getSchema(true)
        const ds = schema?.datasource ?? []
        const filterFn = this.filter ? this.filter : () => true
        this.options = ds.filter(filterFn).map((e) => ({
          value: e.id,
          label: e.id,
          desc: e.desc,
        }))
      }
    },
  },
  watch: {
    currentValue(v) {
      this._emit(v)
    },
    modelValue(v) {
      this.currentValue = v
    },
  },
})
</script>
<style lang="scss">
.ds-option.el-select-dropdown__item {
  height: auto;
  line-height: 1.2;
  min-height: 32px;
  padding: 8px 16px;
}
</style>
