<template>
  <el-select
    v-model="currentValue"
    size="mini"
    :collapse-tags="mode === 'tag'"
    :multiple="['tag', 'multiple'].includes(mode)"
    placeholder="请选择"
    clearable
  >
    <el-option
      v-for="option in options"
      :key="option.value"
      :label="option.label || option.title"
      :value="option.value"
    >
    </el-option>
  </el-select>
</template>

<script>
import { ElSelect, ElOption } from 'adaptor/element-ui'
import { createComponent } from '@utils/createComponent'
export default createComponent({
  name: 'SelectSetter',
  props: ['options', 'defaultValue', 'mode'],
  data() {
    return {
      currentValue: this.modelValue || this.defaultValue,
    }
  },
  components: {
    ElSelect,
    ElOption,
  },
  watch: {
    currentValue(v) {
      this._emit(v)
    },
    modelValue(v) {
      this.currentValue = v
    },
  },
})
</script>
