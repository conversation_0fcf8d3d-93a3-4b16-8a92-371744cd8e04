<template>
  <div class="flex">
    <ElInputNumber
      v-model="currentValue"
      size="mini"
      :min="min"
      :max="max"
      :step="step"
      :precision="precision"
      controls-position="right"
    />
    <div v-if="units" class="units">{{ units }}</div>
  </div>
</template>

<script>
import { ElInputNumber } from 'adaptor/element-ui'
import { createComponent } from '@utils/createComponent'
export default createComponent({
  name: 'NumberSetter',
  props: ['defaultValue', 'min', 'max', 'step', 'precision', 'units'],
  data() {
    return {
      currentValue: this.modelValue || this.defaultValue,
    }
  },
  components: {
    ElInputNumber,
  },
  watch: {
    currentValue(v) {
      this._emit(v)
    },
    modelValue(v) {
      this.currentValue = v
    },
  },
})
</script>
