<template>
  <component
    :is="options.length ? 'ElAutocomplete' : 'ElInput'"
    v-model="currentValue"
    size="mini"
    :placeholder="placeholder"
    clearable
    :fetch-suggestions="fetchSuggestions"
  >
    <el-button
      v-if="removeProp"
      slot="append"
      float="会设置为undefined,使用组件的默认值。"
      icon="el-icon-remove-outline"
      @click="onRemoveProp"
    ></el-button>
  </component>
</template>

<script>
import { ElInput, ElAutocomplete } from 'adaptor/element-ui'
import { createComponent } from '@utils/createComponent'
export default createComponent({
  name: 'StringSetter',
  props: ['defaultValue', 'placeholder', 'removeProp', 'suggestions'],
  inject: ['$doc', 'fetcher'],
  data() {
    return {
      currentValue: this.modelValue || this.defaultValue,
      options: [],
    }
  },
  components: {
    ElInput,
    ElAutocomplete,
  },
  mounted() {
    if (this.suggestions) {
      this.suggestions({
        ...this.$doc.materials.getTarget(),
        getModelFields: this.fetcher.getModelFields,
      }).then((res) => {
        this.options = res
      })
    }
  },
  methods: {
    onRemoveProp() {
      this.currentValue = undefined
    },
    fetchSuggestions(q, cb) {
      cb(
        q ? this.options.filter((item) => item.label.includes(q)) : this.options
      )
    },
  },
  watch: {
    currentValue(v) {
      this._emit(v)
    },
    modelValue(v) {
      this.currentValue = v
    },
  },
})
</script>
