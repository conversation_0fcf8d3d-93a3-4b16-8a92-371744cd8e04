<template>
  <ElSwitch v-model="currentValue" size="mini" :disabled="disabled" />
</template>

<script>
import { ElSwitch } from 'adaptor/element-ui'
import { createComponent } from '@utils/createComponent'
export default createComponent({
  name: '<PERSON><PERSON><PERSON>etter',
  props: ['options', 'defaultValue', 'disabled'],
  data() {
    return {
      currentValue: this?.modelValue ?? this.defaultValue,
    }
  },
  components: {
    ElSwitch,
  },
  watch: {
    currentValue(v) {
      this._emit(v)
    },
    modelValue(v) {
      this.currentValue = v
    },
  },
})
</script>
