// const setterList = [
//   'RadioGroup',
//   'Select',
//   'Object',
//   'Array',
//   'Function',
//   'Color',
// ]

// const simpleSetters = ['String', 'Bool', 'Number', 'TextArea']
// import { getInstallData } from '@utils/install'
import { getCtxSetter } from 'sinitek-lowcode-shared'

const radioSetter = (setter) => {
  if (typeof setter !== 'object') {
    throw new Error('RadioGroupSetter is not object')
  }
  return setter?.props ?? { options: [], defaultValue: '' }
}
const selectSetter = (setter) => {
  if (typeof setter !== 'object') {
    throw new Error('SelectSetter is not object')
  }
  return setter?.props ?? { options: [], defaultValue: '' }
}
const arraySetter = (setter) => {
  if (typeof setter !== 'object') {
    throw new Error('ArraySetter is not object')
  }
  if (!setter?.props?.itemSetter) {
    throw new Error('ArraySetter must have itemSetter')
  }
  if (setter?.props?.itemSetter?.componentName !== 'ObjectSetter') {
    throw new Error('ArraySetter.itemSetter.componentName must be ObjectSetter')
  }
  if (!setter?.props?.itemSetter?.initialValue) {
    throw new Error('ArraySetter.itemSetter must have initialValue')
  }
  return setter.props
}

const generalSetter = (setter) => {
  return setter?.props ?? {}
}

const map = {
  RadioGroup: radioSetter,
  String: generalSetter,
  Textarea: generalSetter,
  Select: selectSetter,
  Bool: generalSetter,
  Number: generalSetter,
  Color: generalSetter,
  Icon: generalSetter,
  Object: generalSetter,
  Array: arraySetter,
  JSON: generalSetter,
  Function: generalSetter,
  Model: generalSetter,
}

export function getSetterName(setter) {
  const name = setter?.componentName ?? setter
  return name
}

const getProps = (setter) => {
  const name = getSetterName(setter)
  const _setter = name?.replace('Setter', '')
  const ctxSetter = getCtxSetter(_setter)
  if (ctxSetter) {
    return generalSetter(ctxSetter)
  }
  if (map[_setter]) {
    return map[_setter](setter)
  }
  return generalSetter(setter)
}

// 解析setter字段，返回setter选项
// 返回数组形式的[{component,option}]
export function getSetter(setter) {
  if (!setter) return null
  let result = []
  if (typeof setter === 'string') {
    result.push({ componentName: setter, props: getProps(setter) })
  } else if (Array.isArray(setter)) {
    setter.forEach((s) => {
      const v = getSetter(s)
      if (v) {
        result.push(...v)
      }
    })
  } else if (typeof setter === 'object') {
    if (setter.componentName) {
      result.push({
        componentName: setter.componentName,
        props: getProps(setter),
      })
    } else {
      throw new Error('setter must have componentName field')
    }
  }
  return result
}
