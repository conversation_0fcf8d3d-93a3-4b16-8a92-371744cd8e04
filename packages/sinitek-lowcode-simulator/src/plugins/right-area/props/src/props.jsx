import RenderConfigItem from './render-config-item'
import renderChildren from './render-children'
import { ElAlert } from 'adaptor/element-ui'
import { setObserver } from 'sinitek-lowcode-shared'
// import AI from './ai'
const defaultConfigList = [
  { name: 'className', title: 'className', setter: 'StringSetter' },
  {
    name: 'ref',
    title: { label: 'ref', tip: 'ref引用' },
    setter: 'StringSetter',
  },
]
export default {
  name: 'EditProps',
  inheritAttrs: false,
  inject: ['$doc', 'hideDefaultProp'],
  components: {
    RenderConfigItem,
    ElAlert,
    // AI,
  },
  props: {
    material: {
      type: Object,
    },
  },
  data() {
    return {
      // fix:使用computed后可能会导致在object-setter时数据不更新
      model: {},
    }
  },
  computed: {
    propsList() {
      return this.material?.props ?? []
    },
    // showAI() {
    //   return ['LAFormItem', 'LATable', 'XnFormItem'].includes(
    //     this.material?.componentName
    //   )
    // },
    // model() {
    //   return this.$doc.getCurrent().schema.props
    // },
  },
  methods: {
    updateModel() {
      this.$nextTick(() => {
        const props = this.$doc.getCurrent(true).schema?.props
        this.model = props || {}
      })
    },
    onChange(name, v) {
      const { schema } = this.$doc.getCurrent(true)
      const newMode = { ...schema.props }
      newMode[name] = v
      this.model[name] = v
      this.$emit('change', newMode)
    },
    onOtherChange(name, v) {
      // value要放到props里
      if (name === 'value') {
        this.onChange(name, v)
        return
      }
      // 和props同级的属性
      // 比如class ref
      const { schema } = this.$doc.getCurrent()
      if (schema[name] === void 0) {
        setObserver(schema, name, v)
      } else {
        schema[name] = v
      }
    },
  },
  created() {
    this.$doc.event.on('ai-change', () => {
      this.updateModel()
    })
  },
  beforeDestroy() {
    this.$doc.event.off('ai-change')
  },
  render() {
    const schema = this.$doc.getCurrent(true).schema
    const dcl = [].concat(this.hideDefaultProp ? [] : defaultConfigList)
    const material = this.$doc.getMaterial(schema.componentName)
    const configure = this.$doc.select.state.configure
    if (configure?.supports?.supportBindState !== false) {
      dcl.push({
        name: 'LCBindState',
        title: {
          label: '绑定字段',
          tip: '绑定对应的状态管理名称，实现v-model语法糖。',
        },
        setter: 'BindStateSetter',
      })
      // 给所有可以绑定值得组件添加value属性，限制父组件是LATable才显示
      dcl.push({
        name: 'value',
        title: {
          label: '输入框的值',
          tip: '使用变量绑定，绑定this.scope.row.xxx来绑定对应的值',
        },
        setter: 'StringSetter',
        condition: (_, target) => {
          return target.findParent('LATable')
        },
      })
    }

    const defaultListMode = dcl.reduce((cur, next) => {
      cur[next.name] = schema[next.name]
      return cur
    }, {})
    return (
      <div class="h-full select-none overflow-y-auto">
        {/* AI按钮 */}
        {/* {this.showAI ? <AI class="ml-1" /> : null} */}
        {/* tips */}
        {material?.tips ? (
          <div px-4 mb-3>
            <ElAlert title={material.tips} type="info" />
          </div>
        ) : null}
        {/* 渲染默认属性 */}
        <RenderConfigItem
          list={dcl}
          modelValue={{ ...defaultListMode, ...this.model }}
          onChange={this.onOtherChange}
        />
        {/* 渲染组件属性 */}
        <RenderConfigItem
          list={this.propsList}
          modelValue={this.model}
          onChange={this.onChange}
        />
        {/* 渲染子元素的配置 */}
        {!this.material?.childConfig ? null : (
          <renderChildren modelValue={this.model} material={this.material} />
        )}
      </div>
    )
  },
}
