import omit from 'lodash/omit'
import { getSetter } from './setter/get-setter'
import LCConfigGroup from './config-group'
export default {
  name: 'RenderChildren',
  inject: ['$doc'],
  props: {
    modelValue: {
      type: Object,
    },
    material: {
      type: Object,
    },
  },
  provide: {
    isRenderChildren: true,
  },
  data() {
    return {
      model: [],
    }
  },
  methods: {
    onInput(v) {
      if (Array.isArray(v)) {
        const schema = this.$doc.getCurrent().schema
        // 过滤掉slot组件
        const children = schema.children.filter(
          (e) => e.componentName !== 'Slot'
        )
        if (v.filter((e) => e.__id__).length !== children.length) {
          // 删除
          // 删除不能完全靠下标，因为children里不是只有对应的子元素，还有slot
          const deleteIndex = schema.children.findIndex(
            (e) =>
              !v.find((f) => f.__id__ === e.id) && e.componentName !== 'Slot'
          )
          schema.children.splice(deleteIndex, 1)
          schema.__key__ = Date.now()
        } else {
          v.forEach((e) => {
            if (!e.__id__) {
              // 新增的
              schema.children.push(e)
              schema.__key__ = Date.now()
            } else {
              // 修改
              const find = children.find((f) => f.id === e.__id__)
              if (find) {
                // assign 某些时候不会触发数据修改
                // Object.assign(find.props, omit(e, '__id__'))
                // 直接赋值已经被观察的响应对象，会触发数据修改，导致页面刷新
                find.props = omit(e, '__id__')
              }
            }
          })
        }
        this.$doc.schema.update()
        this.$doc.history.addHistory()
      }
    },
  },
  created() {
    const getModel = () => {
      const { schema } = this.$doc.getCurrent()
      const model = (schema?.children ?? [])
        .filter((e) => e.componentName !== 'Slot')
        .map((e) => {
          return { ...e.props, __id__: e.id }
        })
      this.model = model
    }
    getModel()

    this.clean = this.$doc.event.combine(
      this.$doc.schema.onChange(() => {
        setTimeout(() => {
          getModel()
        }, 100)
      }),
      this.$doc.select.onChange(() => {
        setTimeout(() => {
          getModel()
        }, 100)
      })
    )
  },
  beforeDestroy() {
    this?.clean?.()
  },
  render() {
    const mcc = this.material.childConfig
    // TODO this.$doc.getCurrent().schema
    // schema修改后render检测不到，需要在change中获取
    const schema = this.$doc.getCurrent().schema
    const currentMcc = Array.isArray(mcc)
      ? mcc.find((e) => e.componentName === schema.children[0].componentName)
      : mcc
    const setters = getSetter(currentMcc.setter)
    // 这里只能渲染ArraySetter
    return (
      <LCConfigGroup group={currentMcc.title}>
        <LCConfigItem
          config={currentMcc}
          setters={setters}
          modelValue={this.model}
          onInput={this.onInput}
          hideTitle
          hideVariable
        />
      </LCConfigGroup>
    )
  },
}
