<template>
  <div>
    <LCIcon
      icon="i-custom:bind"
      float="变量绑定"
      :class="[isJSE ? 'bg-blue-500 text-white rounded-sm' : '']"
      class="ml-4"
      :size="25"
      @click="show"
    />
  </div>
</template>

<script>
import LCIcon from '@common/icon.vue'
import { createComponent } from '@utils/createComponent'
import { getAPI, JSExpression, isJSExpression } from 'sinitek-lowcode-shared'
export default createComponent({
  components: {
    LCIcon,
  },
  inject: ['$doc'],
  props: {
    config: Object,
    modelValue: null,
  },
  computed: {
    isJSE() {
      return isJSExpression(this.modelValue)
    },
  },
  methods: {
    show() {
      // this.isShow = true
      getAPI().showExpressionEditor(this.modelValue?.value || '', (v) => {
        this.$emit(
          'confirm',
          !v
            ? null
            : {
                type: JSExpression,
                value: v,
              }
        )
      })
    },
  },
})
</script>
