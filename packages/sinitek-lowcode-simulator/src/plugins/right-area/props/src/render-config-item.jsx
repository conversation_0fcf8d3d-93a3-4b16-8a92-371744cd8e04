import LCConfigItem from './config-item'
import LCConfigGroup from './config-group'
import { h } from 'vue'
import { getSetter } from './setter/get-setter'
import { overrideProps } from '@utils/materials'
export default {
  name: 'RenderConfigItem',
  props: ['modelValue', 'list'],
  inject: ['$doc'],
  data() {
    return {
      currentValue: this.modelValue,
    }
  },
  computed: {
    computedList() {
      const schema = this.$doc.getCurrent().schema
      return overrideProps(
        this.list ?? [],
        this.$doc.getMaterial(schema.componentName)?.overrideProps
      )
    },
  },
  render() {
    const renderItem = (item) => {
      const setters = getSetter(item.setter)
      if (
        item.condition &&
        !item.condition(this.currentValue, this.$doc.materials.getTarget())
      ) {
        return null
      }
      const onInput = (v) => {
        if (this.currentValue[item.name] === void 0) {
          this.$set(this.currentValue, item.name, v)
        } else {
          this.currentValue[item.name] = v
        }
        this.$emit('change', item.name, v)
      }

      return (
        <LCConfigItem
          config={item}
          setters={setters}
          rootProps={this.currentValue}
          modelValue={this.modelValue?.[item.name]}
          onInput={onInput}
          key={item.name}
        />
      )
    }
    const renderGroup = (item) => {
      if (
        item.condition &&
        !item.condition(this.currentValue, this.$doc.materials.getTarget())
      ) {
        return null
      }
      return (
        <LCConfigGroup key={item.title} group={item.title}>
          {item.items.map(renderItem)}
        </LCConfigGroup>
      )
    }
    return h(
      'div',
      null,
      this.computedList.map((e) => {
        return [e?.type === 'group' ? renderGroup(e) : renderItem(e)]
      })
    )
  },
  watch: {
    modelValue(v) {
      this.currentValue = v
    },
  },
}
