<template>
  <!-- <div
    ref="magic"
    class="rounded-full cursor-pointer w-7 h-7 bg-white z-1 shadow f-c-c absolute right-10 top-15"
  > -->
  <!-- absolute left-10 top-15  -->
  <div v-if="show" ref="magic" class="z-1 inline-block w-5 cursor-pointer">
    <div class="ai-icon-wrapper" @click="getMagicProps">
      <AIIcon class="pointer-events-none block h-4 w-4" />
    </div>

    <template v-if="showMagic">
      <xn-dialog
        :show.sync="showMagic"
        title="组件属性预览"
        :buttons="buttons"
        @confirm="onMagicConfirm"
        @save="onMagicConfirm"
        @cancel="showMagic = false"
      >
        <el-table
          ref="table"
          :data="tableData"
          stripe
          style="width: 100%"
          @selection-change="onSelectionChange"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column prop="code" label="属性" width="180">
          </el-table-column>
          <el-table-column prop="value" label="值">
            <template #default="{ row }">{{ row.value }}</template>
          </el-table-column>
        </el-table>
      </xn-dialog>
    </template>
  </div>
</template>

<script>
import AIIcon from './icon'
import cloneDeep from 'lodash/cloneDeep'
import { setObserver } from 'sinitek-lowcode-shared'
export default {
  name: 'LCAI',
  components: {
    AIIcon,
  },
  inject: {
    fetcher: { default: null },
    $doc: { default: null },
    getEditIndex: { default: () => void 0 },
  },
  data() {
    return {
      showMagic: false,
      tableData: [],
      multipleSelection: [],
      buttons: [
        {
          label: '保存',
          type: this.saveDisabled ? 'info' : 'primary',
          action: 'save',
          event: 'save',
          showLoading: false,
        },
        { label: '取消', type: 'info', action: 'cancel', event: 'cancel' },
      ],
      // 先本地测试
      show: window.__LCShowAI__ ?? false,
    }
  },
  mounted() {
    if (!this.show) {
      return
    }
    // const el = this.$refs.magic
    // el.addEventListener('mousedown', (e) => {
    //   let [, , , , x = 0, y = 0] = window
    //     .getComputedStyle(el)
    //     .getPropertyValue('transform')
    //     .split(',')
    //   let isMoving = false
    //   const move = (e1) => {
    //     isMoving = true
    //     let left = +parseFloat(x) + e1.clientX - e.clientX
    //     let top = +parseFloat(y) + e1.clientY - e.clientY
    //     el.style.transform = `translate(${left}px, ${top}px)`
    //   }
    //   const mouseup = () => {
    //     setTimeout(() => {
    //       // 200ms内没有移动就是点击
    //       if (!isMoving) {
    //         this.getMagicProps()
    //       }
    //     }, 200)
    //     document.removeEventListener('mousemove', move)
    //     document.removeEventListener('mouseup', mouseup)
    //   }
    //   document.addEventListener('mousemove', move)
    //   document.addEventListener('mouseup', mouseup)
    // })
  },
  methods: {
    async getMagicProps() {
      if (this.fetcher?.getAIProps) {
        const index = this?.getEditIndex?.()
        let schema = this.$doc.getCurrent().schema
        let material = this.$doc.getMaterial(schema.componentName)
        let getAIParams = material.configure.component?.getAIParams
        let state = cloneDeep(this.$doc.select.state)
        if (index !== void 0) {
          schema = schema.children[index]
          material = this.$doc.getMaterial(schema.componentName)
          const parent = { ...state.current }
          state.current = state.current.children[index]
          state.parent = parent
        }

        this.tableData = await this.fetcher.getAIProps({
          componentType: material?.componentType,
          componentTitle: material.name,
          formContext: material?.formContext,
          ...getAIParams?.(state),
        })
        this.showMagic = true
        this.$nextTick(() => {
          this.$refs?.table?.toggleAllSelection?.()
        })
      }
    },
    onMagicConfirm() {
      const schema = this.$doc.getCurrent().schema
      const result = this.$doc.select.state.configure.component.setAIParams(
        this.multipleSelection
      )
      if (result.props) {
        const index = this?.getEditIndex?.()
        //index用来处理table的col列
        if (index !== void 0) {
          setObserver(schema.children[index], 'props', {
            ...schema.children[index].props,
            ...result.props,
          })
        } else {
          setObserver(schema, 'props', { ...schema.props, ...result.props })
        }
        this.$doc.history.addHistory()
        this.$doc.event.emit('ai-change')
      }
      this.showMagic = false
      // this.multipleSelection.forEach((e) => {
      //   if (schema.props[e.code] === void 0) {
      //     this.$set(schema.props, e.code, e.value)
      //   } else {
      //     schema.props[e.code] = e.value
      //   }
      // })
    },
    onSelectionChange(val) {
      this.multipleSelection = val
    },
  },
}
</script>
