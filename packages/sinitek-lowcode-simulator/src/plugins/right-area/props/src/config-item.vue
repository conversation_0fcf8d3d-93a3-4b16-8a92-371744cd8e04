<template>
  <div
    class="config-item mb-3 flex items-center justify-between overflow-hidden px-4 fs-14"
    :class="{ 'b-2 border-solid border-red': isError }"
  >
    <div
      class="w-full flex items-center overflow-hidden"
      :class="{
        'flex-col': !isInLine,
      }"
    >
      <template v-if="!hideTitle && title">
        <div
          class="w-full flex items-center break-all fs-14"
          :class="{ 'mb-1': !isInLine }"
        >
          <span>{{ (title && title.label) || title }}</span>
          <el-tooltip
            v-if="title && title.tip"
            effect="dark"
            :content="title.tip"
            placement="bottom"
            class="ml-1"
          >
            <div class="i-custom:question h-3 w-3 text-#999"></div>
          </el-tooltip>
          <LCIcon
            v-if="setters.length > 1"
            icon="i-lucide-loop"
            float="切换组件"
            class="mx-2"
            :size="20"
            @click="toggleSetter"
          />
          <AI v-show="currentValue && config.showAI" class="ml-1 h-5 w-5" />
        </div>
      </template>
      <div
        class="content flex items-center overflow-hidden"
        :class="[!isInLine ? 'w-full' : 'shrink-0']"
      >
        <component
          :is="getSetterComponent"
          v-bind="setterProps"
          :key="`setter-${config.name}`"
          :model-value="isBindState ? currentValue.defaultValue : currentValue"
          :config="config"
          class="w-full flex-1"
          @input="onChange"
        />
        <BindVariable
          v-if="isSupportVariable"
          :config="config"
          class="shrink-0"
          :model-value="currentValue"
          @confirm="onVariableChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import BindVariable from './bind-variable.vue'
import { createComponent } from '@utils/createComponent'
import {
  isJSExpression,
  getCtxSetter,
  isJSFunction,
} from 'sinitek-lowcode-shared'
import RadioGroupSetter from './setter/radio-setter.vue'
import SelectSetter from './setter/select-setter.vue'
import BoolSetter from './setter/bool-setter.vue'
import StringSetter from './setter/string-setter.vue'
import TextareaSetter from './setter/textarea-setter.vue'
import IconSetter from './setter/icon-setter.vue'
import ObjectSetter from './setter/object-setter.vue'
import NumberSetter from './setter/number-setter.vue'
import JSONSetter from './setter/json-setter.vue'
import FunctionSetter from './setter/function-setter.vue'
import SlotSetter from './setter/slot-setter.vue'
import ColorSetter from './setter/color-setter.vue'
import ModelSetter from './setter/model-setter.vue'
import BindStateSetter from './setter/bind-state-setter.vue'
import RichTextSetter from './setter/rich-text-setter.vue'
import { getSetterName } from './setter/get-setter'
import DsSetter from './setter/ds-setter.vue'
import SelectStyleSetter from './setter/select-style-setter.vue'
import LCIcon from '@common/icon.vue'
import { propType } from './propType'
import get from 'lodash/get'
import { emit } from '@/utils/emitter'
import AI from './ai'
const map = {
  [StringSetter.name]: 'string',
  [NumberSetter.name]: 'number',
  [BoolSetter.name]: 'boolean',
}

export default createComponent({
  components: {
    BindVariable,
    RadioGroupSetter,
    SelectSetter,
    BoolSetter,
    StringSetter,
    TextareaSetter,
    IconSetter,
    ObjectSetter,
    NumberSetter,
    JSONSetter,
    FunctionSetter,
    SlotSetter,
    ColorSetter,
    ModelSetter,
    BindStateSetter,
    RichTextSetter,
    DsSetter,
    LCIcon,
    SelectStyleSetter,
    AI,
  },
  name: 'LCConfigItem',
  inject: {
    // 当前的provide
    getParentConfig: { default: null },
    // 获取array-setter编辑的index
    getEditIndex: { default: () => void 0 },
    // simulator.vue
    rootSupportVariable: { default: undefined, from: 'supportVariable' },
  },
  provide() {
    return {
      getParentConfig: () => {
        // 是array-setter的editIndex
        const index = this.$children?.[0]?.editIndex
        let result = [this.config]
        if (index !== void 0) {
          result.push(index)
        }
        if (this.getParentConfig) {
          result.push(this.getParentConfig()?.[0])
        }
        return result
      },
    }
  },
  props: {
    config: Object,
    modelValue: null,
    setters: Array,
    hideTitle: Boolean,
    hideVariable: BoolSetter,
    rootProps: Object,
  },
  computed: {
    isBindState() {
      if (getSetterName(this.setter) === 'FunctionSetter') {
        return false
      }
      const value = this.currentValue
      return this.config?.supportVariable !== false && isJSExpression(value)
    },
    setterProps() {
      const props = this.setter?.props
      if (
        props?.defaultValue === void 0 &&
        this.config?.defaultValue !== void 0
      )
        props.defaultValue = this.config.defaultValue
      return { ...props }
    },
    title() {
      return this.config?.title ?? this.config?.description
    },
    isPositionTop() {
      // 分行和列的样式展示
      const setterName = this.setter?.componentName
      const titlePosition = this?.config?.titlePosition
      return (
        ['ArraySetter', 'ModelSetter'].includes(setterName) ||
        titlePosition === 'top'
      )
    },
    getSetterComponent() {
      return (
        getCtxSetter(this.setter?.componentName) || this.setter?.componentName
      )
    },
    isInLine() {
      return ['BoolSetter', 'SlotSetter'].includes(this.getSetterComponent)
    },
    isSupportVariable() {
      if (this.hideVariable) {
        return false
      }
      let result = true
      // 如果rootSupportVariable有值，则以rootSupportVariable为准
      // 这里的rootSupportVariable是simulator.vue的provide
      if (this.rootSupportVariable !== void 0) {
        result = this.rootSupportVariable
      }
      if (this.config?.supportVariable !== void 0) {
        result = this.config.supportVariable !== false
      }
      return result
    },
    setter() {
      return this?.setters?.[this.setterIndex]
    },
  },
  data() {
    return {
      currentValue:
        this?.modelValue ?? this.config?.value ?? this.config?.defaultValue,
      setterIndex: 0,
      isError: false,
    }
  },
  created() {
    this.getSetterIndex()
  },
  methods: {
    getSetterIndex() {
      this.isError = false
      if (this.setters.length <= 1) return
      const schema = this.$doc.getCurrent().schema
      const si = get(schema.setterIndex, this.getSetterPath(), void 0)
      if (si !== void 0) {
        this.setterIndex = si
      } else {
        // 判断 string, boolean, number，和function和json的情况
        // 找不到情况下判断值是否和setter一致
        if (this.modelValue != void 0) {
          const type = typeof this.modelValue
          const cn = this.setters[0].componentName
          let i = this.setterIndex
          if (isJSFunction(this.modelValue) && cn !== FunctionSetter.name) {
            i = this.setters.findIndex(
              (e) => e.componentName === FunctionSetter.name
            )
          } else if (type === 'object' && cn !== JSONSetter.name) {
            i = this.setters.findIndex(
              (e) => e.componentName === JSONSetter.name
            )
          } else if (map[cn] && map[cn] !== type) {
            this.isError = true
          }
          this.setterIndex = i === -1 ? this.setterIndex : i
        }
      }
    },
    /**
     * xxx.xxx
     * xxx[1].xxx
     */
    getSetterPath() {
      const arr = this.getParentConfig?.()
      let path = ''
      if (arr?.length) {
        arr.forEach((e) => {
          if (e.name) {
            if (!path) {
              path += e.name
            } else {
              path += `.${e.name}`
            }
          } else if (!isNaN(e)) {
            path += `[${e}]`
          }
        })
      }
      if (path) {
        return path + '.' + this.config.name
      }
      return this.config.name
    },
    onChange(v) {
      if (this.isBindState) {
        this.currentValue.defaultValue = v
        this.onVariableChange(this.currentValue)
        return
      }
      if (this.config.propType) {
        v = propType[this.config.propType](v)
      }
      if (this.config?.extraProps?.setValue) {
        const index = this?.getEditIndex?.()
        let childTarget
        if (index !== void 0) {
          const children = (
            this.$doc.getCurrent()?.schema?.children ?? []
          ).filter((e) => e.componentName !== 'Slot')
          const child = children[index]
          childTarget = this.$doc.materials.getTarget(() => {
            const node = this.$doc.node.getNode(child.id, true)
            return { schema: node.node, parent: node.parent }
          })
        }
        this.config.extraProps.setValue(
          this.$doc.materials.getTarget(),
          v,
          childTarget
        )
      }
      this._change(v)
      emit('props:change')
    },
    onVariableChange(v) {
      this._change(v)
      emit('props:change')
    },
    toggleSetter() {
      this.setterIndex = (this.setterIndex + 1) % this.setters.length
      const schema = this.$doc.getCurrent().schema
      if (!schema.setterIndex) {
        schema.setterIndex = {}
      }
      schema.setterIndex[this.getSetterPath()] = this.setterIndex
    },
  },
  watch: {
    modelValue(v) {
      this.currentValue = v ?? this.config?.defaultValue
    },
  },
})
</script>
<style scoped lang="scss">
.config-item {
  > .flex.flex-col {
    > .label {
      @apply w-full h-8 bg-bgGrayLight flex items-center px-2;
    }
  }
}
</style>
