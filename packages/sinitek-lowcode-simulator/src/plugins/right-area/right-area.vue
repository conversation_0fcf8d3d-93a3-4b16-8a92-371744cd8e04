<template>
  <div class="lc-settings relative h-full w-300px flex shrink-0 flex-col b-l-1">
    <div class="p-4">
      <Tab
        v-show="isSelected"
        :tabs="tabs"
        :default-key="activeKey"
        @change="tabChange"
      />
    </div>
    <template v-if="componentName && activeConfig.component">
      <component
        :is="activeConfig.component"
        :key="componentName"
        ref="comp"
        :material="material"
        @change="onChange"
      />
    </template>
    <template v-if="!isSelected">
      <div class="mb-a mt-a text-center text-gray fs-14">请选择元素</div>
    </template>
  </div>
</template>

<script>
import Tab from '@common/tab'
import cloneDeep from 'lodash/cloneDeep'
import { DesignMode } from 'sinitek-lowcode-shared'
import { isRootComponent } from '@utils/materials'
import material from './material'

import LCConfigItem from './props/src/config-item'
import ArraySetter from './props/src/setter/array-setter.vue'
import ObjectSetter from './props/src/setter/object-setter.vue'
import RenderConfigItem from './props/src/render-config-item'
import Vue from 'vue'
import { combine } from '@/utils'
Vue.component(LCConfigItem.name, LCConfigItem)
Vue.component(ArraySetter.name, ArraySetter)
Vue.component(ObjectSetter.name, ObjectSetter)
Vue.component(RenderConfigItem.name, RenderConfigItem)

export default {
  name: 'LCRightArea',
  components: {
    Tab,
  },
  inject: ['$doc', 'mode'],
  data() {
    return {
      tabs: [],
      isSelected: false,
      material: {},
      id: '',
      activeKey: '',
      currentSettings: [],
      componentName: '',
    }
  },
  computed: {
    activeConfig() {
      return this.currentSettings.find((e) => e.id === this.activeKey)
    },
  },
  watch: {
    componentName() {
      this.getTabs()
    },
  },
  created() {
    // 低代码组件时，预先加载material文件
    this.$doc.getPlugins('rightArea', (plugins) => {
      this.currentSettings = [...plugins]
      if (this.mode === DesignMode.LOWCODE) {
        this.currentSettings.unshift(material)
      }
    })
  },
  mounted() {
    this.clean = combine(
      this.$doc.select.onChange((arg) => {
        if (arg.isChoiceComponent) return
        this.isSelected = !!this.$doc.getCurrent(true).schema
        // 小优化，没有进行切换时，不进行重新渲染。
        if (this.id === arg?.current?.id) return
        if (!this.activeKey && this.tabs?.[0]?.value) {
          this.activeKey = this.tabs[0].value
        }
        if (arg?.current) {
          this.componentName = arg?.current?.componentName
          this.id = arg.current.id
          this.material = cloneDeep(
            this.$doc.getMaterial(arg?.current?.componentName)
          )
          setTimeout(() => {
            this.$refs.comp?.updateModel?.()
          })
        } else {
          this.componentName = ''
          this.id = ''
        }
      }),
      this.$doc.event.on('props:change', () => {
        this.$refs.comp?.updateModel?.()
      })
    )
  },
  beforeDestroy() {
    this?.clean?.()
  },
  methods: {
    async getTabs() {
      let tabs = []
      this.currentSettings
        .filter((e) => e.id !== 'SettingMaterial')
        .forEach((e) => {
          tabs.push({
            label: e.title,
            value: e.id,
          })
        })

      const { schema } = this.$doc.getCurrent()
      if (schema) {
        const config = this.$doc.getConfigure(schema.componentName)
        const events = config?.supports?.events ?? []

        if (
          !this.material?.props?.length &&
          !this.material?.childConfig &&
          !isRootComponent(schema.componentName)
        ) {
          tabs = tabs.filter((e) => !['SettingProps'].includes(e.value))
        }
        if (!events.length) {
          tabs = tabs.filter((e) => !['SettingEvents'].includes(e.value))
        }
        if (config?.supports.style === false) {
          tabs = tabs.filter((e) => !['SettingStyle'].includes(e.value))
        }

        if (
          this.mode === DesignMode.LOWCODE &&
          isRootComponent(schema.componentName) &&
          material
        ) {
          // 低代码组件的模型，page元素添加一个属性设置
          tabs.unshift({
            label: material.title,
            value: material.id,
          })
        }
        this.tabs = tabs
        this.activeKey = this.tabs[0].value
      }
    },
    tabChange(item) {
      this.activeKey = item.value
      setTimeout(() => {
        this.$refs.comp?.updateModel?.()
      })
    },
    onChange(val) {
      if (
        this.mode === DesignMode.LOWCODE &&
        this.activeConfig.id === 'SettingMaterial'
      ) {
        this.$doc.node.root[this.activeConfig.name] = val
      } else {
        const { schema } = this.$doc.getCurrent()
        // 当前组件如果是低代码组件，修改属性时，需要刷新组件
        if (this.$doc.getMaterial(schema.componentName).lowcodeComponent) {
          schema.__key__ = Math.random().toString(36).substr(2)
        }
        schema[this.activeConfig.name] = val
      }
      this.$doc.history.addHistory()
    },
  },
}
</script>
