<template>
  <div mt-2 flex items-center gap-2>
    <div @click="choiceComponent">
      <div
        v-show="!isChoiceComponent"
        float="点击后选择组件来绑定组件属性"
        class="i-mdi:location-searching"
      ></div>
      <div
        v-show="isChoiceComponent"
        float="选择组件自动修改属性"
        class="i-mdi:my-location text-primary"
      ></div>
    </div>
    <ElInput
      v-model="currentValue.key"
      placeholder="输入key"
      @input="onChange"
    />
    <ElSelect
      v-model="currentValue.value"
      placeholder="选择属性"
      @change="onPropChange"
    >
      <ElOption
        v-for="option of currentComponent"
        :key="option.label"
        :label="option.label"
        :value="option.value"
      ></ElOption>
    </ElSelect>
    <div class="i-custom:remove h-5 w-5 flex-shrink-0" @click="onRemove"></div>
  </div>
</template>

<script>
import { ElInput, ElSelect, ElOption } from 'adaptor/element-ui'
export default {
  name: 'LCPropsMapReflectItem',
  components: {
    ElInput,
    ElSelect,
    ElOption,
  },
  inject: ['$doc'],
  props: {
    options: Array,
    value: Object,
    changeActionIndex: Function,
    index: Number,
  },
  data() {
    return {
      currentValue: this.value,
      propsList: [],
      isChoiceComponent: false,
    }
  },
  computed: {
    currentComponent() {
      if (!this.currentValue.id) return []
      const { componentName } = this.$doc.node.getNode(this.currentValue.id)
      return (this.$doc.getMaterial(componentName)?.props ?? []).map((e) => {
        return {
          label: e.title?.label ? e.title.label : e.title,
          value: e.name,
        }
      })
    },
  },
  watch: {
    value(val) {
      this.currentValue = val
    },
  },
  beforeDestroy() {
    this?.clean?.()
    this.$doc.select.set({ isChoiceComponent: false })
  },
  methods: {
    choiceComponent() {
      this.isChoiceComponent = !this.isChoiceComponent
      if (this.isChoiceComponent) {
        if (this.clean) {
          this.clean()
        }
        this.clean = this.$doc.select.onChange((arg) => {
          this.currentValue.id = arg.current.id
          this.currentValue.value = ''
        })
      }
      this.$doc.select.set({ isChoiceComponent: this.isChoiceComponent })
    },
    getValue() {
      return this.currentValue
    },
    cancelChoice() {
      this.isChoiceComponent = false
      if (this.clean) {
        this.clean()
      }
    },
    onChange() {
      this.$nextTick(() => {
        this.changeActionIndex(this.index)
      })
      this.$emit('change', this.getValue())
    },
    onPropChange() {
      this.changeActionIndex(null)
      this.cancelChoice()
      this.$emit('change', this.getValue())
    },
    onRemove() {
      this.$emit('remove')
    },
  },
}
</script>
