<template>
  <collapse-item ref="advancedModule" :border="true" title="属性映射">
    <div px-4>
      <ReflectItem
        v-for="(item, i) of reflectList"
        ref="reflectItem"
        :key="item.id || i"
        :value="item"
        :index="i"
        :change-action-index="changeActionIndex"
        @change="(v) => onChange(i, v)"
        @remove="onRemove(i)"
      />
      <div mt-2 flex items-center>
        <span f-c-c cursor-pointer text-primary fs-14 @click="add"
          >添加一项
          <span inline-block class="i-material-symbols-light:add h-6 w-6"></span
        ></span>
      </div>
    </div>
  </collapse-item>
</template>

<script>
import { CollapseItem } from 'sinitek-lowcode-shared'

import { createComponent } from '@utils'
import ReflectItem from './item.vue'
export default createComponent({
  name: 'LCPropsMapReflect',
  components: {
    CollapseItem,
    ReflectItem,
  },
  props: {
    modelValue: Array,
  },
  data() {
    return {
      reflectList: this.modelValue,
      // 当前操作的下标
      actionIndex: 0,
    }
  },
  methods: {
    add() {
      this.reflectList.push({ key: '', id: '', value: '' })
      this.changeActionIndex(this.reflectList.length - 1)
      this._emit(this.reflectList)
    },
    changeActionIndex(i) {
      if (this.actionIndex === i) return
      this.$refs.reflectItem?.forEach((item) => item.cancelChoice())
      this.actionIndex = i
    },
    onChange(i, v) {
      this.reflectList[i] = v
      this._emit(this.reflectList)
    },
    onRemove(i) {
      this.reflectList.splice(i, 1)
      this._emit(this.reflectList)
    },
  },
  watch: {
    modelValue(val, o) {
      if (JSON.stringify(val) === JSON.stringify(o)) return
      this.reflectList = val
    },
  },
})
</script>
