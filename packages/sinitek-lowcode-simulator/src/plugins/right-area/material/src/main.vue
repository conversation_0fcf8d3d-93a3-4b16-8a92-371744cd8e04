<template>
  <div>
    <collapse-item :border="true" title="属性配置">
      <RenderConfigItem
        :list="list"
        :model-value="modelValue"
        @change="onChange"
      />
      <Reflect v-model="currentReflect" @input="onInput" />
    </collapse-item>
    <collapse-item :border="true" title="配置信息">
      <div class="wh-full px-4 py-2">
        <xn-button
          size="mini"
          :show-loading="false"
          class="w-full"
          @click="show"
        >
          编辑配置数据
        </xn-button>
      </div>
    </collapse-item>
  </div>
</template>

<script>
import Reflect from './reflect/reflect.vue'
import RenderConfigItem from '../../props/src/render-config-item.jsx'
import { deepClone } from '@/utils'
import { CollapseItem } from 'sinitek-lowcode-shared'
export default {
  name: 'SettingsEvents',
  components: {
    Reflect,
    RenderConfigItem,
    CollapseItem,
  },
  inject: ['$doc', 'showMED'],
  data() {
    return {
      currentReflect: [],
      configure: '',
      modelValue: {
        props: [],
      },
    }
  },
  computed: {
    list() {
      const generateBasicDefault = [
        'NumberSetter',
        'StringSetter',
        'BoolSetter',
        'TextareaSetter',
        'ColorSetter',
        'IconSetter',
      ].map((e) => {
        return {
          name: 'defaultValue',
          title: '默认值',
          setter: e,
          supportVariable: false,
          condition(props) {
            return props.setter === e
          },
        }
      })
      return [
        {
          title: '属性设置',
          name: 'props',
          setter: {
            componentName: 'ArraySetter',
            props: {
              itemSetter: {
                componentName: 'ObjectSetter',
                initialValue: {
                  key: 'propName',
                  title: '属性标题',
                },
                fieldText: 'key',
                props: {
                  config: {
                    items: [
                      {
                        name: 'title',
                        title: '属性标题',
                        setter: 'StringSetter',
                        supportVariable: false,
                      },
                      {
                        name: 'key',
                        title: '属性名称',
                        setter: 'StringSetter',
                        supportVariable: false,
                      },
                      {
                        name: 'setter',
                        title: '设置器',
                        supportVariable: false,
                        setter: {
                          componentName: 'SelectSetter',
                          props: {
                            options: [
                              { label: '字符串', value: 'StringSetter' },
                              { label: '多行文本', value: 'TextareaSetter' },
                              { label: '数字', value: 'NumberSetter' },
                              { label: '布尔', value: 'BoolSetter' },
                              { label: '对象', value: 'ObjectSetter' },
                              { label: '数组', value: 'ArraySetter' },
                              { label: '选择器', value: 'SelectSetter' },
                              { label: '函数', value: 'FunctionSetter' },
                              { label: '图标', value: 'IconSetter' },
                              { label: '颜色', value: 'ColorSetter' },
                              { label: '模型', value: 'ModelSetter' },
                            ],
                          },
                        },
                      },
                      {
                        name: 'valueChange',
                        title: '值变化',
                        setter: 'FunctionSetter',
                        supportVariable: false,
                      },
                      ...generateBasicDefault,
                      {
                        name: 'defaultValue',
                        title: '默认值',
                        setter: 'JSONSetter',
                        supportVariable: false,
                        condition(props) {
                          return [
                            'ObjectSetter',
                            'ArraySetter',
                            'SelectSetter',
                            'ModelSetter',
                          ].includes(props.setter)
                        },
                      },
                      {
                        name: 'iconType',
                        title: '图标类型',
                        setter: {
                          componentName: 'SelectSetter',
                          props: {
                            options: [
                              { label: '饿了么', value: 'element-ui' },
                              { label: 'svg', value: 'svg-icon' },
                              { label: '第三方', value: 'iconify' },
                            ],
                          },
                        },
                        supportVariable: false,
                        condition(props) {
                          return props.setter === 'IconSetter'
                        },
                      },
                      {
                        title: '选项',
                        name: 'options',
                        supportVariable: false,
                        setter: {
                          componentName: 'ArraySetter',
                          props: {
                            itemSetter: {
                              componentName: 'ObjectSetter',
                              initialValue: {
                                label: '选项',
                                value: '选项',
                              },
                              props: {
                                textField: 'label',
                                config: {
                                  items: [
                                    {
                                      name: 'label',
                                      title: '标题',
                                      setter: 'StringSetter',
                                      supportVariable: false,
                                    },
                                    {
                                      name: 'value',
                                      title: '值',
                                      setter: 'StringSetter',
                                      supportVariable: false,
                                    },
                                  ],
                                },
                              },
                            },
                          },
                        },
                        condition(props) {
                          return ['SelectSetter', 'RadioGroupSetter'].includes(
                            props.setter
                          )
                        },
                      },
                      {
                        title: '配置项',
                        name: 'items',
                        supportVariable: false,
                        setter: {
                          componentName: 'ArraySetter',
                          props: {
                            itemSetter: {
                              componentName: 'ObjectSetter',
                              initialValue: {
                                title: '标题',
                                name: '名称',
                              },
                              props: {
                                textField: 'title',
                                config: {
                                  items: [
                                    {
                                      name: 'title',
                                      title: {
                                        label: '标题',
                                        tip: 'title',
                                      },
                                      setter: 'StringSetter',
                                      supportVariable: false,
                                    },
                                    {
                                      name: 'name',
                                      title: {
                                        label: '属性名称',
                                        tip: 'name',
                                      },
                                      setter: 'StringSetter',
                                      supportVariable: false,
                                    },
                                    {
                                      name: 'setter',
                                      title: '设置器',
                                      supportVariable: false,
                                      setter: {
                                        componentName: 'SelectSetter',
                                        props: {
                                          options: [
                                            {
                                              label: '字符串',
                                              value: 'StringSetter',
                                            },
                                            {
                                              label: '数字',
                                              value: 'NumberSetter',
                                            },
                                            {
                                              label: '布尔',
                                              value: 'BoolSetter',
                                            },
                                          ],
                                        },
                                      },
                                    },
                                    ...generateBasicDefault,
                                  ],
                                },
                              },
                            },
                          },
                        },
                        condition(props) {
                          return ['ArraySetter', 'ObjectSetter'].includes(
                            props.setter
                          )
                        },
                      },
                      {
                        name: 'initialValue',
                        title: '初始化',
                        setter: 'JSONSetter',
                        supportVariable: false,
                        condition(props) {
                          return ['ArraySetter'].includes(props.setter)
                        },
                      },
                    ],
                  },
                },
              },
            },
          },
        },
      ]
    },
  },
  mounted() {
    const root = deepClone(this.$doc.node.root)
    this.currentReflect = root?.material?.reflect ?? []
    this.modelValue.props = root?.material?.props ?? []
    this.configure = root?.material?.configure ?? {}
  },
  methods: {
    onInput() {
      this.$emit('change', {
        reflect: deepClone(this.currentReflect),
        props: deepClone(this.modelValue.props),
        configure: deepClone(this.configure),
      })
    },
    show() {
      // this.isShow = true
      this.showMED({
        title: '编辑物料配置数据',
        value: JSON.stringify(this.configure, null, 2),
        language: 'json',
        useMaterialConfigureSchema: true,
        onConfirm: (v) => {
          this.configure = JSON.parse(v)
          this.onInput()
        },
      })
    },
    onChange() {
      this.onInput()
    },
  },
}
</script>
