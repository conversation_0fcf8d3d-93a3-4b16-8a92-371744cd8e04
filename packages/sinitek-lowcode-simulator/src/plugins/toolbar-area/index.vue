<template>
  <div
    v-if="list.length"
    class="lc-toolbar-area px-x min-h-10 w-full flex bg-white py-2"
  >
    <component
      :is="item.component"
      v-for="item of list"
      :key="item.id"
      :style="{ index: item.index || 1 }"
    />
  </div>
</template>

<script>
export default {
  name: 'LCToolbarArea',
  inject: ['$doc'],
  data() {
    return {
      list: [],
    }
  },
  created() {
    this.$doc.getPlugins('toolbarArea', (plugins) => {
      this.list = plugins
    })
  },
}
</script>
