<template>
  <div>
    <component :is="item.component" v-for="item of plugins" :key="item.id" />
  </div>
</template>

<script>
// 浮动区域，用于显示一些浮动按钮，比如AI助手
export default {
  name: 'LCFloatArea',
  inject: ['$doc'],
  data() {
    return {
      components: {},
      plugins: [],
      left: [],
    }
  },
  created() {
    this.$doc.getPlugins('floatArea', (plugins) => {
      this.plugins = plugins
    })
  },
}
</script>
