<template>
  <div
    v-show="showPanel"
    ref="el"
    class="panel h-full flex-shrink bg-bg fs-14"
    :class="[isPinClass]"
    :style="computedOffset"
  >
    <div
      class="h-full flex flex-col bg-white"
      :class="[!placement || placement === 'left' ? 'shadow-r' : 'shadow-l']"
    >
      <div
        v-if="!hideHeader"
        class="h-11 flex flex-shrink-0 items-center px-4 font-bold fs-14"
      >
        <span :class="icon" class="h-4 w-4 text-primary"></span>
        <span class="ml-2 inline-block w-full font-bold">{{ title }}</span>
        <div class="flex flex-shrink-0">
          <slot name="action" />
          <div
            v-if="!hidePin"
            class="h-5 w-5 icon-hover"
            :float="isPin ? '解除固定面板' : '固定面板'"
            @click="togglePin"
          >
            <div
              class="h-5 w-5"
              :class="[
                isPin ? 'i-custom-pin text-primary' : 'i-custom-pin-off',
              ]"
            ></div>
          </div>
          <div
            class="ml-2 h-5 w-5 icon-hover"
            float="关闭"
            @click="showPanel = false"
          >
            <div class="i-custom-x h-5 w-5"></div>
          </div>
        </div>
      </div>
      <div class="h-full overflow-y-auto">
        <slot />
      </div>
    </div>
    <div
      v-if="modal"
      class="modal absolute top-0 h-full w-500 bg-[rgba(0,0,0,0.30)] -left-500"
      @click="showPanel = false"
    ></div>
  </div>
</template>
<script>
import { getZIndex } from '@utils'
export default {
  name: 'LCPanel',
  props: [
    'pin',
    'placement',
    'title',
    'value',
    'hidePin',
    'offset',
    'hideHeader',
    'icon',
    'modal',
  ],
  data() {
    return {
      showPanel: this.value,
      isPin: this.pin,
      zIndex: getZIndex(),
    }
  },
  computed: {
    isPinClass() {
      let placement = this.placement || 'left'
      return !this.isPin
        ? 'absolute ' +
            (this.offset ? '' : placement === 'left' ? 'left-10' : 'right-0')
        : ''
    },
    computedOffset() {
      let placement = this.placement || 'left'
      const [x, y] = this.offset || [0, 0]
      if (placement === 'right') {
        return {
          right: x + 'px',
          top: y + 'px',
          zIndex: this.zIndex,
        }
      }
      return {
        left: x + 'px',
        top: y + 'px',
        zIndex: this.zIndex,
      }
    },
  },
  watch: {
    value(v) {
      this.isPin = this.pin
      this.showPanel = v
    },
    pin(v) {
      this.isPin = v
    },
    isPin(v) {
      this.$emit('update:pin', v)
    },
    showPanel(v) {
      this.$emit('input', v)
      if (v) {
        this.$emit('show')
      } else {
        this.$emit('hide')
      }
    },
  },
  methods: {
    togglePin() {
      this.isPin = !this.isPin
    },
    show() {
      this.showPanel = true
      this.zIndex = getZIndex()
    },
    hide() {
      this.showPanel = false
    },
  },
}
</script>
