<template>
  <div :style="styleSize" :class="[disabled ? 'icon-disabled' : 'icon-hover']">
    <div
      :style="styleSizeDot8"
      :class="[icon]"
      :float="float"
      @click="$emit('click')"
    ></div>
  </div>
</template>

<script>
export default {
  name: 'LCIcon',
  props: ['icon', 'disabled', 'size', 'float'],
  computed: {
    styleSize() {
      const size = this.size || 24
      return {
        width: size + 'px',
        height: size + 'px',
      }
    },
    styleSizeDot8() {
      const size = this.size || 24
      return {
        width: size * 0.8 + 'px',
        height: size * 0.8 + 'px',
      }
    },
  },
}
</script>
