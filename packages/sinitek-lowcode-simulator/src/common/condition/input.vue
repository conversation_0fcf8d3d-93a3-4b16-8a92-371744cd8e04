<template>
  <el-input-number
    v-if="isNumber"
    v-model="currentValue"
    style="width: 100px"
    clearable
  ></el-input-number>
  <el-switch
    v-else-if="isBool"
    v-model="currentValue"
    style="width: 100px"
    clearable
  ></el-switch>
  <el-date-picker
    v-else-if="isDate"
    v-model="currentValue"
    style="width: 130px"
    clearable
  ></el-date-picker>
  <el-time-select
    v-else-if="isTime"
    v-model="currentValue"
    style="width: 100px"
    clearable
  ></el-time-select>
  <el-date-picker
    v-else-if="isDateTime"
    v-model="currentValue"
    style="width: 182px"
    type="datetime"
    clearable
  ></el-date-picker>
  <el-input
    v-else
    v-model="currentValue"
    style="width: 100px"
    clearable
  ></el-input>
</template>

<script>
export default {
  name: 'LCConditionInput',
  props: {
    isDateTime: Boolean,
    isTime: <PERSON>olean,
    isDate: Boolean,
    isNumber: Boolean,
    isString: Boolean,
    isBool: <PERSON>ole<PERSON>,
    value: null,
  },
  data() {
    return {
      currentValue: this.value,
    }
  },
  watch: {
    value(v) {
      this.currentValue = v
    },
    currentValue(v) {
      this.$emit('input', v)
    },
  },
}
</script>
