<template>
  <div class="mb-4 w-full flex rounded bg-#f7f8fa px-2 py-1">
    <div class="drag-handler"></div>
    <el-form ref="form" :model="currentCondition" :inline="true">
      <el-form-item
        prop="field"
        style="margin-bottom: 0"
        :rules="[{ required: true, message: '请选择字段', trigger: 'blur' }]"
      >
        <el-cascader
          ref="cascader"
          style="width: 100px"
          :value="field"
          :options="fields"
          @input="onFieldChange"
          @change="cleanValue"
        ></el-cascader>
      </el-form-item>
      <template v-if="currentCondition.field">
        <el-form-item
          prop="op"
          style="margin-bottom: 0"
          :rules="[
            { required: true, message: '请选择操作符', trigger: 'blur' },
          ]"
        >
          <xn-select
            v-model="currentCondition.op"
            style="width: 100px"
            :options="getOp"
            @change="cleanValue"
          >
          </xn-select>
        </el-form-item>

        <el-form-item v-if="!isOpAndNull" style="margin-bottom: 0">
          <SelectExpression v-model="componentIndex" />
        </el-form-item>

        <el-form-item
          v-if="!isOpAndNull"
          prop="value"
          :class="[isOpAndArray ? 'w-full' : '']"
          style="margin-bottom: 0"
          :rules="[{ required: true, message: '请输入值', trigger: 'blur' }]"
        >
          <template v-if="componentIndex === Status.static">
            <div v-if="isOpAndArray || isOpAndBetween" mt-3 w-full flex>
              <div
                v-if="isOpAndArray"
                class="i-mdi:add mr-1 mt-1 size-5 flex-shrink-0"
                @click="onAddItem"
              ></div>
              <div flex="~ wrap">
                <ConditionInput
                  v-for="(item, i) of currentValue"
                  :key="i"
                  v-model="currentValue[i]"
                  mb-2
                  mr-2
                  :is-date-time="isDateTime"
                  :is-date="isDate"
                  :is-time="isTime"
                  :is-number="isNumber"
                  :is-string="isString"
                  :is-bool="isBool"
                />
              </div>
            </div>
            <ConditionInput
              v-else
              v-model="currentValue"
              :is-date-time="isDateTime"
              :is-date="isDate"
              :is-time="isTime"
              :is-number="isNumber"
              :is-string="isString"
              :is-bool="isBool"
            />
          </template>
          <!-- <ElInput
            v-model="currentValue"
            style="width: 100px"
            placeholder="输入表达式"
            v-if="componentIndex === Status.expression"
          >
            <span slot="prefix" v-text="'{{'"></span>
            <span slot="suffix" v-text="'}}'"></span>
          </ElInput> -->
          <ExpressionEditor
            v-if="componentIndex === Status.expression"
            v-model="currentValue"
            style="width: 150px"
            size="small"
          />
        </el-form-item>
      </template>
    </el-form>
    <div
      class="i-material-symbols-light:delete-outline-rounded ml-auto mt-1 size-5 flex-shrink-0"
      @click.stop="onRemove"
    ></div>
  </div>
</template>

<script>
import {
  JSExpression,
  Op,
  ComponentName,
  ExpressionEditor,
} from 'sinitek-lowcode-shared'
import SelectExpression, { Status } from '../select-expression.vue'
import ConditionInput from './input'
export default {
  name: 'LCConditionItem',
  components: {
    SelectExpression,
    ConditionInput,
    ExpressionEditor,
  },
  inject: ['fetcher', '$doc'],
  props: {
    value: Object,
    fields: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      currentCondition: this.value,
      options: [],
      currentValue: this.value?.value,
      componentIndex: 0,
      Status,
    }
  },
  computed: {
    isOpAndNull() {
      return [Op.IS_NULL, Op.IS_NOT_NULL].includes(this.currentCondition.op)
    },
    isOpAndArray() {
      return [Op.IN, Op.NOT_IN, Op.NOT_LIKE].includes(this.currentCondition.op)
    },
    isOpAndBetween() {
      return [Op.BETWEEN].includes(this.currentCondition.op)
    },
    getOp() {
      const numberOP = [
        { label: '大于', value: Op.GT },
        { label: '大于等于', value: Op.GE },
        { label: '小于', value: Op.LT },
        { label: '小于等于', value: Op.LE },
      ]
      const stringOP = [
        { label: '包含', value: Op.LIKE },
        { label: '包含其中一项', value: Op.OR_LIKE },
        { label: '不包含', value: Op.NOT_LIKE },
        { label: '以...开头', value: Op.LEFT_LIKE },
        { label: '以...结尾', value: Op.RIGHT_LIKE },
      ]

      const op = [
        { label: '等于', value: Op.EQ },
        { label: '不等', value: Op.NE },
        { label: '为null', value: Op.IS_NULL },
        { label: '不为null', value: Op.IS_NOT_NULL },
      ]
      const array = [
        { label: '在...中', value: Op.IN },
        { label: '不在...中', value: Op.NOT_IN },
      ]
      const between = { label: '范围', value: Op.BETWEEN }
      if (this.isString) {
        op.push(...stringOP)
        // op.push(...array)
      } else if (this.isNumber) {
        op.push(...numberOP)
        op.push(between)
      } else if (this.isDate || this.isDateTime || this.isTime) {
        op.push(...numberOP)
        // op.push(...array)
        op.push(between)
      }
      op.push(...array)
      return op
    },
    field() {
      return this.currentCondition.field.split('.')
    },
    isBool() {
      return ['SWITCH'].includes(this.getComponentType)
    },
    isString() {
      return [
        ComponentName.TEXT,
        ComponentName.INPUT,
        ComponentName.TEXTAREA,
        ComponentName.COLOR,
        ComponentName.CHECKBOX,
        ComponentName.RADIO_GROUP,
        ComponentName.CHECKBOX_GROUP,
      ].includes(this.getComponentType)
    },
    isNumber() {
      return [
        ComponentName.NUMBER,
        ComponentName.MONEY,
        ComponentName.RATE,
        ComponentName.SLIDER,
      ].includes(this.getComponentType)
    },
    isArray() {
      return [ComponentName.SELECT].includes(this.getComponentType)
    },
    isDate() {
      return [ComponentName.DATE].includes(this.getComponentType)
    },
    isDateTime() {
      return [ComponentName.DATETIME].includes(this.getComponentType)
    },
    isTime() {
      return [ComponentName.TIME].includes(this.getComponentType)
    },
    getComponentType() {
      const field = this.field.slice()
      let list = this.fields.slice()
      let result = ComponentName.TEXT
      while (field && field.length > 0) {
        const val = field.shift()
        const f = list.find((item) => item.value === val)
        if (f) {
          list = f.children
          result = f.data.componentType
        }
      }
      return result
    },
  },
  watch: {
    currentValue(v) {
      if (this.componentIndex === Status.expression) {
        this.currentCondition.value = {
          type: JSExpression,
          value: v,
        }
      } else {
        this.currentCondition.value = v
      }
    },
    value: {
      immediate: true,
      handler(v) {
        if (v.value?.type === JSExpression) {
          this.componentIndex = Status.expression
          this.currentValue = v.value.value
        } else {
          this.componentIndex = Status.static
          this.currentValue = v.value
        }
        this.currentCondition = v
      },
    },
    componentIndex() {
      this.cleanValue()
    },
    currentCondition: {
      deep: true,
      handler(v) {
        this.$emit('input', v)
      },
    },
  },
  methods: {
    onRemove() {
      this.$emit('remove')
    },
    onFieldChange(v) {
      this.currentCondition.field = v.join('.')
    },
    getDefaultValue() {
      let val = ''
      if (this.isBool) {
        val = false
      }
      return val
    },
    cleanValue() {
      let val = this.getDefaultValue()
      if (
        (this.isOpAndArray || this.isOpAndBetween) &&
        this.componentIndex === Status.static
      ) {
        this.currentValue = this.isOpAndBetween ? [val, val] : [val]
      } else {
        this.currentValue = val
      }
      this.clearValidate()
    },
    onToggle() {
      this.componentIndex = (this.componentIndex + 1) % 2
    },
    validate(cb) {
      this.$refs.form.validate(cb)
    },
    clearValidate() {
      this.$refs.form.clearValidate()
    },
    onAddItem() {
      this.currentValue.push(this.getDefaultValue())
    },
  },
}
</script>
