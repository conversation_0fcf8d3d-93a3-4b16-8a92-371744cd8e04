<template>
  <div class="relative w-full flex pl-12">
    <!-- 线条 -->
    <div class="absolute bottom-0 left-6 top-0 w-2px bg-primary -ml-1px"></div>
    <div class="absolute left-0 top-1/2 w-12 -mt-3">
      <!-- <el-popover
        placement="bottom"
        popper-class="min-w-8 p-0"
        style="min-width: 20px"
        trigger="manual"
        v-model="visible"
      >
        <div class="w-full cursor-pointer">
          <span
            class="f-c-c w-full h-6 fs-12 mx-a"
            :class="{ 'text-primary': item[0] === type }"
            v-for="item of types"
            :key="item[0]"
            @click="changeType(item[0])"
            >{{ item[1] }}</span
          >
        </div> -->
      <span
        slot="reference"
        class="mx-a h-6 w-6 f-c-c cursor-pointer rounded bg-primary color-white fs-12"
        @click="visible = true"
        >{{ getType() }}</span
      >
      <!-- </el-popover> -->
    </div>
    <div class="w-full">
      <!-- list -->
      <template v-for="(condition, index) in currentConditions">
        <template v-if="!isGroup(condition)">
          <LCConditionItem
            :key="index"
            ref="item"
            v-model="currentConditions[index]"
            :fields="fields"
            @remove="onRemove(index)"
          />
        </template>
        <template v-else>
          <LCConditionGroup
            :key="index"
            ref="item"
            v-model="currentConditions[index]"
            has-remove
            :fields="fields"
            @remove="onRemove(index)"
          />
        </template>
      </template>
      <template v-if="!currentConditions.length">
        <div class="mb-4 bg-#f7f8fa px-2 fs-12">空</div>
      </template>
      <!-- 操作 -->
      <div class="flex cursor-pointer p-b-4 text-primary fs-14">
        <div class="" @click="addCondition">添加条件</div>
        <!-- <div class="ml-5" @click="addConditions">添加条件组</div> -->
        <!-- <div class="ml-5" v-if="hasRemove" @click="removeConditions">
          删除组
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import isPlainObject from 'lodash/isPlainObject'
import LCConditionItem from './item.vue'
import { getConditionItem } from 'sinitek-lowcode-shared'

const map = {
  and: '且',
  or: '或',
}
export default {
  name: 'LCConditionGroup',
  components: {
    LCConditionItem,
  },
  props: {
    // "field": "stockQuantity",
    // "op": ">",-
    // "value": 0
    value: Object,
    hasRemove: Boolean,
    fields: Array,
  },
  data() {
    return {
      visible: false,
    }
  },
  computed: {
    // and or
    type() {
      return Object.keys(this.value)[0]
    },
    currentConditions: {
      get: function () {
        return this.value[this.type] || []
      },
      set: function (v) {
        this.currentValue = v
        this.$emit('input', { [this.type]: v })
      },
    },
    types() {
      return Object.entries(map)
    },
  },
  methods: {
    isPlainObject,
    getItems(obj) {
      return obj['or'] || obj['and']
    },
    getType() {
      return map[this.type]
    },
    isGroup(obj) {
      return !!this.getItems(obj)
    },
    addCondition() {
      this.currentConditions.push(getConditionItem('', '', ''))
    },
    addConditions() {
      this.currentConditions.push({
        and: [getConditionItem('', '', '')],
      })
    },
    removeConditions() {
      this.$emit('remove')
    },
    onRemove(index) {
      this.currentConditions.splice(index, 1)
    },
    changeType(type) {
      this.$emit('input', { [type]: this.value[this.type] })
      this.visible = false
    },
    validate(fn) {
      let valid = true
      this.$refs.item?.forEach?.((e) => {
        e.validate((v) => {
          if (v === false) valid = false
        })
      })
      return fn(valid)
    },
    clearValidate(props) {
      this.$refs.item.forEach((e) => {
        e.clearValidate(props)
      })
    },
    // onGroupInput(index) {
    //   return (v) => {
    //     const key = Object.keys(this.currentConditions[index])[0]
    //     this.currentConditions[index][key] = v
    //   }
    // },
  },
}
</script>

<style lang="scss" scoped></style>
