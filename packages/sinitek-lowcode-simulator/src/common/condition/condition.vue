<template>
  <div class="w-full overflow-hidden bg-white">
    <LCConditionGroup
      ref="group"
      v-model="currentCondition"
      :fields="fields"
    ></LCConditionGroup>
  </div>
</template>

<script>
import LCConditionGroup from './group.vue'
export default {
  name: 'LCCondition',
  components: {
    LCConditionGroup,
  },
  props: {
    // cascader 的options格式
    fields: Array,
    value: {
      type: Object,
      default: () => {
        return {
          and: [],
        }
      },
    },
  },
  computed: {
    currentCondition: {
      get: function () {
        return this.value
      },
      set: function (v) {
        this.$emit('input', v)
      },
    },
  },
  methods: {
    validate(fn) {
      this.$refs.group.validate(fn)
    },
    clearValidate(fn) {
      this.$refs.group.clearValidate(fn)
    },
  },
}
</script>
