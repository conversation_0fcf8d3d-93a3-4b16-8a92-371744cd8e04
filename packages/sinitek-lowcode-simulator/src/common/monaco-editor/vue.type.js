export default `
interface Vue {
  $route: {
    /**
     * 字符串，对应当前路由的路径，总是解析为绝对路径，如 "/foo/bar"
    */
    name: string
    /**
     * 一个 key/value 对象，包含了动态片段和全匹配片段，如果没有路由参数，就是一个空对象
    */
    params: Record<string, string>
    /**
     * 一个 key/value 对象，表示 URL 查询参数。例如，对于路径 /foo?user=1，则有 $route.query.user == 1，如果没有查询参数，则是个空对象。
    */
    query: Record<string, string>
    /**
     * 当前路由的 hash 值 (带 #) ，如果没有 hash 值，则为空字符串。
    */
    hash:string
    /**
     * 完成解析后的 URL，包含查询参数和 hash 的完整路径。
    */
    fullPath: string
    /**
     * 一个数组，包含当前路由的所有嵌套路径片段的路由记录 。路由记录就是 routes 配置数组中的对象副本 (还有在 children 数组)
    */
    matched: any[]
    /**
     * 如果存在重定向，即为重定向来源的路由的名字
    */
    redirectedFrom: string
  }
  $router: {
    push: (location: string) => Promise<void>
    replace: (location: string) => Promise<void>
    back: () => void
    go: (index: number) => void
    forward: () => void
  }
}
`
