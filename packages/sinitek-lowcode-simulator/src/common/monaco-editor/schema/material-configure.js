import * as monaco from 'monaco-editor'
const schema = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  type: 'object',
  properties: {
    component: {
      type: 'object',
      description: '组件能力配置',
      properties: {
        isContainer: {
          type: 'boolean',
          description: '是否为容器组件',
        },
        isModal: {
          type: 'boolean',
          description: '是否为模态框',
        },
        clickCapture: {
          type: 'boolean',
          description:
            '是否需要捕获点击，用在模拟器上。所有组件默认是不能点击的，需要点击时设置false，例如tabs组件',
        },
        clickCaptureBlackList: {
          type: 'array',
          description:
            '设置了clickCapture为false，会导致组组件内的所有元素都可以交互，单独设置拥有className的元素不可以交互',
          items: {
            type: 'string',
          },
        },
        nestingRule: {
          type: 'object',
          description: '嵌套规则',
          properties: {
            childWhitelist: {
              type: 'array',
              description: '允许的子组件白名单',
              items: {
                type: 'string',
              },
            },
            childBlacklist: {
              type: 'array',
              description: '禁止的子组件黑名单',
              items: {
                type: 'string',
              },
            },
            parentWhitelist: {
              type: 'array',
              description: '允许的父组件白名单',
              items: {
                type: 'string',
              },
            },
            parentBlacklist: {
              type: 'array',
              description: '禁止的父组件黑名单',
              items: {
                type: 'string',
              },
            },
            allowInsert: {
              type: 'object',
              description: '返回true可以添加组件',
            },
          },
        },
        rootSelector: {
          type: 'string',
          description: '组件选中框的cssSelector',
        },
        modalVisibleProp: {
          type: 'string',
          description: '模态框显示属性',
        },
        modelProps: {
          type: 'object',
          description: '模态框属性',
        },
        getAIParams: {
          type: 'object',
          description: '获取ai参数',
        },
        setAIParams: {
          type: 'object',
          description: '设置ai参数',
        },
        disableBehaviors: {
          description: '禁用的行为',
          type: 'array',
          items: {
            type: 'string',
            enum: ['*', 'copy', 'move', 'remove'],
          },
        },
        childWrapRenderer: {
          description: '子组件是否需要包裹渲染',
          type: 'boolean',
        },
        useWrap: {
          type: 'boolean',
          description: '是否使用包裹组件',
        },
        wrapType: {
          description: '包裹组件类型',
          type: 'string',
          enum: ['inline', 'block'],
        },
        placeholder: {
          type: 'object',
          description: '当组件拖入时没有宽高，导致组件无法选中时使用',
          properties: {
            width: {
              type: 'string',
              description: '宽度',
            },
            height: {
              type: 'string',
              description: '高度',
            },
            text: {
              type: 'string',
              description: '文本',
            },
          },
        },
      },
    },
    supports: {
      type: 'object',
      description: '通用扩展配置能力支持性',
      properties: {
        condition: {
          type: 'boolean',
          description: '支持展示条件设置',
        },
        style: {
          type: 'boolean',
          description: '支持样式设置',
        },
        supportBindState: {
          type: 'boolean',
          description: '设置为false，则不支持绑定状态管理的值',
        },
        events: {
          type: 'array',
          description: '事件',
          items: {
            type: 'object',
            description: '事件配置',
            properties: {
              name: {
                type: 'string',
                description: '事件名称',
              },
              description: {
                type: 'string',
                description: '事件描述',
              },
              template: {
                type: 'string',
                description: '事件模板',
              },
            },
          },
        },
        methods: {
          type: 'array',
          description: '可调用方法，主要用于js提示',
          items: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
                description: '方法名称',
              },
              description: {
                type: 'string',
                description: '方法描述',
              },
              example: {
                type: 'string',
                description: '方法示例',
              },
              type: {
                type: 'string',
                description: '不是函数时设置，例如string或number',
              },
              returnType: {
                type: 'string',
                description: '返回值类型',
              },
              args: {
                type: 'array',
                description: '参数类型说明',
                items: {
                  type: 'object',
                  properties: {
                    name: {
                      type: 'string',
                      description: '参数名称',
                    },
                    type: {
                      type: 'string',
                      description: '参数类型',
                    },
                    description: {
                      type: 'string',
                      description: '参数描述',
                    },
                    required: {
                      type: 'boolean',
                      description: '参数是否必填',
                    },
                  },
                },
              },
            },
          },
        },
        states: {
          type: 'array',
          description: '状态字段',
          items: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
                description: '字段名称',
              },
              description: {
                type: 'string',
                description: '字段描述',
              },
              example: {
                type: 'string',
                description: '字段例子',
              },
              type: {
                type: 'string',
                description: '字段类型',
              },
            },
          },
        },
      },
    },
    advanced: {
      type: 'object',
      description: '高级配置',
      properties: {
        callbacks: {
          type: 'object',
          description: '配置callbacks可捕获引擎抛出的一些事件',
          properties: {
            onNodeAdd: {
              type: 'object',
              description: '在容器中拖入组件时触发的事件回调',
            },
          },
        },
      },
    },
    design: {
      type: 'object',
      description: '针对设计区域的设置',
      properties: {
        excludeProps: {
          type: 'array',
          description: '设计时，不应用props到设计区域',
          items: {
            type: 'string',
          },
        },
        excludeAttrs: {
          type: 'array',
          description: '设计时，不应用attrs到设计区域',
          items: {
            type: 'string',
          },
        },
      },
    },
  },
}

export const register = () => {
  return monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
    validate: true,
    schemas: [
      {
        uri: 'http://json-schema.org/draft-07/schema',
        fileMatch: ['*'],
        schema,
      },
    ],
  })
}
