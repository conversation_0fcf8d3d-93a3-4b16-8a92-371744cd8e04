import { string2AST } from '@utils'
import * as monaco from 'monaco-editor'
import { getAPI } from 'sinitek-lowcode-shared'
import { Message } from 'element-ui'
let codeLens
let findComponent

function getMethods(value) {
  let methods = []
  const ast = string2AST(value)
  ast.program.body.forEach((e) => {
    if (e.type === 'ClassDeclaration') {
      methods = e.body.body.filter((e) => e.type === 'ClassMethod')
    }
  })
  return methods
}

function hasMethod(data, method) {
  if (!data) return false
  const check = (data) => {
    if (Array.isArray(data)) {
      return data.some((item) => check(item))
    } else if (typeof data === 'object' && data !== null) {
      return Object.values(data).some((item) => check(item))
    } else if (typeof data === 'string') {
      return data.includes(`this.methods.${method}.apply`)
    }
    return false
  }
  return check(data)
}

function findUseMethod(schema, method) {
  const finds = new Set()
  const _find = (data) => {
    data.children.forEach((e) => {
      if (hasMethod(e.events, method) || hasMethod(e.props, method)) {
        finds.add(e.id)
      }
      if (e?.children?.length) {
        _find(e)
      }
    })
  }
  _find(schema)
  return finds
}
let methods = []
export function registryCodeLens(editor, $doc) {
  dispose()
  findComponent = editor.addCommand(0, function (_, methodName) {
    const schema = $doc.getSchema(true)
    const finds = findUseMethod(schema, methodName)
    if (finds.size) {
      getAPI().openPanel('Tree', [...finds])
    } else {
      Message.warning('未找到使用该方法的组件')
    }
  })
  codeLens = monaco.languages.registerCodeLensProvider('javascript', {
    provideCodeLenses: function (model) {
      try {
        methods = getMethods(model.getValue()).map((e) => {
          return {
            range: {
              startColumn: e.loc.start.column,
              startLineNumber: e.loc.start.line,
              endLineNumber: e.loc.start.line + 1,
              endColumn: e.loc.start.column,
            },
            id: 'findComponent',
            command: {
              id: findComponent,
              title: '查找使用的组件',
              arguments: [e.key.name],
            },
          }
        })
      } catch (e) {
        if (process.env.NODE_ENV === 'development') {
          console.error(e, '333')
        }
      }
      return {
        lenses: methods,
        dispose: () => {},
      }
    },
    resolveCodeLens: function (model, codeLens) {
      return codeLens
    },
  })
  return {
    dispose,
  }
}

function dispose() {
  findComponent?.dispose?.()
  codeLens?.dispose?.()
  codeLens = null
  findComponent = null
}
