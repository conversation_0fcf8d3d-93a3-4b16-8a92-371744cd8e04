type getSetterType<T> = T extends 'StringSetter' ? string : number

type MaterialProps<T extends {key: string,setter: string}[]> = {
  [K in keyof T]: {[K2 in keyof T[K] as K2 extends 'key' ? T[K][K2]: never]: getSetterType<T[K]['setter']>}
}

// 使用映射类型和条件类型将数组转换为交叉类型
type UnionToIntersection<U> = 
  (U extends any ? (x: U) => any : never) extends 
    (x: infer R) => any ? R : never;

type ArrayToIntersection<T extends readonly any[]> = 
    UnionToIntersection<T[number]>;

type ArrayToUnion<T extends readonly any[]> = T[number]

type Props<T> = ArrayToIntersection<MaterialProps<T>>