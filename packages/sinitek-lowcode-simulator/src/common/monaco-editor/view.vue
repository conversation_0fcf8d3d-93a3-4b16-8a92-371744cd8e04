<template>
  <fullscreenContainer :fullscreen.sync="fullScreen">
    <div ref="el" class="wh-full"></div>
  </fullscreenContainer>
</template>

<script>
import * as monaco from 'monaco-editor'
import fullscreenContainer from '../fullscreen-container'
export default {
  name: 'MonacoEditorView',
  components: {
    fullscreenContainer,
  },
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    language: {
      type: String,
      default: 'json',
    },
    modelValue: String,
    options: Object,
  },
  data() {
    return {
      markers: [],
      fullScreen: false,
    }
  },
  watch: {
    language() {
      this.editor?.dispose()
      this.init()
    },
    fullScreen() {
      this.$nextTick(() => {
        this.editor.layout()
      })
    },
  },
  mounted() {
    this.$emit('mounted')
    this.init()
  },
  beforeDestroy() {
    this.$emit('unmount')
    this?.editor?.dispose()
  },
  methods: {
    setFullScreen(fullScreen) {
      this.fullScreen = fullScreen
    },
    init() {
      const editor = (this.editor = monaco.editor.create(this.$refs.el, {
        value: this.modelValue,
        language: this.language,
        minimap: {
          enabled: false,
        },
        formatOnPaste: true,
        ...this.options,
        fixedOverflowWidgets: true, // 解决overflow: hidden 导致弹框提示截断问题
      }))
      editor.onDidFocusEditorText(() => {
        this.$emit('focus')
      })
      editor.onDidChangeCursorPosition(() => {
        this.$emit('focus')
      })
      this.editor.onDidChangeModelContent(() => {
        const val = this.editor.getValue()
        if (this.modelValue !== val) {
          this.$emit('update:modelValue', val)
          this.$emit('input', val)
        }
      })
      this.editor.onDidBlurEditorText(() => {
        const val = this.editor.getValue()
        if (this.modelValue !== val) {
          // 过滤css空内容的情况
          const markers = this.markers.filter(
            (e) => e.source !== 'css' && e.code !== 'emptyRules'
          )
          if (!markers.length) {
            this.$emit('change', val)
          }
        }
      })
      // 获取错误提示
      monaco.editor.onDidChangeMarkers(([uri]) => {
        const markers = monaco.editor.getModelMarkers({
          resource: uri,
        })
        this.markers = markers
        if (markers.length) {
          this.$emit(
            'error',
            markers.map(
              ({
                message,
                startLineNumber,
                startColumn,
                endLineNumber,
                endColumn,
              }) =>
                `${message} [${startLineNumber}:${startColumn}-${endLineNumber}:${endColumn}]`
            )
          )
        } else {
          this.$emit('error', [])
        }
      })
    },
    refresh() {
      // 刷新monaco-editor大小
      this.$nextTick(() => {
        this.editor.layout()
      })
    },
    getValue() {
      return this.editor.getValue()
    },
  },
}
</script>
<style lang="scss">
.monaco-editor {
  outline: none;
}
</style>
