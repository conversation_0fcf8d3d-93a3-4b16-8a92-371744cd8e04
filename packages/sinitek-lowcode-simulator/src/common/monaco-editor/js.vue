<template>
  <XView
    v-bind="$attrs"
    ref="view"
    class="h-full w-full"
    language="javascript"
    v-on="$listeners"
  />
</template>

<script>
import XView from './view.vue'
import { registryCodeLens } from './codeLens.js'
import * as monaco from 'monaco-editor'
import {
  DatasourceType,
  camelize,
  Op,
  getCtxUtils,
} from 'sinitek-lowcode-shared'
import VueType from './vue.type.js'
import LowcodeType from './lowcode.d.ts'

const cacheMethods = {}

function getRefSuggestions(sup) {
  if (!sup?.componentName) return
  let componentName = camelize(sup?.componentName)
  if (!cacheMethods[componentName]) {
    const methods = sup?.configure?.supports?.methods ?? []
    const states = sup?.configure?.supports?.states ?? []
    cacheMethods[componentName]
    cacheMethods[componentName] = [...methods, ...states]
  }
  return cacheMethods[componentName]
}

function generateMethodInterface() {
  if (Object.keys(cacheMethods).length === 0) {
    return ''
  }
  let result = []
  for (const componentName in cacheMethods) {
    const name = camelize(componentName)
    result.push(`interface ${name} {`)
    const methods = cacheMethods[componentName]
    if (methods) {
      for (const method of methods) {
        result.push(`/** `)
        result.push(`* ${method.description}`)
        if (method.args) {
          for (const arg of method.args) {
            result.push(
              `* @param ${arg.type ? `{${arg.type}}` : ''} ${arg.name} ${arg.description}`
            )
          }
        }
        if (method.example) {
          result.push(`* @example ${method.example}`)
        }
        result.push(`*/`)
        if (method.type) {
          result.push(`${method.name}: ${method.type || 'any'}`)
        } else {
          result.push(
            `${method.name}: (${(method.args || []).map((e) => [`${e.name}${e.required === false ? '?' : ''}`, e.type].join(':'))}) => ${method?.returnType ?? 'any'}`
          )
        }
      }
    }
    result.push('}')
  }
  return result.join('\n')
}

export default {
  name: 'MonacoEditorJS',
  components: {
    XView,
  },
  inject: ['$doc'],
  mounted() {
    this.editor = this.$refs.view.editor
    monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
      allowNonTsExtensions: true,
      checkJs: true, // 启用 JavaScript 的类型检查
      jsx: monaco.languages.typescript.JsxEmit.Preserve,
      target: monaco.languages.typescript.ScriptTarget.Latest,
      allowJs: true,
    })

    monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
      noSemanticValidation: false,
      noSyntaxValidation: false,
    })
    this.addExtraLib()
  },
  beforeDestroy() {
    this.dispose()
  },
  methods: {
    addExtraLib() {
      this.dispose()
      const schema = this.$doc.getSchema(true)
      // 低代码----start----
      const _props = []
        .concat(schema?.material?.props ?? [])
        .concat(schema?.material?.reflect ?? [])
      const hasProps = _props.length > 0
      const events = schema?.material?.configure?.supports?.events || []
      // 低代码----end----

      // const hasMainModel = !!schema?.mainModel?.modelCode
      const _datasource = schema.datasource || []

      // if (hasMainModel) {
      //   _datasource.push({ id: 'main', type: DatasourceType.MODEL })
      // }
      const fetcher = _datasource.map((ds) => {
        if (ds.type === DatasourceType.MODEL) {
          return `
        /** 模型数据源  ${ds.id} ${ds.desc ? `(${ds.desc})` : ''}*/
        ${ds.id}: ModelFetcher`
        }
        return `
      /** 自定义数据源 ${ds.id} ${ds.desc ? `(${ds.desc})` : ''} */
      ${ds.id}: BaseFetcher`
      })
      // fetcher.push('[key: string]: BaseFetcher')
      const datasource = _datasource.map((ds) => {
        return `
      /** 数据源 ${ds.id} ${ds.desc ? `(${ds.desc})` : ''}*/
      ${ds.id}: any`
      })
      // datasource.push('[key: string]: any')
      const methods = Object.keys(schema.methods || {}).map((key) => {
        return `
      /** ${key} */
      ${key}: Function`
      })
      // methods.push('[key: string]: Function')
      const refs = Object.values(this.$doc.node.nodes || {}).map((value) => {
        const s = value.node
        const type = getRefSuggestions(this.$doc.getMaterial(s.componentName))
        return `
      /**
       * ${s.componentName} */
      ${s.ref}: ${type ? camelize(s.componentName) : 'any'}`
      })
      // refs.push('[key: string]: any')

      const utils = Object.entries(getCtxUtils()).map(([k, v]) => {
        const params = Array.from(v)
          .map((_, i) => `arg${i}: any`)
          .join(',')
        return `
      /** ${k} */
      ${k}: (${params}) => any`
      })
      // utils.push('[key: string]: Function')
      // this.addAction()
      const content = `
      ${VueType}
      ${LowcodeType}

      ${generateMethodInterface()}
          const schemaState = ${JSON.stringify(schema.state || {})}
          type StateTypeArrayToAny<T> = {
            [K in keyof T]: T[K] extends never[] ? any[]: T[K]
          }
          /** 
           * 全局状态 
          */
          type State = StateTypeArrayToAny<typeof schemaState> 
          /** 
           * 数据源控制
          */
          interface Fetcher { 
            ${fetcher.join(';')}
          }
          /** 
           * 数据源
          */
          interface Datasource { 
            ${datasource.join(';')}
          }

          /** 基础数据源 */
          interface BaseFetcher {
            /** 发送请求 */
            send: (params?: Object) => Promise<any>
          }
          interface ModelFetcher 
          {
            /** 
             * 获取列表数据
             * @param searchParam {ListParam} 查询条件
             * @example
             * this.fetcher.model.list({pageIndex: 1, pageSize: 20, condition: {and: [{field: 'name', op: 'eq', value: '张三'}]}}, filterFields: ['id', 'name'], orderName: 'name', orderType: 'desc', orders: ['order desc'], {showLoading: false, loadingOptions: {fullscreen: true, text: 'loading', background: 'blue', customClass: 'class'}})
            *  */
            list: (searchParam?: ListParam, otherOption?: OtherParam) => Promise<any>
            /** 获取单条数据 */
            single: (opts?: {id:string}, otherOption?: OtherParam) => Promise<any>
            /** 创建数据 */
            create: (data?: {data: Object, draftFlag?: boolean}, otherOption?: OtherParam) => Promise<any>
            /** 更新数据 */
            update: (data?: {data: Object, draftFlag?: boolean}, otherOption?: OtherParam) => Promise<any>
            /** 删除数据 */
            delete: (opts?: ({id: string, ids?: never} | {ids: string[], id?: never}) & OtherParam)?, otherOption?: OtherParam) => Promise<any>
            /** 
             * 懒加载获取列表数据，会将关联表单的完整数据获取到，不会递归获取
             * @param searchParam {ListParam} 查询条件
             * this.fetcher.model.lazyList({pageIndex: 1, pageSize: 20, condition: {and: [{field: 'name', op: 'eq', value: '张三'}]}}, filterFields: ['id', 'name'], orderName: 'name', orderType: 'desc', orders: ['order desc'], {showLoading: false, loadingOptions: {fullscreen: true, text: 'loading', background: 'blue', customClass: 'class'}})
            *  */
            lazyList: (searchParam?: ListParam, otherOption?: OtherParam) => Promise<any[]>
            /** 
             * 懒加载获取单条数据，会将关联表单/子表单的完整数据获取到，不会递归获取
            *  */
            lazySingle: (opts?: {id:string}, otherOption?: OtherParam) => Promise<any>
          }
          interface ConditionItem {
            /** 字段名称 */
            field: string
            /** 操作类型 */
            op:${Object.values(Op)
              .map((e) => `'${e}'`)
              .join(' | ')}
            /** 值 */
            value: string | {type: 'JSExpression', value: string}
          }
          interface ListParam {
            /** 搜索条件，默认使用数据源配置时的条件。非必要不要直接设置condition。 */
            condition?: {and: ConditionItem[]}
            /** 当前分页页数 */
            pageIndex?: number
            /** 当前页面展示的数据条数 */
            pageSize?: number
            /** 排序字段 */
            filterFields?: string[]
            /** 排序属性名称 */
            orderName?: string
            /** 排序类型，asc-升序，desc-降序 */
            orderType?: 'asc' | 'desc'
            /** 多值排序 */
            orders?: string[]
          }
          interface OtherParam {
            /** 是否显示加载时的圆圈，默认 false 不显示. */
            showLoading?: boolean
            /** 支持ElemenUI的所有loading参数. */
            loadingOptions?: Object
          }
          interface Refs {
            ${refs.join(';')}
          }
            /** 
               * 全局状态 
              */
            declare var state: State
            declare var fetcher:Fetcher
            declare var datasource:Datasource
            declare var methods:{
              ${methods.join(',')}
            }
            declare var refs: Refs
            declare var utils:{
            ${utils.join(',')}
            }
            /** 插槽参数 */
            declare var scope: any
            ${Object.entries(schema.state || {})
              .map(([key, value]) => {
                return `
                  const temp_${key} = ${JSON.stringify(value)}
                  `
              })
              .join('\n')}
            class Page {
              /** 
               * 页面加载完成后触发 
              */
              mounted: () => void
              /** 
               * 页面创建完成后触发 
              */
              created: () => void
              /** 
               * 页面激活时触发 
              */
              activated: () => void
              /** 
               * 页面失活时触发 
              */
              deactivated: () => void
              /** 
               * 页面销毁前触发 
              */
              beforeDestroy: () => void
              /** 
               * 页面销毁后触发 
              */
              destroyed: () => void
              /** 
               * 自动请求全部加载完成后，触发。可以通过this.datasource获取对应的数据源 
              */
              datasourceLoaded: () => void

              /* vue实例 */
              vm: Vue
              /** 数据源控制 */
              fetcher: Fetcher
              /** 数据源 */
              datasource: Datasource
              /** 当前页面所有元素ref索引集合 */
              refs: Refs
              /** 导入的工具函数 */
              utils: {
                ${utils.join(',')}
              }
              ${
                hasProps
                  ? `/*当前组件属性*/
              props: Props<${JSON.stringify(_props)}>
              emit: (event: ${events.length ? `ArrayToUnion<${JSON.stringify(events.map((e) => e.name))}>` : 'never'}, ...args: any[]) => void`
                  : ''
              }
              [key: string]: any
            }
            `
      let libUri = 'ts:filename/Page.d.ts'
      this.model = monaco.editor.createModel(
        content,
        'typescript',
        monaco.Uri.parse(libUri)
      )

      this.js = monaco.languages.typescript.javascriptDefaults.addExtraLib(
        content,
        libUri
      )

      // 查找使用的组件
      this.registryCodeLens = registryCodeLens(
        this.$refs.view.editor,
        this.$doc
      )
    },
    dispose() {
      this?.js?.dispose()
      this?.model?.dispose()
      this?.registryCodeLens?.dispose()
    },
    refresh() {
      // 刷新monaco-editor大小
      this.$refs.view.refresh()
      this.addExtraLib()
    },
    getValue() {
      return this.$refs.view.getValue()
    },
    addAction() {
      const editor = this.$refs.view.editor
      // 自定义操作：插入 JSDoc 注释
      function insertThisJsDoc(lineNumber) {
        const lineContent = editor.getModel().getLineContent(lineNumber)

        // 判断当前行是否是函数定义
        if (lineContent.trim().startsWith('function')) {
          const range = new monaco.Range(lineNumber, 1, lineNumber, 1)
          const id = { major: 1, minor: 1 } // 唯一标识符
          const text = '/**\n * @this {Page} \n */\n'
          const op = {
            identifier: id,
            range: range,
            text: text,
            forceMoveMarkers: true,
          }

          editor.executeEdits('insert-this-jsdoc', [op])

          // 将光标移动到函数定义行的下方
          editor.setPosition({
            lineNumber: lineNumber + 3,
            column: 1,
          })
        } else {
          // 向上找
          if (lineNumber > 1) {
            insertThisJsDoc(lineNumber - 1)
          }
        }
      }

      // 添加快捷键绑定
      // editor.addCommand(
      //   monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyI,
      //   insertThisJsDoc
      // )

      editor.addAction({
        id: 'insert-this-jsdoc',
        label: '添加 @this 注释',
        contextMenuGroupId: 'navigation',
        // 添加快捷键绑定
        keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyI],
        run: () => {
          const position = editor.getPosition()
          insertThisJsDoc(position.lineNumber)
        },
      })
    },
  },
}
</script>
