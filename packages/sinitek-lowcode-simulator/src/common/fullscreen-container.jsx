export default {
  name: 'FullscreenContainer',
  props: {
    fullscreen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isFullscreen: this.fullscreen,
    }
  },
  watch: {
    fullscreen(val) {
      this.isFullscreen = val
      this.handleFullscreenChange()
    },
  },
  methods: {
    handleKeyDown(e) {
      if (e.key === 'Escape' && this.isFullscreen) {
        this.closeFullscreen()
      }
    },
    closeFullscreen() {
      this.isFullscreen = false
      this.$emit('update:fullscreen', false)
      this.$emit('fullscreen-change', false)
    },
    handleFullscreenChange() {
      if (this.isFullscreen) {
        document.body.style.overflow = 'hidden'
      } else {
        document.body.style.overflow = ''
      }
    },
  },
  mounted() {
    window.addEventListener('keydown', this.handleKeyDown)
    this.handleFullscreenChange()
  },
  beforeDestroy() {
    window.removeEventListener('keydown', this.handleKeyDown)
  },
  render() {
    return (
      <div class="fullscreen-container wh-full">
        {/* 正常模式 */}
        {!this.isFullscreen && this.$slots.default}

        {/* 全屏模式 */}
        {this.isFullscreen && (
          <div class="fullscreen-content fixed inset-0 z-1000 flex flex-col bg-white p-4">
            <div class="h-full w-full overflow-auto">{this.$slots.default}</div>
            <div
              class="absolute right-10 top-2 cursor-pointer"
              onClick={this.closeFullscreen}
            >
              <div class="i-mdi:fullscreen-exit h-4 w-4"></div>
            </div>
          </div>
        )}
      </div>
    )
  },
}
