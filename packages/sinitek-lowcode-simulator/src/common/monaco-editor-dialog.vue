<template>
  <xn-dialog
    :show.sync="isShow"
    :buttons="buttons"
    :title="options.title"
    @save="onConfirm"
    @cancel="close"
  >
    <MonacoEditorView
      ref="monaco"
      v-model="currentValue"
      class="h-500px!"
      :language="options.language || 'javascript'"
      @error="onError"
    />
    <div
      class="max-h-14 min-h-7 overflow-auto whitespace-pre-line text-red-500 fs-20"
    >
      {{ errors }}
    </div>
  </xn-dialog>
</template>

<script>
// 弹框
// props
// title
// confirm 执行的事件
// language 语言
import MonacoEditorView from '@common/monaco-editor/view.vue'
import { createComponent } from '@utils/createComponent'
import { register } from './monaco-editor/schema/material-configure.js'
export default createComponent({
  name: 'MonacoEditorDialog',
  components: {
    MonacoEditorView,
  },
  props: {
    options: Object,
  },
  data() {
    return {
      isShow: this.modelValue,
      currentValue: this.options.value,
      errors: '',
    }
  },
  computed: {
    buttons() {
      return [
        // 由于sinitek-ui没有安装vue-i18n，所以此处传入了label来显示按钮的文字
        // 实际项目中不需要传入，会根据action去i18n中寻找对应文字
        {
          label: '保存',
          type: this.errors ? 'info' : 'primary',
          action: 'save',
          event: 'save',
          showLoading: false,
        },
        { label: '取消', type: 'info', action: 'cancel', event: 'cancel' },
      ]
    },
  },
  methods: {
    onConfirm() {
      if (this.errors) {
        return
      }
      this.options.onConfirm(this.currentValue)
      this.close()
    },
    close() {
      this.isShow = false
    },
    onError(e) {
      this.errors = e.join('\n')
    },
  },
  watch: {
    isShow(v) {
      if (v) {
        this.$nextTick(() => {
          this.$refs.monaco.refresh()
          this.$refs.monaco.editor.setValue(this.options.value || '')
          if (this.options.useMaterialConfigureSchema) {
            this.dispose = register()
          }
        })
      } else {
        if (this.dispose) {
          this.dispose()
        }
      }
      this._emit(v)
    },
    modelValue(v) {
      this.isShow = v
    },
  },
})
</script>
