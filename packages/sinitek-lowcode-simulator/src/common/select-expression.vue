<template>
  <xn-select
    v-model="currentValue"
    placeholder="请选择"
    style="width: 90px"
    :options="list"
    @change="onChange"
  >
  </xn-select>
</template>

<script>
import { createComponent } from '@utils'
export const Status = {
  static: 'static',
  bool: 'bool',
  expression: 'expression',
}
export default createComponent({
  props: {
    modelValue: {
      type: String,
      default: 'static',
    },
    options: [],
    // 参数情况下，多个boolean值
    isParams: Boolean,
  },
  data() {
    return {
      currentValue: this.modelValue,
    }
  },
  computed: {
    list() {
      return (
        this.options ||
        [
          { label: '静态值', value: 'static' },
          this.isParams && { label: '布尔值', value: 'bool' },
          { label: '表达式', value: 'expression' },
        ].filter(Boolean)
      )
    },
  },
  methods: {
    onChange(...args) {
      this.$emit('change', ...args)
    },
  },
  watch: {
    currentValue(val) {
      this._emit(val)
    },
    modelValue(val) {
      this.currentValue = val
    },
  },
})
</script>
