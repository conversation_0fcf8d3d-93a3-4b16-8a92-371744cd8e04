<template>
  <div
    draggable="true"
    class="drag-item"
    @dragstart="dragstart"
    @click="handleClick"
  >
    <!-- <el-popover placement="right" width="400" height="300" trigger="click">
      <div class="max-h-100 overflow-hidden">
        <div v-if="prePreview === false">不支持预览</div>
        <div v-else>
          <div v-if="prePreviewSchema" class="text-#999 fs-12 pb-2">
            预览效果，不代表实际效果, 请以实际效果为准
            <hr />
          </div>
          <SinitekLowcodeRender
            class="mt-2"
            :config="config"
            :mode="DesignMode.PREVENT"
            :fetcher="fetcher"
          />
        </div>
      </div>
      <slot slot="reference"></slot>
    </el-popover> -->
    <slot />
  </div>
</template>

<script>
// import { dragStart } from '@/canvas/container.js'
import { unsafeId, DesignMode } from 'sinitek-lowcode-shared'
// import SinitekLowcodeRender from 'sinitek-lowcode-render'
export default {
  components: {
    // SinitekLowcodeRender,
  },
  inject: ['$doc'],
  props: {
    data: [Object, Function],
    pageData: Function,
    prePreviewSchema: Object,
    prePreview: Boolean,
  },
  emits: ['click'],
  data() {
    return {
      DesignMode,
      fetcher: {},
    }
  },
  computed: {
    config() {
      const x = this.prePreviewSchema || this.data
      return typeof x === 'function' ? x() : x
    },
  },
  methods: {
    dragstart(e) {
      let data = {}
      let pageData
      if (typeof this.data === 'function') {
        const shortId = unsafeId(4)
        data = this.data(shortId)
        pageData = this.pageData.bind(null, shortId)
      } else {
        data = { ...this.data }
      }
      if (data) {
        if (process.env.NODE_ENV === 'development') {
          if (data.props === void 0) {
            throw Error('缺少props属性, 请检查组件snippets')
          }
          const configure = this.$doc.getConfigure(data.componentName)
          if (
            configure.component.isContainer === true &&
            data.children === void 0
          ) {
            throw Error('缺少children属性, 请检查组件snippets')
          }
        }
        this.$doc.drag.dragStart(JSON.parse(JSON.stringify(data)), pageData)
        // 设置拖拽鼠标样式和设置拖拽预览图
        const target = e.target.querySelector('.component-item-component')
        e.dataTransfer.effectAllowed = 'move'
        if (target && e) {
          e.dataTransfer.setDragImage(target, 10, 10)
        }
        this.$emit('dragstart', data)
      }
    },
    handleClick() {
      if (this.data) {
        const data = { ...this.data }

        this.$emit('click', data)
      }
    },
  },
}
</script>
<style lang="scss">
.drag-item {
  user-select: none;
  cursor: move;
}
</style>
