<template>
  <div v-show="isShow" ref="el" class="relative" :style="{ zIndex: zIndex }">
    <!--
    Background backdrop, show/hide based on modal state.

    Entering: "ease-out duration-300"
      From: "opacity-0"
      To: "opacity-100"
    Leaving: "ease-in duration-200"
      From: "opacity-100"
      To: "opacity-0"
  -->
    <div
      class="fixed inset-0 bg-gray-300 bg-opacity-75 transition-opacity"
      @click="close"
    ></div>

    <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
      <div class="min-h-full p-4 text-center sm:items-center sm:p-0">
        <!--
        Modal panel, show/hide based on modal state.

        Entering: "ease-out duration-300"
          From: "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          To: "opacity-100 translate-y-0 sm:scale-100"
        Leaving: "ease-in duration-200"
          From: "opacity-100 translate-y-0 sm:scale-100"
          To: "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
      -->
        <div
          class="box absolute left-50% top-50% min-w-xl overflow-hidden rounded-lg bg-bg bg-white text-left shadow-xl -translate-50%"
        >
          <div
            class="i-material-symbols-light:close absolute right-2 top-2 h-6 w-6"
            @click="close"
          ></div>
          <div class="header cursor-move p-4 pr-8 font-bold fs-20">
            {{ title }}
          </div>
          <div class="px-4 py-2">
            <slot />
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
            <button
              type="button"
              :disabled="confirmDisabled"
              :class="{ 'disabled:bg-gray-4': confirmDisabled }"
              class="w-full inline-flex justify-center rounded-md bg-primary px-3 py-2 text-sm text-white font-semibold shadow-sm sm:ml-3 sm:w-auto"
              @click="confirm"
            >
              确定
            </button>
            <button
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md bg-white px-3 py-2 text-sm text-gray-900 font-semibold shadow-sm ring-1 ring-gray-300 ring-inset sm:mt-0 sm:w-auto hover:bg-gray-50"
              @click="cancel"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { createComponent } from '@utils/createComponent'
import { getZIndex } from '@utils'
export default createComponent({
  name: 'LCDialog',
  props: ['title', 'confirmDisabled', 'confirmBefore', 'to'],
  data() {
    return {
      isShow: this.modelValue,
      zIndex: getZIndex(),
    }
  },
  beforeDestroy() {
    this.$refs.el.remove()
  },
  mounted() {
    let container = document.body.querySelector('#app .lc-simulator')
    if (this.to) {
      container = container.querySelector(this.to)
    }
    container.appendChild(this.$refs.el)

    this.$nextTick(() => {
      const dialog = this.$refs.el.querySelector('.box')
      const header = dialog.querySelector('.header')
      // 点击header添加拖拽逻辑，使dialog可以拖动
      header.addEventListener('mousedown', (e) => {
        let [, , , , x, y] = window
          .getComputedStyle(dialog)
          .getPropertyValue('transform')
          .split(',')
        const move = (e1) => {
          let left = +parseFloat(x) + e1.clientX - e.clientX
          let top = +parseFloat(y) + e1.clientY - e.clientY
          dialog.style.transform = `translate(${left}px, ${top}px)`
        }
        const mouseup = () => {
          document.removeEventListener('mousemove', move)
          document.removeEventListener('mouseup', mouseup)
        }
        document.addEventListener('mousemove', move)
        document.addEventListener('mouseup', mouseup)
      })
    })
  },
  watch: {
    modelValue(v) {
      this.isShow = v
    },
    isShow(v) {
      if (v) {
        this.zIndex = getZIndex()
      }
      this._emit(v)
    },
  },
  methods: {
    cancel() {
      this.close()
    },
    close() {
      this.isShow = false
      this.$emit('close')
    },
    confirm() {
      try {
        if (this.confirmBefore) {
          const res = this.confirmBefore()
          if (res.then) {
            res.then(() => {
              this.isShow = false
              this.$emit('confirm')
            })
            return
          } else if (!res) {
            return
          }
        }
        this.isShow = false
        this.$emit('confirm')
      } catch (e) {
        console.error(e)
      }
    },
  },
})
</script>
