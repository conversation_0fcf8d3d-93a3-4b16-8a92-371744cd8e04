<template>
  <div>
    <div
      v-show="hidden"
      float="显示"
      class="i-custom-eye h-4 w-4"
      :class="[`h-${size || 5}`, `w-${size || 5}`]"
    ></div>
    <div
      v-show="!hidden"
      float="隐藏"
      class="i-custom-eye-off h-4 w-4"
      :class="[`h-${size || 5}`, `w-${size || 5}`]"
    ></div>
  </div>
</template>

<script>
export default {
  props: ['hidden', 'size'],
}
</script>
