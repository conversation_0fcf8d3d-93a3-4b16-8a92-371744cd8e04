<template>
  <Tooltip
    ref="tooltip"
    v-model="visible"
    :el="el"
    placement="bottom"
    :flip="{
      fallbackAxisSideDirection: 'start',
    }"
  >
    <div
      class="text-textSecond max-w-200px shrink-0 break-words rounded-1 bg-bg px-2 py-1 shadow fs-14"
    >
      {{ text }}
    </div>
  </Tooltip>
</template>

<script>
import Tooltip from './tooltip.vue'
export default {
  name: 'CTooltip',
  components: { Tooltip },
  props: {},
  data() {
    return {
      el: null,
      visible: false,
      text: '',
    }
  },
  methods: {
    show(el, text) {
      this.el = el
      this.visible = true
      this.text = text
    },
    hide() {
      this.visible = false
    },
  },
}
</script>
