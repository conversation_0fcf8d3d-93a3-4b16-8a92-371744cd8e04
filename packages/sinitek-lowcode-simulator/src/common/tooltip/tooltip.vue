<template>
  <div
    ref="tooltip"
    :style="{
      top: position.y + 'px',
      left: position.x + 'px',
      zIndex: zIndex,
    }"
    class="tooltip pointer-events-auto absolute z-5"
    :class="{ hidden: !visible }"
  >
    <slot />
  </div>
</template>

<script>
import {
  computePosition,
  flip,
  shift,
  autoPlacement,
  autoUpdate,
  inline,
  offset,
} from '@floating-ui/dom'
import { clickOutside, getZIndex } from '@utils'
export default {
  name: 'LCTooltip',
  props: [
    'el',
    'placement',
    'middleware',
    'flip',
    'shift',
    'autoPlacement',
    'inline',
    'offset',
    'value',
  ],
  data() {
    return {
      isUpdate: false,
      position: {
        x: 0,
        y: 0,
      },
      clean: null,
      visible: this.value,
      zIndex: getZIndex(),
    }
  },
  watch: {
    el(v) {
      if (!v) return
      if (this.clean) this.clean()
      this.clean = autoUpdate(v, this.$refs.tooltip, this.update)
    },
    value(v) {
      this.visible = v
    },
    visible(v) {
      if (v) {
        this.dispose = clickOutside([this.el, this.$refs.tooltip], () => {
          this.visible = false
        })
        this.zIndex = getZIndex()
        this.$nextTick(() => {
          this.update()
        })
      } else {
        this?.dispose?.()
      }
      this.$emit('input', v)
    },
  },
  mounted() {
    this.update()
  },
  beforeDestroy() {
    if (this.clean) this.clean()
    this?.dispose?.()
  },
  methods: {
    update() {
      this.$nextTick(() => {
        if (!this.el || !this.$refs.tooltip || this.isUpdate) return
        this.isUpdate = true
        let middleware = []
        if (this.flip) {
          middleware.push(flip(this.flip))
        }
        if (this.shift) {
          middleware.push(shift(this.shift))
        }
        if (this.autoPlacement) {
          middleware.push(autoPlacement(this.autoPlacement))
        }
        if (this.inline) {
          middleware.push(inline(this.inline))
        }
        if (this.offset) {
          middleware.push(offset(this.offset))
        }
        computePosition(this.el, this.$refs.tooltip, {
          placement: this.placement || 'bottom-end',
          middleware,
        }).then(({ x, y }) => {
          this.position.x = x
          this.position.y = y
          this.isUpdate = false
        })
      })
    },
  },
}
</script>
