<template>
  <div class="bg-[#f5f6f8]" mb-2 min-h-12 flex items-center px-2 py-2 fs-13>
    <div cursor-pointer overflow-hidden>
      <div :float="`${event.name} - ${event.description || ''}`" ellipsis>
        <span>{{ event.name }}</span
        >&nbsp;<span class="text-[rgba(102,102,102,0.88)]">{{
          event.description
        }}</span>
      </div>
      <div
        :float="`函数名称: ${event.methodName}`"
        ellipsis
        flex="~ items-center"
        class="text-primary"
      >
        <div
          v-if="isJSAction(event)"
          pointer-events-none
          size-3
          flex-shrink-0
          class="i-custom-logic-line"
        />
        <div v-else pointer-events-none size-3 class="i-custom-code-line" />
        <span pointer-events-none ml-2>{{ event.methodName }}</span>
      </div>
    </div>

    <!-- 操作 -->
    <div class="ml-a flex">
      <div
        :float="`定位到${isJSAction(event) ? '逻辑编排' : '方法'}`"
        class="i-custom:event-position h-5 w-5 text-[#333]"
        @click="onFindMethod(event)"
      ></div>
      <div
        float="设置"
        class="i-custom:setting h-5 w-5 text-[#333]"
        @click="onEdit(event)"
      ></div>
      <div
        float="删除"
        class="-[#333] i-custom:remove h-5 w-5"
        @click="onRemoveEvent(event.name)"
      ></div>
    </div>
  </div>
</template>

<script>
import { isJSAction } from 'sinitek-lowcode-shared'
import { getAPI } from 'sinitek-lowcode-shared'
export default {
  name: 'EventItem',
  inject: {
    $doc: { default: null },
    hideObjectPanel: { default: () => void 0 },
  },
  props: {
    event: Object,
  },
  data() {
    return {
      isJSAction,
    }
  },
  methods: {
    onRemoveEvent(eventName) {
      this.$emit('remove', eventName)
    },
    onFindMethod(event) {
      if (isJSAction(event)) {
        getAPI().openPanel('LogicFlow')
        this.$nextTick(() => {
          this.$doc.event.emit('logic:locate', event.methodName)
        })
        // this.$emit('findMethod', event)
      } else {
        // TODO 定位到代码
        this.$doc.event.emit('highlight:method', event.methodName)
        getAPI().openPanel('Methods')
      }
      this.hideObjectPanel?.()
    },
    onEdit(event) {
      this.$emit('edit', event)
    },
  },
}
</script>
