<template>
  <div v-if="theme === 'tab'" class="x-tabs w-full flex fs-14">
    <div
      v-for="(tab, i) of currentTabs"
      :key="i"
      class="x-tab h-12 f-c-c cursor-pointer b-b-2 hover:text-primary"
      :class="[
        full ? 'flex-1' : 'px-4',
        { 'text-primary b-b-primary': activeKey === tab.value },
      ]"
      @click="setActiveIndex(tab)"
    >
      {{ tab.label }}
    </div>
  </div>
  <div v-else class="lc-button-group">
    <div
      v-for="(tab, i) of currentTabs"
      :key="i"
      :class="{
        'lc-button-item--active': activeKey === tab.value,
      }"
      class="lc-button-item w-full"
      @click="setActiveIndex(tab)"
    >
      {{ tab.label }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'XTab',
  props: {
    tabs: Array,
    // tab, button
    theme: String,
    type: {
      type: String,
      // bordered lifted boxed
      default: 'bordered',
    },
    defaultKey: String,
    full: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeKey: this.defaultKey,
    }
  },
  computed: {
    currentTabs() {
      return this.tabs.map((e) => {
        if (typeof e === 'string')
          return {
            label: e,
            value: e,
          }
        return e
      })
    },
  },
  watch: {
    defaultKey(v) {
      this.activeKey = v
    },
  },
  methods: {
    setActiveIndex(item) {
      const key = item?.value ?? item
      this.activeKey = key
      this.$emit('change', item)
    },
  },
}
</script>
