<template>
  <div class="dnd-indicator bg-primary" :class="[edge]"></div>
</template>

<script>
export default {
  name: 'DndIndicator',
  props: {
    edge: {
      type: String,
      default: 'top',
    },
  },
}
</script>

<style lang="scss" scoped>
.dnd-indicator {
  display: block;
  position: absolute;
  z-index: 1;
  pointer-events: none;
  height: 2px;
  transition: all 0.2s ease;
  &.top,
  &.bottom {
    &::before {
      content: '';
      position: absolute;
      left: 0;
      height: 7px;
      width: 7px;
      border-radius: 50%;
      background: var(--xn-color-primary, #1476ff);
      animation: indicator-pulse 1.5s infinite;
      transform: translateY(-3px);
    }
  }
  &.top {
    top: 0;
    left: 0;
    right: 0;
  }
  &.bottom {
    bottom: 0;
    left: 0;
    right: 0;
  }
  &.in {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100%;
    border: 2px solid #0c66e4;
    background: unset;
  }
  &.forbid {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100%;
    border: 2px solid red;
    background: unset;
  }
}
@keyframes indicator-pulse {
  0% {
    box-shadow: 0 0 0 0 var(--xn-color-primary, #1476ff);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(12, 102, 228, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(12, 102, 228, 0);
  }
}
</style>
