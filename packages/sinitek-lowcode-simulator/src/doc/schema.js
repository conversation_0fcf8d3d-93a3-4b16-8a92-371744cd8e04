import { deepClone, guid } from '@utils'
import { camelize, capitalize } from 'sinitek-lowcode-shared'
export class Schema {
  #schema = {
    value: {},
  }
  #refKey = new Map()

  originalSchema = null

  get schema() {
    return this.#schema.value
  }

  constructor(doc) {
    this.doc = doc
  }
  /**
   * 替换schema用在初始化或者导入配置时
   * @param {*} schema
   */
  replace(schema) {
    this.doc.history.clean()
    this.doc.select.clean()
    this.doc.hover.clean()
    this.#refKey.clear()
    this.#schema.value = schema
    this.originalSchema = deepClone(schema)
    this.doc.history.addHistory()
    this.update()
  }
  // 重置原始数据
  // 保存后需要重置原始数据，用来匹配页面是否有修改
  resetOriginal() {
    this.originalSchema = deepClone(this.#schema.value)
  }

  set(schema) {
    if (!schema) {
      throw new Error('schema is required')
    }
    this.#schema.value = schema
  }
  update() {
    this.doc.event.emit('schema:change', this.#schema.value)
  }
  clean() {
    this.#schema.value = {}
    this.update()
    this.#refKey.clear()
  }

  onChange(fn) {
    return this.doc.event.on('schema:change', fn)
  }

  generateRefKey(schema) {
    const name = capitalize(camelize(schema.componentName))
    // let refIndex = this.#refKey.get(name) || 1
    // this.#refKey.set(name, refIndex + 1)
    return `${name}_${guid().slice(0, 4)}`
  }
  cloneSchema(schema) {
    let result = deepClone(schema)
    result.id = guid()
    result.ref = this.generateRefKey(schema)
    delete result.LCBindState
    if (schema?.children?.length) {
      result.children = schema.children.map(this.cloneSchema.bind(this))
    }
    return result
  }

  // 将children重新设置来触发渲染
  // 主要针对不存在的属性设置值
  // vue3 没有这种问题
  // 这个是vue2未定义的属性无法监测变化的问题
  updateChildById(id, data) {
    const { parent } = this.doc.node.getNode(id, true)
    const findIndex = parent.children.findIndex((e) => e.id === id)
    const children = parent.children.slice()
    if (findIndex > -1) {
      children[findIndex] = data
      parent.children = children
      this.doc.history.addHistory()
    }
  }
}
