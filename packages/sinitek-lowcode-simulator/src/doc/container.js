export class Container {
  // 容器宽度，不同设备宽度不一样
  #device = 'desktop'
  get device() {
    return this.#device
  }

  constructor(doc) {
    this.doc = doc
  }

  /**
   * 获取几何信息
   */
  get rect() {
    return this.el?.getBoundingClientRect?.()
  }
  get el() {
    return this.doc.frameDocument.body
  }

  setDevice(device) {
    this.doc.event.emit('update:device', device)
    setTimeout(() => {
      this.doc.select.reselect()
    })
    this.#device = device
  }
  onDeviceChange(fn) {
    this.doc.event.on('update:device', fn)
  }
}
