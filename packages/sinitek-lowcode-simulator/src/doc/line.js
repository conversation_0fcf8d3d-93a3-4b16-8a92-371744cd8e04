import { deepClone } from '@utils'
import { getRect } from '../utils/dom'
import { Design } from 'sinitek-lowcode-shared'

//定义POSITION枚举，用于表示拖拽位置

const size = 2
export const POSITION = Object.freeze({
  TOP: 'top',
  BOTTOM: 'bottom',
  LEFT: 'left',
  RIGHT: 'right',
  IN: 'in',
  FORBID: 'forbid',
})

const initialLineState = {
  top: 0, // 线条顶部坐标
  height: 0, // 线条高度
  width: 0, // 线条宽度
  left: 0, // 线条左侧坐标
  position: '', // 线条位置
  id: '', // 当前选中的节点ID
  configure: null, // 配置
  errorMessage: '', // 错误信息
}

// 元素边缘区域
const lineAbs = 15

export class Line {
  #state = { ...initialLineState }
  get state() {
    return this.#state
  }
  constructor(doc) {
    this.doc = doc
  }
  set(state) {
    Object.assign(this.#state, { ...state })
  }
  update() {
    this.doc.event.emit('line:change', deepClone(this.#state))
  }
  onChange(fn) {
    this.doc.event.on('line:change', fn)
  }

  /**
   * 是否允许插入
   * @param {*} hover 当前放置目标对象
   * @param {*} data 当前插入目标的schame数据
   * @returns
   */
  allowInsert(hover = {}, data = {}, position = POSITION.IN) {
    // 如果使用递归父级，则深度为5，否则为1
    let depth = this.doc.drag.state?.configure?.component?.nestingRule
      ?.upRecursiveParent
      ? 5
      : 1

    let flag = true
    const _run = (hover) => {
      if (depth === 0) return
      flag = true
      depth--
      const { configure, componentName } = hover
      const dragName = data?.componentName
      if (data.parentWhiteList) {
        return data.parentWhiteList.includes(componentName)
      }
      const childWhitelist =
        configure?.component?.nestingRule?.childWhitelist ?? []
      const childBlacklist =
        configure?.component?.nestingRule?.childBlacklist ?? []

      const dragConfigure = this.doc.drag.state.configure
      const parentWhitelist =
        dragConfigure?.component?.nestingRule?.parentWhitelist ?? []
      const parentBlacklist =
        dragConfigure?.component?.nestingRule?.parentBlacklist ?? []
      const setErrorMessage = (message) => {
        this.set({ errorMessage: message })
      }
      // 要插入的父节点必须是容器
      // if (!configure?.component?.isContainer) {
      //   setErrorMessage(`无法插入当前组件`)
      //   return false
      // }
      if (position === POSITION.IN) {
        // 子白名单
        if (childWhitelist.length && flag) {
          flag = childWhitelist.includes(dragName)
          !flag &&
            setErrorMessage(
              `只能插入${this.doc.materials.getTranslateName(childWhitelist).join(',')}组件`
            )
        }
        // 子黑名单
        if (childBlacklist.length && flag) {
          flag = !childBlacklist.includes(dragName)
          !flag &&
            setErrorMessage(
              `不能插入${this.doc.materials.getTranslateName(childBlacklist).join(',')}组件`
            )
        }
      } else {
        // 父白名单
        if (parentWhitelist.length && flag) {
          flag = parentWhitelist.includes(componentName)
          !flag &&
            setErrorMessage(
              `只能插入到${this.doc.materials.getTranslateName(parentWhitelist).join(',')}组件`
            )
        }

        // 父黑名单
        if (parentBlacklist.length && flag) {
          flag = !parentBlacklist.includes(componentName)
          !flag &&
            setErrorMessage(
              `不能能插入到${this.doc.materials.getTranslateName(parentBlacklist).join(',')}组件`
            )
        }
        // 支持特定有特定属性的组件插入
      }
      if (configure?.component?.nestingRule?.allowInsert) {
        flag = configure.component.nestingRule.allowInsert(data, hover)
      }
      if (!flag) {
        // 向上找5层
        // 在form->嵌套div->在嵌套item时候非常有用
        const parent = this.doc.node.getNode(hover.id, true)?.parent
        if (parent) {
          _run({
            ...parent,
            ...this.doc.materials.getMaterial(parent.componentName),
          })
        }
      }
    }
    _run(hover)
    return flag
  }

  // isAncestor(ancestor, descendant) {
  //   const ancestorId = typeof ancestor === 'string' ? ancestor : ancestor.id
  //   let descendantId =
  //     typeof descendant === 'string' ? descendant : descendant.id

  //   while (descendantId) {
  //     const { parent } = this.doc.node.getNode(descendantId, true) || {}
  //     if (parent?.id === ancestorId) {
  //       return true
  //     }

  //     descendantId = parent?.id
  //   }

  //   return false
  // }

  /**
   * 根据鼠标位置和元素矩形计算线条位置
   * @param {*} rect 元素rect数据
   * @param {*} configure 组件配置
   * @returns {object} {'type': POSITION}
   */
  getPosLine(rect, configure) {
    const mousePos = this.doc.drag.state.mouse
    if (!mousePos) return { type: POSITION.BOTTOM, forbidden: false }

    // 增大边界区域，确保更容易触发边缘判定
    const yAbs = Math.min(lineAbs * 1.5, rect.height / 3)
    const xAbs = Math.min(lineAbs * 1.5, rect.width / 3)
    let type
    let forbidden = false

    // 边界判断优先级：上下左右
    if (mousePos.y < rect.top + yAbs) {
      type = POSITION.TOP
    } else if (mousePos.y > rect.bottom - yAbs) {
      type = POSITION.BOTTOM
    } else if (mousePos.x < rect.left + xAbs) {
      type = POSITION.LEFT
    } else if (mousePos.x > rect.right - xAbs) {
      type = POSITION.RIGHT
    } else if (configure?.component?.isContainer) {
      // 如果是容器，检查是否允许插入
      type = this.allowInsert(
        this.doc.hover.state,
        this.doc.drag.state.data,
        POSITION.IN
      )
        ? POSITION.IN
        : POSITION.BOTTOM
    } else {
      type = POSITION.BOTTOM
    }

    // 四个方向上判断,当前参照物的父级是否可以插入拖拽的节点
    // 用来判断规则
    if (![POSITION.FORBID, POSITION.IN].includes(type) && this.state.id) {
      // 判断当前元素的父节点是否接受这个子节点
      let { parent } = this.doc.node.getNode(this.state.id, true)
      // 不接受则禁止插入
      if (parent?.componentName) {
        const allow = this.allowInsert(
          {
            ...parent,
            ...this.doc.materials.getMaterial(parent.componentName),
          },
          this.doc.drag.state.data,
          type
        )
        if (!allow) {
          forbidden = true
        }
      }
    }

    return { type, forbidden }
  }

  /**
   * 查找父级节点
   * @param {*} el
   * @returns
   */
  findParent(el) {
    let parent = el.parentElement
    while (
      !parent.getAttribute(Design.NODE_UID) &&
      !parent.getAttribute(Design.NODE_UID)
    ) {
      parent = parent.parentElement
    }
    return parent
  }
  /**
   * 设置线条位置
   * @param {*} element
   * @param {*} data  当前插入目标的schame数据
   * @returns
   */
  select(element, data) {
    if (!element) {
      return this.doc.hover.clean()
    }

    let componentName = element.getAttribute(Design.NODE_TAG)
    let id = element.getAttribute(Design.NODE_UID)

    // 如果元素没有必要的标识属性，则不处理
    if (!componentName || !id) {
      return
    }
    // 获取合适的参照物
    let configure = this.doc.materials.getConfigure(componentName)
    let { parent, node } = this.doc.node.getNode(id, true)
    let isContainer = configure?.component?.isContainer
    // 如果没有父级
    if (!node?.children?.length) {
      // 没有子级，设置为IN
      this.set({ position: POSITION.IN })
    } else if (!parent) {
      parent = node
      const children = node?.children ?? []
      if (children.length > 0) {
        // 根据上一次位置选择子元素
        let targetChild = null
        if (
          this.state.position === POSITION.TOP ||
          this.state.position === POSITION.LEFT
        ) {
          targetChild = children[0]
        } else {
          targetChild = children[children.length - 1]
        }

        if (targetChild) {
          componentName = targetChild.componentName
          id = targetChild.id
          element = this.doc.node.querySelectById(targetChild.id)
          configure = this.doc.materials.getConfigure(componentName)
          isContainer = configure?.component?.isContainer
        }
      }
    }

    // 如果父级不是容器
    if (
      this.#state.position !== POSITION.IN &&
      this.doc.materials.getConfigure(parent.componentName)?.component
        ?.isContainer === false
    ) {
      // 先向下查找5层
      let foundContainer = false
      let count = 0
      isContainer = false
      // 向下查找最多5层
      while (!isContainer && count < 5) {
        const children = this.doc.node.getNode(id)?.children ?? []
        if (children.length === 0) break

        let targetChild = null
        if (
          this.state.position === POSITION.TOP ||
          this.state.position === POSITION.LEFT
        ) {
          targetChild = children[0]
        } else {
          targetChild = children[children.length - 1]
        }

        if (targetChild) {
          componentName = targetChild.componentName
          id = targetChild.id
          element = this.doc.node.querySelectById(targetChild.id)
          configure = this.doc.materials.getConfigure(componentName)
          isContainer = configure?.component?.isContainer

          if (isContainer) {
            foundContainer = true
            break
          }
        }
        count++
      }

      // 如果向下没找到容器，向上查找
      if (!foundContainer) {
        let currentParent = parent
        while (currentParent) {
          const parentConfigure = this.doc.materials.getConfigure(
            currentParent.componentName
          )
          if (parentConfigure?.component?.isContainer) {
            componentName = currentParent.componentName
            id = currentParent.id
            element = this.doc.node.querySelectById(currentParent.id)
            configure = parentConfigure
            isContainer = true
            break
          }
          currentParent = this.doc.node.getNode(currentParent.id, true)?.parent
        }
      }
    }
    // 如果父级是容器，直接使用当前元素作为参照物
    else {
      isContainer = configure?.component?.isContainer
    }

    // 主要针对弹框等组件，需要获取根节点
    const rootSelector = configure?.component?.rootSelector
    element = rootSelector ? element.querySelector(rootSelector) : element
    if (!element) {
      return this.clean()
    }

    const rect = getRect(element)
    const { left, height, top, width } = rect

    // 修改当前鼠标悬停的元素配置
    this.doc.hover.set({ configure, componentName })
    if (!data) {
      return
    }

    // 添加当前插入元素的id和配置
    this.set({ id, configure })
    const { type: rectType, forbidden } = this.getPosLine(rect, configure)

    // 设置四向线条位置 - 简化逻辑，直接根据type设置线条
    // 确保设置非零的宽度和高度，避免线条不显示
    if (rectType === POSITION.TOP) {
      this.set({
        width: Math.max(width, 10),
        height: size, // 设置固定高度，确保线条可见
        top,
        left,
        position: POSITION.TOP,
        forbidden,
      })
    } else if (rectType === POSITION.BOTTOM) {
      this.set({
        width: Math.max(width, 10),
        height: size, // 设置固定高度，确保线条可见
        top: top + height,
        left,
        position: POSITION.BOTTOM,
        forbidden,
      })
    } else if (rectType === POSITION.LEFT) {
      this.set({
        width: size, // 设置固定宽度，确保线条可见
        height: Math.max(height, 10),
        top,
        left,
        position: POSITION.LEFT,
        forbidden,
      })
    } else if (rectType === POSITION.RIGHT) {
      this.set({
        width: size, // 设置固定宽度，确保线条可见
        height: Math.max(height, 10),
        top,
        left: left + width,
        position: POSITION.RIGHT,
        forbidden,
      })
    } else if (rectType === POSITION.IN && isContainer) {
      // 容器内部插入
      this.set({
        width: Math.max(width, 10),
        height: Math.max(height, 10),
        top,
        left,
        position: POSITION.IN,
        forbidden,
      })
    } else {
      // 默认情况
      this.set({
        width: Math.max(width, 10),
        height: size, // 设置固定高度，确保线条可见
        top: rectType === POSITION.BOTTOM ? top + height : top,
        left,
        position: rectType,
        forbidden,
      })
    }

    // 必须调用update触发更新
    this.update()

    // 返回线条状态，用于调试
    return this.#state
  }

  clean() {
    // 恢复清除功能，但确保使用set和update
    this.set({ ...initialLineState })
    this.update()
  }
}
