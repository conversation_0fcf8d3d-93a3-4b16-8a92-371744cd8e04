export class EventQueue {
  constructor() {
    this.queue = []
    this.isProcessing = false
  }

  /**
   * 添加事件到队列
   * @param {Function} handler 事件
   * @param {...any} args 事件参数
   * @returns {Promise<void>}
   */
  async add(handler, ...args) {
    this.queue.push({ handler, args })
    await this.process()
  }

  /**
   * 添加事件到队列
   * @param {Function} handler 事件
   * @param {...any} args 事件参数
   */
  lazyAdd(handler, ...args) {
    this.queue.push({ handler, args })
  }

  /**
   * 运行队列
   * @returns {Promise<void>}
   */
  async run() {
    await this.process()
  }

  /**
   * 处理队列中的事件
   * @returns {Promise<void>}
   */
  async process() {
    if (this.isProcessing || this.queue.length === 0) {
      return
    }

    this.isProcessing = true

    try {
      const event = this.queue.shift()
      if (event?.handler) {
        await event.handler(...event.args)
      }
    } finally {
      this.isProcessing = false

      // 如果队列中还有事件，继续处理
      if (this.queue.length > 0) {
        await this.process()
      }
    }
  }

  /**
   * 清空事件队列
   */
  clear() {
    this.queue = []
  }

  /**
   * 销毁事件队列
   */
  destroy() {
    this.clear()
  }
}
