let uid = 0
export class Emitter {
  #listener = {}

  on(eventName, callback) {
    uid++
    this.#listener[eventName] = this.#listener?.[eventName] ?? []
    this.#listener[eventName].push(callback)
    callback.__uid = uid
    return () => {
      this.#listener[eventName]?.forEach((e) => {
        if (e.__uid === callback.__uid) {
          this.#listener[eventName].splice(
            this.#listener[eventName].indexOf(e),
            1
          )
        }
      })
    }
  }

  emit(eventName, ...args) {
    const cbs = this.#listener[eventName]
    if (cbs?.length) {
      cbs.forEach((fn) => {
        fn(...args)
      })
    }
  }
  off(eventName) {
    if (this.#listener[eventName]) {
      delete this.#listener[eventName]
    }
  }
  combine(...fns) {
    return () => {
      fns.forEach((fn) => fn())
    }
  }

  clean() {
    this.#listener = {}
  }
}
