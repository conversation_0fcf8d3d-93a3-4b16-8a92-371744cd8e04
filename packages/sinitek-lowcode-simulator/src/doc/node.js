import { guid } from '@utils'
import { POSITION } from './line'
import { Design } from 'sinitek-lowcode-shared'
import { isRootComponent } from '@utils/materials'
import merge from 'lodash/merge'
import { emit } from '@/utils/emitter'

// 滚动页面后，目标元素与页面边界至少保留的边距
const SCROLL_MARGIN = 15

export class Node {
  #nodes = {}

  #scope = {}

  #root = {}

  get root() {
    return this.#root
  }

  get nodes() {
    return this.#nodes
  }
  constructor(doc) {
    this.doc = doc
  }

  /**
   * 设置节点信息
   * @param {*} schema
   * @param {*} parent
   */
  setNode(schema, parent, scope) {
    // 如果节点ID不存在，则生成一个唯一的ID
    schema.id = schema.id || guid()
    // 如果节点引用不存在，则生成一个引用键
    schema.ref = schema.ref || this.doc.schema.generateRefKey(schema)
    ;(schema?.children ?? []).forEach((e) => {
      e.id = e.id || guid()
      e.ref = e.ref || this.doc.schema.generateRefKey(e)
    })
    if (isRootComponent(schema.componentName)) {
      this.#root = schema
    }
    // 将节点信息存储到#nodes对象中
    this.#nodes[schema.id] = { node: schema, parent }
    if (scope.scope) {
      this.#scope[schema.id] = scope
    }
  }

  getNode(id, isParent) {
    return isParent ? this.#nodes[id] : this.#nodes[id]?.node
  }

  getScope(id) {
    return this.#scope[id]
  }

  delNode(id) {
    delete this.#nodes[id]
  }

  /**
   * 清空所有节点信息
   */
  clearNodes() {
    Object.keys(this.#nodes).forEach(this.delNode)
  }

  /**
   *  获取根节点信息
   * @param {*} id
   * @returns
   */
  getRoot(id) {
    const { parent } = this.getNode(id, true)

    return parent?.id ? this.getRoot(parent.id) : parent
  }

  scrollToNode(element) {
    if (element) {
      const container = this.doc.container.el.parentNode
      const { clientWidth, clientHeight } = container
      const { left, right, top, bottom, width, height } =
        element.getBoundingClientRect()
      const option = {}

      if (right < 0) {
        option.left = container.scrollLeft + left - SCROLL_MARGIN
      } else if (left > clientWidth) {
        option.left =
          container.scrollLeft + left - clientWidth + width + SCROLL_MARGIN
      }
      if (bottom < 0) {
        option.top = container.scrollTop + top - SCROLL_MARGIN
      } else if (top > clientHeight) {
        option.top =
          container.scrollTop + top - clientHeight + height + SCROLL_MARGIN
      }
      if (typeof option.left === 'number' || typeof option.top === 'number') {
        container.scrollTo(option)
      }
    }
  }
  /**
   * 选择指定ID的节点
   * @param {*} id
   * @returns
   */
  selectNode(id) {
    const { node, parent } = this.getNode(id, true) || {}
    let element = this.querySelectById(id)
    this.scrollToNode(element)
    this.doc.select.setCurrent(node, parent)
    this.doc.select.select(element)
    return node
  }

  /**
   * 在指定位置插入子节点
   * @param {*} param0
   * @param {*} position
   */
  insertInner({ node, data }, position) {
    if (position === POSITION.TOP || position === POSITION.LEFT) {
      node.children.unshift(data)
    } else {
      node.children.push(data)
    }
  }
  /**
   * 在父节点之前插入子节点
   * @param {*} param0
   */
  insertBefore({ parent, node, data }) {
    const parentChildren = parent.children
    const index = parentChildren.findIndex((p) => p.id === node.id)
    parent.children.splice(index, 0, data)
  }
  /**
   * 在父节点之后插入子节点
   * @param {*} param0
   */
  insertAfter({ parent, node, data }) {
    const parentChildren = parent.children
    const index = parentChildren.findIndex((p) => p.id === node.id)
    parent.children.splice(index + 1, 0, data)
  }
  /**
   * 在指定位置插入节点
   * @param {*} node
   * @param {*} position
   * @param {*} select
   */
  insertNode(node, position = POSITION.IN, select = true) {
    const configure = this.doc.materials.getConfigure(node.data.componentName)
    if (configure?.advanced?.callbacks?.onNodeAdd) {
      const data = configure.advanced.callbacks.onNodeAdd(
        node.data,
        this.doc.materials.getTarget(() => ({
          schema: node.node,
          parent: node.parent,
        }))
      )
      if (data.forbidden) {
        data.errorMessage &&
          this.doc.event.emit('drag:error', data.errorMessage)
        return
      }
      node.data = data
    }
    if (!node.parent) {
      this.insertInner(
        { node: this.doc.getSchema(), data: node.data },
        position
      )
    } else {
      switch (position) {
        case POSITION.TOP:
        case POSITION.LEFT:
          this.insertBefore(node)
          break
        case POSITION.BOTTOM:
        case POSITION.RIGHT:
          this.insertAfter(node)
          break
        case POSITION.IN:
          this.insertInner(node)
          break
        default:
          this.insertInner(node)
          break
      }
    }
    const pageData = this.doc.drag.state.pageData

    select &&
      setTimeout(() => {
        const id = node?.data?.id
        const selectNode = (id) => {
          if (!id) return
          this.selectNode(id)
        }
        selectNode(id)
        if (pageData) {
          this.doc.schema.set(
            merge(
              this.doc.getSchema(true),
              pageData(this.doc.materials.getTarget())
            )
          )
          setTimeout(() => {
            this.doc.schema.update()
            // 这里需要重新选中一次，因为刷新了schema数据，
            // 导致select的current响应式数据对象不是最新的
            this.doc.select.clean()
            // 不能直接调用，vue更新是异步的，直接调用无法更新视图
            setTimeout(() => selectNode(id))
          })
        }
      })
    emit('node:insert')
    this.doc.event.emit('insertNode', node)
    this.doc.history.addHistory()
  }
  /**
   * 删除父节点里对应的子节点
   * @param {*} param0
   */
  removeNode({ parent, node }) {
    const parentChildren = parent.children || parent.value
    let index = parentChildren.indexOf(node)
    // fix: 可能出现插入多个节点的问题，在交换时循环删除。
    while (index > -1) {
      parentChildren.splice(index, 1)
      index = parentChildren.indexOf(node)
    }
    emit('node:remove')
    this.doc.event.emit('removeNode')
    const configure = this.doc.materials.getConfigure(node.componentName)
    if (configure?.advanced?.callbacks?.onNodeRemove) {
      configure.advanced.callbacks.onNodeRemove(
        node,
        this.doc.materials.getTarget(() => ({
          schema: node,
          parent: parent,
        }))
      )
    }
  }

  /**
   * 复制当前选中的节点
   */
  copy() {
    const { schema } = this.doc.getCurrent(true)

    if (!schema) return

    this.copyNodeById(schema.id)
  }

  copyNode({ data, node, parent }) {
    if (!node) return
    const _saveData = this.doc.schema.cloneSchema(data)
    if (this.doc.materials.isModalComponent(_saveData.componentName)) {
      const pageSchema = this.doc.schema.schema
      pageSchema.children.push(_saveData)
      this.doc.schema.set(pageSchema)
      this.doc.event.emit('insertNode', _saveData)
      this.doc.schema.update()
    } else {
      this.insertNode(
        {
          data: _saveData,
          node: node,
          parent: parent,
        },
        POSITION.BOTTOM,
        false
      )
    }
    queueMicrotask(() => {
      this.doc.select.reselect()
      this.doc.schema.update()
    })
  }

  copyNodeById(id) {
    const { node, parent } = this.getNode(id, true)
    if (!node) return
    this.copyNode({ data: node, node, parent })
  }

  /**
   * 根据ID查询DOM元素
   * @param {*} id
   * @returns
   */
  querySelectById(id) {
    let selector = `[${Design.NODE_UID}="${id}"]`
    const doc = this.doc.container.el
    let element = doc.querySelector(selector)
    // const loopId = element?.getAttribute('loop-id')
    // if (element && loopId) {
    //   const currentLoopId = this.doc.select.getCurrent().loopId
    //   selector = `[${Design.NODE_UID}="${id}"][${Design.NODE_LOOP}="${currentLoopId}"]`
    //   element = doc.querySelector(selector)
    // }
    return element
  }

  /**
   * 根据ID删除节点
   * @param {*} id
   * @returns
   */

  removeNodeById(id) {
    if (!id) {
      return
    }

    this.removeNode(this.getNode(id, true))
    this.doc.select.clean()
    this.doc.hover.clean()
    this.doc.history.addHistory()
  }
}
