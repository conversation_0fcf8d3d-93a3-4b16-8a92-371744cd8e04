export class Hotkey {
  #hotkey = new Map() // 快捷键映射表

  constructor() {
    this.bindEvent = this.handleKeydown.bind(this)
    this.currentTarget = null // 当前目标元素
    this.init()
  }

  init() {
    // 监听全局键盘事件
    window.addEventListener('keydown', this.bindEvent)
  }

  handleKeydown(event) {
    const key = this.getKeyString(event)
    const handlers = this.#hotkey.get(this.currentTarget)
    if (handlers && handlers[key]) {
      event.preventDefault() // 防止默认行为
      handlers[key](event) // 执行快捷键处理函数
    }
  }

  getKeyString(event) {
    const keys = []
    if (event.metaKey) keys.push('Ctrl')
    else if (event.ctrlKey) keys.push('Ctrl')
    if (event.shiftKey) keys.push('Shift')
    if (event.altKey) keys.push('Alt')
    keys.push(event.key)
    return keys.join('+')
  }

  setTarget(target) {
    this.currentTarget = target
  }

  /**
   * 绑定快捷键
   * @param {*} target 目标元素
   * @param {*} keys 快捷键
   * @param {*} handler 处理函数
   * @example
   * // 绑定单个快捷键
   * const unbind = hotkey.bind('canvas', 'Delete', () => {
   *   // 删除当前元素
   *   console.log('删除元素')
   * })
   *
   * // 绑定组合键
   * hotkey.bind('canvas', 'Ctrl+c', () => {
   *   // 复制元素
   *   console.log('复制元素')
   * })
   *
   * // 绑定多个快捷键
   * hotkey.bind('canvas', ['Ctrl+z', 'Ctrl+y'], () => {
   *   // 撤销/重做操作
   *   console.log('撤销/重做操作')
   * })
   *
   * // 解绑快捷键
   * unbind()
   */
  bind(target, keys, handler) {
    if (!this.#hotkey.has(target)) {
      this.#hotkey.set(target, {})
    }

    // 支持数组和字符串
    const keyArray = Array.isArray(keys) ? keys : [keys]

    keyArray.forEach((key) => {
      const keyString = this.getKeyString({
        key,
        ctrlKey: false,
        shiftKey: false,
        altKey: false,
        metaKey: false,
      })
      this.#hotkey.get(target)[keyString] = handler
    })

    return () => {
      if (this.#hotkey?.has(target)) {
        keyArray.forEach((key) => {
          const keyString = this.getKeyString({
            key,
            ctrlKey: false,
            shiftKey: false,
            altKey: false,
            metaKey: false,
          })
          delete this.#hotkey.get(target)[keyString]
        })
      }
    }
  }

  clean() {
    // 清除快捷键
    window.removeEventListener('keydown', this.bindEvent)
    this.#hotkey.clear()
  }
}
