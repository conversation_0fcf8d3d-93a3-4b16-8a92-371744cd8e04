import { deepClone } from '@utils'

export const initialRectState = {
  top: 0,
  height: 0,
  width: 0,
  left: 0,
  schema: null,
  configure: null,
  current: null,
  parent: null,
  componentName: '',
}
export class Select {
  #state = { ...initialRectState }

  #current = null
  #parent = null

  get state() {
    return this.#state
  }

  constructor(doc) {
    this.doc = doc
  }

  select(element) {
    element = element || document.body
    const componentName = this.#current.componentName
    const configure = this.doc.getConfigure(componentName)
    const rootSelector = configure?.component?.rootSelector
    element = rootSelector ? element.querySelector(rootSelector) : element
    const rect = element.getBoundingClientRect()
    const { left, height, top, width } = rect
    // const siteCanvasRect = this.doc.container.rect

    // 设置元素hover状态
    Object.assign(this.#state, {
      width: width,
      height: height,
      top: top,
      left: left,
      componentName,
      configure,
    })
    this.set(this.#state)
    this.doc.hover.clean()
    this.update()
  }
  set(state = {}) {
    Object.assign(this.#state, {
      ...state,
      current: this.#current,
      parent: this.#parent,
    })
  }
  update() {
    this.doc.event.emit('select:change', deepClone(this.#state))
  }
  /**
   * 清楚
   */
  clean() {
    // fix: set是合并，导致清除后current还存在的问题
    this.#state = { ...initialRectState }
    this.#current = null
    this.#parent = null
    this.update()
  }

  /**
   * 设置当前选中的节点
   * @param {*} current
   * @param {*} parent
   */
  setCurrent(current, parent) {
    this.#current = current
    this.#parent = parent
  }
  /**
   * 获取当前选中的schema
   * @returns {schema, parent}
   */
  getCurrent() {
    // 这里的输出是vue响应式的，直接修改会使试图更新
    return { schema: this.#current, parent: this.#parent }
  }

  onChange(fn) {
    return this.doc.event.on('select:change', fn)
  }

  /**
   * 重新选择，用在元素修改后
   * @returns
   */
  reselect() {
    if (!this.#current) return
    this.select(this.doc.node.querySelectById(this.#current.id))
  }
}
