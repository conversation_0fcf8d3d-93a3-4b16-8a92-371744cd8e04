import cloneDeep from 'lodash/cloneDeep'
// import { getInstallData } from '@utils/install'

import { getCtxMaterialsToArray, log } from 'sinitek-lowcode-shared'
import { setByPath } from '../../../sinitek-lowcode-render/src/shared/setByPath'
let _caches = {
  materials: {},
}
export class Materials {
  #materials = []

  constructor(doc) {
    this.doc = doc
  }

  getConfigure(name) {
    let schema = this.getMaterial(name)
    return schema?.configure
  }

  getMaterial(name) {
    if (!_caches.materials[name]) {
      _caches.materials[name] = this.getMaterials().find(
        (item) => item.componentName === name
      )
    }
    return _caches.materials[name]
  }

  getMaterialByRef(ref) {
    const node = Object.values(this.doc.node.nodes).find(
      (item) => item.node.ref === ref
    )
    if (!node) {
      return null
    }
    return this.getMaterial(node.node.componentName)
  }

  getMaterialById(id) {
    const nodes = this.doc.node.nodes
    const node = nodes[id]
    if (!node) {
      return null
    }
    return this.getMaterial(node.node.componentName)
  }

  getTranslateName(names) {
    return names.map((name) => {
      const material = this.getMaterial(name)
      return material?.title ?? name
    })
  }

  getMaterials() {
    return Object.freeze(
      [].concat(getCtxMaterialsToArray()).concat(this.#materials)
    )
  }

  getSnippets() {
    return this.getMaterials().reduce((pre, e) => {
      pre.push(...(e?.snippets ?? []))
      return pre
    }, [])
  }

  onChange(fn) {
    this.doc.event.on('material:change', fn)
  }

  _update() {
    this.doc.event.emit('material:change', this.getMaterials())
  }

  getTarget(getCurrent = () => this.doc.getCurrent()) {
    // 给物料数据判断当前属性是否需要展示
    let { schema, parent } = getCurrent()
    const root = this.doc.schema.schema
    if (!parent) parent = cloneDeep(root)
    return {
      // 当前选中物料的属性
      props: cloneDeep(schema.props),
      componentName: schema.componentName,
      configure: schema.configure,
      parent: parent,
      root,
      // 获取属性值
      getPropValue: (propName) => {
        return cloneDeep(schema.props[propName])
      },
      getParentPropValue: (propName) => {
        return cloneDeep(parent.props[propName])
      },
      /**
       *
       * @param {*} propName
       * @param {*} value 只能是原始类型，不能是新对象，会变成canvas的 ob
       */
      setPropValue: (propName, value) => {
        setByPath(schema.props, propName, value)
        this.doc.event.emit('props:change')
      },
      setParentPropValue: (propName, value) => {
        setByPath(parent.props, propName, value)
      },
      // 找到指定名称的最近父级的物料
      findParent: (componentName) => {
        let _schema = cloneDeep(schema)
        const _find = (_componentName) => {
          const _parent = this.doc.node.getNode(_schema.id, true).parent
          if (!_parent) return null
          _schema = _parent
          if (_parent.componentName === _componentName) {
            return _parent
          }
          return _find(_componentName)
        }
        return _find(componentName)
      },
      log,
    }
  }

  clean() {
    this.#materials = []
    _caches = {
      materials: {},
    }
  }

  isModalComponent(componentName) {
    return this.getMaterial(componentName)?.configure?.component?.isModal
  }
}
