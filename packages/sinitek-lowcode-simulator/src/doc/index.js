import History from './history'
import { Emitter } from './mitter'
import { Node } from './node'
import { Materials } from './materials'
import { Hover } from './hover'
import { Container } from './container'
import { Line } from './line'
import { Drag } from './drag'
import { Schema } from './schema'
import { Select } from './select'
import { Hotkey } from './hotkey'
import { deepClone } from '@utils'
import { PluginSystem } from './plugin'
import { EventQueue } from './event-queue'
import isPlainObject from 'lodash/isPlainObject'
export class Doc {
  frameWindow = null
  frameDocument = null
  #eq = null
  #isLoaded = false
  constructor(option) {
    this.event = new Emitter(this)
    this.node = new Node(this)
    this.history = new History(this)
    this.materials = new Materials(this)
    this.schema = new Schema(this)

    this.container = new Container(this)
    this.hover = new Hover(this)
    this.line = new Line(this)
    this.drag = new Drag(this)
    this.select = new Select(this)

    this.hotkey = new Hotkey()
    this.#eq = new EventQueue()
    this.pluginSystem = new PluginSystem(this)
    setTimeout(async () => {
      await this.pluginSystem.builtinPlugins(option?.plugins)
      this.#isLoaded = true
      this.#eq.run()
    })
  }
  get isLoaded() {
    return this.#isLoaded
  }

  /**
   * 不能在simulator中复制给vue响应式对象。
   * 会修改导致和canvas的schema隔离
   * @param {*} clone
   * @returns
   */
  getSchema(clone) {
    return clone ? deepClone(this.schema.schema) : this.schema.schema
  }

  /**
   * 获取保存用的schema，用来清除一些数据
   */
  getSaveSchema() {
    const schema = this.getSchema(true)
    const _run = (obj) => {
      if (obj.componentName === 'Page') {
        obj.children = obj.children.map((child) => {
          if (
            child.componentName === 'LADialog' ||
            child.componentName === 'LADrawer'
          ) {
            child.hidden = true
          }
          return _run(child)
        })
      }
      return obj
    }
    return _run(schema)
  }

  /**
   * 添加数据源里不用shouldFetch等函数
   * 1）函数里去掉original
   * 2）组件里的id都不需要
   * 3）空属性不需要，比如"style": ""， "data": {},
   */
  getSimpleSchema() {
    const schema = this.getSchema(true)
    // 处理函数
    if (schema.methods) {
      let methods = {}
      Object.keys(schema.methods).forEach((key) => {
        methods[key] = schema.methods[key]
        delete methods[key].original
      })
      schema.methods = methods
    }
    // 处理数据源
    if (schema.datasource) {
      schema.datasource.forEach((item) => {
        delete item.shouldFetch
        delete item.willFetch
        delete item.dataHandler
        delete item.errorHandler
      })
    }
    const isEmpty = (v) => {
      return (
        v === '' ||
        v == undefined ||
        v?.length === 0 ||
        JSON.stringify(v) === '{}'
      )
    }
    const _runObj = (obj) => {
      Object.keys(obj).forEach((k) => {
        if (k === 'id') {
          delete obj[k]
        } else if (isEmpty(obj[k])) {
          delete obj[k]
        } else if (k === 'children') {
          obj[k] = obj[k].map((obj) => {
            return _runObj(obj)
          })
        } else if (isPlainObject(obj[k])) {
          _runObj(obj[k])
        } else if (Array.isArray(obj[k])) {
          obj[k] = obj[k].filter((item) => {
            return !isEmpty(item)
          })
        }
      })
      return obj
    }

    return _runObj(schema)
  }

  setSchema(schema) {
    this.schema.set(schema)
    this.schema.update()
  }

  replaceSchema(schema) {
    this.schema.replace(schema)
  }

  getConfigure(componentName) {
    return this.materials.getConfigure(componentName)
  }

  getMaterials() {
    return this.materials.getMaterials()
  }
  // 根据组件名称获取物料配置
  getMaterial(componentName) {
    return this.materials.getMaterial(componentName)
  }
  // 可以添加多个物料
  // pushMaterials(materials) {
  //   return this.materials.push(materials)
  // }
  // 获取所有物料的片段
  getSnippets() {
    return this.materials.getSnippets()
  }
  // 选择节点
  selectNode(id) {
    this.node.selectNode(id)
  }

  copy() {
    this.node.copy()
  }

  getCurrent(clone) {
    const schema = this.select.getCurrent()
    return clone ? deepClone(schema) : schema
  }

  mount(frameWindow, frameDocument) {
    this.frameWindow = frameWindow
    this.frameDocument = frameDocument
    this.drag.bindEvent()
  }

  read(cb) {
    if (this.#isLoaded) {
      cb()
    } else {
      this.#eq.lazyAdd(cb)
    }
  }

  auto(cb) {
    this.#isLoaded ? this.#eq.add(cb) : this.#eq.lazyAdd(cb)
  }

  checkChange() {
    return (
      JSON.stringify(this.schema.schema || {}) !==
      JSON.stringify(this.schema.originalSchema || {})
    )
  }

  resetOriginal() {
    this.schema.resetOriginal()
  }

  getPlugins(area, cb) {
    this.pluginSystem.getPluginsByCallback(area, cb)
  }

  /**
   * 安全清除，在编辑器卸载时，清除不会影响功能。
   */
  safeClean() {
    this.event.clean()
    this.history.clean()
    this.schema.clean()
    this.hover.clean()
    this.select.clean()
    this.line.clean()
  }

  destroy() {
    this.hotkey.clean()
    this.drag.clean()
    this.materials.clean()
    this.safeClean()
    this.event = null
    this.history = null
    this.node = null
    this.material = null
    this.schema = null
    this.container = null
    this.hover = null
    this.line = null
  }
}
