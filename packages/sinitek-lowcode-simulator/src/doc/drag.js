import { getElement } from '../utils/dom'
import { Design } from 'sinitek-lowcode-shared'
import { POSITION } from './line'
// import { POSITION } from './line'
// Object.freeze防止初始化数据被修改
const initialDragState = Object.freeze({
  keydown: false, // 表示鼠标是否按下
  dragging: false, // 表示是否正在拖拽
  data: null, // 当前拖拽的数据
  pageData: null, // 当前拖拽需要合并到页面中的数据
  position: null, // ghost位置
  mouse: null, // 鼠标在视口中的位置
  element: null, // 当前拖拽的元素
  offset: {}, // 拖拽元素相对于视口的位置偏移量
  configure: null, // 当前拖拽组件的配置信息
})
export class Drag {
  // 解构初始化数据，不然不无法修改
  #state = { ...initialDragState }

  get state() {
    return this.#state
  }
  constructor(doc) {
    this.doc = doc
    // doc.container.checkMounted(() => {
    //   if (!doc.container?.el) return
    //   doc.container.el.addEventListener('dragover', this._onDragover.bind(this))
    //   doc.container.el.addEventListener('drop', this._onDrop.bind(this))
    //   doc.container.el.addEventListener('mousemove', this._mousemove.bind(this))
    // })
  }

  bindEvent() {
    if (!this.doc.container?.el) return
    this.doc.container.el.addEventListener(
      'dragover',
      this._onDragover.bind(this)
    )
    this.doc.container.el.addEventListener('drop', this._onDrop.bind(this))
    this.doc.container.el.addEventListener(
      'mousemove',
      this._mousemove.bind(this)
    )
  }
  /**
   * 检查鼠标是否在模拟器区域内
   * @param {x,y} param0 位置信息
   * @returns
   */
  isInSimulator({ x, y }) {
    const rect = this.doc.container.rect
    return (
      x > rect.x &&
      x < rect.x + rect.width &&
      y > rect.y &&
      y < rect.y + rect.height
    )
  }

  dragStart(
    data,
    pageData,
    element,
    { offsetX = 0, offsetY = 0, horizontal, vertical, width, height, x, y } = {}
  ) {
    // 表示鼠标按下开始拖拽
    this.#state.keydown = true
    this.#state.data = data
    this.#state.pageData = pageData
    // 记录上次一开始拖拽的时间
    this.#state.timer = Date.now()

    this.#state.element = element
    this.#state.offset = {
      offsetX,
      offsetY,
      horizontal,
      vertical,
      width,
      height,
      x,
      y,
    }
    this.#state.configure = this.doc.getConfigure(data.componentName)
    this.doc.hover.clean()
  }
  /**
   * 设置拖拽元素在视口中的位置
   * @param {*} param0
   */
  setDragPosition({
    clientX,
    x,
    clientY,
    y,
    // offsetBottom,
    // offsetTop,
  }) {
    const left = clientX + x
    const top = clientY + y
    // if (clientY < 20) {
    //   smoothScroll.start(false)
    // } else if (offsetBottom - clientY - offsetTop < 20) {
    //   smoothScroll.start(true)
    // } else {
    //   smoothScroll.stop()
    // }

    this.#state.position = { left, top }
  }

  _mousemove(event) {
    this._dragMove(event, true)
  }

  _dragMove(event, isHover) {
    if (
      (!this.#state.dragging &&
        this.#state.keydown &&
        new Date().getTime() - this.#state.timer < 200) ||
      (!this.#state.dragging && !this.#state.keydown)
    ) {
      return
    }

    const {
      x,
      y,
      bottom: offsetBottom,
      top: offsetTop,
    } = this.doc.container.rect
    const { clientX, clientY } = event

    this.#state.dragging = this.#state.keydown

    this.#state.mouse = { x: clientX, y: clientY }
    // 如果仅仅是mouseover事件直接return,并重置拖拽位置状态，优化性能
    const el = getElement(event.target)
    if (isHover) {
      // lineState.position = ''
      this.doc.line.set({ position: '' })
      // 如果当前id的元素已经被选中则不处理hover
      if (!el) return
      const id = el.getAttribute(Design.NODE_UID)
      const node = this.doc.getCurrent(true).schema
      if (node && node.id === id) {
        return
      }
      this.doc.hover.select(el, null)
      return
    }

    if (this.#state.dragging) {
      this.setDragPosition({ clientX, x, clientY, y, offsetBottom, offsetTop })
    }
    // 防止弹跳
    if (this.preMouse?.x !== clientX && this.preMouse?.y !== clientY) {
      this.doc.hover.select(el)
      this.doc.line.select(el, this.#state.data)
    }
    this.preMouse = { x: clientX, y: clientY }
  }

  _onDragover(e) {
    e.dataTransfer.dropEffect = 'move'
    e.preventDefault()
    if (!this.isInSimulator({ x: e.pageX, y: e.pageY })) {
      this.doc.line.clean()
      return
    }
    this._dragMove(e)
  }
  _onDrop(e) {
    if (!this.isInSimulator({ x: e.pageX, y: e.pageY })) return
    e.preventDefault()
    this._onMouseUp(e)
  }
  _onMouseUp() {
    const { dragging, data } = this.#state
    const { position, forbidden } = this.doc.line.state
    // 新增时候没有这个id
    const sourceId = data?.id
    const lineId = this.doc.line.state.id
    const allowInsert = position !== POSITION.FORBID && !forbidden
    const configure = this.doc.getConfigure(data.componentName)
    const insertSuccessEmit = () => {
      this.doc.event.emit('drag:end')
    }
    if (configure?.component?.isModal) {
      // 直接插入page的children
      const { parent, node } =
        this.doc.node.getNode(this.doc.schema.schema.id, true) || {} // target
      const targetNode = { parent, node, data: { ...data, hidden: false } }
      this.doc.node.insertNode(targetNode, position)
    } else if (!allowInsert) {
      this.doc.event.emit('drag:error', this.doc.line.state.errorMessage)
    } else if (dragging) {
      const { parent, node } = this.doc.node.getNode(lineId, true) || {} // target
      const targetNode = { parent, node, data: { ...data, hidden: false } }
      if (sourceId) {
        // 内部拖拽
        if (sourceId !== lineId) {
          this.doc.node.removeNode(this.doc.node.getNode(sourceId, true))
          this.doc.node.insertNode(targetNode, position)
          insertSuccessEmit()
        }
      } else {
        this.doc.node.insertNode(targetNode, position)
        insertSuccessEmit()
      }
    }

    // 重置拖拽状态
    this.dragEnd()
  }

  set(state) {
    Object.assign(this.#state, { ...state })
  }

  dragEnd() {
    const { element, data } = this.#state

    if (element) {
      data.props = data.props || {}
      data.props.style = element.style.cssText
    }

    // 重置拖拽状态
    this.set(initialDragState)
    // 重置拖拽插入位置状态
    this.doc.line.clean()
    this.doc.hover.clean()
    // smoothScroll.stop()
  }
  clean() {}
}
