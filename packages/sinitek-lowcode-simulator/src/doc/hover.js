import { deepClone } from '@utils'
import { getRect } from '../utils/dom'
import { Design } from 'sinitek-lowcode-shared'

const initialHoverState = {
  top: 0,
  height: 0,
  width: 0,
  left: 0,
  position: '',
  id: '',
  config: null,
  doc: null,
  componentName: '',
}
export class Hover {
  #state = { ...initialHoverState }
  #current

  get state() {
    return this.#state
  }

  constructor(doc) {
    this.doc = doc
  }

  select(element) {
    if (!element) {
      this.clean()
      return
    }
    const componentName = element.getAttribute(Design.NODE_TAG)
    const configure = this.doc.getConfigure(componentName)
    const rootSelector = configure?.component?.rootSelector
    element = rootSelector ? element.querySelector(rootSelector) : element

    if (this.#current === element) return
    this.#current = element

    this.#state.configure = configure

    const rect = getRect(element)
    // 设置元素hover状态
    Object.assign(this.#state, {
      width: rect.width,
      height: rect.height,
      top: rect.top,
      left: rect.left,
      componentName,
      id: element.getAttribute(Design.NODE_UID),
    })
    this.set(this.#state)
    this.update()
    return undefined
  }
  set(state = {}) {
    Object.assign(this.#state, { ...state })
  }
  update() {
    this.doc.event.emit('hover:change', deepClone(this.#state))
  }
  clean() {
    this.set({ ...initialHoverState })
    this.#current = null
    this.update()
  }

  onChange(fn) {
    this.doc.event.on('hover:change', fn)
  }
}
