export const HistoryState = {
  UNDO: 3,
  REDO: 1,
  BOTH: 4,
  NONE: 0,
}

const maxLength = 10

const schema2String = (schema) => {
  return JSON.stringify(schema)
}

const string2Schema = (string) => {
  let schema

  try {
    schema = JSON.parse(string)
  } catch (error) {
    schema = {}
    console.error('history string2Schema error', error)
  }

  return schema
}

/**
 * 历史记录类
 */
export default class History {
  #history = []
  #index = 0

  constructor(doc) {
    this.doc = doc
  }

  state = HistoryState.NONE

  /**
   * 获取历史记录数组
   */
  getHistory() {
    return this.#history
  }

  /**
   * 添加历史记录
   */
  addHistory() {
    const len = this.#history.length
    if (this.#index < len - 1) {
      this.#history.splice(this.#index + 1)
    }
    if (len >= maxLength) {
      this.#history.splice(0, len - maxLength + 1)
    }
    this.#index = this.#history.push(schema2String(this.doc.getSchema())) - 1
    this._updateState()
  }

  /**
   * 更新状态, 根据当前索引和历史记录数组的长度判断是否可以撤销或重做，并触发事件通知
   */
  _updateState() {
    let _state = HistoryState.NONE
    const i = this.#index
    if (i > 0 && i <= this.#history.length - 1) {
      _state += HistoryState.UNDO
    } // 撤销
    if (this.#history.length - 1 > 0 && this.#history.length - 1 > i) {
      _state += HistoryState.REDO
    }
    this.state = _state

    this.doc.event.emit('history:change', this.state)
  }

  /**
   * 清空历史记录数组和索引
   */
  clean() {
    this.#history = []
    this.#index = 0
  }

  /**
   * 跳转到指定索引的历史记录，并更新文档的schema和状态
   * @param {*} index 历史数据下标
   */
  go(index) {
    this.doc.setSchema(string2Schema(this.#history[index]))
    this._updateState()
    this.doc.select.clean()
  }

  canBack() {
    return [HistoryState.BOTH, HistoryState.UNDO].includes(this.state)
  }

  canForward() {
    return [HistoryState.BOTH, HistoryState.REDO].includes(this.state)
  }

  /**
   * 撤销操作，如果当前状态允许，则向前移动索引并跳转到对应的历史记录
   */
  back() {
    if ([HistoryState.BOTH, HistoryState.UNDO].includes(this.state)) {
      this.go(--this.#index)
    }
    return this.#index
  }

  /**
   * 重做操作，如果当前状态允许，则向后移动索引并跳转到对应的历史记录
   */
  forward() {
    if ([HistoryState.BOTH, HistoryState.REDO].includes(this.state)) {
      this.go(++this.#index)
    }
    return this.#index
  }

  /**
   * 注册历史状态变化事件监听器
   * @param {*} fn 回调
   */
  onChange(fn) {
    this.doc.event.on('history:change', fn)
  }
}
