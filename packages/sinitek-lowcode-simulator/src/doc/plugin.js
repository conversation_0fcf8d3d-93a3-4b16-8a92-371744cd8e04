import { getGlobalPlugins } from 'sinitek-lowcode-shared'

export const PluginEvent = {
  register: 'register:add',
  unregister: 'register:remove',
  change: 'register:change',
}

/**
 * 插件系统类
 * 用于管理和注册插件
 */
export class PluginSystem {
  #cbList = new Map()
  constructor(doc) {
    this.plugins = new Map()
    this.doc = doc
    const fn = (area) => {
      if (!this.doc.isLoaded) return
      this.#cbList.get(area)?.forEach((fn) => {
        fn()
      })
    }
    this.doc.event.on(PluginEvent.register, fn)
    this.doc.event.on(PluginEvent.unregister, fn)
    this.doc.event.on(PluginEvent.change, fn)
  }

  async builtinPlugins({ disabled } = {}) {
    // Plugins 改成leftArea
    const leftArea = await import('../plugins/left-area')
    // Settings 改成rightArea
    const rightArea = await import('../plugins/right-area')
    // Toolbar 改成topArea
    const topArea = await import('../plugins/top-area')
    // 浮动插件
    const floatArea = await import('../plugins/float-area')
    const selectArea = await import('../canvas/components/actions')
    const globalPlugins = await getGlobalPlugins()
    const builtinPlugins = [
      ...globalPlugins,
      ...leftArea.default.map((e) => {
        e.area = 'leftArea'
        return e
      }),
      ...rightArea.default.map((e) => {
        e.area = 'rightArea'
        return e
      }),
      ...topArea.default.map((e) => {
        e.area = 'topArea'
        return e
      }),
      ...selectArea.default.map((e) => {
        e.area = 'selectActionArea'
        return e
      }),
      ...floatArea.default.map((e) => {
        e.area = 'floatArea'
        return e
      }),
    ]
    await Promise.all(builtinPlugins.map((e) => this.register(e)))
    if (disabled?.length) {
      disabled.forEach((e) => {
        this.disable(e.area, e.id)
      })
    }
  }

  /**
   * 注册插件
   * @param {Object} plugin 插件对象
   * @returns {PluginSystem} 当前插件系统实例
   */
  register(plugin) {
    if (!plugin.id) {
      console.error('插件必须有ID属性')
      return this
    }
    const level1 = this._getPlugin(plugin.area)
    if (level1.has(plugin.id)) {
      console.warn(`插件 "${plugin.id}" 已经注册过了`)
      return this
    }

    level1.set(plugin.id, plugin)
    this.doc.event.emit(PluginEvent.register, plugin.area)
    return this
  }

  unregister(plugin) {
    const level1 = this._getPlugin(plugin.area)
    level1.delete(plugin.id)
    this.doc.event.emit(PluginEvent.unregister, plugin.area)
    return this
  }

  _getPlugin(area, id) {
    let level1 = this.plugins.get(area)
    if (!level1) {
      level1 = new Map()
      this.plugins.set(area, level1)
    }
    if (id) {
      let level2 = level1.get(id)
      return level2
    }
    return level1
  }

  getPlugin(area, id) {
    return this._getPlugin(area, id)
  }

  disable(area, id) {
    const plugin = this._getPlugin(area, id)
    if (plugin) {
      plugin.disabled = true
      this.doc.event.emit(PluginEvent.change, area)
    }
    return this
  }

  enable(area, id) {
    const plugin = this._getPlugin(area, id)
    if (plugin) {
      plugin.disabled = false
      this.doc.event.emit(PluginEvent.change, area)
    }
    return this
  }

  getPlugins(area) {
    return Array.from(this._getPlugin(area))

      .map(([_, plugin]) => plugin)
      .filter((plugin) => !plugin.disabled)
      .sort((a, b) => (a.index || 0) - (b.index || 0))
  }

  getPluginsByCallback(area, cb) {
    const fn = () => {
      cb(this.getPlugins(area))
    }
    fn()
    this.#cbList.set(area, [...(this.#cbList.get(area) || []), fn])
    // this.getPlugins(area).forEach(fn)
  }
}

export default PluginSystem
