import CollapseTransition from 'element-ui/lib/transitions/collapse-transition'

import {
  RadioGroup as ElRadioGroup,
  RadioButton as ElRadioButton,
  Select as ElSelect,
  Option as ElOption,
  Switch as ElSwitch,
  Input as ElInput,
  InputNumber as ElInputNumber,
  ColorPicker as ElColorPicker,
  Autocomplete as ElAutocomplete,
  Icon as ElIcon,
  Dropdown as ElDrop<PERSON>,
  DropdownMenu as ElDropdownMenu,
  DropdownItem as ElDropdownItem,
  Dialog as ElDialog,
  Tree as ElTree,
  Form as ElForm,
  FormItem as ElFormItem,
  Popover as ElPopover,
  <PERSON><PERSON> as ElButton,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
} from 'element-ui'

const components = {
  CollapseTransition,
  ElRadioGroup,
  ElRadioButton,
  ElSelect,
  ElOption,
  ElSwitch,
  ElInput,
  ElInputNumber,
  ElColorPicker,
  ElAutocomplete,
  ElIcon,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElDialog,
  ElTree,
  ElForm,
  ElFormItem,
  ElPopover,
  ElButton,
  El<PERSON><PERSON><PERSON>,
}
export const install = (vue) => {
  Object.keys(components).forEach((key) => {
    vue.component(components[key].name, components[key])
  })
}
export {
  CollapseTransition,
  ElRadioGroup,
  ElRadioButton,
  ElSelect,
  ElOption,
  ElSwitch,
  ElInput,
  ElInputNumber,
  ElColorPicker,
  ElAutocomplete,
  ElIcon,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElDialog,
  ElTree,
  ElForm,
  ElFormItem,
  ElPopover,
  ElButton,
  ElAlert,
}
