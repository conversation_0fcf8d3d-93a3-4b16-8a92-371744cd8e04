import isFunction from 'lodash/isFunction'
export const guid = () => {
  return 'xxxxxxxx'.replace(/[x]/g, (c) => {
    const random = parseFloat(
      '0.' + crypto.getRandomValues(new Uint32Array(1))[0]
    )
    const r = (random * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

export function deepClone(val) {
  return JSON.parse(JSON.stringify(val))
}

export { isFunction }

export const isVueComponent = (component) => {
  return Object.hasOwn(component, '_compiled')
}

export const arrToUnderlineVariable = (arr) => {
  return arr
    .join('_')
    .replaceAll(/[^a-zA-Z0-9_]/g, '_')
    .toLowerCase()
}

export * from './dom'
export * from './js/format'
export * from './js/ast'
export * from './zIndexManage'
export * from './createComponent'
export * from './lowcode'
