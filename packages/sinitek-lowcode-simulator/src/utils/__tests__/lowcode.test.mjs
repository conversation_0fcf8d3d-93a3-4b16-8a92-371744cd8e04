import { getLCMaterial } from '../lowcode.js'

const data = {
  material: {
    reflect: [],
    props: [
      {
        key: 'key1',
        title: '属性标题',
        setter: 'StringSetter',
      },
      {
        key: 'key1',
        title: '属性标题',
        setter: 'StringSetter',
        defaultValue: 'df',
      },
      {
        key: 'key1',
        title: '属性标题',
        setter: 'TextareaSetter',
      },
      {
        key: 'key1',
        title: '属性标题',
        setter: 'TextareaSetter',
        defaultValue: 'df',
      },
      {
        key: 'key1',
        title: '属性标题',
        setter: 'NumberSetter',
      },
      {
        key: 'key1',
        title: '属性标题',
        setter: 'NumberSetter',
        defaultValue: 1,
      },
      {
        key: 'key1',
        title: '属性标题',
        setter: 'BoolSetter',
      },
      {
        key: 'key1',
        title: '属性标题',
        setter: 'BoolSetter',
        defaultValue: false,
      },
      {
        key: 'key1',
        title: '属性标题',
        setter: 'ColorSetter',
      },
      {
        key: 'key1',
        title: '属性标题',
        setter: 'ColorSetter',
        defaultValue: '#ccc',
      },
      {
        key: 'key1',
        title: '属性标题',
        setter: 'IconSetter',
        iconType: 'element-ui',
      },
      {
        key: 'key1',
        title: '属性标题',
        setter: 'IconSetter',
        iconType: 'svg-icon',
        defaultValue: 'user',
      },
      {
        key: 'propName',
        title: '属性标题',
        setter: 'SelectSetter',
        options: [
          {
            label: 'primary',
            value: 'primary',
          },
          {
            label: 'success',
            value: 'success',
          },
        ],
      },
      {
        key: 'propName',
        title: '属性标题',
        setter: 'SelectSetter',
        defaultValue: 'primary',
        options: [
          {
            label: 'primary',
            value: 'primary',
          },
          {
            label: 'success',
            value: 'success',
          },
        ],
      },
      {
        key: 'propName',
        title: '属性标题',
        setter: 'SelectSetter',
        options: [
          {
            label: 'primary',
            value: 'primary',
          },
          {
            label: 'success',
            value: 'success',
          },
        ],
      },
      {
        key: 'propName',
        title: '属性标题',
        setter: 'RadioGroupSetter',
        defaultValue: 'primary',
        initialValue: {
          title: '标题',
          name: '名称',
        },
        options: [
          {
            key: 'key1',
            title: '属性标题',
            setter: 'StringSetter',
          },
          {
            key: 'key1',
            title: '属性标题',
            setter: 'NumberSetter',
          },
          {
            key: 'key1',
            title: '属性标题',
            setter: 'BoolSetter',
          },
        ],
      },
      {
        key: 'propName',
        title: '属性标题',
        setter: 'RadioGroupSetter',
        defaultValue: 'primary',
        initialValue: {
          title: '标题',
          name: '名称',
        },
        options: [
          {
            key: 'key1',
            title: '属性标题',
            setter: 'StringSetter',
            defaultValue: 'df',
          },
          {
            key: 'key1',
            title: '属性标题',
            setter: 'NumberSetter',
            defaultValue: 1,
          },
          {
            key: 'key1',
            title: '属性标题',
            setter: 'BoolSetter',
            defaultValue: true,
          },
        ],
      },
      {
        key: 'propName',
        title: '属性标题',
        setter: 'ObjectSetter',
        defaultValue: 'primary',
        initialValue: {
          title: '标题',
          name: '名称',
        },
        items: [
          {
            key: 'key1',
            title: '属性标题',
            setter: 'StringSetter',
            defaultValue: 'df',
          },
          {
            key: 'key1',
            title: '属性标题',
            setter: 'NumberSetter',
            defaultValue: 1,
          },
          {
            key: 'key1',
            title: '属性标题',
            setter: 'BoolSetter',
            defaultValue: true,
          },
        ],
      },
      {
        key: 'propName',
        title: '属性标题',
        setter: 'ObjectSetter',
        defaultValue: 'primary',
        initialValue: {
          title: '标题',
          name: '名称',
        },
        items: [
          {
            key: 'key1',
            title: '属性标题',
            setter: 'StringSetter',
          },
          {
            key: 'key1',
            title: '属性标题',
            setter: 'NumberSetter',
          },
          {
            key: 'key1',
            title: '属性标题',
            setter: 'BoolSetter',
          },
        ],
      },
      {
        key: 'propName',
        title: '属性标题',
        setter: 'ArraySetter',
        defaultValue: 'primary',
        initialValue: {
          title: '标题',
          name: '名称',
        },
        items: [
          {
            key: 'key1',
            title: '属性标题',
            setter: 'StringSetter',
            defaultValue: 'df',
          },
          {
            key: 'key1',
            title: '属性标题',
            setter: 'NumberSetter',
            defaultValue: 1,
          },
          {
            key: 'key1',
            title: '属性标题',
            setter: 'BoolSetter',
            defaultValue: true,
          },
        ],
      },
      {
        key: 'propName',
        title: '属性标题',
        setter: 'ArraySetter',
        defaultValue: 'primary',
        initialValue: {
          title: '标题',
          name: '名称',
        },
        items: [
          {
            key: 'key1',
            title: '属性标题',
            setter: 'StringSetter',
          },
          {
            key: 'key1',
            title: '属性标题',
            setter: 'NumberSetter',
          },
          {
            key: 'key1',
            title: '属性标题',
            setter: 'BoolSetter',
          },
        ],
      },
    ],
    configure: {},
  },
}

describe('测试物料数据生成', () => {
  test('测试物料setter设置是否正常', () => {
    const fn = jest.fn()

    const result = getLCMaterial(data.material, fn)

    expect(result).toMatchSnapshot()
  })
})
