// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`测试物料数据生成 测试物料setter设置是否正常 1`] = `
"{
    title: '低代码组件',
    lowcodeComponent: true,
    componentName,
    props: [
  {
    "title": "属性标题",
    "name": "key1",
    "setter": "StringSetter"
  },
  {
    "title": "属性标题",
    "name": "key1",
    "setter": "StringSetter",
    "defaultValue": "df"
  },
  {
    "title": "属性标题",
    "name": "key1",
    "setter": "TextareaSetter"
  },
  {
    "title": "属性标题",
    "name": "key1",
    "setter": "TextareaSetter",
    "defaultValue": "df"
  },
  {
    "title": "属性标题",
    "name": "key1",
    "setter": "NumberSetter"
  },
  {
    "title": "属性标题",
    "name": "key1",
    "setter": "NumberSetter",
    "defaultValue": 1
  },
  {
    "title": "属性标题",
    "name": "key1",
    "setter": "BoolSetter"
  },
  {
    "title": "属性标题",
    "name": "key1",
    "setter": "BoolSetter",
    "defaultValue": false
  },
  {
    "title": "属性标题",
    "name": "key1",
    "setter": "ColorSetter"
  },
  {
    "title": "属性标题",
    "name": "key1",
    "setter": "ColorSetter",
    "defaultValue": "#ccc"
  },
  {
    "title": "属性标题",
    "name": "key1",
    "setter": {
      "componentName": "IconSetter",
      "props": {
        "type": "element-ui"
      }
    }
  },
  {
    "title": "属性标题",
    "name": "key1",
    "setter": {
      "componentName": "IconSetter",
      "props": {
        "type": "svg-icon"
      }
    },
    "defaultValue": "user"
  },
  {
    "title": "属性标题",
    "name": "propName",
    "setter": {
      "componentName": "SelectSetter",
      "props": {
        "options": [
          {
            "label": "primary",
            "value": "primary"
          },
          {
            "label": "success",
            "value": "success"
          }
        ]
      }
    }
  },
  {
    "title": "属性标题",
    "name": "propName",
    "setter": {
      "componentName": "SelectSetter",
      "props": {
        "options": [
          {
            "label": "primary",
            "value": "primary"
          },
          {
            "label": "success",
            "value": "success"
          }
        ]
      }
    },
    "defaultValue": "primary"
  },
  {
    "title": "属性标题",
    "name": "propName",
    "setter": {
      "componentName": "SelectSetter",
      "props": {
        "options": [
          {
            "label": "primary",
            "value": "primary"
          },
          {
            "label": "success",
            "value": "success"
          }
        ]
      }
    }
  },
  {
    "title": "属性标题",
    "name": "propName",
    "setter": {
      "componentName": "RadioGroupSetter",
      "props": {
        "options": [
          {
            "key": "key1",
            "title": "属性标题",
            "setter": "StringSetter"
          },
          {
            "key": "key1",
            "title": "属性标题",
            "setter": "NumberSetter"
          },
          {
            "key": "key1",
            "title": "属性标题",
            "setter": "BoolSetter"
          }
        ]
      }
    },
    "defaultValue": "primary"
  },
  {
    "title": "属性标题",
    "name": "propName",
    "setter": {
      "componentName": "RadioGroupSetter",
      "props": {
        "options": [
          {
            "key": "key1",
            "title": "属性标题",
            "setter": "StringSetter",
            "defaultValue": "df"
          },
          {
            "key": "key1",
            "title": "属性标题",
            "setter": "NumberSetter",
            "defaultValue": 1
          },
          {
            "key": "key1",
            "title": "属性标题",
            "setter": "BoolSetter",
            "defaultValue": true
          }
        ]
      }
    },
    "defaultValue": "primary"
  },
  {
    "title": "属性标题",
    "name": "propName",
    "setter": {
      "componentName": "ObjectSetter",
      "initialValue": {
        "title": "标题",
        "name": "名称"
      },
      "props": {
        "config": {
          "items": [
            {
              "key": "key1",
              "title": "属性标题",
              "setter": "StringSetter",
              "defaultValue": "df"
            },
            {
              "key": "key1",
              "title": "属性标题",
              "setter": "NumberSetter",
              "defaultValue": 1
            },
            {
              "key": "key1",
              "title": "属性标题",
              "setter": "BoolSetter",
              "defaultValue": true
            }
          ]
        }
      }
    },
    "defaultValue": "primary"
  },
  {
    "title": "属性标题",
    "name": "propName",
    "setter": {
      "componentName": "ObjectSetter",
      "initialValue": {
        "title": "标题",
        "name": "名称"
      },
      "props": {
        "config": {
          "items": [
            {
              "key": "key1",
              "title": "属性标题",
              "setter": "StringSetter"
            },
            {
              "key": "key1",
              "title": "属性标题",
              "setter": "NumberSetter"
            },
            {
              "key": "key1",
              "title": "属性标题",
              "setter": "BoolSetter"
            }
          ]
        }
      }
    },
    "defaultValue": "primary"
  },
  {
    "title": "属性标题",
    "name": "propName",
    "setter": {
      "componentName": "ArraySetter",
      "props": {
        "itemSetter": {
          "componentName": "ObjectSetter",
          "initialValue": {
            "title": "标题",
            "name": "名称"
          },
          "props": {
            "config": {
              "items": [
                {
                  "key": "key1",
                  "title": "属性标题",
                  "setter": "StringSetter",
                  "defaultValue": "df"
                },
                {
                  "key": "key1",
                  "title": "属性标题",
                  "setter": "NumberSetter",
                  "defaultValue": 1
                },
                {
                  "key": "key1",
                  "title": "属性标题",
                  "setter": "BoolSetter",
                  "defaultValue": true
                }
              ]
            }
          }
        }
      }
    },
    "defaultValue": "primary"
  },
  {
    "title": "属性标题",
    "name": "propName",
    "setter": {
      "componentName": "ArraySetter",
      "props": {
        "itemSetter": {
          "componentName": "ObjectSetter",
          "initialValue": {
            "title": "标题",
            "name": "名称"
          },
          "props": {
            "config": {
              "items": [
                {
                  "key": "key1",
                  "title": "属性标题",
                  "setter": "StringSetter"
                },
                {
                  "key": "key1",
                  "title": "属性标题",
                  "setter": "NumberSetter"
                },
                {
                  "key": "key1",
                  "title": "属性标题",
                  "setter": "BoolSetter"
                }
              ]
            }
          }
        }
      }
    },
    "defaultValue": "primary"
  }
],
    configure: {"component":{"isContainer":false},"supports":{"condition":true,"style":true}},
    snippets: [
    {
      title: '低代码组件',
      screenshot: '',
      schema: {
        componentName,
        props: {"key1":"user","propName":"primary"},
        children: [],
      },
    },
  ],
  }"
`;
