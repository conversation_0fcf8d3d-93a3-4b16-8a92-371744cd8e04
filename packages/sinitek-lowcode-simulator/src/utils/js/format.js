import * as prettier from 'prettier/standalone'
import * as parserBabel from 'prettier/plugins/babel'
import * as parserEstree from 'prettier/plugins/estree'
import * as parseCss from 'prettier/plugins/postcss'
export const formatScript = async (code) => {
  return prettier.format(code, {
    semi: false,
    parser: 'babel',
    plugins: [parserBabel, parserEstree],
  })
}

export const formatCss = (string) =>
  prettier.format(string, {
    parser: 'css',
    plugins: [parseCss],
  })
export const formatJson = (string) =>
  prettier.format(string, {
    parser: 'json',
    plugins: [parserBabel, parserEstree],
    trailingComma: 'es5',
  })
