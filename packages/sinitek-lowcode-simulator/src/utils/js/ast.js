import { parseSync, transformFromAstSync, createConfigItem } from '@babel/core'

// 配置Vue JSX预设的选项
const vueJsxOptions = {
  injectH: true, // 自动注入h函数
  vModel: true, // 支持v-model
  vOn: true, // 支持v-on
  compositionAPI: false, // 根据项目需要设置
  functional: true, // 支持函数式组件
  mergeProps: true, // 合并props
}

export const string2AST = (code) => {
  return parseSync(code, {
    parserOpts: {
      plugins: ['jsx'],
      sourceType: 'module',
    },
    presets: [
      createConfigItem([require('@vue/babel-preset-jsx'), vueJsxOptions], {
        type: 'preset',
      }),
    ],
  })
}

export const AST2string = (ast, opts = {}) => {
  let newAst = ast
  if (ast.type !== 'Program') {
    newAst = {
      type: 'Program',
      body: [ast],
    }
  }
  const extraOpts = {}
  if (opts.usePresetEnv) {
    extraOpts.plugins = [
      createConfigItem(require('@babel/plugin-transform-optional-chaining')),
      createConfigItem(
        require('@babel/plugin-transform-nullish-coalescing-operator')
      ),
    ]
    extraOpts.presets = [
      createConfigItem([require('@vue/babel-preset-jsx'), vueJsxOptions], {
        type: 'preset',
      }),
    ]
    delete opts.usePresetEnv
  }

  const options = {
    ...extraOpts,
    generatorOpts: {
      retainLines: true,
    },
    parserOpts: {
      plugins: ['jsx'],
      sourceType: 'module',
    },
    ...opts,
  }
  return transformFromAstSync(newAst, '', options).code
}
