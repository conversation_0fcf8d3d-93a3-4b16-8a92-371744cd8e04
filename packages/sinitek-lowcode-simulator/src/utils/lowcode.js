/**
 * 低代码生成器，生成低代码组件，配置和代码
 */
import { JSExpression } from 'sinitek-lowcode-shared'
import { formatScript } from './js/format'

function createSetter(e) {
  let result = {
    title: e.title,
    name: e.key,
    setter: e.setter,
    defaultValue: e.defaultValue,
    valueChange: e.valueChange,
  }

  if (['SelectSetter', 'RadioGroupSetter'].includes(e.setter)) {
    result.setter = {
      componentName: e.setter,
      props: {
        options: e.options,
      },
    }
  } else if (e.setter === 'ObjectSetter') {
    result.setter = {
      componentName: 'ObjectSetter',
      initialValue: e.initialValue,
      props: {
        config: {
          items: e.items,
        },
      },
    }
  } else if (e.setter === 'IconSetter') {
    result.setter = {
      componentName: 'IconSetter',
      props: {
        type: e.iconType,
      },
    }
  } else if (e.setter === 'ArraySetter') {
    result.setter = {
      componentName: 'ArraySetter',
      props: {
        itemSetter: {
          componentName: 'ObjectSetter',
          initialValue: e.initialValue,
          props: {
            config: {
              items: e.items,
            },
          },
        },
      },
    }
  }

  return result
}

export function getLCMaterial(material, getMaterial) {
  const props = [...(material?.props ?? []), ...(material?.reflect ?? [])]
  const processedProps = props.map((e) => {
    if (e.id) {
      const result = getMaterial(e.id).props.find((f) => f.name === e.value)
      return { ...result, name: e.key }
    }
    return createSetter(e)
  })

  const configure = Object.keys(material?.configure ?? {}).length
    ? material.configure
    : {
        component: { isContainer: false },
        supports: {
          condition: true,
          style: true,
        },
      }

  const code = `export default {
    title: '低代码组件',
    lowcodeComponent: true,
    componentName: '${material.componentName}',
    props: ${JSON.stringify(processedProps, null, 2)},
    configure: ${JSON.stringify(configure)},
    snippets: [
    {
      title: '低代码组件',
      screenshot: '',
      schema: {
        componentName: '${material.componentName}',
        props: ${JSON.stringify(
          props.reduce((cur, next) => {
            cur[next.key] = next.defaultValue
            return cur
          }, {})
        )},
        children: [],
      },
    },
  ],
  }`
  return formatScript(code)
}

export function getRenderConfig(schema) {
  const material = schema.material

  const props = new Map(
    Object.entries(Object.groupBy(material?.reflect ?? [], ({ id }) => id))
  )
  let newSchema = JSON.parse(JSON.stringify(schema))
  const _run = (schema, props) => {
    if (props.has(schema.id)) {
      const override = props.get(schema.id).reduce((cur, n) => {
        cur[n.value] = {
          type: JSExpression,
          value: `this.props.${n.key}`,
        }
        return cur
      }, {})
      Object.assign(schema.props, override)
    }

    if (schema.children) {
      schema.children = schema.children.map((e) => _run(e, props))
    }
    return schema
  }

  return formatScript(
    `export default ${JSON.stringify(_run(newSchema, props))}  `
  )
}

export function getLowcode(componentName, schema) {
  const material = schema.material

  const _props = [...(material?.props ?? []), ...(material?.reflect ?? [])]

  const code = `
import { LcRender } from 'sinitek-lowcode-manager';

import config from './${componentName}-config.js';
export default {
  name: '${componentName}',
  props: ${JSON.stringify(_props.map((e) => e.key))},
  inject: {
    mode: {default: null},
    fetcher: { default: null },
  },
  provide() {
    return {
      lowcodeProps: this.$props,
      lowcodeEmit: this.emit,
      lowcodeScope: {
        mode: this.mode,
        componentName: '${componentName}',
      }
    };
  },
  render() {
    return (
      <div>
        <LcRender config={config} fetcher={this.fetcher} isLCComponent ref="render" />
      </div>
    );
  },
  mounted() {
    // 实现methods调用
    // 下划线开头和生命周期会过滤掉掉，不能被调用。
    const methods = this.$refs.render.getMethods();
    if (Object.keys(methods).length) {
      Object.entries(methods).forEach(([key, value]) => {
        this[key] = value;
      });
    }
  },
  methods: {
    emit(...args) {
      this.$emit(...args);
    },
  },
};
`

  return formatScript(code)
}
