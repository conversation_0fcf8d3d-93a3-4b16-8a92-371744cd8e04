import { Design } from 'sinitek-lowcode-shared'

export const getElement = (element) => {
  if (!element || element.nodeType !== 1) {
    return undefined
  }

  if (element.getAttribute(Design.NODE_UID)) {
    return element
  } else if (element.parentElement) {
    return getElement(element.parentElement)
  }

  return undefined
}

export const findParent = (element, selector) => {
  if (!element || !selector) {
    return undefined
  }

  if (element.matches(selector)) {
    return element
  }

  return findParent(element.parentElement, selector)
}

export const getRect = (element) => {
  return element.getBoundingClientRect()
}

export function getParentEl(element) {
  return element?.parentElement
}

export const clickOutside = (elements, callback) => {
  let _els = Array.isArray(elements) ? elements : [elements]
  const handler = (e) => {
    let hasOutsideAttr = false
    let parent = e.target
    while (parent && !hasOutsideAttr) {
      hasOutsideAttr = parent?.hasAttribute?.('click-outside')
      parent = getParentEl(parent)
    }
    if (!hasOutsideAttr && !_els.some((el) => e.composedPath().includes(el))) {
      callback()
    }
  }
  document.addEventListener('click', handler)
  return () => {
    document.removeEventListener('click', handler)
  }
}

export const bind = (element, event, handler) => {
  element.addEventListener(event, handler)
  return () => {
    element.removeEventListener(event, handler)
  }
}

export const combine = (...fns) => {
  return (...args) => {
    fns.forEach((fn) => fn(...args))
  }
}
