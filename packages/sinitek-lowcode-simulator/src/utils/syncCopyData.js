/**
 * 创建广播通道，用于在不同标签页之间同步复制数据
 */
const bc = new BroadcastChannel('syncCopyData')
/**
 * 存储复制数据的sessionStorage键名
 */
const copyDataKey = 'copyData'

/**
 * 监听广播消息
 * @param {MessageEvent} e - 广播消息事件
 */
bc.onmessage = (e) => {
  if (e.data.type === 'searchTab') {
    // 当收到查询请求时，发送当前标签页的复制数据
    bc.postMessage({ type: 'syncTabSession', data: getShareData(copyDataKey) })
  } else if (e.data.type === 'syncTabSession' && e.data.data) {
    // 当收到其他标签页的数据同步请求时，更新当前标签页的数据
    setShareData(copyDataKey, e.data.data)
  }
}

/**
 * 监听页面可见性变化事件
 * 当页面隐藏时，将当前标签页的数据同步到其他标签页
 */
window.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    // 如果当前tab隐藏，则把当前tab的sessionStorage数据同步到其他tab
    bc.postMessage({ type: 'syncTabSession', data: getShareData(copyDataKey) })
  }
})

/**
 * 页面加载时 搜索是否有tab
 */
bc.postMessage({ type: 'searchTab' })

/**
 * 从sessionStorage获取数据
 * @param {string} key - 存储键名
 * @returns {string|null} - 存储的数据
 */
function getShareData(key) {
  return sessionStorage.getItem(key)
}

/**
 * 将数据保存到sessionStorage
 * @param {string} key - 存储键名
 * @param {string} data - 要存储的数据
 */
function setShareData(key, data) {
  sessionStorage.setItem(key, data)
}
