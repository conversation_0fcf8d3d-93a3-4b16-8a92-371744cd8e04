export function overrideProps(props, override) {
  if (!override) return props
  const newProps = props.slice()

  override.forEach((e) => {
    const { name } = e
    const index = props.findIndex((p) => p.name === name)
    if (index > -1) {
      newProps[index] = { ...props[index], ...e }
    }
  })
  return newProps
}

export function isRootComponent(componentName) {
  return componentName === 'Page'
}
