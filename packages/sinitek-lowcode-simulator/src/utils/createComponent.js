/**
 * 注意vue2下会修改v-model的映射关系，变为vue3的格式，输出变化兼容input
 * @param {*} option
 * @returns
 */
export function createComponent(option) {
  option.inject = option.inject || []
  if (!Array.isArray(option.inject)) {
    option.inject.$doc = { default: [] }
  } else {
    option.inject.push('$doc')
  }
  option.methods = option.methods || {}
  option.methods._emit = function (...args) {
    this.$emit('input', ...args)
    this.$emit('update:modelValue', ...args)
  }
  let timer
  option.methods._change = function (v) {
    this.$emit('input', v)
    this.$emit('update:modelValue', v)
    this._research()
  }
  option.methods._research = function () {
    this.$nextTick(() => {
      // 防止事件操作过快反复触发
      if (timer) clearTimeout(timer)
      const reselect = () => {
        document.body.removeEventListener('animationend', reselect)
        document.body.removeEventListener('transitionend', reselect)
        this.$nextTick(() => {
          this.$doc.select.reselect()
        })
      }
      reselect()
      timer = setTimeout(() => {
        reselect()
        timer = null
      }, 500)
      // 有的值修改有动画, 监听动画的结束重新选择
      document.body.addEventListener('animationend', reselect)
      document.body.addEventListener('transitionend', reselect)
    })
  }
  // 兼容vue3的v-model
  option.model = {
    prop: 'modelValue',
    event: 'update:modelValue',
  }
  option.props = option.props || {}
  if (Array.isArray(option.props)) {
    if (!option.props.includes('modelValue')) {
      option.props.push('modelValue')
    }
  } else {
    const mv = option.props.modelValue
    if (!mv) {
      option.props.modelValue = {
        type: null,
      }
    }
  }
  return option
}
