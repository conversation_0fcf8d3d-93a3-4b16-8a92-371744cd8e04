const events = new Map()
export const emit = (name, ...args) => {
  let nameArr = name
  if (!Array.isArray(name)) {
    nameArr = [name]
  }
  for (const n of nameArr) {
    if (!events.has(n)) return
    events.get(n).forEach((fn) => fn(...args))
  }
}

export const on = (name, fn) => {
  let nameArr = name
  if (!Array.isArray(name)) {
    nameArr = [name]
  }
  for (const n of nameArr) {
    if (!events.has(n)) {
      events.set(n, [])
    }
    events.get(n).push(fn)
  }
}

export const off = (name, fn) => {
  let nameArr = name
  if (!Array.isArray(name)) {
    nameArr = [name]
  }
  for (const n of nameArr) {
    if (!events.has(n)) return
    if (!fn) {
      events.delete(n)
    } else {
      events.get(n).splice(events.get(n).indexOf(fn), 1)
    }
  }
}
