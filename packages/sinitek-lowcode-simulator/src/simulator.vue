<template>
  <div
    ref="simulator"
    data-theme="light"
    class="lc-simulator relative z-1 h-full w-full flex flex-col select-none overflow-hidden"
  >
    <LCTopArea v-show="isLoaded" v-if="resourceLoaded" />
    <div
      v-show="isLoaded"
      class="lc-container h-full w-full flex overflow-hidden bg-white"
    >
      <LCLeftArea v-if="resourceLoaded" ref="plugins" />
      <!-- <LCCanvas /> -->
      <div
        ref="simulator"
        class="relative wh-full flex flex-col bg-bgGrayLight"
        @mouseout="mouseout"
        @click.capture="setTarget"
      >
        <LCToolbarArea v-if="resourceLoaded" />
        <div ref="canvas" class="box-border h-full w-full p-4">
          <div class="h-full w-full f-c-c">
            <div
              style="transform: scale(1)"
              class="h-full"
              :style="{ width: width || '100%' }"
            >
              <iframe
                ref="iframe"
                src="./canvas.html"
                class="mx-a block wh-full"
              ></iframe>
              <template v-if="isLoaded && resourceLoaded">
                <LCLine :line-state="lineState" :hover-state="hoverState" />
                <LCHover :hover-state="hoverState" :line-state="lineState" />
                <LCSelect :select-state="selectState" />
              </template>
            </div>
          </div>
        </div>
      </div>
      <LCRightArea v-if="resourceLoaded" />
    </div>
    <!-- 全局popover -->
    <LCPopover ref="popover" />
    <!-- 表达式编辑器 -->
    <ExpressionEditorDialog ref="ee" />
    <!-- 全局代码编辑弹框 -->
    <MonacoEditorDialog v-model="medShow" :options="medOptions" />
    <LCFloatArea v-if="resourceLoaded" />
    <div v-if="!isLoaded" class="loadingio-spinner-spinner-2by998twmg8">
      <div class="ldio-yzaezf3dcmj">
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </div>
    </div>
  </div>
</template>

<script>
// import LCCanvas from '@canvas'
// import LCTopArea from '@toolbar/toolbar.vue'
import { Doc } from '@doc'
import { cleanZIndex, clickOutside } from '@utils'
import LCHover from '@canvas/hover.vue'
import LCSelect from '@canvas/select.vue'
import LCLine from '@canvas/line.vue'

import {
  DesignMode,
  getCtxRawComponents,
  setAPI,
  ExpressionEditorDialog,
  copyTextToClipboard,
} from 'sinitek-lowcode-shared'
// import { POSITION } from './doc/line'

export default {
  name: 'SinitekLowcodeSimulator',
  components: {
    // LCCanvas,
    LCTopArea: () => import('./plugins/top-area/top-area.vue'),
    LCPopover: () => import('@common/tooltip/popover.vue'),
    LCLeftArea: () => import('./plugins/left-area/left-area.vue'),
    LCRightArea: () => import('./plugins/right-area/right-area.vue'),
    LCToolbarArea: () => import('./plugins/toolbar-area/index.vue'),
    LCFloatArea: () => import('./plugins/float-area/float-area.vue'),
    MonacoEditorDialog: () => import('@common/monaco-editor-dialog.vue'),
    LCHover,
    LCSelect,
    LCLine,
    ExpressionEditorDialog,
  },
  provide() {
    return {
      showMED: this.showMED,
      fetcher: this.config?.fetcher,
      message: this.config?.message ?? {},
      // 隐藏默认的属性 class ref
      hideDefaultProp: this.config?.hideDefaultProp ?? false,
      // 全局是否-支持变量
      supportVariable: this.config?.supportVariable,
      // 获取图标 解耦sirmapp的getIcons
      getIcons: this.config.getIcons,
      $doc: this.doc,
      mode: this.mode,
    }
  },
  props: {
    // 配置
    // config.fetcher 请求相关配置
    // config.fetcher.getAIProps 组件ai获取属性请求
    config: Object,
    // 不会影响画布区域，画布的mode只会是设计状态
    mode: {
      type: String,
      default: DesignMode.DESIGN,
    },
  },
  data() {
    return {
      medShow: false,
      medOptions: {},
      // 空对象时，provide会丢失原型。所有先实例，在iframe里使用
      doc: new Doc({
        plugins: this.config?.plugins ?? [],
      }),
      width: '',
      components: {},
      queue: [],
      isLoaded: false,
      task: [],
      hoverState: {},
      selectState: {},
      lineState: {},
      resourceLoaded: false,
    }
  },
  watch: {
    showPanel(v) {
      if (!v) {
        this.$refs.plugins.hide()
      }
    },
  },
  created() {
    window.__LCDoc__ = this.doc
  },
  mounted() {
    this.doc.read(() => {
      this.resourceLoaded = true
      this.doc.hover.onChange((arg) => {
        this.hoverState = arg
      })
      this.doc.select.onChange((arg) => {
        this.selectState = arg
      })
      this.doc.line.onChange((arg) => {
        this.lineState = arg
      })
      this.doc.event.on('drag:error', (msg) => {
        this.config?.message?.error?.(msg)
      })
      this.clean = clickOutside(this.$refs.simulator, () => {
        this.doc.hotkey.setTarget('')
      })
    })
    window.__LCConfig__ = this.config
    // window.__LCMaterials__ = this.doc.materials.getMaterials()
    // canvas渲染用
    window.__LCScopedSlots__ = {
      $scopedSlots: this.$scopedSlots,
      $scope: this.$scope,
    }

    window.addEventListener('mounted', this.init, { once: true })

    setAPI({
      showExpressionEditor: (value, onChange) => {
        this.$refs.ee.show(value, onChange)
      },
    })

    // 避免 ResizeObserver 警告
    this.$refs.iframe.contentWindow.addEventListener('resize', () => {
      requestAnimationFrame(() => {
        // 原有的 resize 处理逻辑
        this.doc.select.reselect()
      })
    })
  },
  beforeDestroy() {
    window.removeEventListener('mounted', this.init)
    this.doc.destroy()
    cleanZIndex()
    this?.clean?.()
  },
  methods: {
    setTarget() {
      this.doc.hotkey.setTarget('canvas')
    },
    init($event) {
      // 在main环境注入的组件添加到canvas环境
      $event.detail.addCtxComponents(getCtxRawComponents())

      this.doc.mount(
        this.$refs.iframe.contentWindow,
        this.$refs.iframe.contentDocument
      )
      this.doc.container.onDeviceChange((v) => {
        this.width = v === 'phone' ? '375px' : ''
      })
      this.isLoaded = true
      this._runTask()
      this.initFloat()
      this.bindShortcut()
    },
    initFloat() {
      let popoverEl = null
      const showFloat = (e) => {
        // 如果移入的元素有float，自动添加popover
        // float属性支持字符串和JSON对象字符串, 如:float='内容' float="{'content': '内容'}"
        try {
          if (popoverEl === e.target) return
          popoverEl = e.target
          let float = e.target.getAttribute('float')
          // 查找e.target的父元素，直到找到有float属性的元素, 最多找10层
          let parent = e.target
          for (let i = 0; i < 10; i++) {
            if (parent?.getAttribute?.('float')) {
              float = parent.getAttribute('float')
              break
            }
            parent = parent?.parentElement
          }
          if (float) {
            float = float.replace(/'/g, '"')
            let val
            try {
              val = JSON.parse(float)
            } catch {
              val = { content: float }
            }
            this.$refs.popover.show(e.target, decodeURIComponent(val.content))
          } else {
            this.$refs.popover.hide()
          }
        } catch (e) {
          console.warn(e, 'e')
        }
      }
      // if (this.$refs?.iframe?.contentDocument) {
      this.$refs.iframe.contentDocument
        .querySelector('.canvas-wrap')
        .addEventListener('mousemove', showFloat)
      // }
      this.$refs.simulator.addEventListener('mousemove', showFloat)
    },
    bindShortcut() {
      clickOutside(this.$refs.canvas, () => {
        this.doc.hotkey.setTarget('')
      })
      // 绑定快捷键
      this.doc.hotkey.bind('canvas', 'Ctrl+c', () => {
        if (this.doc.getCurrent().schema) {
          const copyData = this.doc.getCurrent(true).schema
          copyTextToClipboard(
            JSON.stringify({
              type: 'schema',
              node: copyData,
            }),
            this.$refs.iframe.contentDocument
          )
        }
      })

      this.doc.hotkey.bind('canvas', 'Ctrl+z', () => {
        // 历史记录撤回
        if (this.doc.history.canBack()) {
          this.doc.history.back()
        } else {
          this.config?.message?.error('没有要撤销的')
        }
      })
      this.doc.hotkey.bind('canvas', 'Ctrl+y', () => {
        // 历史记录重做
        if (this.doc.history.canForward()) {
          this.doc.history.forward()
        } else {
          this.config?.message?.error('没有要恢复的')
        }
      })
      this.doc.hotkey.bind('canvas', 'Delete', () => {
        // 删除当前元素
        const { parent, schema } = this.doc.getCurrent()
        const config = this.doc.materials.getConfigure(schema.componentName)
        if (
          config?.component?.disableBehaviors?.includes('remove') ||
          config?.component?.disableBehaviors?.includes('*')
        ) {
          this.config?.message?.error('该组件不允许删除')
          return
        }
        if (!schema || !parent) return
        this.doc.node.removeNodeById(schema.id)
        this.doc.select.clean()
      })
    },
    showMED(options) {
      this.medShow = true
      this.medOptions = options
    },
    // setMaterials(materials) {
    //   this.task.push(() => {
    //     this.doc.materials.replaceMaterials(materials)
    //     window.__LCMaterials__ = this.doc.materials.getMaterials()
    //   })
    //   this._runTask()
    // },
    setSchema(schema) {
      this.task.push(() => {
        this.doc.schema.replace(schema)
      })
      this._runTask()
    },
    getSchema() {
      return this.doc.getSaveSchema()
    },
    _runTask() {
      if (this.isLoaded && this.task.length) {
        const pop = this.task.pop()
        if (pop) {
          pop()
        }
        this.task = []
      }
    },
    checkChange() {
      return this.doc.checkChange()
    },
    mouseout() {
      // 可能没有this.doc被销毁了又触发了mouseout
      this.doc?.drag?.dragEnd?.()
    },
    async register(plugin) {
      await this.doc.pluginSystem.register(plugin)
      if (this.isLoaded) {
        this.doc.event.emit('register:add', plugin)
      }
    },
    unregister(plugin) {
      this.doc.pluginSystem.unregister(plugin)
      if (this.isLoaded) {
        this.doc.event.emit('register:remove', plugin)
      }
    },
  },
}
</script>
<style lang="scss">
.lc-hidden {
  display: none !important;
}
div[class*='i-'] {
  cursor: pointer;
}
.lc-simulator {
  color: #333;
  font-size: 14px;
  input,
  .el-select-dropdown__item {
    font-size: 14px;
  }
  .el-form-item__label {
    font-weight: 400;
    color: #333;
  }
}
// .LCCanvas {
//   [class*='xn-input--width'] {
//     width: 100% !important;
//   }
// }

@keyframes ldio-yzaezf3dcmj {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.ldio-yzaezf3dcmj div {
  left: 94px;
  top: 48px;
  position: absolute;
  animation: ldio-yzaezf3dcmj linear 1s infinite;
  background: #030303;
  width: 12px;
  height: 24px;
  border-radius: 4.08px / 4.08px;
  transform-origin: 6px 52px;
}
.ldio-yzaezf3dcmj div:nth-child(1) {
  transform: rotate(0deg);
  animation-delay: -0.9166666666666666s;
  background: #030303;
}
.ldio-yzaezf3dcmj div:nth-child(2) {
  transform: rotate(30deg);
  animation-delay: -0.8333333333333334s;
  background: #030303;
}
.ldio-yzaezf3dcmj div:nth-child(3) {
  transform: rotate(60deg);
  animation-delay: -0.75s;
  background: #030303;
}
.ldio-yzaezf3dcmj div:nth-child(4) {
  transform: rotate(90deg);
  animation-delay: -0.6666666666666666s;
  background: #030303;
}
.ldio-yzaezf3dcmj div:nth-child(5) {
  transform: rotate(120deg);
  animation-delay: -0.5833333333333334s;
  background: #030303;
}
.ldio-yzaezf3dcmj div:nth-child(6) {
  transform: rotate(150deg);
  animation-delay: -0.5s;
  background: #030303;
}
.ldio-yzaezf3dcmj div:nth-child(7) {
  transform: rotate(180deg);
  animation-delay: -0.4166666666666667s;
  background: #030303;
}
.ldio-yzaezf3dcmj div:nth-child(8) {
  transform: rotate(210deg);
  animation-delay: -0.3333333333333333s;
  background: #030303;
}
.ldio-yzaezf3dcmj div:nth-child(9) {
  transform: rotate(240deg);
  animation-delay: -0.25s;
  background: #030303;
}
.ldio-yzaezf3dcmj div:nth-child(10) {
  transform: rotate(270deg);
  animation-delay: -0.16666666666666666s;
  background: #030303;
}
.ldio-yzaezf3dcmj div:nth-child(11) {
  transform: rotate(300deg);
  animation-delay: -0.08333333333333333s;
  background: #030303;
}
.ldio-yzaezf3dcmj div:nth-child(12) {
  transform: rotate(330deg);
  animation-delay: 0s;
  background: #030303;
}
.loadingio-spinner-spinner-2by998twmg8 {
  width: 200px;
  height: 200px;
  display: inline-block;
  overflow: hidden;
  margin: auto;
  transform: scale(0.3);
}
.ldio-yzaezf3dcmj {
  width: 100%;
  height: 100%;
  position: relative;
  transform: translateZ(0) scale(1);
  backface-visibility: hidden;
  transform-origin: 0 0; /* see note above */
}
.ldio-yzaezf3dcmj div {
  box-sizing: content-box;
}
/* [ldio] generated by https://loading.io */
</style>
