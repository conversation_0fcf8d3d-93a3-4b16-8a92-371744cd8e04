/**
 * 设置响应式对象数据
 * @param {*} target 响应式对象
 * @param {*} key 属性名
 * @param {*} value 属性值
 */
export function setObserver(target, key, value) {
  if (!target.__ob__) return
  const sub = target?.__ob__?.dep?.subs?.[0]
  // 获取当前响应对象的vm
  const that = sub?.vm
  if (that) {
    let notify = target[key] !== void 0
    that.$set(target, key, value)
    if (notify) {
      target?.__ob__?.dep?.notify()
    }
  }
}
