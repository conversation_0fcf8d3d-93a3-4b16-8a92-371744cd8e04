<template>
  <div class="collapse-item">
    <div
      class="collapse-item-header"
      :class="{ isBorder, isActive, isMiniTitle }"
      @click="toggleActive"
    >
      <div class="title">
        {{ title }}
      </div>
      <div
        class="el-icon-arrow-down arrow"
        :class="[isActive ? '' : 'rotate-90 ']"
      ></div>
    </div>
    <CollapseTransition>
      <div v-show="isActive" class="collapse-item__wrap">
        <div class="collapse-item__content">
          <slot></slot>
        </div>
      </div>
    </CollapseTransition>
  </div>
</template>

<script>
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition'

export default {
  components: {
    CollapseTransition,
  },
  props: ['title', 'isMiniTitle', 'border'],
  data() {
    return {
      isActive: true,
    }
  },
  computed: {
    isBorder() {
      return this.border
    },
  },
  methods: {
    toggleActive() {
      this.isActive = !this.isActive
    },
  },
}
</script>
<style lang="scss" scoped>
.collapse-item-header {
  height: 30px;
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  font-size: 14px;
  font-family: MicrosoftYaHeiSemibold;

  &.isBorder {
    border-top: 1px solid #e5e5e5;
  }
  &.isMiniTitle {
    font-size: 12px;
    padding: 0 10px;
  }
  .title {
    font-weight: 600;
    color: #333;
  }
  .arrow {
    color: #666;
    transition: transform 400ms;
  }
  .rotate-90 {
    transform: rotate(-90deg);
  }
}
</style>
