<template>
  <div class="expression-suggestions">
    <!-- 左右布局左边显示提示树形列表，右边显示提示内容 -->
    <div class="expression-suggestions-left">
      <el-input
        v-model="filterText"
        suffix-icon="el-icon-search"
        placeholder="输入关键字进行过滤"
        clearable
      >
      </el-input>

      <div class="expression-suggestions-wrap">
        <el-tree
          ref="tree"
          class="filter-tree"
          :data="list"
          :filter-node-method="filterNode"
          :expand-on-click-node="false"
          @node-click="handleSuggestionClick"
        >
        </el-tree>
      </div>
    </div>
    <div class="expression-suggestions-right">
      {{ content }}
    </div>
  </div>
</template>

<script>
function transformToTree(data) {
  const root = []

  data.forEach((item) => {
    const parts = item.label.split('.')
    let currentLevel = root

    parts.forEach((part, index) => {
      let existingNode = currentLevel.find((node) => node.label === part)

      if (!existingNode) {
        existingNode = {
          label: part,
          insertText: item.insertText,
          children: [],
        }
        currentLevel.push(existingNode)
      }

      if (index === parts.length - 1) {
        existingNode.detail = item.detail
      }

      currentLevel = existingNode.children
    })
  })

  return root.filter((e) => e.children.length)
}
export default {
  name: 'ExpressionSuggestions',
  props: {
    suggestions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      content: '',
      filterText: '',
    }
  },
  computed: {
    list() {
      /**
       * 将
       * [
       *  {
       *    label: 'datasource.user.name',
       *    detail: 'value1'
       *  },
       *  ...
       * ]
       * 转成
       * [
       *   {
       *     label: 'datasource',
       *     children: [
       *       {
       *         label: 'user',
       *         children: [
       *           {
       *             label: 'name',
       *             detail: 'value1'
       *           }
       *         ]
       *       }
       *     ]
       *   }
       * ]
       */

      return transformToTree(this.suggestions)
    },
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
  },
  methods: {
    dblclick(item) {
      this.$emit('click', item.insertText)
    },
    handleSuggestionClick(item) {
      if (this.firstClickTime + 200 > Date.now()) {
        this.dblclick(item)
      }
      this.firstClickTime = Date.now()
      this.content = item.insertText + '\n' + item.detail
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    reset() {
      this.content = ''
      this.filterText = ''
    },
  },
}
</script>

<style lang="scss" scoped>
.expression-suggestions {
  height: 300px;
  display: flex;
  overflow: hidden;
  background: #ffffff;
  border: 1px solid rgba(224, 227, 229, 1);
  border-radius: 8px;
  font-size: 12px;
  &-left {
    width: 45%;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    ::v-deep .el-input input {
      border: none;
      font-size: 14px;
      font-weight: 400;
      &:placeholder {
        color: #999999;
        font-weight: 400;
      }
    }
  }
  &-wrap {
    height: 100%;
    overflow-y: auto;
    border-top: 1px solid #e5e5e5;
  }
  &-right {
    width: 100%;
    border-left: 1px solid #e5e5e5;
    white-space: pre-line;
    padding: 10px 20px;
    overflow: auto;
  }
}
</style>
