<template>
  <div class="operator-items">
    <div
      v-for="item in operatorList"
      :key="item.value"
      class="operator-item"
      :class="{ split: item === '-' }"
      :float="item.label"
      :title="item.label"
      @click="handleOperatorClick(item)"
    >
      {{ item.value }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'ExpressionOperator',
  data() {
    return {
      // 计算符
      calculationOperatorList: [
        {
          label: '加',
          value: '+',
        },
        {
          label: '减',
          value: '-',
        },
        {
          label: '乘',
          value: '*',
        },
        {
          label: '除',
          value: '/',
        },
      ],
      // 比较符
      comparisonOperatorList: [
        {
          label: '等于',
          value: '==',
        },
        {
          label: '不等于',
          value: '!=',
        },
        {
          label: '大于',
          value: '>',
        },
        {
          label: '小于',
          value: '<',
        },
        {
          label: '大于等于',
          value: '>=',
        },
        {
          label: '小于等于',
          value: '<=',
        },
      ],
      // 逻辑符
      logicOperatorList: [
        {
          label: '与',
          value: '&&',
        },
        {
          label: '或',
          value: '||',
        },
        {
          label: '非',
          value: '!',
        },
      ],
      // 其他符
      otherOperatorList: [
        {
          label: '分组',
          value: '()',
        },
        {
          label: '索引',
          value: '[]',
        },
        {
          label: '空',
          value: 'null',
        },
      ],
    }
  },
  computed: {
    operatorList() {
      return [
        ...this.calculationOperatorList,
        '-',
        ...this.comparisonOperatorList,
        '-',
        ...this.logicOperatorList,
        ...this.otherOperatorList,
      ]
    },
  },
  methods: {
    handleOperatorClick(item) {
      this.$emit('click', item.value)
    },
  },
}
</script>

<style lang="scss" scoped>
.operator-items {
  display: flex;
  margin: 15px auto;
  justify-content: center;
  width: 100%;
  font-size: 14px;
  color: #333;
  font-size: 12px;
}
.operator-item {
  cursor: pointer;
  margin-right: 10px;
  width: 25px;
  height: 25px;
  border: 1px solid rgba(224, 227, 229, 1);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;

  &.split {
    margin-right: 30px;
    padding: 0;
    border: none;
    width: 0;
  }
  &:hover {
    background-color: #eaf1fb;
  }
}
</style>
