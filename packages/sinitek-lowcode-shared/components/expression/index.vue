<template>
  <el-autocomplete
    v-model="localSuggestions"
    class="custom-expression"
    popper-class="popper-custom-expression"
    placeholder="请输入内容"
    :fetch-suggestions="getSuggestions"
    clearable
    value-key="insertText"
    :size="size"
    @clear="onClear"
    @input="onInput"
    @dblclick.native="handleIconClick"
  >
    <i
      slot="suffix"
      class="el-input__icon el-icon-arrow-right"
      @click="handleIconClick"
    >
    </i>
    <template slot-scope="{ item }">
      <div class="name" :float="item.detail">{{ item.insertText }}</div>
      <div class="desc">{{ item.description || item.detail }}</div>
    </template>
  </el-autocomplete>
</template>

<script>
import { getDocSuggestions } from './suggestion.js'

import { getAPI } from '../../context/index.js'

export default {
  name: 'ExpressionEditor',
  inject: {
    $doc: {
      default: null,
    },
  },
  props: {
    suggestions: {
      type: Array,
      default: () => [],
    },
    inline: {
      type: Boolean,
      default: true,
    },
    value: null,
    size: {
      type: String,
      default: 'mini',
    },
  },
  data() {
    return {
      localSuggestions: this.value,
      docSuggestions: [],
    }
  },
  computed: {
    currentSuggestions() {
      if (this.docSuggestions.length) {
        return this.docSuggestions
      }
      return this.suggestions
    },
  },
  watch: {
    value(v) {
      this.localSuggestions = v
    },
  },
  mounted() {
    if (this.$doc) {
      this.docSuggestions = getDocSuggestions(this.$doc)
    }
  },
  methods: {
    getSuggestions(queryString, cb) {
      // 内联模式下，默认显示level为2的提示
      cb(
        this.currentSuggestions.filter((item) =>
          queryString
            ? item.label.toLowerCase().includes(queryString.toLowerCase()) &&
              item.inline
            : item.inline && (item.level === 2 || !item.level)
        )
      )
    },
    handleIconClick() {
      getAPI().showExpressionEditor(this.localSuggestions, (v) => {
        this.$emit('input', v)
      })
    },
    onClear() {
      this.$emit('clear')
    },
    // 这里使用事件input。watch修改后input。会导致input触发在clear之后
    onInput(v) {
      this.$emit('input', v)
    },
  },
}
</script>

<style lang="scss">
.popper-custom-expression {
  width: 200px !important;
  &.el-autocomplete-suggestion li {
    min-height: 38px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .name {
    line-height: 1.3;
  }
  .desc {
    line-height: 1.3;
    font-size: 12px;
    color: #b4b4b4;
  }
}
.custom-expression {
  &:hover {
    .el-input__inner {
      padding-right: 50px;
    }
  }
  .el-input__inner:focus {
    padding-right: 50px;
  }
  .el-input__icon.el-icon-arrow-right {
    float: right;
  }
}
.editor-container {
  width: 100%;
  height: 300px;
  border: 1px solid #ccc; /* 添加边框以模拟输入框 */
  overflow: visible; /* 隐藏溢出内容 */
  position: relative;
}
/*.monaco-editor .monaco-hover {
  transform: translateY(10px);
}*/
/* 自定义 CompletionItemKind 图标 */
/* .monaco-editor .suggest-widget .monaco-list-row .icon.variable {
  background: url('path/to/your/custom-icon.png') no-repeat center center;
  background-size: contain;
} */
</style>
