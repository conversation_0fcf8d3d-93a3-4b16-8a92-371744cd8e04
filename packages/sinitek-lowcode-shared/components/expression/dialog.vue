<template>
  <xn-dialog
    title="编辑表达式"
    click-outside
    :show.sync="visible"
    :buttons="buttons"
    to="body"
    limit-to-tab
    @save="onSave"
    @cancel="visible = false"
    @clear="onClear"
  >
    <div ref="editorContainer" class="editor-container"></div>
    <ExpressionOperator @click="handleOperatorClick" />
    <ExpressionSuggestions
      ref="suggestions"
      :suggestions="docSuggestions"
      @click="handleSuggestionClick"
    />
  </xn-dialog>
</template>

<script>
/**
 * 表达式编辑器
 * 默认显示简单基础的提示
 * 内联模式下，使用el-autocomplete，可以通过点来输出层级更深的提示
 * 点击右侧按钮，显示monaco编辑器
 */
import * as monaco from 'monaco-editor'
import lang from './lang.js'
import { parse } from 'acorn'
import { full as acornFull } from 'acorn-walk'
import ExpressionOperator from './operator.vue'
import ExpressionSuggestions from './suggestions.vue'
import {
  registryCustomExpression,
  language,
  getDocSuggestions,
} from './suggestion.js'

// 自定义变量存在性校验函数
const validateVariablesExistence = (code, suggestions) => {
  const errors = []
  if (!code) return errors
  try {
    const ast = parse(code, { sourceType: 'module', locations: true })
    acornFull(ast, (node) => {
      if (node.type !== 'Identifier') return
      const variable = node.name
      if (!suggestions.includes(variable)) {
        errors.push({
          line: node.loc.start.line,
          startColumn: node.loc.start.column + 1,
          endColumn: node.loc.end.column + 1,
          message: `未定义的变量: ${variable}`,
        })
      }
    })
  } catch (e) {
    if (!e?.loc?.line) return errors
    errors.push({
      line: e.loc.line,
      startColumn: e.loc.column + 1,
      endColumn: e.loc.column + 1,
      message: `语法错误: ${e.message}`,
    })
  }

  return errors
}

// 简单的语法检查函数
const validateSyntax = (code) => {
  const errors = []
  const lines = code.split('\n')

  lines.forEach((line, lineIndex) => {
    // 检查每一行是否是有效的 JavaScript 语法
    try {
      // 使用 Function 构造函数来验证语法
      new Function(line)
    } catch (e) {
      errors.push({
        line: lineIndex + 1,
        startColumn: 1,
        endColumn: line.length + 1,
        message: `语法错误: ${e.message}`,
      })
    }
  })

  return errors
}

export default {
  name: 'ExpressionEditorDialog',
  components: {
    ExpressionOperator,
    ExpressionSuggestions,
  },
  inject: {
    $doc: {
      default: null,
    },
  },
  props: {
    suggestions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      suggestion: '',
      visible: false,
      docSuggestions: [],
      buttons: [
        {
          label: '清除',
          type: 'default',
          showLoading: false,
          action: 'clear',
          event: 'clear',
        },
        {
          label: '保存',
          type: 'primary',
          showLoading: false,
          action: 'save',
          event: 'save',
        },
        { label: '取消', type: 'info', action: 'cancel', event: 'cancel' },
      ],
      cursorPosition: null,
    }
  },
  watch: {
    visible(v) {
      if (v) {
        this.$nextTick(() => {
          this.registerCustomCompletion()
        })
      }
    },
  },
  mounted() {
    monaco.languages.register({ id: language })

    // 定义自定义语言的语法和高亮规则
    monaco.languages.setMonarchTokensProvider(language, lang())
  },
  beforeDestroy() {
    this.editor && this.editor.dispose()
    this.registerCustom && this.registerCustom.dispose()
  },
  methods: {
    initMonaco(value) {
      if (this.editor) return

      this.editor = monaco.editor.create(this.$refs.editorContainer, {
        value: value,
        language: language,
        theme: 'vs-light',
        lineNumbers: 'off', // 关闭行号
        // lineHeight: 30, // 设置行高为20像素
        // fontSize: 12, // 设置字体大小为12像素
        minimap: { enabled: false }, // 关闭小地图
        scrollBeyondLastLine: false, // 禁止滚动到最后一行之后
        automaticLayout: true, // 自动布局
        wordWrap: 'on', // 启用自动换行
        scrollbar: {
          vertical: 'hidden', // 隐藏垂直滚动条
          horizontal: 'hidden', // 隐藏水平滚动条
        },
        fixedOverflowWidgets: true, // 解决overflow: hidden 导致弹框提示截断问题
      })

      this.editor.onDidChangeModelContent(() => {
        this.suggestion = this.editor.getValue()

        const _suggestions = this.docSuggestions.map((s) => s.label) // 获取所有建议的标签
        const lintErrors = validateVariablesExistence(
          this.suggestion,
          _suggestions
        )

        const syntaxErrors = validateSyntax(this.suggestion)
        const allErrors = [...lintErrors, ...syntaxErrors]

        // 显示错误
        const markers = allErrors.map((error) => ({
          severity: monaco.MarkerSeverity.Error,
          startLineNumber: error.line,
          startColumn: error.startColumn,
          endLineNumber: error.line,
          endColumn: error.endColumn,
          message: error.message,
        }))
        monaco.editor.setModelMarkers(this.editor.getModel(), 'owner', markers)
      })

      this.editor.onDidFocusEditorText(() => {
        this.triggerCompletion()
      })

      this.editor.onDidChangeCursorPosition((e) => {
        this.cursorPosition = e.position
      })
    },
    show(value, onChange) {
      this.visible = true
      this.onChange = onChange
      this.docSuggestions = getDocSuggestions(this.$doc)
      this.$nextTick(() => {
        this.initMonaco(value || '')
        // this.editor.layout()
        this.editor.setValue(value || '')
        this.$refs.suggestions.reset()
      })
    },
    registerCustomCompletion() {
      this.registerCustom && this.registerCustom.dispose()
      this.registerCustom = registryCustomExpression(this.$doc)
    },
    triggerCompletion() {
      // 手动触发自动完成
      this.editor.trigger('keyboard', 'editor.action.triggerSuggest', {})
    },
    focus() {
      setTimeout(() => {
        // 鼠标移动到输入框最后
        this.editor.focus()
        const line = this.editor.getModel().getLineCount()
        this.editor.setPosition({
          lineNumber: line,
          column: this.editor.getModel().getLineContent(line).length + 1,
        })
      })
    },
    handleOperatorClick(value) {
      this.editor.setValue(this.editor.getValue() + ` ${value} `)
      this.focus()
    },
    handleSuggestionClick(value) {
      const position = this.cursorPosition || this.editor.getPosition()
      const range = new monaco.Range(
        position.lineNumber,
        position.column,
        position.lineNumber,
        position.column
      )
      this.editor.executeEdits('', [
        {
          range,
          text: value,
          forceMoveMarkers: true,
        },
      ])
      // 插入后将光标移动到插入内容后
      const newPosition = {
        lineNumber: position.lineNumber,
        column: position.column + value.length,
      }
      this.editor.focus()
      this.editor.setPosition(newPosition)
    },
    onSave() {
      this.onChange?.(this.editor.getValue())
      this.visible = false
    },
    onClear() {
      this.editor.setValue('')
    },
  },
}
</script>

<style lang="scss">
.popper-custom-expression {
  width: 200px !important;
  .name {
    line-height: 1.3;
  }
  .desc {
    line-height: 1.3;
    font-size: 12px;
    color: #b4b4b4;
  }
}
.custom-expression {
  &:hover {
    .el-input__inner {
      padding-right: 50px;
    }
  }
  .el-input__inner:focus {
    padding-right: 50px;
  }
  .el-input__icon.el-icon-arrow-right {
    float: right;
  }
}
.editor-container {
  width: 100%;
  height: 100px;
  width: 100%;
  border: 1px solid #e5e5e5; /* 添加边框以模拟输入框 */
  overflow: visible; /* 隐藏溢出内容 */
  position: relative;
}
// .monaco-editor .monaco-hover {
//   transform: translateY(calc(100% + 20px));
// }
/* 自定义 CompletionItemKind 图标 */
/* .monaco-editor .suggest-widget .monaco-list-row .icon.variable {
  background: url('path/to/your/custom-icon.png') no-repeat center center;
  background-size: contain;
} */
</style>
