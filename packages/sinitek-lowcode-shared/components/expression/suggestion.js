import { languages, Range, editor, MarkerSeverity } from 'monaco-editor'
import { getPageCtx, utilsMapDetail } from '../../context/index.js'
import { extractObjectPaths } from '../../utils/index.js'
import get from 'lodash/get'
const deepClone = (obj) => JSON.parse(JSON.stringify(obj))

export const language = 'customExpression'

export const registryCustomExpression = (doc) => {
  const suggestion = languages.registerCompletionItemProvider(language, {
    triggerCharacters: ['.'],
    provideCompletionItems: () => {
      const suggestions = deepClone(getDocSuggestions(doc))
      // const textUntilPosition = model.getValueInRange({
      //   startLineNumber: position.lineNumber,
      //   startColumn: 1,
      //   endLineNumber: position.lineNumber,
      //   endColumn: position.column,
      // })
      // let lastWord = textUntilPosition.split(splitReg).pop()
      // const baseSuggestions = deepClone(_suggestions).filter(
      //   (e) => e.label.indexOf('.') === -1
      // )
      // let suggestions = baseSuggestions

      // if (lastWord) {
      //   const reg = new RegExp(
      //     `^${lastWord.replace(/\./g, '\\.')}[a-z0-9_]+$`,
      //     'i'
      //   )
      //   suggestions = deepClone(_suggestions)
      //     .filter((e) => reg.test(e.label.toLowerCase()))
      //     .map((e) => {
      //       e.label = e.label.split('.').pop()
      //       e.insertText = e.insertText.split('.').pop()
      //       return e
      //     })
      // }

      return {
        suggestions,
      }
    },
  })
  const hover = languages.registerHoverProvider(language, {
    provideHover: (model, position) => {
      const markers = editor.getModelMarkers({
        owner: 'owner',
        resource: model.uri,
      })
      const hasErrors = markers.some(
        (marker) => marker.severity === MarkerSeverity.Error
      )

      // 如果存在错误，则不显示 hover
      if (hasErrors) {
        return null
      }
      // 获取当前行的完整文本
      const lineContent = model.getLineContent(position.lineNumber)

      // 获取当前光标位置的单词
      const word = model.getWordAtPosition(position)

      if (word) {
        // 使用正则表达式解析完整的调用链
        const regex = new RegExp(`\\b(\\S+\\.)*${word.word}\\b`)
        const match = lineContent.match(regex)

        if (match) {
          const fullPath = match[0] // 获取完整的调用链
          // 查找suggestions中的信息
          const suggestion = getDocSuggestions(doc).find(
            (item) => item.label === fullPath
          )

          // 如果在suggestions中找到了
          if (suggestion) {
            return {
              range: new Range(
                position.lineNumber,
                word.startColumn,
                position.lineNumber,
                word.endColumn
              ),
              contents: [
                { value: `**${fullPath}**` }, // 显示完整的调用链
                { value: '```text \n' + suggestion.detail + '\n```' },
                { value: suggestion.documentation },
              ],
            }
          }
        }
      }
      return null
    },
  })
  return {
    dispose: () => {
      suggestion.dispose()
      hover.dispose()
    },
  }
}

const isObject = (obj) =>
  Object.prototype.toString.call(obj) === '[object Object]'

/**
 * 将ctx对象转换成代码提示
 * ctx = {
 *  state: {
 *    name: '张三'
 *  },
 *  fetcher: {
 *    user: {
 *      send() {}
 *    },
 *    user2: {
 *       list: () => {}
 *       update: ()=> {}
 *       ...
 *    }
 *  },
 *  doAction: () => {},
 *  refs: {
 *   ref1: {},
 *   ref2: {}
 *  },
 *  datasource:{
 *     user: {}
 *  },
 *  utils: {
 *    download: ()=>{}
 *  },
 *  props: {
 *    prop1: 'prop1'
 *  }
 * }
 *
 * @param {*} _suggestions
 * @returns
 */

const getDetail = (key, obj, prefix) => {
  if (prefix.startsWith('state')) {
    return `变量: ${key}\n默认值: ${JSON.stringify(get(obj, key), null, 2)}\n类型：${typeof get(obj, key)}`
  } else if (prefix.startsWith('utils.')) {
    return utilsMapDetail[key] || ''
  }
  return ''
}

const getObjectSuggestions = (obj, prefix = '') => {
  const suggestions = []
  Object.keys(obj)
    .filter((e) => !e.startsWith('_'))
    .filter((e) => e !== 'state' && e !== 'cloneState')
    .filter((e) => {
      if (isObject(obj[e])) {
        // 没有值的对象不显示
        return Object.keys(obj[e]).length > 0
      }
      return true
    })
    .forEach((key) => {
      // 过滤fetcher
      if (
        prefix.startsWith('fetcher.') ||
        prefix.startsWith('datasource.') ||
        key === 'emit'
      ) {
        return
      }
      // 过滤methods的type和value字段
      if (
        prefix.startsWith('methods.') &&
        ['type', 'value', 'original'].includes(key)
      ) {
        return
      }
      suggestions.push({
        label: `${prefix}${key}`,
        insertText: `${prefix}${key}`,
        inline: false,
        level: Math.max(prefix.split('.').length, 1),
        kind:
          typeof obj[key] === 'function' || prefix.startsWith('methods.')
            ? languages.CompletionItemKind.Function
            : languages.CompletionItemKind.Variable,
        detail: getDetail(key, obj, prefix),
      })
      if (!['refs', 'vm'].includes(key)) {
        if (isObject(obj[key])) {
          suggestions.push(
            ...getObjectSuggestions(obj[key], `${prefix}${key}.`)
          )
        }
      }
    })
  return suggestions
}

export const getDocSuggestions = (doc) => {
  const ctx = getPageCtx()

  const suggestions = [
    ...extractObjectPaths(ctx.state, 5, 0, 'state').map((e) => {
      return {
        label: e,
        insertText: e,
        inline: true,
        kind: languages.CompletionItemKind.Variable,
      }
    }),
  ]
  suggestions.push(...getObjectSuggestions(ctx, ''))
  // 添加scope
  let _schema = doc.getCurrent(true).schema
  if (_schema?.id) {
    const scope = getPageCtx()?.refs?.[_schema.ref]?.$parent?.scope?.scope
    if (scope) {
      const paths = extractObjectPaths(scope, 5, 0, 'scope')
      paths.forEach((key) => {
        suggestions.push({
          label: key,
          insertText: key,
          kind: languages.CompletionItemKind.Variable,
          inline: true,
          detail: getDetail(key, ctx, 'scope'),
        })
      })
    }
  }

  Object.keys(ctx.refs).forEach((key) => {
    const material = doc.materials.getMaterialByRef(key)
    if (material) {
      const { methods = [], states = [] } = material?.configure?.supports ?? {}
      if (methods.length === 0 && states.length === 0) return
      suggestions.push({
        label: `refs.${key}`,
        insertText: `refs.${key}`,
        kind: languages.CompletionItemKind.Variable,
        detail: `${material.title}`,
      })
      methods.forEach((e) => {
        suggestions.push({
          label: `refs.${key}.${e.name}`,
          insertText: `refs.${key}.${e.name}`,
          kind: languages.CompletionItemKind.Function,
          description: e.description,
          detail: [
            e.description,
            `例子: ${e.example}`,
            e.args
              ? `参数: ${e.description} ${e.args.map((e) => `${e.name}: ${e.type} `).join('\n')}`
              : '',
            `返回值类型: ${e.returnType || 'void'}`,
          ].join('\n'),
        })
      })
      states.forEach((e) => {
        suggestions.push({
          label: `refs.${key}.${e.name}`,
          insertText: `refs.${key}.${e.name}`,
          kind: languages.CompletionItemKind.Variable,
          inline: true,
          description: e.description,
          detail: [
            e.description,
            e.example ? `例子: ${e.example}` : '',
            e.type ? `类型: ${e.type}` : '',
          ].join('\n'),
        })
      })
    }
  })

  Object.keys(ctx.fetcher).forEach((key) => {
    Object.keys(ctx.fetcher[key]).forEach((k) => {
      if (k === 'auto' || k === 'type' || k === 'list') return
      if (k === 'send') {
        suggestions.push({
          label: `datasource.${key}`,
          insertText: `datasource.${key}`,
          kind: languages.CompletionItemKind.Variable,
          detail: `${key}.${k}列表数据\n 返回值: {datalist:any[],totalsize:number}`,
        })
        suggestions.push({
          label: `datasource.${key}.datalist`,
          insertText: `datasource.${key}.datalist`,
          kind: languages.CompletionItemKind.Variable,
          detail: `${key}.${k}列表数据\n 类型: any[]`,
        })
        suggestions.push({
          label: `datasource.${key}.totalsize`,
          insertText: `datasource.${key}.totalsize`,
          kind: languages.CompletionItemKind.Variable,
          detail: `${key}.${k}列表数据总长度\n 类型: number`,
        })
      } else {
        suggestions.push({
          label: `datasource.${key}.${k}.data`,
          insertText: `datasource.${key}.${k}.data`,
          kind: languages.CompletionItemKind.Variable,
          detail: `${key}.${k}返回数据`,
        })
      }
    })
  })

  return suggestions
}
