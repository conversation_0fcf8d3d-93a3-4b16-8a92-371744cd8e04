/**
 * 跨平台复制文本到剪贴板
 * 兼容现代浏览器、Safari及IE
 * @param {string} text - 要复制的文本
 * @returns {Promise<boolean>} - 成功返回true，失败返回false
 */
export function copyTextToClipboard(text, doc = document) {
  return new Promise((resolve, reject) => {
    // 方法1: 使用现代Clipboard API (Chrome 66+, Firefox 63+, Safari 13.1+)
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          resolve(true)
        })
        .catch((err) => {
          console.warn('Clipboard API失败:', err)
          // 如果Clipboard API失败，尝试其他方法
          fallbackCopy()
        })
    } else {
      // 如果Clipboard API不可用，尝试其他方法
      fallbackCopy()
    }

    // 备用方法处理
    function fallbackCopy() {
      try {
        // 方法2: 创建临时input元素
        const textArea = doc.createElement('textarea')
        textArea.value = text

        // 防止滚动到底部
        textArea.style.position = 'fixed'
        textArea.style.top = '0'
        textArea.style.left = '0'
        textArea.style.width = '2em'
        textArea.style.height = '2em'
        textArea.style.padding = '0'
        textArea.style.border = 'none'
        textArea.style.outline = 'none'
        textArea.style.boxShadow = 'none'
        textArea.style.background = 'transparent'
        textArea.style.opacity = '0'

        doc.body.appendChild(textArea)
        textArea.focus()
        textArea.select()

        // 尝试使用document.execCommand (兼容IE和旧版浏览器)
        const successful = doc.execCommand('copy')
        doc.body.removeChild(textArea)

        if (successful) {
          resolve(true)
        } else {
          console.warn('execCommand复制失败')
          resolve(false)
        }
      } catch (err) {
        console.error('复制失败:', err)
        reject(false)
      }
    }
  })
}
