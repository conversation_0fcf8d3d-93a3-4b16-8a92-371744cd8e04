export * from './isEnumType.js'
export * from './isInputType.js'
export * from './node.js'
export * from './platform'
export * from './copy.js'
import isPlainObject from 'lodash/isPlainObject.js'

export function cached(fn) {
  const cache = Object.create(null)
  return function cachedFn(str) {
    const hit = cache[str]
    return hit || (cache[str] = fn(str))
  }
}

const camelizeRE = /-(\w)/g
export const camelize = cached((str) => {
  return str.replace(camelizeRE, (_, c) => (c ? c.toUpperCase() : ''))
})

export const capitalize = cached((str) => {
  return str.charAt(0).toUpperCase() + str.slice(1)
})

const hyphenateRE = /\B([A-Z])/g
export const hyphenate = cached((str) => {
  return str.replace(hyphenateRE, '-$1').toLowerCase()
})

export const styleToObject = (style) => {
  if (!style) return {}
  if (typeof style === 'object') return style
  const styleMap = {}
  style.split(';').forEach((item) => {
    if (!item) return
    const [key, value] = item.split(':')
    if (!key || !value) return
    styleMap[camelize(key.trim())] = value.trim()
  })
  return styleMap
}
export const objectToStyle = (style) => {
  if (!style) return ''
  if (typeof style === 'string') return style
  return Object.entries(style)
    .map(([key, value]) => `${hyphenate(key)}: ${value}`)
    .join(';')
}

export const omitHidden = (list = []) => {
  return list.filter((item) => !item.hidden)
}

export const download = async (text, mime, filename) => {
  const blob = new Blob([await text], {
    type: mime,
  })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  setTimeout(() => {
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }, 0)
}

/**
 * 获取id，这个不能保证唯一
 * @param {*} len 长度
 * @returns {String} 例子： vo5n9zh41o9  h6f3wz610xi
 * @example
 * unsafeId()
 * returns vo5n9zh41o9
 * @example
 * unsafeId(4)
 * returns vo5n
 */
export const unsafeId = (len) => {
  if (len) {
    len += 2
  }
  return Math.random().toString(36).slice(2, len)
}

/**
 * 生成函数绑定的模板字符串
 * @param {string} name 函数名称
 * @param {object} extParams  是否有外部函数
 * @returns function(){return this.methods.submit.apply(this,[].concat(Array.prototype.slice.call(arguments))) }
 * @example
 * generateBindMethodTemplate(submit)
 * function(){return this.methods.submit.apply(this,[].concat(Array.prototype.slice.call(arguments))) }
 * @example
 * generateBindMethodTemplate(submit, {a:1})
 * function(){return this.methods.submit.apply(this,[].concat(Array.prototype.slice.call([{a:1}]))) }
 */
export const generateBindMethodTemplate = (name, extParams) => {
  return `function(){return this.methods.${name}.apply(this, Array.prototype.slice.call(arguments)${extParams ? `.concat([${JSON.stringify(extParams)}])` : ''}) }`
}

export const isEmpty = (v) => {
  if (v == null) return true // 检查 null 和 undefined
  if (typeof v === 'string' || Array.isArray(v)) return v.length === 0 // 字符串和数组
  if (typeof v === 'object') return Object.keys(v).length === 0 // 对象
  return false // 其他类型默认为非空
}

export const getObjectType = (obj) => {
  const type = Object.prototype.toString.call(obj)
  return type.slice(8, -1).toLowerCase()
}

/**
 * 递归提取对象所有层级的路径
 * @param {Object} obj - 要遍历的对象
 * @param {Number} maxDepth - 最大递归深度，-1表示无限制
 * @param {Number} currentDepth - 当前递归深度，内部使用
 * @param {String} parentPath - 父路径，内部使用
 * @param {Boolean} fullObject - 是否返回完整对象，false则只返回路径字符串
 * @returns {Array} - 路径数组
 */
export function extractObjectPaths(
  obj,
  maxDepth = 5,
  currentDepth = 0,
  parentPath = ''
) {
  if (
    !obj ||
    typeof obj !== 'object' ||
    Array.isArray(obj) ||
    (currentDepth > maxDepth && maxDepth !== -1)
  ) {
    return []
  }

  let result = []

  // 遍历当前对象的所有键
  Object.keys(obj).forEach((key) => {
    const currentPath = parentPath ? `${parentPath}.${key}` : key

    result.push(currentPath)

    // 如果是对象且未达到最大深度或深度无限制，则递归处理
    if (
      isPlainObject(obj[key]) &&
      (currentDepth < maxDepth || maxDepth === -1)
    ) {
      // 递归处理子对象
      const childPaths = extractObjectPaths(
        obj[key],
        maxDepth,
        currentDepth + 1,
        currentPath
      )
      result = result.concat(childPaths)
    }
  })

  return result
}
