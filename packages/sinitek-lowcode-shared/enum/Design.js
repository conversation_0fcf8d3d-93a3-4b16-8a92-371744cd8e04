export const Design = {
  NODE_UID: 'data-uid',
  NODE_TAG: 'data-tag',
  NODE_LOOP: 'loop-id',
}

export const DatasourceType = {
  /** 枚举类型的数据源 */
  ENUM: 'enum',
  /** 模型类型的数据源 */
  MODEL: 'model',
  /** 自定义的数据源 */
  CUSTOM: 'custom',
}

export const JSExpression = 'JSExpression'
export const JSFunction = 'JSFunction'
export const JSAction = 'JSAction'

export function isJSExpression(obj) {
  return obj?.type === JSExpression
}

export function isJSFunction(obj) {
  return obj?.type === JSFunction
}
export function isJSAction(obj) {
  return obj?.type === JSAction
}
