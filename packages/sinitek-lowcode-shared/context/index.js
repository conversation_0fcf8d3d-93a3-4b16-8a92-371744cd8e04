import { addCtxComponent } from './components.js'
import { addCtxMaterial } from './materials.js'
import { addCtxSetter } from './setter.js'
import { addCtxUtil } from './utils.js'

export * from './components.js'
export * from './materials.js'
export * from './utils.js'
export * from './setter.js'
export * from './pageCtx.js'
export * from './api.js'
export * from './plugin.js'

import { plugin as pm } from './plugin.js'

export async function registry(plugin) {
  let simulator = {
    addComponent(name, component) {
      addCtxComponent(name, component)
    },
    addMaterials(materials) {
      if (Array.isArray(materials)) {
        materials.forEach((material) => {
          addCtxMaterial(material)
        })
      } else {
        addCtxMaterial(materials)
      }
    },
    addSetter(name, component) {
      addCtxSetter(name, component)
    },
    addUtil(name, fn) {
      addCtxUtil(name, fn)
    },
    registerPlugin(option) {
      pm.register(option)
    },
    unregisterPlugin(option) {
      pm.unregister(option)
    },
  }
  const _plugin = await plugin
  if (_plugin.install || _plugin?.default?.install) {
    _plugin?.install?.(simulator)
    _plugin?.default?.install?.(simulator)
  }
}
