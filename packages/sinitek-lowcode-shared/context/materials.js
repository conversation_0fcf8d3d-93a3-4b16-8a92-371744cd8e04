window.__LCMaterials__ = {}

export function addCtxMaterial(material) {
  if (!material.componentName) {
    throw new Error('注册的物料必须要有componentName字段')
  }
  window.__LCMaterials__[material.componentName] = material
}

export function addCtxMaterials(materials) {
  if (Array.isArray(materials)) {
    materials.forEach((material) => {
      addCtxMaterial(material)
    })
    return
  }
  Object.assign(window.__LCMaterials__, materials)
}

export function getCtxMaterial(name) {
  return window.__LCMaterials__[name] || window.parent.__LCMaterials__[name]
}

export function getCtxMaterials() {
  return window.__LCMaterials__ || window.parent.__LCMaterials__
}

export function getCtxMaterialsToArray() {
  return Object.values(window.parent.__LCMaterials__ || window.__LCMaterials__)
}
