import { findNodesByTag } from '../utils/node.js'

const awaitNextTick = () => {
  return new Promise((resolve) => setTimeout(resolve, 0))
}
const defaultUtils = {
  // 通用方法
  getArrayIds: (arr) => {
    return arr.map((item) => item.id)
  },
  isResponseSuccess: (res = {}) => {
    return !(res?.success === false || res?.resultcode !== '0')
  },
  getRouter() {
    return this.vm.$router
  },
  getRoute() {
    return this.vm.$route
  },
  getUserInfo() {
    return this.vm.$store.state.user
  },
  isAdmin() {
    return this.vm.$store.getters.isAdmin
  },
  message(text, type) {
    return this.vm.$message({
      message: text,
      type,
    })
  },
  getConfirm() {
    return this.vm.$confirm
  },
  async resetFields(formRef) {
    await awaitNextTick()
    if (!this.refs[formRef]) {
      throw new Error(`调用resetFields时formRef: ${formRef}不存在`)
    }
    if (this.refs[formRef].$listeners.resetModel) {
      this.refs[formRef].$emit('resetModel')
    }
    console.warn(
      `如果是高级表单使用表单的resetFields，使用utils会导致调用2次重置值`
    )
    this.refs[formRef].resetFields()
    // 搜索所有node节点是否存在upload组件
    const nodes = this.refs[formRef].$children
    const uploads = findNodesByTag(nodes, ['XnUpload', 'xn-upload'])
    if (uploads.length) {
      uploads.forEach((upload) => {
        upload.clear()
      })
    }
  },
  openTab(path, options, callback) {
    this.vm.$openTab(path, options, callback)
  },
  closeTab(route, options) {
    this.vm.$closeTab(route, options)
  },
}

window.__LCUtils__ = new Proxy(
  { ...defaultUtils },
  {
    set(target, key, value) {
      if (defaultUtils[key]) {
        throw new Error(`addCtxUtil无法修改默认的${key}方法`)
      }
      target[key] = value
      return true
    },
  }
)

export const utilsMapDetail = {
  getArrayIds:
    '获取数组id,返回id数组\n 类型: (arr: {id:string,[key:string]:any}[]) => string[]',
  isResponseSuccess:
    '判断请求结果是否成功,先根据success判断,再根据resultcode是否为0判断\n返回boolean\n 类型: (res: 请求返回结果) => boolean\n例子: utils.isResponseSuccess(datasource.main.create)',
  getRouter:
    "获取vue-router的router对象\n例子: utils.getRouter().push({name: 'home'})",
  getRoute: '获取vue-router的route对象\n例子: utils.getRoute().params.id',
  getUserInfo: '获取用户信息\n类型: () => any\n例子: utils.getUserInfo()',
  isAdmin: '判断是否为管理员\n类型: () => boolean\n例子: utils.isAdmin()',
  message:
    "显示消息提示\n类型: (text: string, type: string) => void\n例子: utils.message('hello', 'success')",
  resetFields:
    '重置表单,并清空上传组件\n类型: (formKey: string) => Promise<void>\n例子: utils.resetFields("form")',
  getConfirm: `获取confirm方法
  类型:  () => { 
    (message: string): Promise<boolean>, 
    operation: () => Promise<boolean>, 
    save: () => Promise<boolean>, 
    submit: () => Promise<boolean>, 
    send: () => Promise<boolean>, 
    delete: () => Promise<boolean> 
  } 
  例子: utils.getConfirm()("消息内容").then(() => {}).catch(() => {})
  utils.getConfirm().operation().then(() => {}).catch(() => {})
  utils.getConfirm().save().then(() => {}).catch(() => {})
  utils.getConfirm().submit().then(() => {}).catch(() => {})
  utils.getConfirm().send().then(() => {}).catch(() => {})
  utils.getConfirm().delete().then(() => {}).catch(() => {})
  `,
  openTab:
    '打开新标签页\n类型: (path: string, options: object, callback: function) => void\n例子: utils.openTab("/quartz/trigger-history-list", { query: { title: "外部消息发送" } }, () => { vm.query() })',
  closeTab:
    '关闭标签页\n类型: (route: Object, options: object) => void\n例子: utils.closeTab()',
}

export function addCtxUtil(name, util) {
  window.__LCUtils__[name] = util
}

export function addCtxUtils(utils) {
  Object.assign(window.__LCutils__, utils)
}

export function getCtxUtil(name) {
  return window.__LCUtils__[name] || window.parent.__LCUtils__[name]
}
export function getCtxUtils() {
  return window.__LCUtils__ || window.parent.__LCUtils__
}
