import cloneDeep from 'lodash/cloneDeep.js'

/**
 * 因为有2个实例需要访问component，其中一个实例使用后，另一个实例就无法使用了。
 * 这里需要cloneDeep，避免修改原
 * 场景1是 先打开渲染页面在打开设计页面
 * 如果公用一个对象，在渲染页面这个组件被使用后，实例就绑定到渲染页面了。设计页面使用时，其实是使用的是渲染页面的组件，这个时候会出现异常。因为2端的实例不一致。
 * 场景2是 先打开设计页面在打开渲染页面
 * 和上面是反的。
 * 所以这里需要保存一个原始的组件对象
 * 在设计器打开时，给设计区域的渲染组件去clone一份设计器有的组件。
 * 只有保证设计器添加到对应的组件即可
 * 这里的设计器一般是main环境
 * 设计区域是canvas环境
 */
window.__LCComponents__ = {}
const componentRawMap = {}

export function addCtxComponent(name, component) {
  componentRawMap[name] = cloneDeep(component)
  window.__LCComponents__[name] = cloneDeep(component)
}

export function addCtxComponents(components) {
  for (const key in components) {
    addCtxComponent(key, components[key])
  }
}

//拿到的是未处理的原始组件
export function getCtxRawComponents() {
  return componentRawMap
}
export function getCtxComponents() {
  return window.__LCComponents__
}

export function getCtxComponent(name) {
  // 访问parent防止canvas无法获取main的组件
  return window.__LCComponents__[name] || window.parent.__LCComponents__[name]
}
