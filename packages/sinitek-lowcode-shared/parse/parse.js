// import { getCtxUtils } from '../context'
import * as log from '../log/index.js'
import {
  isJSExpression,
  isJSFunction,
  isJSAction,
  DesignMode,
} from '../enum/index.js'

export const newFn = (...argv) => {
  const Fn = Function
  return new Fn(...argv)
}

export const parseExpression = (data, scope, mode) => {
  try {
    // 设计时，有defaultValue，则使用defaultValue
    const expression = [DesignMode.DESIGN, DesignMode.LOWCODE].includes(mode)
      ? data?.defaultValue != undefined
        ? `'${data.defaultValue}'`
        : data.value
      : data.value
    return newFn(`with(this) { return ${expression} }`).call(
      Object.assign({}, scope, {
        // vm: ctx.vm,
        // TODO: 重新处理methods，防止scope访问不到
        methods: parseData(scope.methods, scope),
        // utils: getCtxUtils(),
      })
    )
  } catch (error) {
    log.error({ message: `错误表达式:${data.value}`, error })
    return undefined
  }
}

const generateFn = (innerFn, scope) => {
  return (...args) => {
    let result = null
    try {
      result = innerFn.call(
        Object.assign({}, scope, {
          // vm: ctx.vm,
          // TODO: 重新处理methods，防止scope访问不到
          methods: parseData(scope.methods, scope),
          // utils: getCtxUtils(),
        }),
        ...args
      )
      // emitState(ctx.state)
    } catch (error) {
      log.error({
        type: 'warning',
        title: `函数:${innerFn.name}执行报错`,
        message: error?.message || `函数:${innerFn.name}执行报错，请检查语法`,
        error,
      })
    }

    // 这里注意如果innerFn返回的是一个promise则需要捕获异常
    if (result?.then) {
      result = new Promise((resolve, reject) => {
        result.then(resolve).catch((error) => {
          log.error({
            type: 'warning',
            title: '异步函数执行报错',
            message: error?.message || '异步函数执行报错，请检查语法',
            error,
          })
          reject(error)
        })
      })
    }

    return result
  }
}
let isUseTransform = false
export const setTransformJSFunctionState = (v = false) => {
  isUseTransform = v
}
export const parseJSFunction = (data, scope) => {
  try {
    // 如果isUseTransform为true，则使用转换后的代码，否则优先使用原始代码
    const code = isUseTransform ? data.value : (data.original ?? data.value)
    const innerFn = newFn(`return (${code}) `)()
    return generateFn(innerFn, scope, data)
  } catch (error) {
    log.error({ message: `错误代码:${data.value}`, error })
  }
}
export const parseJSAction = (data, scope) => {
  try {
    return generateFn(scope._renderDoAction(data.value), scope, data)
  } catch (error) {
    log.error({ message: `错误Action:${data.value}`, error })
  }
}

const isArray = (data) => {
  return Array.isArray(data)
}
const isString = (data) => {
  return typeof data === 'string'
}
const isFunction = (data) => {
  return typeof data === 'function'
}

const isObject = (data) => {
  return typeof data === 'object'
}
const parseArray = (data, scope, mode) => {
  return data.map((item) => parseData(item, scope, mode))
}

const parseString = (data) => {
  return data.trim()
}

const parseFunction = (data, scope) => {
  return data.bind(scope)
}

const parseObjectData = (data, scope, mode) => {
  if (!data) {
    return data
  }

  const res = {}
  Object.entries(data).forEach(([key, value]) => {
    res[key] = parseData(value, scope, mode)
  })
  return res
}
export function parseData(data, scope, mode = DesignMode.PUBLISH) {
  let res = data
  parseList.some((item) => {
    if (item.type(data)) {
      res = item.parseFn(data, scope, mode)
      return true
    }

    return false
  })

  return res
}

const parseList = [
  {
    type: isJSExpression,
    parseFn: parseExpression,
  },
  { type: isJSFunction, parseFn: parseJSFunction },
  { type: isJSAction, parseFn: parseJSAction },
  {
    type: isArray,
    parseFn: parseArray,
  },
  {
    type: isString,
    parseFn: parseString,
  },
  {
    type: isFunction,
    parseFn: parseFunction,
  },
  {
    type: isObject,
    parseFn: parseObjectData,
  },
]
