export const isValidVariableName = (name, keywords = []) => {
  const regex = /^[a-zA-Z_$][a-zA-Z0-9_$]*$/
  const reservedKeywords = new Set([
    'break',
    'case',
    'catch',
    'class',
    'const',
    'continue',
    'debugger',
    'default',
    'delete',
    'do',
    'else',
    'export',
    'extends',
    'finally',
    'for',
    'function',
    'if',
    'import',
    'in',
    'instanceof',
    'new',
    'return',
    'super',
    'switch',
    'this',
    'throw',
    'try',
    'typeof',
    'var',
    'void',
    'while',
    'with',
    'yield',
    'enum',
    'implements',
    'interface',
    'let',
    'package',
    'private',
    'protected',
    'public',
    'static',
    'await',
    'abstract',
    'boolean',
    'byte',
    'char',
    'double',
    'final',
    'float',
    'goto',
    'int',
    'long',
    'native',
    'short',
    'synchronized',
    'throws',
    'transient',
    'volatile',
  ])
  if (keywords) {
    if (Array.isArray(keywords)) {
      keywords.forEach((e) => {
        reservedKeywords.add(e)
      })
    } else if (typeof keywords === 'string') {
      reservedKeywords.add(keywords)
    } else {
      throw new Error('keywords只能是数组或者字符串')
    }
  }
  return regex.test(name) && !reservedKeywords.has(name)
}

export const elVariableValid = (msg = '变量名不合法') => {
  return (rule, value, callback) => {
    if (!isValidVariableName(value, 'main')) {
      callback(new Error(msg))
    } else {
      callback()
    }
  }
}
