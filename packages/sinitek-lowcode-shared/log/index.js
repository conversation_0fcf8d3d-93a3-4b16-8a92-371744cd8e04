let isDebug = process.env.NODE_ENV === 'development'

// 防止被terser 删掉console
const _console = window['console']
export const openDebug = () => {
  isDebug = true
}

export const closeDebug = () => {
  isDebug = false
}

export const log = (...args) => {
  if (isDebug) {
    _console['log'](...args)
  }
}

export const error = ({ message, error }) => {
  _console['error']({ message, error })
}

export const warn = (...args) => {
  _console['warn'](...args)
}

export const info = (...args) => {
  _console['info'](...args)
}
