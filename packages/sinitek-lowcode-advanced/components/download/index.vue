<template>
  <div class="download">
    <xn-attachment-model ref="download" v-bind="$attrs" v-on="$listeners">
      <template #attachment-item="{ file }">
        <slot name="attachment-item" :file="file" />
      </template>
    </xn-attachment-model>
  </div>
</template>
<script>
export default {
  name: 'LADownload',
  data() {
    return {
      finished: false,
    }
  },
  methods: {
    clear() {
      this.$refs.download.clear()
    },
    isFinished() {
      return this.$refs.download.isFinished
    },
    downloadFile(file) {
      this.$refs.download.downloadFile(file)
    },
  },
}
</script>
