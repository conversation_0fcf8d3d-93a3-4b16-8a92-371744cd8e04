<template>
  <xn-form-item v-bind="itemProps">
    <slot v-if="!isPreview || isReadonly" name="content" :data="currentValue">
      <template v-if="isReadonly">
        <xn-form-item-content type="textarea" :value="getLabel">
        </xn-form-item-content>
      </template>
    </slot>
    <!-- 依赖sinitek-ui -->
    <el-select
      v-if="!isReadonly"
      v-model="currentValue"
      :multiple="multiple"
      :placeholder="placeholder"
      filterable
      remote
      :remote-method="getData"
      :loading="loading"
      clearable
      class="w-full"
      @change="handleChange"
    >
      <el-option
        v-for="item of options"
        :key="item.id"
        :label="item[displayKey]"
        :value="item.id"
      >
        <span class="float-left">{{
          item[display && display.primaryKey] || item.id
        }}</span>
        <span v-if="display && display.secondaryKey" class="float-right">{{
          item[display.secondaryKey]
        }}</span>
      </el-option>
    </el-select>
  </xn-form-item>
</template>

<script>
import { DesignMode } from 'sinitek-lowcode-shared'
export default {
  name: 'LARelaForm',
  inject: {
    LAForm: {
      default: {},
    },
    fetcher: {
      default: {},
    },
    mode: {},
  },
  props: {
    modelAndCondition: Object,
    itemProps: Object,
    placeholder: String,
    multiple: Boolean,
    // primaryKey secondaryKey
    display: {
      type: Object,
    },
    value: [String, Array],
    readonly: Boolean,
  },
  data() {
    return {
      options: [],
      currentValue: '',
      loading: false,
    }
  },
  computed: {
    isReadonly() {
      return this.readonly || this.LAForm.isReadonly
    },
    getLabel() {
      if (Array.isArray(this.currentValue)) {
        return this.currentValue.map((item) => item[this.displayKey]).join(',')
      }
      return (
        this.options.find((item) => item.value === this.currentValue)?.[
          this.displayKey
        ] || ''
      )
    },
    isPreview() {
      return (
        this.mode === DesignMode.PREVIEW || this.mode === DesignMode.PUBLISH
      )
    },
    displayKey() {
      return this?.display?.primaryKey || 'id'
    },
  },
  watch: {
    value: {
      handler(v) {
        if (this.isReadonly) {
          this.currentValue = v
          return
        }
        if (Array.isArray(v)) {
          this.currentValue = v.map((e) => {
            return e?.id || e
          })
        } else {
          this.currentValue = v?.id || v
        }
      },
      deep: true,
      immediate: true,
    },
    modelAndCondition: {
      handler() {
        this.getData()
      },
      deep: true,
    },
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData(query) {
      this.loading = true
      const condition = JSON.parse(
        JSON.stringify(this.modelAndCondition?.condition ?? {})
      )
      if (this.display?.primaryKey && query) {
        condition.and.push({
          field: this.display.primaryKey,
          op: 'like',
          value: query,
        })
      }
      if (!this?.fetcher?.modelFetchers?.list) return
      this.fetcher.modelFetchers
        .list({
          data: {
            modelCode: this.modelAndCondition?.modelCode,
            condition: condition,
          },
        })
        .then((res) => {
          this.options = res?.data?.datalist || []
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleChange(v) {
      this.$emit('input', v)
    },
  },
}
</script>
