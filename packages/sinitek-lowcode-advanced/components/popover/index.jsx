import { Design, DesignMode } from 'sinitek-lowcode-shared'

export default {
  name: 'LAPopover',
  data() {
    return { visible: false }
  },
  inject: {
    mode: {
      default: DesignMode.PUBLISH,
    },
    $doc: {
      default: () => null,
    },
  },
  computed: {
    attrs() {
      const result = {
        ...this.$attrs,
      }
      return result
    },
  },
  render() {
    const hideEvents = [this.hide, this.$listeners?.hide].filter(Boolean)
    return (
      <el-popover
        {...{
          props: {
            ...this.$attrs,
            trigger:
              DesignMode.PUBLISH !== this.mode ? 'manual' : this.$attrs.trigger,
          },
        }}
        value={this.visible || this.$attrs.value}
        on={{
          ...this.$listeners,
          'hook:mounted': this.popoverRendered,
          hide: hideEvents,
        }}
        ref="popover"
      >
        {this.$slots.default}
        <span slot="reference">{this.$slots.reference}</span>
      </el-popover>
    )
  },
  methods: {
    popoverRendered() {
      if (DesignMode.PUBLISH !== this.mode) {
        this.visible = true
        this.$nextTick(() => {
          const id = this.$refs.popover.tooltipId
          if (id) {
            const el = this.$doc.frameDocument.getElementById(id)
            el.setAttribute(Design.NODE_UID, this.$attrs[Design.NODE_UID])
            el.setAttribute(Design.NODE_TAG, this.$attrs[Design.NODE_TAG])
          }
        })
        this.clean = this.$watch(
          () => this.$attrs.showDesign,
          (val) => {
            this.visible = val
          }
        )
      }
    },
  },
  beforeDestroy() {
    this?.clean?.()
  },
}
