import tableValid from '../mixins/tableValid'
export default {
  name: 'LAInputTemplate',
  mixins: [tableValid],
  inject: {
    // ref la-table
    LATable: {
      default: null,
    },
  },
  props: {
    lcTag: {
      type: String,
      default: 'xn-input',
    },
  },
  data() {
    return {
      value: this.$attrs.value,
      validateState: '',
      validateMessage: '',
    }
  },
  watch: {
    '$attrs.value'(v) {
      this.value = v
    },
  },
  render(h) {
    const el = h(this.lcTag, {
      attrs: { ...this.$attrs },
      domProps: { value: this.value },
      style: this.$attrs.style,
      on: {
        ...this.$listeners,
        input: (e) => {
          this.onInput(e)
          this.$listeners?.input?.(e)
        },
        blur: (e) => {
          this.onBlur(e)
          this.$listeners?.blur?.(e)
        },
      },
    })
    // 如果不是在table中使用，直接返回元素
    if (!this.LATable?.useForm) {
      return el
    }
    return h(
      'div',
      {
        class: [
          'el-form-item',
          {
            'is-error': this.validateState === 'error',
            'is-validating': this.validateState === 'validating',
            'is-success': this.validateState === 'success',
          },
        ],
      },
      [
        h('div', { class: 'el-form-item__content' }, [
          el,
          this.validateState === 'error'
            ? h(
                'div',
                {
                  attrs: { name: 'error', error: this.validateMessage },
                },
                [
                  h(
                    'div',
                    {
                      class: 'el-form-item__error el-form-item__error--inline',
                    },
                    [this.validateMessage]
                  ),
                ]
              )
            : null,
        ]),
      ]
    )
  },
  methods: {
    onInput(v) {
      this.value = v
      this.validate('change')
      this.$emit('input', v)
    },
    onBlur() {
      this.validate('blur')
    },
  },
}
