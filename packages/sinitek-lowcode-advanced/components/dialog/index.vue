<template>
  <xn-dialog
    v-bind="{ ...$props, ...$attrs, ...extendsProp }"
    v-on="$listeners"
    @update:show="updateShow"
    @opened="opened"
  >
    <template #title>
      <slot name="title"></slot>
    </template>
    <slot></slot>
    <template #footer>
      <slot name="footer"></slot>
    </template>
  </xn-dialog>
</template>

<script>
import { DesignMode, isDesignMode } from 'sinitek-lowcode-shared'

export default {
  name: 'LADialog',
  inject: {
    mode: { default: DesignMode.PUBLISH },
    // 低代码组件的
    lowcodeScope: { default: {} },
    $doc: { default: null },
  },
  inheritAttrs: false,
  props: {
    show: Boolean,
    value: Boolean,
    to: {
      type: [String, Boolean],
      default: '#dialog-transfer',
    },
    modal: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
    },
    width: {
      type: String,
    },
    top: {
      type: String,
      default: '15vh',
    },
    innerTop: {
      type: String,
      default: '10vh',
    },
    height: {
      type: String,
    },
    showFooter: {
      type: <PERSON>olean,
      default: true,
    },
    lockScroll: {
      type: Boolean,
      default: true,
    },
    beforeClose: Function,
    buttons: Array,
    // 默认按ESC键不关闭dialog
    closeOnPressEscape: {
      type: Boolean,
      default: true,
    },
    closeOnClickModal: {
      type: Boolean,
      default: null,
    },
    loadingNotAllowClose: {
      type: Boolean,
      default: null,
    },
    widthSize: {
      type: [String, Number],
      default: '',
    },
    customClass: {
      type: String,
      default: '',
    },
    limitToTab: {
      type: Boolean,
      default: function () {
        return this.$SINITEKUI.dialogLimitToTab
      },
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    fullscreen: {
      type: Boolean,
      default: false,
    },
    showFullscreenIcon: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: this.value,
    }
  },
  computed: {
    extendsProp() {
      // 组件设计时添加的属性
      if (this.lowcodeScope?.mode && isDesignMode(this.lowcodeScope?.mode)) {
        return {
          to: '',
          limitToTab: true,
          show: this.value || this.visible,
        }
      }
      return {
        show: isDesignMode(this.mode) ? this.show : this.value || this.visible,
        buttons: isDesignMode(this.mode)
          ? this.buttons
          : this.buttons.filter((button) => !button.hidden),
      }
    },
  },
  mounted() {
    if (isDesignMode(this.mode)) {
      // fix: 处理设计模式下选中框位置不对的问题
      setTimeout(() => {
        const el = this.$doc.frameDocument.querySelector(
          `div[data-uid="${this.$attrs['data-uid']}"] .body-container`
        )
        if (!el) return
        el.style.maxHeight = 'unset'
        // 设置inheritAttrs 导致选择层级变化问题处理
        this.$doc.frameDocument
          .querySelector(
            `div[data-uid="${this.$attrs['data-uid']}"].el-dialog__wrapper`
          )
          .addEventListener('scroll', () => {
            this.$doc.hover.clean()
            this.$doc.select.reselect()
          })
      }, 16)
    }
  },
  methods: {
    updateShow(v) {
      this.visible = v
      this.$emit('input', v)
    },
    open() {
      this.visible = true
      const { promise, resolve } = Promise.withResolvers()
      this.openResolve = resolve
      return promise
    },
    opened() {
      if (this.openResolve) {
        this.$nextTick(() => {
          this.openResolve()
        })
      }
    },
    close() {
      this.openResolve = null
      this.visible = false
    },
  },
}
</script>
