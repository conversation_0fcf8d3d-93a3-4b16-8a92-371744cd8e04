<template>
  <xn-form-item
    v-bind="itemProps"
    :rules="computedRules"
    :class="[span ? `grid-col-span-${span}` : '']"
  >
    <xn-form-item-content
      v-if="isReadonly && !isFile"
      type="textarea"
      :value="getText"
    >
    </xn-form-item-content>
    <XnDownload v-else-if="isFile && isReadonly" v-bind="fieldProps" />
    <!-- 依赖sinitek-ui -->
    <components
      :is="currentComponent.component"
      v-else
      v-bind="{
        ...fieldProps,
        ...currentComponent.defaultProps,
        value: $attrs.value,
      }"
      v-on="computedListeners"
    />
  </xn-form-item>
</template>

<script>
import { ComponentName, isInputType } from 'sinitek-lowcode-shared'
import { getComponent } from '../../utils/getComponent'
import LAChildForm from '../child-form'
import LAEmployee from '../employee'
import LARelaForm from '../rela-form'
import LARadio from '../radio'
import LACheck from '../checkbox'
import { validatorUtil } from 'sinitek-util'
import moment from 'moment'

export default {
  name: 'LAFormItem',
  components: {
    LAChildForm,
    LAEmployee,
    LARelaForm,
    LARadio,
    LACheck,
  },
  inject: {
    LAForm: {
      default: {},
    },
  },
  props: {
    itemProps: Object,
    fieldProps: Object,
    field: String,
    span: [Number, String],
  },
  computed: {
    isFile() {
      return ComponentName.FILE === this.field
    },
    computedListeners() {
      const result = {
        ...this.$listeners,
      }
      if (this.field === ComponentName.SIRMORG) {
        result.getResult = this.$listeners.input
      }
      return result
    },
    isReadonly() {
      return this.LAForm.isReadonly
    },
    currentComponent() {
      const result = {
        component: getComponent(this.field),
        defaultProps: {},
      }
      if (this.field === ComponentName.DATETIME) {
        result.defaultProps.type = 'datetime'
      }
      return result
    },
    computedRules() {
      if (this.itemProps.required && !this.itemProps?.rules) {
        if (this.field === ComponentName.FILE) {
          return [
            {
              required: true,
              validator: validatorUtil.validateUploader(
                this.$t('org.message.emptyFile')
              ),
            },
          ]
        }
        return [
          {
            required: true,
            message: `请${isInputType(this.field) ? '输入' : '选择'}${this.itemProps.label}`,
            trigger: ['blur', 'change'],
          },
        ]
      }
      return this.itemProps?.rules
    },
    getText() {
      const val =
        (this.fieldProps && this.fieldProps.value) || this.$attrs.value
      if (!val) return
      // 多选和单选只读回显处理
      if (
        [
          ComponentName.CHECKBOX_GROUP,
          ComponentName.RADIO_GROUP,
          ComponentName.SELECT,
        ].includes(this.field)
      ) {
        const arr = this.fieldProps?.options || []
        return arr
          .filter((e) => val?.includes?.(e.value) ?? val === e.value)
          .map((item) => item?.label ?? item)
          .join(',')
      } else if (
        [ComponentName.DATE, ComponentName.DATETIME].includes(this.field)
      ) {
        if (this.fieldProps?.isRange) {
          return (
            val[0] &&
            moment(val[0]).format(this.fieldProps?.format ?? 'YYYY-MM-DD') +
              ` ${this.fieldProps?.rangeSeparator ?? '-'} ` +
              val[1] &&
            moment(val[1]).format(this.fieldProps?.format ?? 'YYYY-MM-DD')
          )
        }
        return moment(val).format(this.fieldProps?.format ?? 'YYYY-MM-DD')
      } else if (
        [ComponentName.TIME, ComponentName.TIME_SELECT].includes(this.field)
      ) {
        return moment(val).format(this.fieldProps?.valueFormat ?? 'HH:mm:ss')
      } else if (ComponentName.SWITCH === this.field) {
        return val === (this.fieldProps?.activeValue ?? true)
          ? (this.fieldProps?.activeText ?? '是')
          : (this.fieldProps?.inactiveText ?? '否')
      } else if (ComponentName.SIRMORG === this.field) {
        return val?.linkOrg?.map((e) => e.text).join(',')
      }
      if (Array.isArray(val)) {
        return val.map((item) => item?.label ?? item).join(',')
      }
      return val
    },
  },
}
</script>
