<template>
  <xn-employee
    v-bind="$attrs"
    ref="employee"
    :value="model"
    :model="model"
    v-on="$listeners"
    @getResult="_getResult"
  ></xn-employee>
</template>
<script>
// 处理返回，只返回id
export default {
  name: 'LAEmployee',
  data() {
    return {
      model: '',
    }
  },
  computed: {
    excludeInput() {
      const result = {}
      Object.keys(this.$listeners).forEach((key) => {
        if (key !== 'input') {
          result[key] = this.$listeners[key]
        }
      })
      return result
    },
  },
  watch: {
    '$attrs.value': {
      handler(v) {
        this.model = Array.isArray(v) ? v.join(',') : v
      },
      immediate: true,
    },
  },
  methods: {
    _getResult({ linkOrg }) {
      let _v = linkOrg.map((e) => e.id)
      if (!this.$attrs.multi) {
        _v = _v[0]
      }
      this.model = Array.isArray(_v) ? _v.join(',') : _v
      this.$emit('input', _v)
    },
    clear() {
      return this.$refs.employee.clear()
    },
    getResult() {
      return this.$refs.employee.getResult()
    },
  },
}
</script>
