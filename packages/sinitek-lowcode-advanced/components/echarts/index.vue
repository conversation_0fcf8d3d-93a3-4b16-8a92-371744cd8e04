<template>
  <div ref="wrap" class="laechart">
    <div :style="computedStyle">
      <div ref="chart" class="laechart"></div>
    </div>
  </div>
</template>

<script>
// import * as echarts from 'echarts/lib/echarts'
// import 'echarts/lib/component/grid'
window.LCECharts = window.echarts
// 全局map，用于记录echarts的图表类型是否已经加载
// let loadingMap = new Map()
export default {
  name: 'LAECharts',
  props: {
    width: String,
    height: String,
    theme: String,
    config: Object,
  },
  data() {
    return {
      needReInit: false,
      isLoading: false,
    }
  },
  computed: {
    computedStyle() {
      const result = {
        width: '100%',
        height: '300px',
      }
      if (this.width) {
        result.width =
          typeof this.width === 'string' ? this.width : this.width + 'px'
      }
      if (this.height) {
        result.height =
          typeof this.height === 'string' ? this.height : this.height + 'px'
      }
      return result
    },
  },
  watch: {
    theme() {
      this.init()
    },
    width() {
      this.callResize()
    },
    height() {
      this.callResize()
    },
    config: {
      handler(n, o) {
        if (JSON.stringify(n) !== JSON.stringify(o)) {
          this._autoImport().then(() => {
            this.ecInstance.setOption(n)
            this.ecInstance.hideLoading()
          })
        }
      },
      deep: true,
    },
  },
  mounted() {
    this._autoImport().then(() => {
      this.ecInstance.setOption(this.config)
      this.ecInstance.hideLoading()
    })
    this.ob = new ResizeObserver(() => {
      setTimeout(() => {
        this.callResize()
      })
    })
    this.ob.observe(this.$refs.wrap.parentElement)
  },
  beforeDestroy() {
    this.ecInstance.dispose()
    this.ecInstance = null
    this.ob.disconnect()
  },
  methods: {
    async _autoImport() {
      this.isLoading = true
      // const series = (
      //   Array.isArray(this.config?.series)
      //     ? this.config.series
      //     : [this.config?.series]
      // ).filter(Boolean)
      // const types = series?.map((item) => item.type) ?? []
      // const hasMarkLine = series?.some((item) => item.markLine) ?? false
      // const hasMarkPoint = series?.some((item) => item.markPoint) ?? false
      // const hasMarkArea = series?.some((item) => item.markArea) ?? false
      // if (!loadingMap.get('title') && this.config?.title) {
      //   await import('echarts/lib/component/title')
      //   loadingMap.set('title', true)
      // }
      // if (!loadingMap.get('dataset') && this.config?.dataset) {
      //   await import('echarts/lib/component/dataset')
      //   loadingMap.set('dataset', true)
      // }
      // if (!loadingMap.get('tooltip') && this.config?.tooltip) {
      //   await import('echarts/lib/component/tooltip')
      //   loadingMap.set('tooltip', true)
      // }
      // if (!loadingMap.get('legend') && this.config?.legend) {
      //   await import('echarts/lib/component/legend')
      //   loadingMap.set('legend', true)
      // }
      // if (!loadingMap.get('toolbox') && this.config?.toolbox) {
      //   await import('echarts/lib/component/toolbox')
      //   loadingMap.set('toolbox', true)
      // }
      // if (!loadingMap.get('dataZoom') && this.config?.dataZoom) {
      //   await import('echarts/lib/component/dataZoom')
      //   loadingMap.set('dataZoom', true)
      // }
      // if (!loadingMap.get('visualMap') && this.config?.visualMap) {
      //   await import('echarts/lib/component/visualMap')
      //   loadingMap.set('visualMap', true)
      // }
      // if (!loadingMap.get('polar') && this.config?.polar) {
      //   await import('echarts/lib/component/polar')
      //   loadingMap.set('polar', true)
      // }
      // if (!loadingMap.get('calendar') && this.config?.calendar) {
      //   await import('echarts/lib/component/calendar')
      //   loadingMap.set('calendar', true)
      // }
      // if (!loadingMap.get('brush') && this.config?.brush) {
      //   await import('echarts/lib/component/brush')
      //   loadingMap.set('brush', true)
      // }
      // if (!loadingMap.get('geo') && this.config?.geo) {
      //   await import('echarts/lib/component/geo')
      //   loadingMap.set('geo', true)
      // }
      // if (!loadingMap.get('graphic') && this.config?.graphic) {
      //   await import('echarts/lib/component/graphic')
      //   loadingMap.set('graphic', true)
      // }
      // if (!loadingMap.get('parallel') && this.config?.parallel) {
      //   await import('echarts/lib/component/parallel')
      //   loadingMap.set('parallel', true)
      // }
      // if (!loadingMap.get('radar') && this.config?.radar) {
      //   await import('echarts/lib/component/radar')
      //   loadingMap.set('radar', true)
      // }
      // if (!loadingMap.get('timeline') && this.config?.timeline) {
      //   await import('echarts/lib/component/timeline')
      //   loadingMap.set('timeline', true)
      // }
      // if (!loadingMap.get('transform') && this.config?.transform) {
      //   await import('echarts/lib/component/transform')
      //   loadingMap.set('transform', true)
      // }

      // if (!loadingMap.get('line') && types.includes('line')) {
      //   await import('echarts/lib/chart/line')
      //   loadingMap.set('line', true)
      // }
      // if (!loadingMap.get('bar') && types.includes('bar')) {
      //   await import('echarts/lib/chart/bar')
      //   loadingMap.set('bar', true)
      // }
      // if (!loadingMap.get('pie') && types.includes('pie')) {
      //   await import('echarts/lib/chart/pie')
      //   loadingMap.set('pie', true)
      // }
      // if (!loadingMap.get('scatter') && types.includes('scatter')) {
      //   await import('echarts/lib/chart/scatter')
      //   loadingMap.set('scatter', true)
      // }
      // if (!loadingMap.get('map') && types.includes('map')) {
      //   await import('echarts/lib/chart/map')
      //   loadingMap.set('map', true)
      // }
      // if (!loadingMap.get('candlestick') && types.includes('candlestick')) {
      //   await import('echarts/lib/chart/candlestick')
      //   loadingMap.set('candlestick', true)
      // }
      // if (!loadingMap.get('custom') && types.includes('custom')) {
      //   await import('echarts/lib/chart/custom')
      //   loadingMap.set('custom', true)
      // }
      // if (!loadingMap.get('radar') && types.includes('radar')) {
      //   await import('echarts/lib/chart/radar')
      //   loadingMap.set('radar', true)
      // }
      // if (!loadingMap.get('boxplot') && types.includes('boxplot')) {
      //   await import('echarts/lib/chart/boxplot')
      //   loadingMap.set('boxplot', true)
      // }
      // if (!loadingMap.get('heatmap') && types.includes('heatmap')) {
      //   await import('echarts/lib/chart/heatmap')
      //   loadingMap.set('heatmap', true)
      // }
      // if (!loadingMap.get('graph') && types.includes('graph')) {
      //   await import('echarts/lib/chart/graph')
      //   loadingMap.set('graph', true)
      // }
      // if (!loadingMap.get('lines') && types.includes('lines')) {
      //   await import('echarts/lib/chart/lines')
      //   loadingMap.set('lines', true)
      // }
      // if (!loadingMap.get('tree') && types.includes('tree')) {
      //   await import('echarts/lib/chart/tree')
      //   loadingMap.set('tree', true)
      // }
      // if (!loadingMap.get('treemap') && types.includes('treemap')) {
      //   await import('echarts/lib/chart/treemap')
      //   loadingMap.set('treemap', true)
      // }
      // if (!loadingMap.get('sunburst') && types.includes('sunburst')) {
      //   await import('echarts/lib/chart/sunburst')
      //   loadingMap.set('sunburst', true)
      // }
      // if (!loadingMap.get('parallel') && types.includes('parallel')) {
      //   await import('echarts/lib/chart/parallel')
      //   loadingMap.set('parallel', true)
      // }
      // if (!loadingMap.get('sankey') && types.includes('sankey')) {
      //   await import('echarts/lib/chart/sankey')
      //   loadingMap.set('sankey', true)
      // }
      // if (!loadingMap.get('funnel') && types.includes('funnel')) {
      //   await import('echarts/lib/chart/funnel')
      //   loadingMap.set('funnel', true)
      // }
      // if (!loadingMap.get('gauge') && types.includes('gauge')) {
      //   await import('echarts/lib/chart/gauge')
      //   loadingMap.set('gauge', true)
      // }
      // if (!loadingMap.get('pictorialBar') && types.includes('pictorialBar')) {
      //   await import('echarts/lib/chart/pictorialBar')
      //   loadingMap.set('pictorialBar', true)
      // }
      // if (!loadingMap.get('themeRiver') && types.includes('themeRiver')) {
      //   await import('echarts/lib/chart/themeRiver')
      //   loadingMap.set('themeRiver', true)
      // }

      // if (!loadingMap.get('markLine') && hasMarkLine) {
      //   await import('echarts/lib/component/markLine')
      //   loadingMap.set('markLine', true)
      // }
      // if (!loadingMap.get('markPoint') && hasMarkPoint) {
      //   await import('echarts/lib/component/markPoint')
      //   loadingMap.set('markPoint', true)
      // }
      // if (!loadingMap.get('markArea') && hasMarkArea) {
      //   await import('echarts/lib/component/markArea')
      //   loadingMap.set('markArea', true)
      // }
      this.init()
    },
    init() {
      if (this.ecInstance) {
        this.ecInstance.dispose()
        this.ecInstance = null
      }
      const ecInstance = new window.echarts.init(this.$refs.chart, this.theme, {
        width: null,
        height: null,
      })
      ecInstance.showLoading()
      // 列出所有的echarts 实例方法
      const instanceMethods = [
        'group',
        'setOption',
        'resize',
        'clear',
        'on',
        'off',
        'getWidth',
        'getHeight',
        'getDom',
        'getOption',
        'renderToSVGString',
        'dispatchAction',
        'convertToPixel',
        'convertFromPixel',
        'containPixel',
        'showLoading',
        'hideLoading',
        'getDataURL',
        'getConnectedDataURL',
        'appendData',
        'isDisposed',
        'dispose',
      ]
      instanceMethods.forEach((key) => {
        if (typeof ecInstance[key] === 'function') {
          this[key] = ecInstance[key].bind(ecInstance)
        } else {
          this[key] = ecInstance[key]
        }
      })
      const events = [
        'click',
        'dblclick',
        'mousedown',
        'mouseup',
        'mouseout',
        'mouseover',
        'globalout',
        'contextmenu',
        'legendselectchanged',
        'legendselected',
        'legendunselected',
        'legendselectall',
        'legendinverseselect',
        'legendscroll',
        'datazoom',
        'datarangeselected',
        'graphroam',
        'georoam',
        'treeroam',
        'timelinechanged',
        'timelineplaychanged',
        'restore',
        'dataviewchanged',
        'magictypechanged',
        'geoselectchanged',
        'geoselected',
        'geounselected',
        'axisareaselected',
        'brush',
        'brushEnd',
        'brushselected',
        'globalcursortaken',
        'rendered',
        'finished',
      ]
      events.forEach((event) => {
        ecInstance.on(event, (...args) => {
          this.$emit(event, ...args)
        })
      })
      this.ecInstance = ecInstance
      this.isLoading = false
    },
    callResize() {
      this.$nextTick(() => {
        this.ecInstance?.resize()
      })
    },
  },
}
</script>
<style scoped>
.laechart {
  width: 100%;
  height: 100%;
}
</style>
