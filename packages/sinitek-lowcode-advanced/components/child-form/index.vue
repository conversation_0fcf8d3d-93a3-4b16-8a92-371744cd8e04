<script lang="jsx">
import { getComponent } from '../../utils/getComponent'
import {
  ComponentName,
  DesignMode,
  parseData,
  isInputType,
} from 'sinitek-lowcode-shared'
import Emitter from 'element-ui/src/mixins/emitter'

export default {
  name: 'LAChildForm',
  mixins: [Emitter],
  // mode 当前渲染状态
  inject: {
    mode: { default: null },
    // laForm 是否是LAForm的子组件
    LAForm: { default: {} },
    isParentLAForm: { default: false },
    elForm: { default: {} },
  },
  props: {
    value: Array,
    status: {
      type: String,
      default: 'edit',
    },
    tableProps: Object,
    formProps: Object,
    addLabel: {
      type: String,
      default: '添加',
    },
    addButtonType: String,
    addIcon: String,
    useAdd: {
      type: Boolean,
      default: true,
    },
    useDelete: {
      type: Boolean,
      default: true,
    },
    // 使用formItem包裹
    useFormItem: Boolean,
    itemProps: Object,
    span: String,
  },
  data() {
    return {
      currentValue: [],
      widths: [],
    }
  },
  computed: {
    isReadonly() {
      return this.LAForm.isReadonly || this.status === 'readonly'
    },
    showAdd() {
      return !this.isReadonly
    },
  },
  watch: {
    value: {
      handler(v) {
        // fix: 不为数组时会卡死
        if (Array.isArray(v)) {
          this.currentValue = v || []
        } else {
          this.currentValue = []
        }
      },
      immediate: true,
    },
    currentValue: {
      deep: true,
      handler(v) {
        this.$emit('input', v)
      },
    },
  },
  mounted() {
    this.form = this.$refs?.form
    this.table = this.$refs?.table
  },
  methods: {
    validate(fn) {
      this.$refs.form.validate((valid) => {
        this.isValid = valid
        this.$nextTick(() => {
          // 清除表单项的错误状态
          // 出现错误时会导致表格里的每一项记录都变成错误的样式
          this.$refs.formItem?.$el?.classList?.remove?.('is-error')
        })
        fn?.(valid)
      })
    },
    resetFields() {
      this.isValid = true
      this.$refs.form.resetFields()
    },
    clearValidate(props) {
      this.isValid = true
      this.$refs.form.clearValidate(props)
    },
    renderDev() {
      const slots = this.$slots.default || []
      const items = slots.map((node) => {
        return node
      })
      return (
        <XnForm labelPosition="top" class="dev-form">
          <XnFormItem label="#">1</XnFormItem>
          {items}
        </XnForm>
      )
    },
    callFormItemValid() {
      this.$refs?.formItem?.$refs?.formItem?.onFieldChange?.()
    },
    renderProd() {
      const renderColumn = (node, formRules) => {
        return (scope) => {
          const schema = parseData(node.componentOptions.propsData.schema, {
            ...node.componentOptions.propsData.scope,
            scope,
          })

          let itemProps = schema.props.itemProps

          if (this.isReadonly) {
            if (schema.props.field === ComponentName.FILE) {
              return this._c('xn-download', {
                props: {
                  ...schema.props.fieldProps,
                },
                attrs: {
                  ...schema.props.fieldProps,
                },
              })
            }
            return this.currentValue[scope.$index][itemProps.prop]
          }

          const events = parseData(schema.events, {
            ...node.componentOptions.propsData.scope,
            scope,
          })
          const on = {
            ...events,
            input: (v) => {
              this.currentValue[scope.$index][itemProps.prop] = v
              this.callFormItemValid()
              events?.input?.(v)
            },
          }
          const compProps = {
            attrs: {
              ...schema.props,
              ...schema.props.fieldProps,
              value: this.currentValue[scope.$index][itemProps.prop],
            },
            props: {
              ...schema.props,
              ...schema.props.fieldProps,
              value: this.currentValue[scope.$index][itemProps.prop],
            },
            on,
          }

          if (schema.props.field === ComponentName.FILE) {
            compProps.props.sourceId = scope.row.id
          }
          if (schema.props.field === ComponentName.DATETIME) {
            compProps.props.type = 'datetime'
          }

          const props = {
            props: {
              labelWidth: 1,
            },
            attrs: {
              ...itemProps,
              prop: `[${scope.$index}].${itemProps.prop}`,
              rules: itemProps.required
                ? [
                    {
                      required: itemProps.required,
                      message: `请${isInputType(schema.props.field) ? '输入' : '选择'}${
                        itemProps.label
                      }`,
                    },
                    ...formRules,
                  ]
                : formRules,
            },
          }

          // 埋点指令
          if (schema.trackCode) {
            props.directives = [
              {
                name: 'has',
                value: schema.trackCode,
              },
            ]
          }

          return (
            <xn-form-item {...props}>
              {this._c(getComponent(schema.props.field), compProps)}
            </xn-form-item>
          )
        }
      }

      const renderIndex = (scope) => {
        const remove = () => {
          this.$emit('remove', scope.row, scope.$index)
          this.currentValue.splice(scope.$index, 1)
          // 触发表单验证
          this.callFormItemValid()
        }
        return (
          <div class={['cf-index', { useDelete: this.useDelete }]}>
            <span>{scope.$index + 1}</span>
            {this.useDelete ? (
              <el-button
                type="danger"
                icon="el-icon-delete"
                circle
                onClick={remove}
              ></el-button>
            ) : null}
          </div>
        )
      }
      return (
        <xn-form
          ref="form"
          {...{ props: { model: this.currentValue } }}
          inline-message
        >
          <el-table
            border
            ref="table"
            data={this.currentValue}
            {...{
              props: this.tableProps,
              attrs: this.tableProps,
            }}
          >
            <el-table-column fixed="left" type="index" label="#" width="50">
              {renderIndex}
            </el-table-column>
            {(this.$slots.default || []).map((node) => {
              const schema = node.componentOptions.propsData.schema
              let itemProps = schema.props.itemProps
              // 取表单的验证规则
              // 先用当前表单项的规则，再用表单规则，最后是elForm的规则
              const formRules =
                itemProps.rules ||
                this.itemProps?.formRules?.[itemProps.prop] ||
                this.elForm?.rules?.[this.itemProps.prop]?.childRules?.[
                  itemProps.prop
                ] ||
                []

              const slots = {
                header: (scope) => {
                  const calcSchema = parseData(
                    node.componentOptions.propsData.schema,
                    {
                      ...node.componentOptions.propsData.scope,
                      scope,
                    }
                  )
                  let hasRequired = calcSchema.props.itemProps?.required
                  // 规则中有必填时，添加必填星号
                  if (
                    !hasRequired &&
                    Array.isArray(formRules) &&
                    formRules.some((e) => e.required)
                  )
                    hasRequired = true

                  const required = hasRequired ? (
                    <span style="color: #f56c6c;margin-right: 4px">*</span>
                  ) : null
                  return (
                    <span>
                      {this.isReadonly ? '' : required}
                      {itemProps.label}
                    </span>
                  )
                },
                default: renderColumn(node, formRules),
              }
              return (
                <el-table-column
                  width={itemProps.labelWidth}
                  minWidth={itemProps.labelWidth}
                  prop={itemProps.prop}
                  label={itemProps.label}
                  {...{ scopedSlots: slots }}
                ></el-table-column>
              )
            })}
          </el-table>
        </xn-form>
      )
    },
    onAdd() {
      const obj = (this.$slots.default || []).reduce((cur, next) => {
        const schema = next.componentOptions.propsData.schema
        let prop = schema.props.itemProps.prop
        cur[prop] = ''
        return cur
      }, {})
      this.currentValue.push(obj)
      // 触发表单验证
      this.callFormItemValid()
      this.$emit('add')
    },
  },
  render() {
    const content = () => {
      return (
        <div
          class={[
            'la-child-form',
            !this.useFormItem ? 'grid-col-span-' + this.span : '',
          ]}
        >
          {this.mode === DesignMode.DESIGN
            ? this.renderDev()
            : this.renderProd()}
          {this.showAdd && this.useAdd ? (
            <el-button
              onClick={this.onAdd}
              type={this.addButtonType}
              icon={this.addIcon}
            >
              {this.addLabel}
            </el-button>
          ) : null}
        </div>
      )
    }
    if (this.LAForm && this.useFormItem) {
      const itemProps = this.itemProps
      const isRequired = itemProps.required
      itemProps.rules = itemProps.rules || []
      if (itemProps.required) {
        itemProps.rules.push({
          validator: (rule, value, callback) => {
            if (!this.currentValue?.length) {
              callback(`请添加${itemProps.label}`)
            }
            callback()
          },
        })
        itemProps.required = false
      }
      itemProps.rules.push({
        validator: (rule, value, callback) => {
          if (this.currentValue.length) {
            this.validate((valid) => {
              callback(!valid ? '子表单验证不通过' : void 0)
            })
          } else {
            callback()
          }
        },
      })
      if (this.elForm?.rules?.[this.itemProps.prop]?.rules?.length) {
        itemProps.rules.push(...this.elForm.rules[this.itemProps.prop].rules)
      }
      const props = {
        props: itemProps,
        attrs: itemProps,
      }
      return (
        <xn-form-item
          ref="formItem"
          class={[
            this.span ? 'grid-col-span-' + this.span : '',
            isRequired ? 'is-required' : '',
          ]}
          {...props}
        >
          {content()}
        </xn-form-item>
      )
    }
    return content()
  },
}
</script>
<style lang="scss">
$border-color: #ebeef5;
.la-child-form {
  border: 1px solid $border-color;
  font-size: 14px;
  &__add {
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .hidden {
    display: none;
  }
  .el-form-item {
    margin-bottom: 0 !important;
  }
  .el-table .el-form-item__label {
    display: none;
  }
  .useDelete {
    .el-button {
      display: none;
    }
    &:hover {
      .el-button {
        display: block;
      }
      & > span {
        display: none;
      }
    }
  }
  // dev mode
  .dev-form {
    display: flex;
    .el-form-item {
      margin-left: 0 !important;
      width: auto !important;
      border: 1px solid $border-color;

      &:first-child {
        width: 50px !important;
        text-align: center;
        .el-form-item__label {
          text-align: center;
        }
      }
    }
    .el-form-item__label {
      width: 100%;
      padding: 0 10px !important;
      margin-bottom: 10px;
      border-bottom: 1px solid $border-color;
    }
  }

  // prod mode
  .el-form {
    display: flex;
  }
}
</style>
