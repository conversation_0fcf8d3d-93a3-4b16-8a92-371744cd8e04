<template>
  <LAECharts
    v-if="config"
    :config="config"
    :width="width"
    :height="height"
    :theme="theme"
  />
</template>

<script>
import LAECharts from '../echarts/index.vue'

// 检查值是否可以转成数值，可以就转成数值否则就是字符串维度
const checkValue = (value) => {
  if (value === void 0) {
    return null
  }
  if (isNaN(parseInt(value))) {
    return value
  }
  return parseInt(value)
}
const needXTypes = ['line', 'bar']

export default {
  name: 'LAEChartsEditor',
  components: {
    LAECharts,
  },
  props: {
    width: String,
    height: String,
    theme: String,
    items: Array,
    legend: Object,
    data: Array,
    tooltip: Object,
  },
  computed: {
    config() {
      const legend = Object.assign({ bottom: '0' }, this.legend || {})
      const tooltip = Object.assign({ show: false }, this.tooltip || {})
      const result = {
        legend,
        tooltip,
      }

      if (this.data) {
        result.dataset = {
          source: this.data,
        }
        // 如果数据是对象，则设置维度
        if (this.data?.length && !Array.isArray(this.data[0])) {
          result.dataset.dimensions = Object.keys(this.data[0])
        }
      }

      const yMap = new Map()

      const series = []
      const yAxis = []
      let needXAxis = false
      if (this.items?.length) {
        this.items.forEach((item) => {
          const seriesItem = {
            type: item.type,
            encode: {
              x: checkValue(item?.itemXDimension),
              y: checkValue(item?.itemYDimension),
              tooltip: checkValue(item.tooltipValueDimension),
            },
            name: item.name,
          }
          if (item.isArea) {
            Object.assign(seriesItem, {
              lineStyle: {
                width: 0,
              },
              showSymbol: false,
              areaStyle: {},
            })
          }
          if (
            item?.yAxis?.name &&
            !yMap.has(item?.yAxis?.name) &&
            yAxis.length < 2 &&
            item.type !== 'pie'
          ) {
            yMap.set(item?.yAxis?.name, true)
            yAxis.push({
              type: 'value',
              nameLocation: 'end',
              name: item?.yAxis?.name,
            })
          }

          const itemStyle = {}
          if (item?.color) {
            itemStyle.color = item.color
          }
          if (needXTypes.includes(item.type)) {
            needXAxis = true
            seriesItem.yAxisIndex = Math.max(yAxis.length - 1, 0)
            seriesItem.stack = item.stack
            seriesItem.smooth = item.smooth
            seriesItem.itemStyle = itemStyle
          }
          if (item.type === 'pie') {
            seriesItem.encode = {
              itemName: checkValue(item.itemNameDimension),
              value: checkValue(item.itemValueDimension),
              tooltip: checkValue(item.tooltipValueDimension),
            }
          } else if (item.type === 'funnel') {
            seriesItem.encode = {
              itemName: checkValue(item.itemNameDimension),
              value: checkValue(item.itemValueDimension),
              tooltip: checkValue(item.tooltipValueDimension),
            }
            if (item.smooth) {
              let sum = 0
              this.data.map((e) => {
                sum += e[seriesItem.encode.value]
              })
              let avg = sum / this.data.length
              const data = this.data.map((e, i) => {
                return {
                  name: e[seriesItem.encode.itemName],
                  value: (i + 1) * avg,
                  tooltip: {
                    valueFormatter: (v) => e[seriesItem.encode.tooltip] ?? v,
                  },
                }
              })
              seriesItem.data = data
              delete seriesItem.encode
            }
          }
          series.push(seriesItem)
        })
      }

      if (needXAxis) {
        result.xAxis = [
          {
            type: 'category',
            boundaryGap: false,
          },
        ]
        if (yAxis?.length === 0) {
          yAxis.push({
            type: 'value',
          })
        }
        result.yAxis = yAxis
      }
      result.series = series
      // console.log(result)
      return result
    },
  },
}
</script>
