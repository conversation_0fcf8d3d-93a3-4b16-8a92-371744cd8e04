<template>
  <div flex>
    <xn-calendar v-bind="$attrs" :mark-list="markList" v-on="$listeners">
      <template #dateCellRender="v">
        <div
          class="mark-item-dot m-auto size-2 rounded-full -translate-y-3"
          :style="{ backgroundColor: getMarkItem(v).color }"
        ></div>
      </template>
    </xn-calendar>
    <div v-if="$attrs.supportMark" my-a ml-4 flex="~ col justify-center">
      <div
        v-for="item in $attrs.markList"
        :key="item.date"
        class="mark-item"
        flex="~ items-center"
      >
        <div
          class="mark-item-dot size-2 rounded-full"
          :style="{ backgroundColor: item.color }"
        ></div>
        <div class="mark-item-text ml-2">{{ item.text }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LACalendar',
  computed: {
    markList() {
      const result = []
      if (this.$attrs.supportMark) {
        this.$attrs?.markList?.forEach((item) => {
          result.push(...(item.date || []))
        })
      }
      return result
    },
  },
  methods: {
    getMarkItem(date) {
      const find = this.$attrs?.markList?.find((item) =>
        item.date?.includes(date.format('YYYY-MM-DD'))
      )
      return {
        color: find?.color,
        text: find?.text,
        date,
      }
    },
  },
}
</script>
