<script lang="jsx">
import { DesignMode, omitHidden, Design, warn } from 'sinitek-lowcode-shared'
import { isFunction } from 'element-ui/src/utils/types'
import LATableSort from './sort.vue'
export default {
  name: 'LATable',
  componentName: 'LATable',
  components: {
    LATableSort,
  },
  provide() {
    return {
      LATable: this,
    }
  },
  inject: {
    mode: {
      default: DesignMode.PUBLISH,
    },
    // sinitek-lowcode-render/page.jsx
    pageScope: {
      default: {},
    },
    $doc: {
      default: {},
    },
  },
  props: {
    useActionCol: Boolean,
    actions: Array,
    actionTitle: String,
    actionWidth: String,
    useSelectionCol: Boolean,
    selectionType: String,
    selectionFixed: Boolean,
    selectionCrossPage: Boolean,
    selectionSelectable: Function,
    selectionTooltip: String,
    selectionDisabled: Function,
    customToolbars: Array,
    useForm: Boolean,
    datasource: String,
    actionFixed: Boolean,
    enableSort: <PERSON>olean,
    url: String,
    useDynamicCols: null,
    useNumberCol: Boolean,
    numberCrossPage: Boolean,
    numberFixed: Boolean,
  },
  data() {
    return {
      count: 0,
      cacheSelection: {},
      pageIndex: 1,
      pageSize: this.$attrs.pageSize || 20,
      omitHidden,
      fields: [],
      dsData: {},
      isLoading: false,
      isValid: true,
      // 设计模式下，表格属性变化刷新表格
      devRefreshTable: true,
      orderProp: '',
      orderType: '',
      columnConfig: [],
      currentUrl: this.$attrs?.url,
      isSort: false,
      actionLoadingIndexList: [],
    }
  },
  computed: {
    computedActions() {
      if (!this.useActionCol) {
        return []
      }
      return this.actions.map((e) => {
        if (e.hidden === void 0) {
          e.hidden = () => {
            return false
          }
        }
        // 不是函数时，处理成函数返回
        return Object.fromEntries(
          Object.entries(e).map(([key, value]) => {
            return [key, isFunction(value) ? value : () => value]
          })
        )
      })
    },
    computedSelectionType() {
      return this.selectionType || 'selection'
    },
    computedAttrs() {
      const attrs = this.$attrs
      const result = {
        ...attrs,
        // 这里字段转换，因为之前用错了字段。
        // 之前用dynamicCols判断是否开启工具栏，一直沿用
        // 现在加入useDynamicCols判断是否开启自定义表格列的功能
        dynamicCols:
          this.useDynamicCols != void 0
            ? this.useDynamicCols
            : this.$attrs.dynamicCols,
        toolbars: [
          ...(attrs?.toolbars ?? []),
          this.enableSort && {
            type: 'slot',
            name: 'toolbarSort',
          },
          this.computedCustomToolbars?.length && {
            type: 'slot',
            name: 'toolbarBtns',
          },
          this.$slots.toolbarSlot && {
            type: 'slot',
            name: 'toolbarSlot',
          },
        ].filter(Boolean),
      }
      // 使用了排序并且是数据源时
      if (this.datasource) {
        // 处理
        result.handleSortChange = this.handleSortChange
      }
      return result
    },
    computedCustomToolbars() {
      return omitHidden(this.customToolbars)
    },
    staticData() {
      // TODO如果使用defaultOrder和spanMethod会卡死
      let data
      if (this.datasource) {
        data = this.dsData?.data?.datalist
      } else {
        data = this.$attrs?.data
      }
      // 如果又slot，在设计器时默认添加一条数据，让slot显示处理
      if (this.mode !== DesignMode.PUBLISH && !data?.length) {
        const node = this.$doc?.node?.getNode?.(this.$attrs[Design.NODE_UID])
        if (!node) return
        const hasSlot = node.children.some((e) =>
          e?.children?.some((c) => c.componentName === 'Slot')
        )
        if (hasSlot) {
          if (!data) {
            data = []
          }
          data.push(
            Object.fromEntries(
              node.children
                .filter(
                  (e) =>
                    ['xn-col', 'XnCol', 'XnColAdvanced'].includes(
                      e.componentName
                    ) && e.props.prop
                )
                .map((e) => {
                  return [e.props.prop, '--']
                })
            )
          )
        }
      }

      return data
    },
    dataTotal() {
      if (this.$attrs?.dataTotal) return this.$attrs?.dataTotal
      if (this.datasource) {
        return this.dsData?.data?.totalsize ?? 0
      }
      return null
    },
    /**
     * 选择的数据
     * 输出的状态，在meta中有定义
     */
    selections() {
      return Object.values(this.cacheSelection)
    },
  },
  watch: {
    datasource() {
      this.getModelData()
    },
    url() {
      this.currentUrl = this.url
    },
  },
  created() {
    if (this.useForm) {
      let isFirst = true
      this.$on('la.input.addField', (field) => {
        // 第一个不加入, 因为第一个是在hidden-columns里，隐藏的元素无法处理。
        if (isFirst) {
          isFirst = false
          return
        }

        if (field) {
          this.fields.push(field)
        }
      })
      /* istanbul ignore next */
      this.$on('la.input.removeField', (field) => {
        this.fields.splice(this.fields.indexOf(field), 1)
      })
    }
  },
  updated() {
    if (
      process.env.NODE_ENV === 'development' &&
      this.computedAttrs.defaultOrder &&
      this.computedAttrs.spanMethod
    ) {
      console.error(
        '表格的defaultOrder和spanMethod一起使用有卡死风险，spanMethod也无法正常运行'
      )
    }
  },
  mounted() {
    this.devMode()
    this.getModelData()
    if (this.enableSort) {
      this.columnConfig = this.$slots.default
        .map((e) => {
          if (!e?.data?.props?.sortable) return null
          return {
            prop: e.data.props.prop,
            label: e.data.props.label,
          }
        })
        .filter(Boolean)
    }
    const refreshTable = () => {
      // 设计模式下，表格属性变化刷新表格
      this.devRefreshTable = false
      this.$nextTick(() => {
        this.devRefreshTable = true
      })
    }
    this.$watch('useSelectionCol', refreshTable)
    this.$watch('useActionCol', refreshTable)
    this.$watch('useNumberCol', refreshTable)
  },
  methods: {
    getPageSize() {
      const table = this.$refs.table
      const size = table.getPageSizeCache()
      return size > 0 ? size : this.pageSize
    },
    _getKey(row) {
      if (row.id) return row.id
      return `${this.pageIndex}_${this.getPageSize()}_${row.selectIndex}`
    },
    showLoading() {
      this.loading = true
    },
    closeLoading() {
      this.loading = false
    },
    onSortChange(list) {
      this.advancedSort = list
      this.orderProp = ''
      this.orderType = ''
      this.clearTableSort()
      this.baseRefreshData()
      this.isSort = true
    },
    async getModelData() {
      if (this.datasource) {
        this.isLoading = true
        const param = {
          pageIndex: this.pageIndex,
          pageSize: this.getPageSize(),
        }
        if (this.orderType) {
          param.orders = [
            {
              orderName: this.orderProp,
              orderType: this.orderType === 'ascending' ? 'asc' : 'desc',
            },
          ]
        } else if (this.enableSort && this.advancedSort?.length) {
          param.orders = this.advancedSort.map((e) => {
            return {
              orderName: e.prop,
              orderType: e.order,
            }
          })
        }
        await this.pageScope()
          .fetcher[this.datasource].lazyList(param)
          .then((res) => {
            this.dsData = res
            // 处理数据源的选中
            this.reSelection()
          })
          .finally(() => {
            this.isLoading = false
          })
      }
    },
    // 用来处理url数据源，点击单个列的sort后清除高级sort
    onAfterQuery() {
      if (this.isSort) {
        this.isSort = false
      } else {
        this.$refs?.sort?.reset?.()
      }
      // 处理url的选中
      this.reSelection()
    },
    handleSortChange({ prop, order }) {
      if (this.$refs.sort) {
        // 单个列sort时
        // 清除高级sort状态
        this.$refs.sort.reset()
        this.advancedSort = []
      }
      // 只处理数据源的情况
      if (prop) {
        this.orderProp = prop
        this.orderType = order
        this.baseRefreshData()
      } else {
        this.orderProp = ''
        this.orderType = ''
      }
    },
    baseRefreshData() {
      this.pageIndex = 1
      this.actionLoadingIndexList = []
      if (this.url) {
        this.$refs.table.pageIndex = 1
        if (this.enableSort && this.advancedSort?.length) {
          let orderNames = ''
          let orderTypes = ''
          this.advancedSort.forEach((e) => {
            orderNames += `${e.prop},`
            orderTypes += `${e.order},`
          })
          const flag = this.currentUrl.includes('?') ? '&' : '?'
          this.currentUrl = `${this.currentUrl}${flag}orderNames=${orderNames.slice(0, -1)}&orderTypes=${orderTypes.slice(0, -1)}`
        }
        setTimeout(() => {
          this.$refs.table.query()
          this.currentUrl = this.url
        })
        return
      }
      if (this.datasource) {
        return this.getModelData()
      }
      throw new Error('未设置url或数据源，刷新数据调用失败')
    },
    refreshData(clearSelection = true) {
      if (this.$refs.sort) {
        // 主动刷新数据时，清楚筛选状态
        this.$refs.sort.reset()
        this.advancedSort = []
      }
      this.baseRefreshData()
      if (clearSelection) {
        // 清除选中，多延迟一会防止query没有获取到值
        setTimeout(() => {
          this.clearSelection()
        }, 300)
      }
    },
    onSelect(row, selected) {
      let arr = Array.isArray(row) ? row : [row]
      if (this.useSelectionCol) {
        if (!selected && row.length === 0) {
          // 清除选中状态
          arr = this.$refs.table.sourceData
        }
        arr.forEach((e) => {
          if (!selected) {
            this.$delete(this.cacheSelection, this._getKey(e))
            return
          }
          this.$set(this.cacheSelection, this._getKey(e), e)
        })
      }
    },
    reSelection() {
      if (!this.selectionCrossPage) return
      this.$nextTick(() => {
        // fix: 这里获取的是上一页的数据，所以需要nextTick进行处理
        // 进行选中状态的处理
        this.$refs.table.sourceData.forEach((row) => {
          const key = this._getKey(row)
          if (this.cacheSelection[key]) {
            this.toggleRowSelection(row, true)
          }
        })
      })
    },
    onSizeChange(index, size) {
      this.pageIndex = index
      this.pageSize = size
      this.getModelData()
      if (!this.useSelectionCol) return
      if (!this.selectionCrossPage) {
        this.cacheSelection = {}
        return
      }
      // 处理静态数据的选中
      this.reSelection()
    },
    onDropdownCommand(children, i) {
      const list = omitHidden(children)
      if (list[i].handler) {
        list[i].handler()
      }
    },
    devMode() {
      // 设计模式时删除hidden-columns
      if (this.mode === DesignMode.DESIGN) {
        setTimeout(() => {
          const table = this.$refs?.table?.$el
          if (table) {
            const hc = table?.querySelector('.hidden-columns')
            if (hc) {
              hc.parentNode.removeChild(hc)
            }
          }
        })
        this.$watch('$attrs', () => {
          // 设计模式下，表格属性变化刷新表格
          this.devRefreshTable = false
          this.$nextTick(() => {
            this.devRefreshTable = true
          })
        })
      }
    },
    // -------------- 校验的功能
    validate(callback) {
      let valid = true
      let count = 0
      let invalidFields = {}
      this.fields.forEach((field) => {
        field.validate('', (message, field) => {
          if (message) {
            valid = false
          }
          invalidFields = Object.assign({}, invalidFields, field)
          if (
            typeof callback === 'function' &&
            ++count === this.fields.length
          ) {
            this.isValid = valid
            callback(valid, invalidFields)
          }
        })
      })
    },
    clearValidate() {
      this.isValid = true
      this.fields.forEach((field) => {
        field.clearValidate()
      })
    },
    resetField() {
      this.isValid = true
      this.fields.forEach((field) => {
        field.resetField()
      })
    },

    // ------------ 下面都是eltable的方法 ------------
    clearSelection() {
      this.cacheSelection = {}
      this.$refs.table.$refs.table.clearSelection()
    },
    getSelection() {
      return Object.values(this.cacheSelection)
    },
    toggleRowSelection(row, selected) {
      this.$refs.table.selectRow(row, selected)
    },
    toggleAllSelection(...args) {
      this.$refs.table.$refs.table.toggleAllSelection(...args)
    },
    toggleRowExpansion(...args) {
      this.$refs.table.$refs.table.toggleRowExpansion(...args)
    },
    setCurrentRow(...args) {
      this.$refs.table.$refs.table.setCurrentRow(...args)
    },
    clearSort(...args) {
      this.$refs.table.$refs.table.clearSort(...args)
    },
    clearFilter(...args) {
      this.$refs.table.$refs.table.clearFilter(...args)
    },
    doLayout(...args) {
      this.$refs.table.$refs.table.doLayout(...args)
    },
    sort(...args) {
      this.$refs.table.$refs.table.sort(...args)
    },
    // ------------ 下面都是xntable的方法 ------------
    initToolbars(...args) {
      this.$refs.table.initToolbars(...args)
    },
    initTableHeight(...args) {
      this.$refs.table.initTableHeight(...args)
    },
    query(...args) {
      warn('不推荐使用table的query，推荐使用refreshData')
      if (this.useSelectionCol) {
        this.clearSelection()
      }
      this.actionLoadingIndexList = []
      this.$refs.table.query(...args)
    },
    clearTableSort(...args) {
      this.$refs.table.clearTableSort(...args)
    },
    exportExcel(...args) {
      this.$refs.table.exportExcel(...args)
    },
    handleCloseFilter(...args) {
      this.$refs.table.handleCloseFilter(...args)
    },
    getTotal(...args) {
      return this.$refs.table.getTotal(...args)
    },
    tableReRender() {
      return this.$refs.table.tableReRender()
    },
    setLoading(loading = true) {
      this.isLoading = loading
    },
  },
  render() {
    const on = {
      'select-callback': [this.onSelect],
      'size-change': [this.onSizeChange],
      'current-page-change': [this.onSizeChange],
      afterQuery: [this.onAfterQuery],
    }
    Object.entries(this.$listeners).forEach(([key, value]) => {
      if (on[key]) {
        on[key].push(value)
      } else {
        on[key] = [value]
      }
    })
    return (
      this.devRefreshTable && (
        <xn-table
          ref="table"
          on={on}
          {...{ attrs: this.computedAttrs }}
          data={this.staticData}
          dataTotal={this.dataTotal}
          pageSize={this.pageSize}
          v-loading={this.isLoading}
          url={this.currentUrl}
          id={`lcTable_${this._uid}`}
        >
          {this.$attrs.dynamicCols && this.computedCustomToolbars?.length && (
            <div slot="toolbarBtns">
              {this.computedCustomToolbars.map((btn, i) =>
                omitHidden(btn.children).length ? (
                  <el-dropdown
                    key={i}
                    disabled={btn.disabled}
                    onCommand={(command) =>
                      this.onDropdownCommand(btn.children, command)
                    }
                  >
                    <el-button plain type="primary">
                      {btn.label}
                      <i class="el-icon-arrow-down el-icon--right" />
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      {omitHidden(btn.children).map((d, di) => (
                        <el-dropdown-item
                          key={`di_${di}`}
                          command={di}
                          disabled={d.disabled}
                          icon={d.icon}
                        >
                          {d.label}
                        </el-dropdown-item>
                      ))}
                    </el-dropdown-menu>
                  </el-dropdown>
                ) : (
                  <xn-button
                    key={i}
                    plain
                    type="primary"
                    icon={btn.icon}
                    disabled={btn.disabled}
                    showLoading={btn.showLoading}
                    onClick={(resolve, reject) =>
                      btn.handler?.(resolve, reject)
                    }
                  >
                    {btn.label}
                  </xn-button>
                )
              )}
            </div>
          )}

          {this.useSelectionCol && (
            <xn-col
              width="50"
              type={this.computedSelectionType}
              fixed={this.selectionFixed}
              selectable={this.selectionSelectable}
              tooltip={this.selectionTooltip}
            />
          )}
          {this.useNumberCol && (
            <xn-col align="center" width="50" fixed={this.numberFixed}>
              {(scope) => {
                return this.numberCrossPage
                  ? scope.$index + (this.pageIndex - 1) * this.getPageSize() + 1
                  : scope.$index + 1
              }}
            </xn-col>
          )}
          {this.$slots.default}
          {this.useActionCol && (
            <xn-col
              width={this.actionWidth}
              label={this.actionTitle}
              fixed={this.actionFixed ? 'right' : false}
              scopedSlots={{
                default: (scope) => (
                  <xn-col-action-group keys={scope.row}>
                    {this.computedActions
                      .filter((item) => !item.hidden(scope))
                      .map((item) => (
                        <xn-col-action
                          key={item.text(scope)}
                          disabled={
                            item?.disabled ? item.disabled(scope) : false
                          }
                          loading={
                            item.loading
                              ? this.actionLoadingIndexList.includes(
                                  scope.$index
                                ) && item.loading(scope)
                              : false
                          }
                          onClick={() => {
                            // 支持多个loading
                            this.actionLoadingIndexList.push(scope.$index)
                            const done = () => {
                              this.actionLoadingIndexList =
                                this.actionLoadingIndexList.filter(
                                  (i) => i !== scope.$index
                                )
                            }
                            const promise = item.handler?.({ ...scope, done })
                            if (promise?.then) {
                              promise.finally(done)
                            }
                          }}
                        >
                          {item.text(scope)}
                        </xn-col-action>
                      ))}
                  </xn-col-action-group>
                ),
              }}
            />
          )}

          {this.enableSort && (
            <LATableSort
              slot="toolbarSort"
              ref="sort"
              columnConfig={this.columnConfig}
              onChange={this.onSortChange}
            />
          )}

          {this.$slots.toolbarSlot && (
            <div slot="toolbarSlot" order-99>
              {this.$slots.toolbarSlot}
            </div>
          )}

          {this.$slots.append && <div slot="append">{this.$slots.append}</div>}
          {this.$slots.empty && <div slot="empty">{this.$slots.empty}</div>}
          {this.$slots.search && <div slot="search">{this.$slots.search}</div>}
        </xn-table>
      )
    )
  },
}
</script>
