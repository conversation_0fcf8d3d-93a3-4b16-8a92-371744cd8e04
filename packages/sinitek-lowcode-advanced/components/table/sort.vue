<template>
  <div>
    <xn-button
      type="primary"
      icon="el-icon-sort"
      plain
      :show-loading="false"
      @click="handleClick"
      >排序</xn-button
    >
    <xn-dialog
      title="排序"
      :show.sync="visible"
      :buttons="buttons"
      width-size="mini"
      @save="handleSave"
      @cancel="handleCancel"
    >
      <div>
        <LATableSortItem
          v-for="(item, i) in list"
          :key="item.prop"
          :label="item.label"
          :prop="item.prop"
          :index="i"
          @move="onMove"
          @change="(data) => handleChange(i, data)"
        />
      </div>
    </xn-dialog>
  </div>
</template>

<script>
import LATableSortItem from './sortItem.vue'
export default {
  name: 'LATableSort',
  components: {
    LATableSortItem,
  },
  props: {
    columnConfig: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      list: [...this.columnConfig],
      originList: [...this.columnConfig],
      buttons: [
        {
          label: '保存',
          type: 'primary',
          action: 'save',
          event: 'save',
          showLoading: false,
        },
        { label: '取消', type: 'info', action: 'cancel', event: 'cancel' },
      ],
    }
  },
  watch: {
    columnConfig: {
      handler(newVal) {
        this.list = [...newVal]
        this.originList = [...newVal]
      },
      deep: true,
    },
  },
  methods: {
    handleClick() {
      this.visible = true
    },
    handleChange(i, data) {
      const list = [...this.list]
      list[i] = data
      this.list = list
    },
    handleSave() {
      this.visible = false
      this.$emit(
        'change',
        this.list.filter((item) => item?.checked)
      )
    },
    handleCancel() {
      this.visible = false
    },
    onMove({ source, target }) {
      if (source.index !== target.index) {
        const temp = this.list[source.index]
        this.list.splice(source.index, 1)
        this.list.splice(target.index, 0, temp)
      }
    },
    reset() {
      this.list = []
      this.$nextTick(() => {
        this.list = [...this.originList]
      })
    },
  },
}
</script>
