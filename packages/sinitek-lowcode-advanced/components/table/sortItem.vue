<template>
  <div ref="el" mb-3 h-10 border border-rounded p-2 flex="~ items-center">
    <i ref="move" class="el-icon-rank" mr-2 flex-shrink-0 cursor-move></i>
    <el-checkbox v-model="checked" w-100 overflow-hidden @change="handleChange">
      {{ label }}
    </el-checkbox>
    <el-radio v-model="order" label="asc" @change="handleOrderChange"
      >升序</el-radio
    >
    <el-radio v-model="order" label="desc" @change="handleOrderChange"
      >降序</el-radio
    >
  </div>
</template>

<script>
import {
  draggable,
  dropTargetForElements,
} from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine'
export default {
  name: 'LATableSortItem',
  props: {
    label: String,
    prop: String,
    index: Number,
  },
  data() {
    return {
      order: '',
      checked: false,
    }
  },
  mounted() {
    const el = this.$refs.el
    const move = this.$refs.move
    this.cleanup = combine(
      draggable({
        dragHandle: move,
        element: el,
        getInitialData: () => ({ index: this.index }),
      }),
      dropTargetForElements({
        element: el,
        getData: () => ({ index: this.index }),
        getIsSticky: () => true,
        onDrop: ({ location, source }) => {
          const target = location.current.dropTargets[0]
          if (!target) {
            return
          }
          const sourceData = source.data
          const targetData = target.data
          if (sourceData.index === targetData.index) {
            return
          }
          this.$emit('move', { source: sourceData, target: targetData })
        },
      })
    )
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    handleChange() {
      if (!this.order) {
        this.order = 'desc'
      }
      this.$emit('change', {
        label: this.label,
        order: this.order,
        checked: this.checked,
        prop: this.prop,
      })
    },
    handleOrderChange() {
      this.checked = true
      this.handleChange()
    },
  },
}
</script>
