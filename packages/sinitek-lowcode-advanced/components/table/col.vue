<template>
  <xn-col v-bind="$attrs">
    <template v-if="$scopedSlots.default" #default="scope">
      <slot v-bind="{ ...scope, enumList }" />
    </template>
    <template v-else-if="$attrs.enumDs" #default="scope">
      <slot v-bind="{ ...scope, enumList }">
        <div v-if="enumList.length">
          {{ getLabel(enumList, scope.row[$attrs.prop]) }}
        </div>
      </slot>
    </template>
  </xn-col>
</template>

<script>
import { getPageCtx } from 'sinitek-lowcode-shared'
export default {
  name: 'XnColAdvanced',
  data() {
    return {
      enumList: [],
    }
  },
  created() {
    const enumId = this.$attrs.enumDs
    if (enumId) {
      const ctx = getPageCtx()
      const ds = ctx.datasource[enumId].slice()
      if (!ds.length) {
        ctx.fetcher[enumId]?.send()?.then((res) => {
          this.enumList = res
        })
      } else {
        this.enumList = ds
      }
    }
  },
  methods: {
    getLabel(list, value) {
      return list.find((item) => item.value === value)?.label ?? value
    },
  },
}
</script>
