<template>
  <el-radio-group v-model="currentValue">
    <component
      v-bind="item"
      :is="type === 'button' ? 'el-radio-button' : 'el-radio'"
      v-for="(item, index) in list"
      :key="index"
      :label="item.value"
      >{{ item.label }}</component
    >
  </el-radio-group>
</template>

<script>
export default {
  name: 'LARadioGroup',
  props: {
    // radio   button
    type: String,
    // label
    // disabled
    // border
    // hidden
    // value
    options: {
      type: Array,
      default: () => [],
    },
    value: [String, Number],
  },
  data() {
    return {
      currentValue: this.value,
    }
  },
  computed: {
    list() {
      if (!Array.isArray(this.options)) return []
      return (this?.options ?? []).filter((e) => !e.hidden)
    },
  },
  watch: {
    value(val) {
      this.currentValue = val
    },
    currentValue(val) {
      this.$emit('input', val)
    },
  },
}
</script>
