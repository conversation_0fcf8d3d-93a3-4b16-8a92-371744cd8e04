<template>
  <xn-form
    v-bind="{ ...$props, ...$attrs }"
    ref="form"
    class="la-form"
    :class="[column > 1 ? 'w-full grid gap-2 grid-cols-' + column : '']"
    v-on="$listeners"
  >
    <!-- 依赖sinitek-ui -->
    <slot />
  </xn-form>
</template>

<script>
import { findNodesByTag } from 'sinitek-lowcode-shared'
export default {
  name: 'LAForm',
  provide() {
    return {
      LAForm: this,
      isParentLAForm: true,
    }
  },
  props: {
    status: String,
    column: String,
    labelPosition: String,
    actions: Array,
  },
  data() {
    return {
      isValid: true,
    }
  },
  computed: {
    isReadonly() {
      return this.status === 'readonly'
    },
  },
  methods: {
    async validate(fn) {
      return new Promise((resolve, reject) => {
        this.$nextTick(() => {
          this.$refs.form.validate((v) => {
            try {
              let valid = v
              this.isValid = valid
              if (valid) {
                const uploads = findNodesByTag(this.$refs.form.$children, [
                  'XnUpload',
                  'xn-upload',
                ])
                if (uploads.length) {
                  for (let i = 0; valid && i < uploads.length; i++) {
                    valid = uploads[i].isFinished
                    if (!valid) {
                      this.$message.warning('文件上传未完成，请稍后提交')
                    }
                  }
                }
              }
              resolve(valid)
              fn?.(valid)
            } catch (e) {
              reject(e)
            }
          })
        })
      })
    },
    resetFields() {
      return new Promise((resolve) => {
        this.$nextTick(() => {
          this.isValid = true
          const uploads = findNodesByTag(this.$refs.form.$children, [
            'XnUpload',
            'xn-upload',
          ])
          // 清空上传组件
          if (uploads.length) {
            uploads.forEach((upload) => {
              upload.clear()
            })
          }
          this.$emit('resetModel')
          this.$refs.form.resetFields()
          resolve()
        })
      })
    },
    clearValidate(fn) {
      return new Promise((resolve) => {
        this.$nextTick(() => {
          this.isValid = true
          this.$refs.form?.clearValidate?.(fn)
          resolve()
        })
      })
    },
  },
}
</script>
<style lang="scss">
//定义scss变量1到4，循环1-4，生成grid-cols-1到grid-cols-4
@for $i from 1 through 4 {
  .grid-cols-#{$i} {
    grid-template-columns: repeat($i, minmax(0, 1fr));
  }
}
.la-form.grid,
.la-form.slc-grid {
  // 覆盖sinitek-ui的宽度
  // class包含xn-input--width
  [class*='xn-input--width'],
  .el-input,
  .xn-input {
    width: 100% !important;
  }
}
</style>
