<template>
  <!-- v-show在渲染时初始化时没有生效，这里改成class隐藏不能兼容动画 -->
  <div
    v-if="!lazy || loaded || active"
    :id="`pane-${paneName}`"
    class="el-tab-pane"
    role="tabpanel"
    :aria-hidden="!active"
    :class="{ hidden: !active, 'h-full overflow-auto': $parent.fullHeight }"
    :aria-labelledby="`tab-${paneName}`"
    :style="{
      display: active ? '' : 'none',
    }"
  >
    <slot></slot>
  </div>
</template>
<script>
export default {
  name: 'LATabPane',

  componentName: 'LATabPane',

  props: {
    label: String,
    labelContent: Function,
    name: String,
    closable: <PERSON><PERSON>an,
    disabled: Boolean,
    lazy: Boolean,
    hidden: Boolean,
  },

  data() {
    return {
      index: null,
      loaded: false,
    }
  },

  computed: {
    isClosable() {
      return this.closable || this.$parent.closable
    },
    active() {
      const active = this.$parent.currentName === (this.name || this.index)
      if (active) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.loaded = true
      }
      return active
    },
    paneName() {
      return this.name || this.index
    },
  },

  updated() {
    this.$parent.$emit('tab-nav-update')
  },
}
</script>
