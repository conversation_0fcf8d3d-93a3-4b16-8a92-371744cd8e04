<template>
  <div
    class="el-tabs__active-bar"
    :class="`is-${rootTabs.tabPosition}`"
    :style="barStyle"
  ></div>
</template>
<script>
import { arrayFind } from 'element-ui/src/utils/util'
export default {
  name: 'TabBar',

  inject: ['rootTabs'],

  props: {
    tabs: Array,
  },

  data() {
    return {
      barStyle: {
        width: null,
        height: null,
        transform: 'translateX(0px)',
        msTransform: 'translateX(0px)',
        webkitTransform: 'translateX(0px)',
      },
    }
  },
  watch: {
    'rootTabs.currentName': {
      handler() {
        this.updateBarStyle()
      },
    },
    'rootTabs.tabs': {
      handler() {
        this.updateBarStyle()
      },
      deep: true,
    },
  },
  mounted() {
    this.updateBarStyle()
  },
  activated() {
    this.updateBarStyle()
  },
  methods: {
    updateBarStyle() {
      setTimeout(() => {
        let offset = 0
        let tabSize = 0
        const sizeName =
          ['top', 'bottom'].indexOf(this.rootTabs.tabPosition) !== -1
            ? 'width'
            : 'height'
        const sizeDir = sizeName === 'width' ? 'x' : 'y'
        const firstUpperCase = (str) => {
          return str
            .toLowerCase()
            .replace(/( |^)[a-z]/g, (L) => L.toUpperCase())
        }
        this.rootTabs.currentName
        this.tabs.every((tab) => {
          // 监听root的name
          const paneName = tab.name || tab.index
          let $el = arrayFind(
            this.$parent.$refs.tabs || [],
            (t) => t.id.replace('tab-', '') === paneName
          )
          if (!$el) {
            return false
          }
          if (!$el.classList.contains('is-active')) {
            offset += $el[`client${firstUpperCase(sizeName)}`]
            return true
          } else {
            tabSize = $el[`client${firstUpperCase(sizeName)}`]
            const tabStyles = window.getComputedStyle($el)
            if (sizeName === 'width' && this.tabs.length > 1) {
              tabSize -=
                parseFloat(tabStyles.paddingLeft) +
                parseFloat(tabStyles.paddingRight)
            }
            if (sizeName === 'width') {
              offset += parseFloat(tabStyles.paddingLeft)
            }
            return false
          }
        })

        const transform = `translate${firstUpperCase(sizeDir)}(${offset}px)`
        this.barStyle[sizeName] = tabSize + 'px'
        this.barStyle.transform = transform
        this.barStyle.msTransform = transform
        this.barStyle.webkitTransform = transform
      })
    },
  },
}
</script>
