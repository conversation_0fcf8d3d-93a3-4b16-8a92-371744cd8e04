import { DesignMode, isDesignMode } from 'sinitek-lowcode-shared'
export default {
  name: 'LADrawer',
  inheritAttrs: false,
  inject: {
    mode: { default: DesignMode.PUBLISH },
    // 低代码组件的
    lowcodeScope: { default: {} },
    $doc: { default: null },
  },
  props: {
    value: <PERSON>olean,
  },
  data() {
    return {
      isVisible: this.value,
    }
  },
  computed: {
    extendsProp() {
      // 组件设计时添加的属性
      if (this.lowcodeScope?.mode && isDesignMode(this.lowcodeScope?.mode)) {
        return {
          appendToBody: false,
          modal: false,
          visible: this.isVisible,
        }
      }
      if (isDesignMode(this.mode)) {
        return {
          appendToBody: false,
          modal: false,
          visible: this.isVisible,
        }
      }
      return {
        visible: this.isVisible,
      }
    },
  },
  watch: {
    value(v) {
      this.isVisible = v
    },
  },
  methods: {
    updateVisible(v) {
      this.isVisible = v
      this.$emit('input', v)
    },
    open() {
      this.isVisible = true
      const { promise, resolve } = Promise.withResolvers()
      this.openResolve = resolve
      return promise
    },
    opened() {
      if (this.openResolve) {
        this.$nextTick(() => {
          this.openResolve()
        })
      }
    },
    close() {
      this.isVisible = false
      this.openResolve = null
      const { promise, resolve } = Promise.withResolvers()
      this.closeResolve = resolve
      return promise
    },
    closed() {
      this.closeResolve = null
    },
  },
  mounted() {
    if (isDesignMode(this.mode)) {
      this.isVisible = true
    }
  },
  render() {
    const events = {
      'update:visible': this.updateVisible,
      open: [this.open, this.$listeners.open].filter(Boolean),
      opened: [this.opened, this.$listeners.opened].filter(Boolean),
      close: [this.close, this.$listeners.close].filter(Boolean),
      closed: [this.closed, this.$listeners.closed].filter(Boolean),
    }

    // 将自定义事件与原生事件合并
    const on = {
      ...this.$listeners,
      ...events,
    }

    const props = {
      ...this.$attrs,
      ...this.extendsProp,
    }
    const style = this.$attrs.style
    delete this.$attrs.style
    return (
      <el-drawer
        {...{
          props,
          on,
          attrs: {
            ...this.$attrs,
          },
        }}
      >
        {this.$slots.title && <div slot="title">{this.$slots.title}</div>}
        <div style={style} class="wh-full">
          {this.$slots.default}
        </div>
      </el-drawer>
    )
  },
}
