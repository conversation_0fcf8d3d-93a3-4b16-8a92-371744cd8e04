<template>
  <div class="LAStage" flex fs-14>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'LAStage',
}
</script>

<style lang="scss">
.LAStage {
  .LAStageItem {
    &-line {
      height: 1px;
      width: 100%;
      background-color: rgb(156, 163, 175);
      position: absolute;
      z-index: 1;
      top: 50%;
      transform: translateY(-50%);
    }
    &-arrow {
      height: 0;
      width: 0;
      background-color: white;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      margin-right: -8px;
      border-width: 8px;
      border-style: solid;
      border-top-color: transparent;
      border-right-color: transparent;
      border-bottom-color: transparent;
      border-left-color: rgb(156, 163, 175);
      z-index: 1;
    }
    &:last-child {
      .LAStageItem-last-hide {
        display: none;
      }
    }
  }
}
</style>
