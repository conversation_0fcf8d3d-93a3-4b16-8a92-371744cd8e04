<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="LAStageItem" my-4 flex-auto cursor-pointer>
    <div px-1>
      <slot :props="$props">
        <div relative flex>
          <el-tooltip :disabled="!tooltip">
            <div slot="content" v-html="tooltip"></div>
            <div relative z-2 bg-white pr-2 @click="onClick">
              <i
                :class="icon"
                mr-1
                rounded-full
                text-white
                :style="{ backgroundColor: color }"
              />
              <span>{{ label }}</span>
            </div>
          </el-tooltip>
          <!-- 直线 -->
          <div
            v-if="!isChildren"
            w-full
            class="LAStageItem-last-hide LAStageItem-line"
          ></div>
          <!-- 三角 -->
          <div
            v-if="!isChildren"
            class="LAStageItem-last-hide LAStageItem-arrow"
          ></div>
        </div>
      </slot>
    </div>
    <div v-for="(item, i) of children" :key="i">
      <LAStageItem
        v-bind="item"
        is-children
        :class="{ 'mb-0': children.length - 1 === i }"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'LAStageItem',
  props: {
    icon: String,
    label: String,
    color: String,
    handler: Function,
    children: Array,
    isChildren: Boolean,
    tooltip: String,
  },
  methods: {
    onClick() {
      this.handler && this.handler(this.$props)
    },
  },
}
</script>
