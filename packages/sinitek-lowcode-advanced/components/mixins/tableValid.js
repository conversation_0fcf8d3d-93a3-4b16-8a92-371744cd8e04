import emitter from 'element-ui/src/mixins/emitter'
import AsyncValidator from 'async-validator'
export default {
  mixins: [emitter],
  data() {
    return {
      validateState: '',
      validateMessage: '',
    }
  },
  mounted() {
    this.dispatch('LATable', 'la.input.addField', [this])
  },
  beforeDestroy() {
    this.dispatch('LATable', 'la.input.removeField', [this])
  },
  methods: {
    validate(trigger, callback = () => {}) {
      // 不能处理trigger，只处理修改时的校验
      const descriptor = {
        test: [],
      }
      const rules = this.getFilteredRule(trigger)
      if (this.$attrs.required) {
        rules.push({ required: true, message: '这个字段是必填的' })
      }
      if (rules && rules.length > 0) {
        rules.forEach((rule) => {
          delete rule.trigger
        })
      }
      descriptor.test = rules
      const validator = new AsyncValidator(descriptor)
      validator.validate(
        { test: this.value },
        { firstFields: true },
        (errors, invalidFields) => {
          this.validateState = !errors ? 'success' : 'error'
          this.validateMessage = errors ? errors[0].message : ''
          callback(this.validateMessage, invalidFields)
        }
      )
    },
    getFilteredRule(trigger) {
      const rules = this.$attrs.rules || []

      return rules
        .filter((rule) => {
          if (!rule.trigger || trigger === '') return true
          if (Array.isArray(rule.trigger)) {
            return rule.trigger.indexOf(trigger) > -1
          } else {
            return rule.trigger === trigger
          }
        })
        .map((rule) => Object.assign({}, rule))
    },
    clearValidate() {
      this.validateState = ''
      this.validateMessage = ''
    },
    resetField() {
      this.validateState = ''
      this.validateMessage = ''
      if (this.value) {
        if (typeof this.value === 'string') {
          this.value = ''
        } else if (typeof this.value === 'number') {
          this.value = null
        } else if (Array.isArray(this.value)) {
          this.value = []
        } else if (typeof this.value === 'object') {
          this.value = {}
        }
      }
    },
  },
}
