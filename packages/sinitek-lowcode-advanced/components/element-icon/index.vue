<template>
  <span inline-block>
    <i :class="[icon]" :style="computeStyle"></i>
  </span>
</template>

<script>
import { styleToObject } from 'sinitek-lowcode-shared'
export default {
  name: 'LAElementIcon',
  props: {
    icon: String,
    size: Number,
    color: String,
  },
  computed: {
    computeStyle() {
      return {
        fontSize: (this.size || 16) + 'px',
        color: this.color,
        ...styleToObject(this.$attrs.style),
      }
    },
  },
}
</script>
