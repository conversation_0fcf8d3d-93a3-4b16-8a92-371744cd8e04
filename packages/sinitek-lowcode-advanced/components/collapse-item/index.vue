<template>
  <div
    class="el-collapse-item"
    :class="{ 'is-active': isActive, 'is-disabled': disabled }"
  >
    <div
      role="tab"
      :aria-expanded="isActive"
      :aria-controls="`el-collapse-content-${id}`"
      :aria-describedby="`el-collapse-content-${id}`"
    >
      <div
        :id="`el-collapse-head-${id}`"
        class="el-collapse-item__header"
        role="button"
        :tabindex="disabled ? undefined : 0"
        :class="{
          focusing: canCollapse && focusing,
          'is-active': canCollapse && isActive,
          'cursor-auto!': !canCollapse,
        }"
        @click="handleHeaderClick"
        @keyup.space.enter.stop="handleEnterClick"
        @focus="handleFocus"
        @blur="focusing = false"
      >
        <slot name="title">{{ title }}</slot>
        <i
          v-show="showIcon"
          class="el-collapse-item__arrow el-icon-arrow-right"
          :class="{ 'is-active': isActive }"
        >
        </i>
      </div>
    </div>
    <el-collapse-transition>
      <div
        v-show="isActive"
        :id="`el-collapse-content-${id}`"
        class="el-collapse-item__wrap"
        role="tabpanel"
        :aria-hidden="!isActive"
        :aria-labelledby="`el-collapse-head-${id}`"
      >
        <div class="el-collapse-item__content">
          <slot></slot>
        </div>
      </div>
    </el-collapse-transition>
  </div>
</template>
<script>
import ElCollapseTransition from 'element-ui/src/transitions/collapse-transition'
import Emitter from 'element-ui/src/mixins/emitter'
import { generateId } from 'element-ui/src/utils/util'
import { DesignMode } from 'sinitek-lowcode-shared'

export default {
  name: 'LACollapseItem',

  componentName: 'LACollapseItem',

  components: { ElCollapseTransition },

  mixins: [Emitter],

  inject: ['collapse', 'mode'],

  props: {
    title: String,
    name: {
      type: [String, Number],
      default() {
        return this._uid
      },
    },
    disabled: Boolean,
    canCollapse: {
      type: Boolean,
      default: true,
    },
    showIcon: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      contentWrapStyle: {
        height: 'auto',
        display: 'block',
      },
      contentHeight: 0,
      focusing: false,
      isClick: false,
      id: generateId(),
    }
  },

  computed: {
    isActive() {
      if (this.mode !== DesignMode.PUBLISH) return true
      if (!this.canCollapse) return true
      return this.collapse.activeNames.indexOf(this.name) > -1
    },
  },

  methods: {
    handleFocus() {
      setTimeout(() => {
        if (!this.isClick) {
          this.focusing = true
        } else {
          this.isClick = false
        }
      }, 50)
    },
    handleHeaderClick() {
      if (this.disabled || !this.canCollapse) return
      this.dispatch('ElCollapse', 'item-click', this)
      this.focusing = false
      this.isClick = true
    },
    handleEnterClick() {
      this.dispatch('ElCollapse', 'item-click', this)
    },
  },
}
</script>
