<template>
  <table class="la-table-view w-full">
    <slot></slot>
  </table>
</template>

<script>
import { DesignMode, isDesignMode } from 'sinitek-lowcode-shared'
export default {
  name: 'LATableView',
  inject: {
    $doc: { default: {} },
    mode: { default: DesignMode.PUBLISH },
  },
  props: {
    useBorder: <PERSON><PERSON><PERSON>,
    bColor: String,
  },
  watch: {
    useBorder: {
      handler(v) {
        // 通过注入style来实现
        this.injectStyle(v)
      },
      immediate: true,
    },
    bColor() {
      this.injectStyle(this.useBorder)
    },
  },
  methods: {
    injectStyle(v) {
      let id = 'la-table-view-style' + this._uid
      const el = document.getElementById(id)
      if (el) {
        document.head.removeChild(el)
      }
      if (isDesignMode(this.mode)) {
        const el2 = this.$doc.frameDocument.getElementById(id)
        if (el2) {
          this.$doc.frameDocument.head.removeChild(el2)
        }
      }
      if (v) {
        const style = document.createElement('style')
        style.id = id
        style.textContent = `
            .la-table-view td{
              border-width: 1px;
              border-style: solid;
              border-color: ${this.bColor || '#e8e9eb'};
            }
          `
        document.head.appendChild(style)
        // 给模拟器添加
        if (isDesignMode(this.mode)) {
          this.$doc.frameDocument.head.appendChild(style)
        }
      }
    },
  },
}
</script>

<style lang="scss">
.la-table-view {
  border-spacing: 0;
  border-collapse: collapse;
  tr {
    background: #fff;
  }
  tr:first-of-type {
    background: #f7f7f7;
  }
  td {
    padding: 10px;
  }
}
</style>
