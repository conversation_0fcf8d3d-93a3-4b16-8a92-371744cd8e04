<template>
  <div v-show="show" class="flex gap-1">
    <div
      class="i-lucide-arrow-up h-4 w-4"
      float="上面添加一行"
      @click.stop="onAdd('top')"
    ></div>
    <div
      class="i-lucide-arrow-down h-4 w-4"
      float="下面添加一行"
      @click.stop="onAdd('bottom')"
    ></div>
    <div
      class="i-lucide-arrow-left h-4 w-4"
      float="左边添加一列"
      @click.stop="onAdd('left')"
    ></div>
    <div
      class="i-lucide-arrow-right h-4 w-4"
      float="右边添加一列"
      @click.stop="onAdd('right')"
    ></div>
  </div>
</template>

<script>
export default {
  name: 'TableViewAction',
  inject: ['$doc'],
  props: {
    selectState: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    show() {
      return ['LATableViewTd', 'LATableViewTr'].includes(
        this.selectState.componentName
      )
    },
  },
  methods: {
    onAdd(type) {
      let { node, parent } = this.$doc.node.getNode(
        this.selectState.current.id,
        true
      )
      let index = -1
      let tr = node
      if (node.componentName === 'LATableViewTd') {
        index = parent.children.indexOf(node)
        let { node: n2, parent: p2 } = this.$doc.node.getNode(parent.id, true)
        tr = n2
        parent = p2
      }
      if (type === 'top' || type === 'bottom') {
        index = parent.children.indexOf(tr)
        let maxColumn = 0
        parent.children.forEach((child) => {
          if (child.children.length > maxColumn) {
            maxColumn = child.children.length
          }
        })
        // 这里的parent是table
        if (index === 0 && type === 'top') {
          parent.children.unshift({
            componentName: 'LATableViewTr',
            children: Array.from({ length: maxColumn }, () => {
              return {
                componentName: node.componentName,
                children: [],
                props: {},
              }
            }),
          })
        } else {
          parent.children.splice(index + (type === 'top' ? 0 : 1), 0, {
            componentName: 'LATableViewTr',
            children: Array.from({ length: maxColumn }, () => {
              return {
                componentName: node.componentName,
                children: [],
                props: {},
              }
            }),
          })
        }
      } else if (type === 'left' || type === 'right') {
        // 这里的parent是table
        // 每个tr左边添加一个td
        parent.children.forEach((child) => {
          child.children.splice(index + (type === 'left' ? 0 : 1), 0, {
            componentName: 'LATableViewTd',
            children: [],
            props: {},
          })
        })
      }
      this.$doc.event.emit('insertNode')
      this.$doc.schema.update()
    },
  },
}
</script>
