<template>
  <el-checkbox-group v-model="currentValue" @change="onChange">
    <component
      v-bind="item"
      :is="type === 'button' ? 'el-checkbox-button' : 'el-checkbox'"
      v-for="(item, index) in list"
      :key="index"
      :label="item.value"
      >{{ item.label }}</component
    >
  </el-checkbox-group>
</template>

<script>
export default {
  name: 'LACheckboxGroup',
  props: {
    type: String,
    options: {
      type: Array,
      default: () => [],
    },
    value: Array,
  },
  data() {
    return {
      currentValue: this.value || [],
    }
  },
  computed: {
    list() {
      if (!Array.isArray(this.options)) return []
      return (this?.options ?? []).filter((e) => !e.hidden)
    },
  },
  watch: {
    value(val) {
      this.currentValue = val
    },
    currentValue(val) {
      this.$emit('input', val)
    },
  },
  methods: {
    onChange(...args) {
      this.$emit('change', ...args)
    },
  },
}
</script>
