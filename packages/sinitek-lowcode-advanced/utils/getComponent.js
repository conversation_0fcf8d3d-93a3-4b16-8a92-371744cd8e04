import { ComponentName } from 'sinitek-lowcode-shared'
// import InputTemplate from '../components/input/index.vue'
// import Select from '../components/select/index.vue'
// import Date from '../components/date/index.vue'
// TODO 值不能为组件对象，只能是组件名称，因为不同的vue使用过组件后会修改对象
const map = {
  [ComponentName.NUMBER]: 'el-input-number',
  [ComponentName.MONEY]: 'xn-money',
  [ComponentName.TIME]: 'el-time-picker',
  [ComponentName.TIME_SELECT]: 'el-time-select',
  [ComponentName.RATE]: 'el-rate',
  [ComponentName.SWITCH]: 'el-switch',
  [ComponentName.SLIDER]: 'el-slider',
  [ComponentName.FILE]: 'xn-upload',
  [ComponentName.COLOR]: 'el-color-picker',
  [ComponentName.LINK]: 'el-link',
  [ComponentName.INPUT]: 'xn-input',
  [ComponentName.SELECT]: 'xn-select',
  [ComponentName.DATE]: 'xn-date-picker',
  [ComponentName.DATETIME]: 'xn-date-picker',
  [ComponentName.RELA_FORM]: 'LARelaForm',
  [ComponentName.SIRMORG]: 'LAEmployee',
  [ComponentName.RADIO_GROUP]: 'LARadio',
  [ComponentName.CHECKBOX_GROUP]: 'LACheck',
  // TODO 这里的child-form在form-item形成了循环引用，在form-item主动注册这个组件
  [ComponentName.CHILDREN_FORM]: 'LAChildForm',
}
export const getComponent = (name) => {
  return map[name] || name
}
