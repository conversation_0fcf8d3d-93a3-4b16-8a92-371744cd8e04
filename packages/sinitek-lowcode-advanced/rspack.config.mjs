import defineConfig from '../../config/rspack/base.mjs'
import path from 'path'
export default function () {
  return defineConfig({
    context: path.resolve(),
    unoConfigPath: path.resolve('uno.config.js'),
    library: {
      type: 'umd',
      name: 'sinitek-lowcode-advanced',
      entry: './index.js',
    },
    assetsDir: 'static', // 静态资源目录名称
    productionSourceMap: process.env.NODE_ENV === 'development', // 去掉打包的时候生成的map文件
    css: {
      extract: {
        filename: '[name].css',
      },
    },
    configureWebpack: {
      externals: [
        {
          vue: 'vue',
          'core-js': 'core-js',
          'element-ui': 'element-ui',
          'monaco-editor': 'monaco-editor',
          'sinitek-ui': 'sinitek-ui',
          'sinitek-util': 'sinitek-util',
          '@iconify/vue2': '@iconify/vue2',
          sirmapp: 'sirmapp',
          'sinitek-lowcode-materials': 'sinitek-lowcode-materials',
          'sinitek-lowcode-render': 'sinitek-lowcode-render',
          'sinitek-lowcode-shared': 'sinitek-lowcode-shared',
          echarts: 'echarts',
          moment: 'moment',
        },
        /^echarts\/lib/i,
        /^lodash\//,
      ],
    },
    chainWebpack(config) {
      config.module.rule('images').type('asset/inline')
    },
  })
}
