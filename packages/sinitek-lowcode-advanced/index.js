import 'uno.css'

import Form from './components/form/index.vue'
import ChildForm from './components/child-form/index'
import FormItem from './components/form-item/index.vue'
import RelaForm from './components/rela-form/index.vue'
import InputTemplate from './components/input'
import Select from './components/select/index.vue'
import Employee from './components/employee/index.vue'
import Dialog from './components/dialog/index.vue'
import Drawer from './components/drawer/index.jsx'
import Tabs from './components/tabs/index.js'
import TabPane from './components/tab-pane/index.js'
import Table from './components/table/index.vue'
import TableCol from './components/table/col.vue'
import Radio from './components/radio/index.vue'
import Checkbox from './components/checkbox/index.vue'
import Download from './components/download/index.vue'
import Stage from './components/stage/stage.vue'
import StageItem from './components/stage/item.vue'
import ElementIcon from './components/element-icon/index.vue'
import CollapseItem from './components/collapse-item/index.vue'
import Calendar from './components/calendar/index.vue'
import materials from './materials/index.js'
import ECharts from './components/echarts/index.vue'
import Dropdown from './components/dropdown/index.vue'
import EChartsEditor from './components/echarts-editor/index.vue'
import Popover from './components/popover'
import TableView from './components/table-view/table.vue'
import TableViewTr from './components/table-view/tr.vue'
import TableViewTd from './components/table-view/td.vue'
import TableViewAction from './components/table-view/action.vue'
import Vue from 'vue'
Vue.component('XnColAdvanced', () => import('./components/table/col.vue'))
export const components = {
  [Form.name]: Form,
  [FormItem.name]: FormItem,
  [ChildForm.name]: ChildForm,
  [RelaForm.name]: RelaForm,
  [Employee.name]: Employee,
  [Dialog.name]: Dialog,
  [Drawer.name]: Drawer,
  LAInput: InputTemplate,
  LAAutocomplete: InputTemplate,
  [Select.name]: Select,
  [Tabs.name]: Tabs,
  [TabPane.name]: TabPane,
  [Table.name]: Table,
  [TableCol.name]: TableCol,
  [Radio.name]: Radio,
  [Checkbox.name]: Checkbox,
  [Download.name]: Download,
  [Stage.name]: Stage,
  [StageItem.name]: StageItem,
  [ElementIcon.name]: ElementIcon,
  [CollapseItem.name]: CollapseItem,
  [Calendar.name]: Calendar,
  [ECharts.name]: ECharts,
  [Dropdown.name]: Dropdown,
  [EChartsEditor.name]: EChartsEditor,
  [Popover.name]: Popover,
  [TableView.name]: TableView,
  [TableViewTr.name]: TableViewTr,
  [TableViewTd.name]: TableViewTd,
}
let installed = false
const Plugin = {
  components,
  materials,
  install(simulator) {
    if (installed) return
    installed = true
    Object.keys(components).forEach((key) => {
      simulator.addComponent(key, components[key])
    })
    simulator.addMaterials(materials)
    simulator.registerPlugin({
      id: 'table-view-action',
      component: TableViewAction,
      area: 'selectActionArea',
      index: 1,
    })
  },
}

export default Plugin
