const { defineConfig } = require('@vue/cli-service')
module.exports = function () {
  return import('@unocss/webpack').then(({ default: UnoCSS }) =>
    defineConfig({
      assetsDir: 'static', // 静态资源目录名称
      productionSourceMap: process.env.NODE_ENV === 'development', // 去掉打包的时候生成的map文件
      css: {
        extract:
          process.env.NODE_ENV === 'development'
            ? {
                filename: 'css/[name].css',
                chunkFilename: 'css/[name].css',
              }
            : true,
      },
      configureWebpack: {
        plugins: [UnoCSS()],
        optimization: {
          realContentHash: true,
        },
        externals: {
          vue: 'vue',
          'core-js': 'core-js',
          'element-ui': 'element-ui',
          'monaco-editor': 'monaco-editor',
          'sinitek-ui': 'sinitek-ui',
          '@iconify/vue2': '@iconify/vue2',
          sirmapp: 'sirmapp',
          'sinitek-lowcode-simulator': 'sinitek-lowcode-simulator',
          'sinitek-lowcode-materials': 'sinitek-lowcode-materials',
          'sinitek-lowcode-render': 'sinitek-lowcode-render',
          'sinitek-lowcode-shared': 'sinitek-lowcode-shared',
        },
      },
    })
  )
}
