{"name": "sinitek-lowcode-advanced", "version": "1.0.0-SNAPSHOT.135", "description": "> TODO: description", "author": "wei.peng <<EMAIL>>", "homepage": "", "license": "ISC", "main": "dist/sinitek-lowcode-advanced.umd.min.js", "private": false, "files": ["dist", "materials"], "directories": {"test": "__tests__"}, "repository": {"url": "http://*************:8088/repository/sinitek-npm/"}, "scripts": {"dev": "vue-cli-service serve", "build": "rspack build ", "doctor": "set RSDOCTOR=true && rspack build ", "build2": "vue-cli-service build --target lib --formats umd-min --name sinitek-lowcode-advanced --dest dist ./index.js", "lint": "eslint . --fix", "format": "prettier --write \"{src,example}/**/*.{vue,js,jsx}\""}, "devDependencies": {"@babel/core": "catalog:", "@babel/eslint-parser": "catalog:", "@iconify-json/lucide": "^1.2.43", "@iconify-json/material-symbols-light": "1.2.6", "@iconify-json/mingcute": "^1.2.3", "@iconify/vue2": "catalog:", "@unocss/postcss": "catalog:", "@unocss/webpack": "catalog:", "@vue/babel-helper-vue-jsx-merge-props": "catalog:", "async-validator": "catalog:", "core-js": "catalog:", "element-ui": "catalog:", "eslint": "catalog:", "eslint-config-prettier": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-vue": "catalog:", "lint-staged": "catalog:", "prettier": "catalog:", "sinitek-util": "catalog:", "unocss": "catalog:", "vue": "catalog:", "vue-template-compiler": "catalog:"}, "volta": {"node": "18.0.0"}, "peerDependencies": {"echarts": "catalog:", "moment": "2.30.1", "sinitek-lowcode-materials": "workspace:^", "sinitek-lowcode-shared": "workspace:^", "vue": "catalog:"}, "gitHooks": {"pre-commit": "lint-staged"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "1.4.0"}}