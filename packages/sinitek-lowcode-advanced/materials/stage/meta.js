export const items = [
  {
    title: '阶段',
    name: 'label',
    setter: 'StringSetter',
  },
  {
    title: '颜色',
    name: 'color',
    setter: 'ColorSetter',
  },
  {
    title: {
      label: '阶段图标',
      tip: '使用的是element-ui图标',
    },
    name: 'icon',
    setter: {
      componentName: 'IconSetter',
      props: {
        type: 'element-ui',
      },
    },
  },
  {
    title: '点击事件',
    name: 'handler',
    setter: 'FunctionSetter',
  },
  {
    title: {
      label: '提示内容',
      tip: '鼠标悬浮在阶段上时显示的内容',
    },
    name: 'tooltip',
    setter: 'StringSetter',
  },
  {
    title: '自定义内容',
    name: 'default',
    setter: 'SlotSetter',
  },
]

export default {
  componentName: 'LAStage',
  title: '阶段展示',
  category: '信息展示',
  props: [],
  // childConfig: {
  //   componentName: 'LAStageItem',
  //   title: '子项',
  //   setter: {
  //     componentName: 'ArraySetter',
  //     props: {
  //       itemSetter: {
  //         componentName: 'ObjectSetter',
  //         props: {
  //           textField: 'label',
  //           config: {
  //             items: [...items],
  //           },
  //         },
  //         initialValue: {
  //           componentName: 'LAStageItem',
  //           props: {
  //             label: '阶段',
  //             color: 'rgb(74,222,128)',
  //             icon: 'el-icon-check',
  //             children: [],
  //           },
  //           children: [],
  //         },
  //       },
  //     },
  //   },
  // },
  priority: 2,
  snippets: [
    {
      title: '阶段展示',
      // screenshot: input1,
      screenshot: require('./__screenshots__/image.svg'),
      schema: {
        componentName: 'LAStage',
        props: {
          options: [
            {
              label: '未开始',
              color: 'rgb(156,163,175)',
              icon: 'el-icon-check',
            },
            { label: '就绪', color: 'rgb(96,165,250)', icon: 'el-icon-check' },
            {
              label: '处理中',
              color: 'rgb(251,146,60)',
              icon: 'el-icon-check',
            },
            {
              label: '已完成',
              color: 'rgb(74,222,128)',
              icon: 'el-icon-check',
            },
            { label: '关闭', color: 'rgb(254,113,113)', icon: 'el-icon-close' },
          ],
        },
        children: [
          {
            componentName: 'LAStageItem',
            props: {
              label: '未开始',
              color: 'rgb(156,163,175)',
              icon: 'el-icon-check',
              children: [],
            },
            children: [],
          },
          {
            componentName: 'LAStageItem',
            props: {
              label: '就绪',
              color: '#FB923C',
              icon: 'el-icon-check',
              children: [],
            },
            children: [],
          },
          {
            componentName: 'LAStageItem',
            props: {
              label: '处理中',
              color: '#FB923C',
              icon: 'el-icon-check',
              children: [],
            },
            children: [],
          },
          {
            componentName: 'LAStageItem',
            props: {
              label: '已完成',
              color: '#4ade80',
              icon: 'el-icon-check',
              children: [],
            },
            children: [],
          },
          {
            componentName: 'LAStageItem',
            props: {
              label: '关闭',
              color: '#FE7171',
              icon: 'el-icon-close',
              children: [],
            },
            children: [],
          },
        ],
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      supportBindState: false,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [],
    },
  },
}
