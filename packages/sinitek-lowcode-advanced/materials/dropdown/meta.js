import { buttonType } from 'sinitek-lowcode-materials/element-ui/components/button/meta'
import screenshots1 from './__screenshots__/image.svg'
import dropItem from 'sinitek-lowcode-materials/element-ui/components/dropdown-item/meta.js'

export default {
  componentName: 'LADropdown',
  title: '菜单按钮',
  category: '信息展示',
  props: [
    {
      name: 'type',
      title: { label: '类型', tip: '设置按钮不同的类型' },
      description: '类型',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: buttonType,
        },
      },
    },
    {
      name: 'size',
      title: { label: '尺寸', tip: '设置尺寸大小' },
      description: '尺寸',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '正常',
              value: 'medium',
            },
            {
              title: '中等',
              value: 'small',
            },
            {
              title: '迷你',
              value: 'mini',
            },
          ],
        },
      },
    },
    {
      name: 'splitButton',
      title: { label: '按钮组', tip: '下拉触发元素呈现为按钮组' },
      setter: 'BoolSetter',
    },
    {
      name: 'placement',
      title: '菜单弹出位置',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '顶部',
              value: 'top',
            },
            {
              title: '顶部开始',
              value: 'top-start',
            },
            {
              title: '顶部结束',
              value: 'top-end',
            },
            {
              title: '底部',
              value: 'bottom',
            },
            {
              title: '底部开始',
              value: 'bottom-start',
            },
            {
              title: '底部结束',
              value: 'bottom-end',
            },
          ],
        },
      },
    },
    {
      name: 'trigger',
      title: '触发下拉的行为',
      setter: {
        componentName: 'RadioGroupSetter',
        defaultValue: 'click',
        props: {
          options: [
            {
              title: '悬停',
              value: 'hover',
            },
            {
              title: '点击',
              value: 'click',
            },
          ],
        },
      },
    },
    {
      name: 'hideOnClick',
      title: { label: '隐藏菜单', tip: '是否在点击菜单项后隐藏菜单' },
      setter: 'BoolSetter',
    },
    {
      name: 'showTimeout',
      title: '展开下拉菜单的延时',
      setter: 'NumberSetter',
      condition(props) {
        return props.trigger === 'hover'
      },
    },
    {
      name: 'hideTimeout',
      title: '收起下拉菜单的延时',
      setter: 'NumberSetter',
      condition(props) {
        return props.trigger === 'hover'
      },
    },
    {
      name: 'tabindex',
      title: '组件的tabindex',
      setter: 'NumberSetter',
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      description: '圆角按钮',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'child',
      title: '菜单项',
      setter: {
        componentName: 'ArraySetter',
        props: {
          itemSetter: {
            componentName: 'ObjectSetter',
            props: {
              textField: 'text',
              config: {
                items: [
                  {
                    name: 'text',
                    title: '文本',
                    setter: 'StringSetter',
                  },
                  ...dropItem.props,
                ],
              },
            },
            initialValue: {
              text: '菜单按钮',
            },
          },
        },
      },
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'default',
          title: '内容',
          setter: 'SlotSetter',
        },
      ],
    },
  ],
  snippets: [
    {
      title: '菜单按钮',
      screenshot: screenshots1,
      schema: {
        componentName: 'LADropdown',
        props: {
          type: 'primary',
          trigger: 'click',
          splitButton: true,
          child: [
            {
              text: '菜单按钮1',
            },
            {
              text: '菜单按钮2',
            },
            {
              text: '菜单按钮3',
            },
          ],
        },
        children: [
          {
            componentName: 'Slot',
            props: { name: 'default' },
            children: [
              {
                componentName: 'LCText',
                props: { text: '菜单按钮' },
              },
            ],
          },
        ],
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'click',
          description: 'split-button 为 true 时，点击左侧按钮的回调',
          template: 'function click() {}',
        },
        {
          name: 'command',
          description: '点击菜单项触发的事件回调',
          template: 'function command() {}',
        },
        {
          name: 'visibleChange',
          description: '下拉框出现/隐藏时触发',
          template: 'function visibleChange() { return true}',
        },
      ],
    },
  },
}
