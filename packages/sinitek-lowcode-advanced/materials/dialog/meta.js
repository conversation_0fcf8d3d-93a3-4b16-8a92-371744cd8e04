import input from 'sinitek-lowcode-materials/sinitek-ui/components/dialog/meta.js'
import screenshots1 from './__screenshots__/dialog-icon.svg'
import { buttonType } from 'sinitek-lowcode-materials/element-ui/components/button/meta'
import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'
export default {
  componentName: 'LADialog',
  title: '对话框',
  category: '容器',
  priority: 2,
  tips: '按钮事件，需要在按钮配置里面配置事件。',
  props: [
    {
      name: 'limitToTab',
      title: '限制在tab内',
      setter: 'BoolSetter',
      defaultValue: false,
    },

    {
      name: 'to',
      title: '挂载的dom选择器',
      setter: 'StringSetter',
    },

    {
      name: 'loadingNotAllowClose',
      title: 'loading时不允许关闭',
      setter: 'BoolSetter',
    },

    {
      type: 'group',
      title: '样式',
      items: [
        {
          name: 'width',
          title: '宽度',
          setter: 'StringSetter',
        },
        {
          name: 'height',
          title: '高度',
          setter: 'StringSetter',
        },
        widthSize,
        {
          name: 'top',
          title: '上边距',
          setter: 'StringSetter',
          defaultValue: '15vh',
        },
        {
          name: 'innerTop',
          title: '限制在tab内时的margin-top',
          setter: 'StringSetter',
          defaultValue: '5vh',
        },
      ],
    },
    {
      type: 'group',
      title: '头部',
      items: [
        {
          name: 'title',
          title: '弹窗标题',
          setter: 'StringSetter',
        },
        {
          name: 'showFullscreenIcon',
          title: '全屏按钮显示',
          setter: 'BoolSetter',
        },
        {
          name: 'fullscreen',
          title: '是否全屏',
          setter: 'BoolSetter',
        },
      ],
    },
    {
      type: 'group',
      title: '底部',
      items: [
        {
          name: 'showFooter',
          title: '显示弹窗底部',
          setter: 'BoolSetter',
          defaultValue: true,
        },
        {
          name: 'buttons',
          title: '按钮配置',
          setter: {
            componentName: 'ArraySetter',
            props: {
              itemSetter: {
                componentName: 'ObjectSetter',
                props: {
                  textField: 'label',
                  config: {
                    items: [
                      {
                        name: 'hidden',
                        title: '隐藏按钮',
                        setter: 'BoolSetter',
                      },
                      {
                        name: 'label',
                        title: '按钮文本',
                        setter: 'StringSetter',
                      },
                      {
                        name: 'type',
                        title: '按钮类型',
                        setter: {
                          componentName: 'SelectSetter',
                          // primary / success / warning / danger / info / text
                          props: {
                            options: buttonType,
                          },
                        },
                      },
                      {
                        name: 'action',
                        title: '按钮效果',
                        setter: {
                          componentName: 'SelectSetter',
                          // primary / success / warning / danger / info / text
                          props: {
                            options: [
                              {
                                title: '新增',
                                value: 'add',
                              },
                              {
                                title: '编辑',
                                value: 'edit',
                              },
                              {
                                title: '删除',
                                value: 'delete',
                              },
                            ],
                          },
                        },
                      },
                      {
                        name: 'event',
                        title: '按钮事件',
                        setter: 'FunctionSetter',
                      },
                      {
                        name: 'showLoading',
                        title: '显示加载',
                        setter: 'BoolSetter',
                        defaultValue: true,
                      },
                    ],
                  },
                },
                initialValue: {
                  label: '按钮',
                  type: 'primary',
                  action: '',
                  event: '',
                },
              },
            },
          },
        },
      ],
    },

    {
      type: 'group',
      title: '插槽',
      items: [
        {
          title: '标题',
          name: 'title',
          setter: 'SlotSetter',
          supportVariable: false,
        },
        {
          title: '底部',
          name: 'footer',
          setter: 'SlotSetter',
          supportVariable: false,
        },
      ],
    },
  ],
  overrideProps: [{ name: 'LCBindState', title: '是否显示' }],
  snippets: [
    {
      title: '对话框',
      screenshot: screenshots1,
      prePreview: false,
      schema: {
        componentName: 'LADialog',
        props: {
          title: '对话框',
          buttons: [
            { label: '保存', type: 'primary', action: 'save', event: '' },
            { label: '取消', type: 'info', action: 'cancel', event: '' },
          ],
          limitToTab: true,
        },
        children: [
          {
            componentName: 'Slot',
            props: {
              name: 'default',
            },
            children: [],
          },
        ],
      },
    },
  ],
  configure: {
    component: input.configure.component,
    supports: {
      ...input.configure.supports,
      supportBindState: true,
      events: [
        {
          name: 'input',
          description: '显示/隐藏状态修改时',
          template: 'function input() { console.log("input");}',
        },
      ],
      methods: [
        {
          name: 'open',
          description: '打开对话框',
          example: 'refs.dialog.open()',
          returnType: 'Promise<void>',
        },
        {
          name: 'close',
          description: '关闭对话框',
          example: 'refs.dialog.close()',
        },
      ],
      state: [
        {
          name: 'visible',
          description: '是否显示',
          type: 'boolean',
        },
      ],
    },
  },
}
