import {
  ComponentN<PERSON>,
  Component<PERSON><PERSON><PERSON><PERSON><PERSON>,
  JSExpression,
} from 'sinitek-lowcode-shared'
import formItem from 'sinitek-lowcode-materials/sinitek-ui/components/form-item/meta.js'
import input from '../input/meta.js'
import number from 'sinitek-lowcode-materials/element-ui/components/input-number/meta.js'
import money from 'sinitek-lowcode-materials/sinitek-ui/components/money/meta.js'
import select from '../select/meta.js'
import time from 'sinitek-lowcode-materials/element-ui/components/time-picker/meta.js'
import timeSelect from 'sinitek-lowcode-materials/element-ui/components/time-select/meta.js'
import datetime from '../date/meta.js'
import employee from '../employee/meta.js'
import $switch from 'sinitek-lowcode-materials/element-ui/components/switch/meta.js'
import rate from 'sinitek-lowcode-materials/element-ui/components/rate/meta.js'
import upload from 'sinitek-lowcode-materials/sinitek-ui/components/upload/meta.js'
import radio from '../radio/meta.js'
import checkbox from '../checkbox/meta.js'
import { formSpan } from '../form/meta'
// import childForm from '../child-form/meta.js'

import image from './__screenshots__/image.svg'
import cloneDeep from 'lodash/cloneDeep'

const fields = {
  [ComponentName.INPUT]: input,
  [ComponentName.NUMBER]: number,
  [ComponentName.MONEY]: money,
  [ComponentName.SELECT]: select,
  [ComponentName.DATE]: datetime,
  [ComponentName.TIME]: time,
  [ComponentName.TIME_SELECT]: timeSelect,
  [ComponentName.DATETIME]: datetime,
  [ComponentName.SWITCH]: $switch,
  [ComponentName.SIRMORG]: employee,
  [ComponentName.FILE]: upload,
  [ComponentName.RATE]: rate,
  [ComponentName.RADIO_GROUP]: radio,
  [ComponentName.CHECKBOX_GROUP]: checkbox,
  // [ComponentName.CHILDREN_FORM]: childForm,
}

export default {
  componentName: 'LAFormItem',
  title: '表单项',
  category: '表单',
  modelComponent: true,
  tips: `在子表单里使用只需要设置'表单项/prop'，用来触发表单校验。\n
  在高级表单里需要多设置'绑定字段'，来绑定数据。`,
  treeKey: 'itemProps.label',
  props: [
    {
      name: 'span',
      title: '跨度',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: formSpan,
        },
      },
    },
    {
      name: 'field',
      title: '组件类型',
      setter: {
        componentName: 'SelectSetter',
        props: {
          clearable: false,
          options: Object.keys(fields).map((type) => {
            return {
              value: type,
              label: ComponentTypeLabel[type],
            }
          }),
        },
      },
    },
    {
      title: '表单项',
      name: 'itemProps',
      setter: {
        componentName: 'ObjectSetter',
        props: {
          config: {
            items: formItem.props,
          },
        },
      },
    },
    ...Object.keys(fields).map((e) => {
      return {
        title: '组件配置',
        name: 'fieldProps',
        setter: {
          componentName: 'ObjectSetter',
          props: {
            config: {
              items: fields[e].props,
            },
          },
        },
        condition(props) {
          return props.field === e
        },
      }
    }),

    // {
    //   title: '表单项',
    //   type: 'group',
    //   items: formItem.props,
    // },
  ],
  overrideProps: [
    {
      name: 'LCBindState',
      condition: (props, target) => {
        const c = target.findParent('LAChildForm')
        return !c
      },
      setter: {
        componentName: 'BindStateSetter',
        props: {
          filter: (target, list) => {
            const c = target.findParent('LAForm')
            if (c?.props?.model) {
              const key = c.props.model.value.replace('this.state.', '').trim()
              return list.filter((item) =>
                item.label ? item?.label?.startsWith(key) : item.startsWith(key)
              )
            }
            return list
          },
        },
      },
      extraProps: {
        setValue: (target, val) => {
          const v = typeof val === 'string' ? val : val?.value
          if (v) {
            const arr = v.split('.')
            const last = arr.pop()
            // 自动设置prop
            target.log('自动设置prop', {
              prop: last,
            })
            target.setPropValue('itemProps.prop', last)
          }
        },
      },
    },
  ],
  snippets: [
    {
      title: '表单项',
      screenshot: image,
      schema: {
        componentName: 'LAFormItem',
        props: {
          field: ComponentName.INPUT,
          itemProps: {
            label: '输入框1',
          },
        },
      },
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        ...Object.entries(fields).reduce((acc, [key, value]) => {
          if (value?.configure?.supports?.events) {
            return acc.concat(
              cloneDeep(value.configure.supports.events).map((e) => {
                e.condition = (props) => props.field === key
                return e
              })
            )
          }
          return acc
        }, []),
      ],
    },
    component: {
      isContainer: false,
      getAIParams: (state) => {
        return {
          componentType: state.current.props.field.toUpperCase(),
          formContext:
            state.parent.props.status === 'readonly' ? 'DETAIL' : 'SUBMIT',
          componentTitle: state.current.props.itemProps.label,
        }
      },
      setAIParams: (arr) => {
        const result = {
          props: {
            itemProps: {},
            fieldProps: {},
          },
        }
        const props = {}
        arr.forEach((e) => {
          props[e.code] = e.value
        })
        result.props.fieldProps = Object.assign({}, props)
        result.props.itemProps = Object.assign({}, props)
        return result
      },
      nestingRule: {
        parentWhitelist: ['LAForm', 'LAChildForm', 'LATable', 'XnFlex'],
      },
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          // 只用主模型的当作数据源的表格可以从主模型树中拖入组件
          if (node?.source !== 'model') return node
          let children = []
          if (node.props.field === ComponentName.FILE) {
            if (target.findParent('LAChildForm')) {
              // 在子表单时
              if (node.props.fieldProps.sourceId) {
                node.props.fieldProps.sourceId = {
                  type: JSExpression,
                  value: `scope.row['${node.props.fieldProps.sourceId}']`,
                }
              }
            } else if (target.findParent('LAForm')) {
              const form = target.findParent('LAForm')
              if (node.props.fieldProps.sourceId) {
                let key = 'this.state'
                if (form?.props?.model?.value) {
                  key = form.props.model.value
                }
                node.props.fieldProps.sourceId = {
                  type: JSExpression,
                  value: `${key}['${node.props.fieldProps.sourceId}']`,
                }
              }
            } else if (target.componentName == 'LATable') {
              children.push({
                componentName: 'Slot',
                props: {
                  name: 'default',
                },
                children: [
                  {
                    componentName: 'LADownload',
                    props: {
                      ...node.props.fieldProps,
                      sourceId: {
                        type: JSExpression,
                        value: `scope.row['${node?.props?.fieldProps?.sourceId ?? 'id'}']`,
                      },
                    },
                  },
                ],
              })
            }
          }
          if (target.componentName == 'LATable') {
            if (target.props.datasource === 'main') {
              const props = node?.props
              return {
                componentName: 'XnColAdvanced',
                props: {
                  prop: props?.itemProps?.prop,
                  label: props?.itemProps?.label,
                },
                children,
              }
            } else {
              return {
                forbidden: true,
                errorMessage: '只有使用主模型当作数据源，才能拖入主模型元素。',
              }
            }
          }
          return node
        },
      },
    },
  },
}
