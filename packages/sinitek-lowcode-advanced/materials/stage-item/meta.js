import { items } from '../stage/meta'
export default {
  componentName: 'LAStageItem',
  title: '阶段项',
  category: '信息展示',
  props: [
    ...items,
    {
      title: '子项',
      name: 'children',
      setter: {
        componentName: 'ArraySetter',
        props: {
          itemSetter: {
            componentName: 'ObjectSetter',
            props: {
              textField: 'label',
              config: {
                items: [...items],
              },
            },
            initialValue: {
              label: '阶段',
              color: 'rgb(74,222,128)',
              icon: 'el-icon-check',
              children: [],
            },
          },
        },
      },
    },
  ],
  snippets: [
    {
      title: '阶段项',
      // screenshot: input1,
      screenshot: require('./__screenshots__/image.svg'),
      schema: {
        componentName: 'LAStageItem',
        props: {
          label: '阶段',
          color: '#4ade80',
          icon: 'el-icon-check',
          children: [],
        },
        children: [],
      },
    },
  ],
  priority: 2,
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [],
    },
  },
}
