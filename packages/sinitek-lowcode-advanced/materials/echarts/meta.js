import image from './__screenshots__/image.svg'

const events = [
  {
    name: 'click',
    description: '图表点击事件',
    template: `function click(event) { console.log('图表点击事件', event); }`,
  },
  {
    name: 'dblclick',
    description: '图表双击事件',
    template: `function dblclick(event) { console.log('图表双击事件', event); }`,
  },
  {
    name: 'mousedown',
    description: '鼠标按下事件',
    template: `function mousedown(event) { console.log('鼠标按下事件', event); }`,
  },
  {
    name: 'mouseup',
    description: '鼠标抬起事件',
    template: `function mouseup(event) { console.log('鼠标抬起事件', event); }`,
  },
  {
    name: 'mouseout',
    description: '鼠标移出图表区域事件',
    template: `function mouseout(event) { console.log('鼠标移出图表区域', event); }`,
  },
  {
    name: 'mouseover',
    description: '鼠标悬停事件',
    template: `function mouseover(event) { console.log('鼠标悬停事件', event); }`,
  },
  {
    name: 'globalout',
    description: '鼠标移出全局区域事件',
    template: `function globalout(event) { console.log('鼠标移出全局区域', event); }`,
  },
  {
    name: 'contextmenu',
    description: '右键菜单事件',
    template: `function contextmenu(event) { console.log('右键菜单事件', event); }`,
  },
  {
    name: 'legendselectchanged',
    description: '图例选择改变事件',
    template: `function legendselectchanged(selected) { console.log('图例选择改变', selected); }`,
  },
  {
    name: 'legendselected',
    description: '图例选中事件',
    template: `function legendselected(selected) { console.log('图例选中', selected); }`,
  },
  {
    name: 'legendunselected',
    description: '图例取消选中事件',
    template: `function legendunselected(selected) { console.log('图例取消选中', selected); }`,
  },
  {
    name: 'legendselectall',
    description: '图例全选事件',
    template: `function legendselectall(selected) { console.log('图例全选', selected); }`,
  },
  {
    name: 'legendinverseselect',
    description: '图例反选事件',
    template: `function legendinverseselect(selected) { console.log('图例反选', selected); }`,
  },
  {
    name: 'legendscroll',
    description: '图例滚动事件',
    template: `function legendscroll(scrollData) { console.log('图例滚动', scrollData); }`,
  },
  {
    name: 'datazoom',
    description: '数据区域缩放事件',
    template: `function datazoom(dataRange) { console.log('数据区域缩放', dataRange); }`,
  },
  {
    name: 'datarangeselected',
    description: '数据范围选择事件',
    template: `function datarangeselected(selectedRange) { console.log('数据范围选择', selectedRange); }`,
  },
  {
    name: 'graphroam',
    description: '图形漫游事件',
    template: `function graphroam(roamType) { console.log('图形漫游', roamType); }`,
  },
  {
    name: 'georoam',
    description: '地理坐标系漫游事件',
    template: `function georoam(geoInfo) { console.log('地理坐标系漫游', geoInfo); }`,
  },
  {
    name: 'treeroam',
    description: '树图漫游事件',
    template: `function treeroam(roamType) { console.log('树图漫游', roamType); }`,
  },
  {
    name: 'timelinechanged',
    description: '时间轴改变事件',
    template: `function timelinechanged(time) { console.log('时间轴改变', time); }`,
  },
  {
    name: 'timelineplaychanged',
    description: '时间轴播放状态改变事件',
    template: `function timelineplaychanged(state) { console.log('时间轴播放状态', state); }`,
  },
  {
    name: 'restore',
    description: '重置选项事件',
    template: `function restore() { console.log('重置选项'); }`,
  },
  {
    name: 'dataviewchanged',
    description: '数据视图修改事件',
    template: `function dataviewchanged(view) { console.log('数据视图修改', view); }`,
  },
  {
    name: 'magictypechanged',
    description: '动态类型切换事件',
    template: `function magictypechanged(type) { console.log('动态类型切换', type); }`,
  },
  {
    name: 'geoselectchanged',
    description: '地理选择改变事件',
    template: `function geoselectchanged(selected) { console.log('地理选择改变', selected); }`,
  },
  {
    name: 'geoselected',
    description: '地理区域选中事件',
    template: `function geoselected(selected) { console.log('地理区域选中', selected); }`,
  },
  {
    name: 'geounselected',
    description: '地理区域取消选中事件',
    template: `function geounselected(selected) { console.log('地理区域取消选中', selected); }`,
  },
  {
    name: 'axisareaselected',
    description: '坐标轴区域选择事件',
    template: `function axisareaselected(area) { console.log('坐标轴区域选择', area); }`,
  },
  {
    name: 'brush',
    description: '选框刷选事件',
    template: `function brush(brushComponents) { console.log('选框刷选', brushComponents); }`,
  },
  {
    name: 'brushEnd',
    description: '选框刷选结束事件',
    template: `function brushEnd(brushComponents) { console.log('选框刷选结束', brushComponents); }`,
  },
  {
    name: 'brushselected',
    description: '选框选中事件',
    template: `function brushselected(selected) { console.log('选框选中', selected); }`,
  },
  {
    name: 'globalcursortaken',
    description: '全局光标捕获事件',
    template: `function globalcursortaken(cursorType) { console.log('全局光标捕获', cursorType); }`,
  },
  {
    name: 'rendered',
    description: '图表渲染完成事件',
    template: `function rendered() { console.log('图表渲染完成'); }`,
  },
  {
    name: 'finished',
    description: '图表动画完成事件',
    template: `function finished() { console.log('图表动画完成'); }`,
  },
]

const methods = [
  {
    name: 'group',
    description: '图表分组',
    example: 'chart.group = "myGroup"',
    returnType: 'void',
    args: [],
    type: 'string',
  },
  {
    name: 'setOption',
    description: '设置图表配置项',
    example: 'chart.setOption(option, true)',
    returnType: 'void',
    args: [
      { name: 'option', type: 'Object', description: '图表配置对象' },
      { name: 'notMerge', type: 'boolean', description: '是否不合并配置' },
    ],
  },
  {
    name: 'resize',
    description: '调整图表尺寸',
    example: 'chart.resize({ width: 800, height: 600 })',
    returnType: 'void',
    args: [{ name: 'options', type: 'Object', description: '尺寸配置对象' }],
  },
  {
    name: 'clear',
    description: '清除图表内容',
    example: 'chart.clear()',
    returnType: 'void',
    args: [],
  },
  {
    name: 'on',
    description: '绑定事件处理函数',
    example: 'chart.on("click", handler)',
    returnType: 'void',
    args: [
      { name: 'eventName', type: 'string', description: '事件名称' },
      { name: 'handler', type: 'Function', description: '事件处理函数' },
    ],
  },
  {
    name: 'off',
    description: '解绑事件处理函数',
    example: 'chart.off("click", handler)',
    returnType: 'void',
    args: [
      { name: 'eventName', type: 'string', description: '事件名称' },
      { name: 'handler', type: 'Function', description: '事件处理函数' },
    ],
  },
  {
    name: 'getWidth',
    description: '获取图表宽度',
    example: 'const width = chart.getWidth()',
    returnType: 'number',
    args: [],
  },
  {
    name: 'getHeight',
    description: '获取图表高度',
    example: 'const height = chart.getHeight()',
    returnType: 'number',
    args: [],
  },
  {
    name: 'getDom',
    description: '获取图表DOM元素',
    example: 'const dom = chart.getDom()',
    returnType: 'HTMLElement',
    args: [],
  },
  {
    name: 'getOption',
    description: '获取当前配置项',
    example: 'const option = chart.getOption()',
    returnType: 'Object',
    args: [],
  },
  {
    name: 'renderToSVGString',
    description: '渲染为SVG字符串',
    example: 'const svg = chart.renderToSVGString()',
    returnType: 'string',
    args: [],
  },
  {
    name: 'dispatchAction',
    description: '触发图表行为',
    example: 'chart.dispatchAction({ type: "highlight", dataIndex: 0 })',
    returnType: 'void',
    args: [{ name: 'payload', type: 'Object', description: '行为配置对象' }],
  },
  {
    name: 'convertToPixel',
    description: '转换坐标到像素值',
    example: 'const pixel = chart.convertToPixel("grid", [0, 0])',
    returnType: 'Array|number',
    args: [
      { name: 'finder', type: 'Object|string', description: '坐标系查找条件' },
      { name: 'value', type: 'Array|string', description: '坐标值' },
    ],
  },
  {
    name: 'convertFromPixel',
    description: '转换像素值到坐标',
    example: 'const coord = chart.convertFromPixel("grid", [100, 100])',
    returnType: 'Array|number',
    args: [
      { name: 'finder', type: 'Object|string', description: '坐标系查找条件' },
      { name: 'value', type: 'Array|string', description: '像素值' },
    ],
  },
  {
    name: 'containPixel',
    description: '判断坐标是否在图表区域内',
    example: 'const isContain = chart.containPixel("grid", [100, 100])',
    returnType: 'boolean',
    args: [
      { name: 'finder', type: 'Object|string', description: '坐标系查找条件' },
      { name: 'value', type: 'Array', description: '像素坐标' },
    ],
  },
  {
    name: 'showLoading',
    description: '显示加载动画',
    example: 'chart.showLoading("default")',
    returnType: 'void',
    args: [
      {
        name: 'type',
        type: '"default"',
        description: '加载动画类型',
        required: false,
      },
      {
        name: 'opts',
        type: 'Object',
        description: '加载动画配置项，跟type有关',
        required: false,
      },
    ],
  },
  {
    name: 'hideLoading',
    description: '隐藏加载动画',
    example: 'chart.hideLoading()',
    returnType: 'void',
    args: [],
  },
  {
    name: 'getDataURL',
    description: '获取图表数据URL',
    example: 'const url = chart.getDataURL({ type: "png" })',
    returnType: 'string',
    args: [{ name: 'options', type: 'Object', description: '导出配置' }],
  },
  {
    name: 'getConnectedDataURL',
    description: '获取关联图表数据URL',
    example: 'const url = chart.getConnectedDataURL({ type: "png" })',
    returnType: 'string',
    args: [{ name: 'options', type: 'Object', description: '导出配置' }],
  },
  {
    name: 'appendData',
    description: '追加数据',
    example: 'chart.appendData({ seriesIndex: 0, data: [10] })',
    returnType: 'void',
    args: [{ name: 'options', type: 'Object', description: '数据追加配置' }],
  },
  {
    name: 'isDisposed',
    description: '判断图表是否已销毁',
    example: 'const disposed = chart.isDisposed()',
    returnType: 'boolean',
    args: [],
  },
  {
    name: 'dispose',
    description: '销毁图表实例',
    example: 'chart.dispose()',
    returnType: 'void',
    args: [],
  },
]
export default {
  componentName: 'LAECharts',
  title: 'echarts图表',
  category: '信息展示',
  tips: '可以全局window.LCECharts替代echarts使用',
  props: [
    {
      title: '宽度',
      name: 'width',
      setter: ['StringSetter', 'NumberSetter'],
    },
    {
      title: '高度',
      name: 'height',
      setter: ['StringSetter', 'NumberSetter'],
    },
    {
      title: '配置',
      name: 'config',
      setter: 'JSONSetter',
      documentUrl: 'https://echarts.apache.org/zh/option.html#title',
    },
  ],
  snippets: [
    {
      title: '自定义图表',
      screenshot: image,
      schema: {
        componentName: 'LAECharts',
        props: {
          width: '100%',
          height: '300px',
          config: {
            xAxis: {
              type: 'category',
              data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            },
            yAxis: {
              type: 'value',
            },
            series: [
              {
                data: [150, 230, 224, 218, 135, 147, 260],
                type: 'line',
              },
            ],
          },
        },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events,
      methods,
    },
  },
}
