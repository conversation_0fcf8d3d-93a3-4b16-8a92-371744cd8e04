export default {
  componentName: 'LADrawer',
  title: '抽屉',
  category: '容器',
  priority: 2,
  tips: '抽屉从父窗体边缘滑入，覆盖住部分父窗体内容。按钮事件需要在按钮配置里面配置事件。',
  props: [
    {
      name: 'direction',
      title: {
        label: '打开方向',
        tip: '抽屉打开的方向',
      },
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '从右往左',
              value: 'rtl',
            },
            {
              title: '从左往右',
              value: 'ltr',
            },
            {
              title: '从上往下',
              value: 'ttb',
            },
            {
              title: '从下往上',
              value: 'btt',
            },
          ],
        },
      },
      defaultValue: 'rtl',
    },
    {
      name: 'size',
      title: {
        label: '抽屉大小',
        tip: '抽屉的宽度或高度，根据方向不同而不同',
      },
      setter: 'StringSetter',
      defaultValue: '30%',
    },
    {
      name: 'title',
      title: {
        label: '标题',
        tip: '抽屉的标题',
      },
      setter: 'StringSetter',
    },
    {
      name: 'modal',
      title: {
        label: '遮罩层',
        tip: '是否需要遮罩层',
      },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'modal-append-to-body',
      title: {
        label: '遮罩插入body',
        tip: '遮罩层是否插入至 body 元素上',
      },
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'append-to-body',
      title: {
        label: '插入body',
        tip: '抽屉是否插入至 body 元素上',
      },
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'lock-scroll',
      title: {
        label: '锁定滚动',
        tip: '是否在 Drawer 出现时将 body 滚动锁定',
      },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'with-header',
      title: {
        label: '显示头部',
        tip: '是否显示 header 栏',
      },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'wrapper-closable',
      title: {
        label: '点击遮罩关闭',
        tip: '点击遮罩层是否可以关闭 Drawer',
      },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'close-on-press-escape',
      title: {
        label: 'ESC关闭',
        tip: '是否可以通过按下 ESC 关闭 Drawer',
      },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'show-close',
      title: {
        label: '显示关闭按钮',
        tip: '是否显示关闭按钮',
      },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'before-close',
      title: {
        label: '关闭前回调',
        tip: '关闭前的回调函数',
      },
      setter: 'FunctionSetter',
    },
    {
      name: 'destroy-on-close',
      title: {
        label: '关闭时销毁',
        tip: '控制是否在关闭 Drawer 之后将子元素全部销毁',
      },
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'custom-class',
      title: {
        label: '自定义类名',
        tip: 'Drawer 的自定义类名',
      },
      setter: 'StringSetter',
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          title: '标题',
          name: 'title',
          setter: 'SlotSetter',
        },
        {
          title: '内容',
          name: 'default',
          setter: 'SlotSetter',
        },
      ],
    },
  ],
  overrideProps: [{ name: 'LCBindState', title: '是否显示' }],
  snippets: [
    {
      title: '抽屉',
      screenshot: require('./__screenshots__/image.svg'),
      prePreview: false,
      schema: {
        componentName: 'LADrawer',
        props: {
          title: '抽屉',
          direction: 'rtl',
          size: '30%',
          modal: false,
          appendToBody: false,
        },
        children: [
          {
            componentName: 'Slot',
            props: {
              name: 'default',
            },
            children: [],
          },
        ],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
      isModal: true,
      rootSelector: '.el-drawer',
    },
    supports: {
      style: true,
      supportBindState: true,
      events: [
        {
          name: 'input',
          description: '显示/隐藏状态修改时',
          template: 'function input() { console.log("input");}',
        },
        {
          name: 'open',
          description: '打开抽屉',
          template: 'function open() { console.log("open");}',
        },
        {
          name: 'opened',
          description: '打开抽屉后触发',
          template: 'function opened() { console.log("opened");}',
        },
        {
          name: 'close',
          description: '关闭抽屉',
          template: 'function close() { console.log("close");}',
        },
        {
          name: 'closed',
          description: '关闭抽屉后触发',
          template: 'function closed() { console.log("closed");}',
        },
      ],
      methods: [
        {
          name: 'open',
          description: '打开抽屉',
          example: 'refs.drawer.open()',
          returnType: 'Promise<void>',
        },
        {
          name: 'close',
          description: '关闭抽屉',
          example: 'refs.drawer.close()',
          returnType: 'Promise<void>',
        },
      ],
      state: [
        {
          name: 'isVisible',
          description: '是否显示',
          type: 'boolean',
        },
      ],
    },
  },
}
