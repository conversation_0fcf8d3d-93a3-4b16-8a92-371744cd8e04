import input from 'sinitek-lowcode-materials/sinitek-ui/components/autocomplete/meta.js'
import input1 from 'sinitek-lowcode-materials/element-ui/components/autocomplete/__screenshots__/image.svg'

export default {
  componentName: 'LAAutocomplete',
  title: '自动完成',
  category: '信息输入',
  props: input.props,
  componentType: 'INPUT',
  formContext: 'SUBMIT',
  priority: 2,
  snippets: [
    {
      title: '自动完成',
      screenshot: input1,
      schema: {
        componentName: 'LAAutocomplete',
        props: {
          LCTag: 'xn-autocomplete',
          placeholder: '请输入',
          triggerOnFocus: true,
          hideLoading: true,
          clearable: true,
          icon: 'el-icon-search',
        },
        children: [],
      },
    },
  ],
  configure: input.configure,
}
