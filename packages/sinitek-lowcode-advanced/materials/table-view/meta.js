export default {
  componentName: 'LATableView',
  title: '表格布局',
  category: '容器',
  props: [
    {
      name: 'useBorder',
      title: '使用边框',
      setter: 'BoolSetter',
    },
    {
      name: 'bColor',
      title: '边框颜色',
      setter: 'ColorSetter',
      condition: (prop) => {
        return prop.useBorder
      },
    },
  ],
  snippets: [
    {
      title: '表格布局',
      screenshot: require('./__screenshots__/table-view.svg'),
      schema: {
        componentName: 'LATableView',
        props: {
          useBorder: true,
        },
        children: [
          {
            componentName: 'LATableViewTr',
            props: {},
            children: [
              {
                componentName: 'LATableViewTd',
                props: {},
                children: [],
              },
              {
                componentName: 'LATableViewTd',
                props: {},
                children: [],
              },
              {
                componentName: 'LATableViewTd',
                props: {},
                children: [],
              },
            ],
          },
          {
            componentName: 'LATableViewTr',
            props: {},
            children: [
              {
                componentName: 'LATableViewTd',
                props: {},
                children: [],
              },
              {
                componentName: 'LATableViewTd',
                props: {},
                children: [],
              },
              {
                componentName: 'LATableViewTd',
                props: {},
                children: [],
              },
            ],
          },
          {
            componentName: 'LATableViewTr',
            props: {},
            children: [
              {
                componentName: 'LATableViewTd',
                props: {},
                children: [],
              },
              {
                componentName: 'LATableViewTd',
                props: {},
                children: [],
              },
              {
                componentName: 'LATableViewTd',
                props: {},
                children: [],
              },
            ],
          },
        ],
      },
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
    component: {
      isContainer: true,
    },
  },
}
