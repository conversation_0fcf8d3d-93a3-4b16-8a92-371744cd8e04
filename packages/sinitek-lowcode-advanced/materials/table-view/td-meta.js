export default {
  componentName: 'LATableViewTd',
  title: '单元格列',
  category: '信息展示',
  props: [
    {
      type: 'group',
      title: '布局',
      items: [
        {
          name: 'align',
          title: '水平对齐',
          setter: {
            componentName: 'SelectSetter',
            props: {
              options: [
                { label: '左对齐', value: 'left' },
                { label: '居中', value: 'center' },
                { label: '右对齐', value: 'right' },
              ],
            },
          },
          defaultValue: 'left',
        },
        {
          name: 'valign',
          title: '垂直对齐',
          setter: {
            componentName: 'SelectSetter',
            props: {
              options: [
                { label: '顶部对齐', value: 'top' },
                { label: '居中', value: 'middle' },
                { label: '底部对齐', value: 'bottom' },
              ],
            },
          },
          defaultValue: 'middle',
        },
        {
          name: 'colspan',
          title: '水平合并列',
          setter: 'NumberSetter',
          defaultValue: 1,
        },
        {
          name: 'rowspan',
          title: '垂直合并列',
          setter: 'NumberSetter',
          defaultValue: 1,
        },
      ],
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
    component: {
      isContainer: true,
    },
  },
}
