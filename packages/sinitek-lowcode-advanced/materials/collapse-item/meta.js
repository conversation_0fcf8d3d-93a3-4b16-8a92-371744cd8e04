// import btn1 from './__screenshots__/button-1.png'
export default {
  componentName: 'LACollapseItem',
  title: '折叠面板项',
  category: '容器',
  props: [
    {
      name: 'name',
      title: '唯一标志符',
      setter: 'StringSetter',
    },
    {
      name: 'title',
      title: '面板标题',
      setter: 'StringSetter',
    },
    {
      name: 'disabled',
      title: '是否禁用',
      setter: 'BoolSetter',
    },
    {
      name: 'canCollapse',
      title: {
        label: '可折叠',
        tip: '是否可折叠，如果为false，则无法折叠，展示展开状态',
      },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'showIcon',
      title: '显示图标',
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'title',
          title: '标题',
          setter: 'SlotSetter',
        },
      ],
    },
  ],
  configure: {
    component: {
      isContainer: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: false,
      events: [],
      supportBindState: false,
    },
  },
}
