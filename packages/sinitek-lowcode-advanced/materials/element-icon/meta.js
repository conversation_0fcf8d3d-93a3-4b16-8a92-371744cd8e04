import img from './__screenshots__/image.svg'
export default {
  componentName: 'LAElementIcon',
  title: '饿了么图标',
  category: '基础',
  props: [
    {
      name: 'icon',
      title: '图标',
      setter: {
        componentName: 'IconSetter',
        props: {
          type: 'element-ui',
        },
      },
    },
    {
      name: 'size',
      title: { label: '大小', tip: '单位是像素' },
      setter: 'NumberSetter',
    },
    {
      name: 'color',
      title: '颜色',
      setter: 'ColorSetter',
    },
  ],
  componentType: 'DATE',
  formContext: 'SUBMIT',
  priority: 2,
  snippets: [
    {
      title: '饿了么图标',
      screenshot: img,
      schema: {
        componentName: 'LAElementIcon',
        props: {
          icon: 'el-icon-platform-eleme',
          size: 16,
        },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      condition: true,
      style: true,
      supportBindState: false,
    },
  },
}
