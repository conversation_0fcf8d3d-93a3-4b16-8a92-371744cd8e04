import input from 'sinitek-lowcode-materials/sinitek-ui/components/select/meta.js'
import input1 from './__screenshots__/image.svg'
import { tableValid } from '../common/tableValid'
import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'
export default {
  componentName: 'LASelect',
  title: '下拉选择',
  category: '信息输入',
  props: [
    // {
    //   name: 'value',
    //   title: '下拉选择器的值',
    //   setter: 'StringSetter',
    // },
    {
      name: 'options',
      title: '数据源',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/select.html#options%E5%8F%82%E6%95%B0',
    },
    {
      name: 'groups',
      title: '分组数据源',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/select.html?prop=groups#xn-select',
    },
    // {
    //   name: 'show',
    //   title: { label: '是否显示', tip: '' },
    //   defaultValue: true,
    //   setter: 'BoolSetter',
    // },
    {
      name: 'sorted',
      title: { label: '是否排序', tip: '' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'optionLabel',
      title: { label: 'optionLabel', tip: '使用options时，选项标签的key' },
      setter: 'StringSetter',
      defaultValue: 'label',
    },
    {
      name: 'optionValue',
      title: { label: 'optionValue', tip: '使用options时，选项值的key' },
      setter: 'StringSetter',
      defaultValue: 'value',
    },

    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'valueKey',
      title: {
        label: 'valueKey',
        tip: '作为 value 唯一标识的键名，绑定值为对象类型时必填',
      },
      defaultValue: 'value',
      setter: 'StringSetter',
    },

    {
      name: 'clearable',
      title: { label: '可清除', tip: '是否可清除' },
      setter: 'BoolSetter',
    },

    // {
    //   name: 'name',
    //   title: 'name 属性',
    //   setter: 'StringSetter',
    // },
    {
      name: 'placeholder',
      title: '占位文本',
      defaultValue: '请选择',
      setter: 'StringSetter',
    },
    {
      type: 'group',
      title: '多选',
      items: [
        {
          name: 'multiple',
          title: '是否多选',
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'collapseTags',
          title: {
            label: '多选展示',
            tip: '多选时是否将选中值按文字的形式展示',
          },
          defaultValue: false,
          setter: 'BoolSetter',
          condition(props) {
            return props.multiple
          },
        },
        {
          name: 'multipleLimit',
          title: '选择上限',
          setter: 'NumberSetter',
          condition(props) {
            return props.multiple
          },
        },
      ],
    },

    {
      type: 'group',
      title: '搜索',
      items: [
        {
          name: 'filterable',
          title: { label: '可搜索', tip: '是否可搜索' },
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'filterMethod',
          title: '自定义搜索',
          setter: 'FunctionSetter',
          condition(props) {
            return props.filterable
          },
        },
        {
          name: 'allowCreate',
          title: { label: '创建新条目', tip: '是否允许用户创建新条目' },
          defaultValue: false,
          setter: 'BoolSetter',
          condition: (props) => {
            return props.filterable
          },
        },
        {
          name: 'remote',
          title: { label: '远程搜索', tip: '是否为远程搜索' },
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'remoteMethod',
          title: '远程搜索方法',
          setter: 'FunctionSetter',
          condition(props) {
            return props.remote
          },
        },
      ],
    },
    {
      type: 'group',
      title: '提示',
      items: [
        {
          name: 'loading',
          title: { label: '正在加载', tip: '是否正在加载' },
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'loadingText',
          title: '加载文字',
          defaultValue: '正在加载',
          setter: 'StringSetter',
        },
        {
          name: 'noMatchText',
          title: '搜索无匹配文字',
          defaultValue: '无数据',
          setter: 'StringSetter',
        },
        {
          name: 'noDataText',
          title: '结果为空文字',
          defaultValue: '无匹配数据',
          setter: 'StringSetter',
        },
      ],
      condition(props) {
        return props.filterable
      },
    },

    {
      type: 'group',
      title: '样式',
      items: [
        {
          name: 'width',
          title: '宽度',
          setter: 'StringSetter',
        },
        widthSize,
        {
          name: 'size',
          title: { label: '选择器尺寸', tip: '设置尺寸大小' },
          description: '尺寸',
          setter: {
            componentName: 'SelectSetter',
            props: {
              options: [
                {
                  title: '正常',
                  value: 'medium',
                },
                {
                  title: '中等',
                  value: 'small',
                },
                {
                  title: '迷你',
                  value: 'mini',
                },
              ],
            },
          },
        },
        {
          name: 'popperClass',
          title: '下拉框的类名',
          setter: 'StringSetter',
        },
        {
          name: 'popperAppendToBody',
          title: {
            label: '弹框插入body',
            tip: '是否将弹出框插入至 body 元素。在弹出框的定位出现问题时，可将该属性设置为 false',
          },
          defaultValue: true,
          setter: 'BoolSetter',
        },
      ],
    },

    {
      name: 'reserveKeyword',
      title: {
        label: '保留关键词',
        tip: '多选且可搜索时，是否在选中一个选项后保留当前的搜索关键词',
      },
      setter: 'BoolSetter',
    },
    {
      name: 'defaultFirstOption',
      title: {
        label: '第一项',
        tip: '在输入框按下回车，选择第一个匹配项。需配合 filterable 或 remote 使用',
      },
      setter: 'BoolSetter',
      condition(props) {
        return props.remote || props.filterable
      },
    },

    {
      name: 'automaticDropdown',
      title: {
        label: '点击弹出下拉框',
        tip: '对于不可搜索的 Select，是否在输入框获得焦点后自动弹出选项菜单',
      },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    tableValid,
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'prefix',
          title: '选择器头部',
          setter: 'SlotSetter',
          supportVariable: false,
        },
        {
          name: 'empty',
          title: '无选项列表',
          setter: 'SlotSetter',
          supportVariable: false,
        },
      ],
    },
  ],
  componentType: 'INPUT',
  formContext: 'SUBMIT',
  priority: 2,
  snippets: [
    {
      title: '下拉选择',
      screenshot: input1,
      schema: {
        componentName: 'LASelect',
        props: {
          options: [
            { value: '2', label: '上海', disabled: true },
            { value: '1', label: '北京' },
            { value: '4', label: '深圳' },
            { value: '3', label: '广州' },
          ],
          value: '1',
          clearable: true,
          popperAppendToBody: true,
          show: true,
        },
        children: [],
      },
    },
  ],
  configure: input.configure,
}
