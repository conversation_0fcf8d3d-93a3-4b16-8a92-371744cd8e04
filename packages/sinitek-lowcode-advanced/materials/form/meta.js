import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'
import form from 'sinitek-lowcode-materials/sinitek-ui/components/form/meta.js'
import {
  generateBindMethodTemplate,
  JSExpression,
  JSFunction,
} from 'sinitek-lowcode-shared'
export const formSpan = [
  { label: '一列', value: '1' },
  { label: '二列', value: '2' },
  { label: '三列', value: '3' },
  { label: '四列', value: '4' },
]
export default {
  componentName: 'LAForm',
  title: '高级表单',
  category: '表单',
  modelComponent: true,
  props: [
    {
      name: 'status',
      title: '状态',
      defaultValue: 'edit',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            { label: '只读', value: 'readonly' },
            { label: '编辑', value: 'edit' },
          ],
        },
      },
    },
    {
      name: 'model',
      title: { label: '数据对象', tip: '表单数据对象，这里需要使用绑定变量。' },
      setter: {
        componentName: 'BindStateSetter',
        props: {
          useExpression: true,
          filter: (_, list) => {
            return list.filter((item) => item?.type === 'object')
          },
        },
      },
      supportVariable: false,
    },
    {
      name: 'disabled',
      title: {
        label: '禁用表单',
        tip: '是否禁用该表单内的所有组件。若设置为 true，则表单内组件上的 disabled 属性不再生效',
      },
      setter: 'BoolSetter',
    },
    {
      type: 'group',
      title: '布局',
      items: [
        {
          name: 'column',
          title: '布局',
          defaultValue: '1',
          setter: {
            componentName: 'RadioGroupSetter',
            props: {
              options: formSpan,
            },
          },
        },
        {
          name: 'inline',
          title: { label: '行内表单模式', tip: '行内表单模式' },
          defaultValue: false,
          setter: 'BoolSetter',
          condition: (props) => props.column == 1,
        },
        {
          ...widthSize,
          condition: (props) => props.column == 1,
        },
        {
          name: 'size',
          title: { label: '大小尺寸', tip: '设置尺寸大小' },
          setter: {
            componentName: 'SelectSetter',
            props: {
              options: [
                {
                  title: '正常',
                  value: 'medium',
                },
                {
                  title: '中等',
                  value: 'small',
                },
                {
                  title: '迷你',
                  value: 'mini',
                },
              ],
            },
          },
        },
      ],
    },
    {
      title: '标签',
      type: 'group',
      items: [
        {
          name: 'labelPosition',
          title: {
            label: '位置',
            tip: '表单域标签的位置，如果值为 left 或者 right 时，则需要设置 label-width',
          },
          defaultValue: 'left',
          setter: {
            componentName: 'RadioGroupSetter',
            props: {
              options: [
                {
                  title: '左',
                  value: 'left',
                },
                {
                  title: '右',
                  value: 'right',
                },
                {
                  title: '上',
                  value: 'top',
                },
              ],
            },
          },
        },
        {
          name: 'showEllipsis',
          title: {
            label: '超出省略',
            tip: 'label 文字长度超过设置宽度时，显示省略号。xn-form 设置后，xn-form-item 都会继承该属性',
          },
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'labelWidth',
          title: {
            label: '宽度',
            tip: '传 false 当前页面不使用默认labelWidth值',
          },
          defaultValue: '100px',
          setter: 'StringSetter',
        },

        {
          name: 'labelSuffix',
          title: '后缀',
          setter: 'StringSetter',
        },
      ],
    },
    // {
    //   name: 'hideRequiredAsterisk',
    //   title: '星号隐藏',
    //   setter: 'BoolSetter',
    // },
    // {
    //   name: 'statusIcon',
    //   title: {
    //     label: '反馈图标',
    //     tip: '是否在输入框中显示校验结果反馈图标',
    //   },
    //   setter: 'BoolSetter',
    // },

    {
      type: 'group',
      title: '校验',
      items: [
        {
          name: 'rules',
          title: { label: '表单验证规则', tip: '表单验证规则' },
          setter: 'JSONSetter',
          documentUrl:
            'https://element.eleme.cn/#/zh-CN/component/form?prop=rules#form-attributes',
        },
        {
          name: 'show-message',
          title: { label: '显示校验', tip: '是否显示校验错误信息' },
          defaultValue: true,
          setter: 'BoolSetter',
        },
        {
          name: 'inline-message',
          title: { label: '行内展示', tip: '是否以行内形式展示校验信息' },
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'validateOnRuleChange',
          title: {
            label: 'rules改变验证',
            tip: '是否在 rules 属性改变后立即触发一次验证',
          },
          setter: 'BoolSetter',
        },
        {
          name: 'scrollToError',
          title: {
            label: '滚动到错误',
            tip: '校验失败后是否跳转到第一个错误字段',
          },
          setter: 'BoolSetter',
        },
      ],
    },
  ],
  // childConfig: {
  //   componentName: 'LAFormItem',
  //   title: '表单项',
  //   setter: {
  //     componentName: 'ArraySetter',
  //     props: {
  //       itemSetter: {
  //         componentName: 'ObjectSetter',
  //         props: {
  //           config: {
  //             items: formItem.props,
  //           },
  //         },
  //         initialValue: {
  //           componentName: 'LAFormItem',
  //           props: {
  //             field: 'INPUT',
  //             itemProps: {
  //               label: '输入框1',
  //             },
  //           },
  //         },
  //       },
  //     },
  //   },
  // },
  snippets: [
    {
      title: '高级表单',
      screenshot: require('./__screenshots__/image.svg'),
      pageData: (id, target) => {
        let str = []
        if (target.root?.mainModel?.modelCode) {
          str.push(
            `this.fetcher.main.create({data: this.state.form_${id}}).then(res => {`
          )
          str.push(`  console.log(res)`)
          str.push('})')
        }
        return {
          state: {
            [`form_${id}`]: {
              field1: '',
              field2: '',
              field3: '',
            },
          },
          methods: {
            [`submit_${id}`]: {
              type: JSFunction,
              value: `function submit_${id} () {
              this.refs.form_${id}.validate((valid) => {
                if (valid) {
                  ${str.join('\n')}
                  console.log(this.state.form_${id})
                } else {
                  console.log('error submit!!');
                  return false;
                }
              });
              
              }`,
            },
          },
        }
      },
      schema: (id) => {
        return {
          componentName: 'LAForm',
          ref: `form_${id}`,
          props: {
            labelPosition: 'right',
            status: 'edit',
            column: 1,
            widthSize: 'medium',
            model: {
              type: JSExpression,
              value: `this.state.form_${id}`,
            },
            labelWidth: '100px',
          },
          children: [
            {
              componentName: 'LAFormItem',
              LCBindState: `form_${id}.field1`,
              props: {
                field: 'INPUT',
                itemProps: {
                  label: '输入框',
                  required: true,
                  prop: 'field1',
                },
              },
            },
            {
              componentName: 'LAFormItem',
              LCBindState: `form_${id}.field2`,
              props: {
                field: 'SELECT',
                itemProps: {
                  label: '选择框',
                  required: true,
                  prop: 'field2',
                },
                fieldProps: {
                  options: [
                    { label: '区域一', value: '区域一' },
                    { label: '区域二', value: '区域二' },
                  ],
                },
              },
            },
            {
              componentName: 'LAFormItem',
              LCBindState: `form_${id}.field3`,
              props: {
                field: 'DATE',
                itemProps: {
                  label: '时间',
                  required: true,
                  prop: 'field3',
                },
                fieldProps: {
                  type: 'datetime',
                  valueFormat: 'yyyy-MM-dd HH:mm:ss',
                },
              },
            },
            {
              componentName: 'XnFormItem',
              children: [
                {
                  componentName: 'XnButton',
                  props: {
                    type: 'primary',
                    showLoading: false,
                  },
                  events: {
                    click: {
                      type: JSFunction,
                      value: generateBindMethodTemplate(`submit_${id}`),
                    },
                  },
                  children: [
                    {
                      componentName: 'LCText',
                      props: {
                        text: '立即创建',
                      },
                    },
                  ],
                },
                {
                  componentName: 'XnButton',
                  props: {
                    showLoading: false,
                  },
                  children: [
                    {
                      componentName: 'LCText',

                      props: {
                        text: '取消',
                      },
                    },
                  ],
                },
              ],
              props: {
                showDefaultLabelWidth: true,
              },
            },
          ],
        }
      },
    },
  ],
  configure: {
    supports: {
      ...form.configure.supports,
      methods: [
        {
          name: 'validate',
          description: '校验表单',
          example: 'validate((valid) => {})',
          args: [
            {
              name: 'callback',
              description: '回调',
              type: '(valid: boolean) => void',
              returnType: 'Promise<void>',
            },
          ],
        },
        {
          name: 'resetFields',
          description: '重置表单',
          example: 'resetFields()',
          returnType: 'Promise<void>',
        },
        {
          name: 'clearValidate',
          description: '清除校验',
          example: 'clearValidate()',
          args: [
            {
              name: 'props',
              description: '需要检验得字段',
              type: 'string[]',
              required: false,
            },
          ],
          returnType: 'Promise<void>',
        },
      ],
      states: [
        {
          name: 'isValid',
          example: 'refs.form.isValid',
          type: 'boolean',
          description: '表单校验是否通过',
        },
      ],
    },
    component: {
      isContainer: true,
      nestingRule: {
        childWhitelist: [
          'LAFormItem',
          'XnFormItem',
          'LARelaForm',
          'LAChildForm',
          'LAEmployee',
          'XnUpload',
          'XnFlex',
        ],
      },
    },
  },
}
