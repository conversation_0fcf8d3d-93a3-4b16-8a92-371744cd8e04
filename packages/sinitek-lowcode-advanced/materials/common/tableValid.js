export const valid = {
  type: 'group',
  title: '校验',
  items: [
    {
      name: 'required',
      title: '必填',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'rules',
      title: '验证规则',
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/form#form-attributes',
    },
  ],
}

export const tableValid = Object.assign(
  {
    condition: (_, target) => {
      // 找不到父级的FormItem，才行
      const el = target.findParent('ElFormItem')
      const xn = target.findParent('XnFormItem')
      const table = target.findParent('LATable')
      return el === null && xn === null && table && table?.props?.useForm
    },
  },
  valid
)
