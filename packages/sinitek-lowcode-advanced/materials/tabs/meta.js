import tabsSvg from './__screenshots__/tags.svg'
import tabpane from '../tab-pane/meta'
export default {
  componentName: 'LATabs',
  title: '标签页',
  category: '容器',
  props: [
    // {
    //   name: 'value',
    //   title: '选中选项卡的值',
    //   setter: 'StringSetter',
    // },
    {
      name: 'type',
      title: '风格类型',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: 'card',
              value: 'card',
            },
            {
              title: 'border-card',
              value: 'border-card',
            },
          ],
        },
      },
    },
    {
      name: 'addable',
      title: '标签可增加',
      setter: 'BoolSetter',
    },
    {
      name: 'editable',
      title: '标签可编辑',
      setter: 'BoolSetter',
    },
    {
      name: 'tabPosition',
      title: '选项卡所在位置',
      defaultValue: 'top',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '顶部',
              value: 'top',
            },
            {
              title: '靠右',
              value: 'right',
            },
            {
              title: '底部',
              value: 'bottom',
            },
            {
              title: '靠左',
              value: 'left',
            },
          ],
        },
      },
    },
    {
      name: 'stretch',
      title: '宽度自撑开',
      setter: 'BoolSetter',
    },
    {
      name: 'fullHeight',
      title: {
        label: '内容自撑开',
        tip: '高度占满父容器，溢出滚动',
      },
      setter: 'BoolSetter',
    },
    {
      name: 'tabs',
      title: {
        label: '动态标签',
        tip: '设置后不能使用slot',
      },
      setter: {
        componentName: 'ArraySetter',
        props: {
          itemSetter: {
            componentName: 'ObjectSetter',
            initialValue: { label: '选项卡' },
            props: {
              textField: 'label',
              config: {
                items: [
                  {
                    name: 'label',
                    title: '选项卡标题',
                    setter: 'StringSetter',
                  },
                  {
                    name: 'disabled',
                    title: { label: '禁用', tip: '是否禁用' },
                    setter: 'BoolSetter',
                  },
                  {
                    name: 'name',
                    title: '选项卡的值',
                    setter: 'StringSetter',
                  },
                  {
                    name: 'closable',
                    title: { label: '可关闭', tip: '标签是否可关闭' },
                    setter: 'BoolSetter',
                  },
                ],
              },
            },
          },
        },
      },
    },
  ],
  overrideProps: [
    { name: 'LCBindState', title: { label: '绑定状态', tip: '默认选中' } },
  ],
  childConfig: {
    componentName: 'LATabPane',
    title: '选项卡',
    setter: {
      componentName: 'ArraySetter',
      props: {
        itemSetter: {
          componentName: 'ObjectSetter',
          props: {
            textField: 'label',
            config: {
              items: [
                {
                  name: 'hidden',
                  title: '是否隐藏',
                  setter: 'BoolSetter',
                },
                ...tabpane.props,
              ],
            },
          },
          initialValue: {
            componentName: 'LATabPane',
            props: { label: '选项卡' },
            children: [
              {
                componentName: 'div',
                children: [],
              },
            ],
            // style: 'display:none',
          },
        },
      },
    },
  },
  snippets: [
    {
      title: '标签页',
      screenshot: tabsSvg,
      schema: {
        componentName: 'LATabs',
        props: {
          value: '0',
        },
        children: [
          {
            componentName: 'LATabPane',
            props: { label: 'tab1', name: '0' },
            // style: 'display:none',
            children: [],
          },
          {
            componentName: 'LATabPane',
            props: { label: 'tab2', name: '1' },
            // style: 'display:none',
            children: [],
          },
        ],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
      clickCapture: false,
      childWrapRenderer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'tab-click',
          description: 'tab 被选中时触发',
          template: 'function tabClick(tab) { console.log(tab);}',
        },
        {
          name: 'tab-remove',
          description: '点击 tab 移除按钮后触发',
          template: 'function tabRemove(tabName) { console.log(tabName);}',
        },
        {
          name: 'tab-add',
          description: '点击 tabs 的新增按钮后触发',
          template: 'function tabAdd() {}',
        },
        {
          name: 'edit',
          description: '点击 tabs 的新增按钮或 tab 被关闭后触发',
          template:
            'function edit(targetName, action) { console.log(targetName, action);}',
        },
      ],
    },
  },
}
