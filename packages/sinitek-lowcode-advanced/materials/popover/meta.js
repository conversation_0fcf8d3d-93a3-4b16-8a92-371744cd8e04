export default {
  componentName: 'LAPopover',
  title: '弹出框',
  category: '容器',
  priority: 2,
  props: [
    {
      name: 'showDesign',
      title: '在设计器中显示',
      setter: 'BoolSetter',
      defaultValue: true,
      supportVariable: false,
    },
    {
      name: 'trigger',
      title: '触发方式',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            { label: '点击', value: 'click' },
            { label: '悬停', value: 'hover' },
            { label: '手动', value: 'manual' },
            { label: '聚焦', value: 'focus' },
          ],
        },
      },
      defaultValue: 'click',
    },
    {
      name: 'title',
      title: '标题',
      setter: 'StringSetter',
    },
    {
      name: 'content',
      title: '显示内容',
      setter: 'StringSetter',
    },
    {
      name: 'width',
      title: '宽度',
      setter: 'StringSetter',
      defaultValue: '150',
    },
    {
      name: 'placement',
      title: '对齐位置',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            { label: '上中', value: 'top' },
            { label: '上左', value: 'top-start' },
            { label: '上右', value: 'top-end' },
            { label: '下中', value: 'bottom' },
            { label: '下左', value: 'bottom-start' },
            { label: '下右', value: 'bottom-end' },
            { label: '左中', value: 'left' },
            { label: '左上', value: 'left-start' },
            { label: '左下', value: 'left-end' },
            { label: '右中', value: 'right' },
            { label: '右上', value: 'right-start' },
            { label: '右下', value: 'right-end' },
          ],
        },
      },
      defaultValue: 'top',
    },
    {
      name: 'disabled',
      title: '是否禁用',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'offset',
      title: '偏移量',
      setter: 'NumberSetter',
      defaultValue: 0,
    },
    {
      name: 'transition',
      title: '动画名称',
      setter: 'StringSetter',
      defaultValue: 'fade-in-linear',
    },
    {
      name: 'visible-arrow',
      title: '显示箭头',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'popper-options',
      title: 'popper选项',
      setter: 'JSONSetter',
      defaultValue: { boundariesElement: 'body', gpuAcceleration: false },
    },
    {
      name: 'popper-class',
      title: '自定义类名',
      setter: 'StringSetter',
    },
    {
      name: 'open-delay',
      title: '延迟出现',
      setter: 'NumberSetter',
      defaultValue: 0,
    },
    {
      name: 'close-delay',
      title: '延迟关闭',
      setter: 'NumberSetter',
      defaultValue: 200,
    },
    {
      name: 'tabindex',
      title: 'tabindex',
      setter: 'NumberSetter',
      defaultValue: 0,
    },
    {
      name: 'default',
      title: '自定义内容',
      setter: {
        componentName: 'SlotSetter',
        props: {
          placeholder: '触发弹出框显示的元素',
        },
      },
      defaultValue: false,
    },
  ],
  overrideProps: [{ name: 'LCBindState', title: '是否显示' }],
  snippets: [
    {
      title: '弹出框',
      // screenshot: screenshots1,
      screenshot: require('./__screenshots__/image.svg'),
      prePreview: false,
      schema: {
        componentName: 'LAPopover',
        props: {
          title: '标题',
          content: '这是一段内容',
          placement: 'bottom-start',
          trigger: 'click',
          width: '200',
          showDesign: true,
        },
        children: [
          // {
          //   componentName: 'Slot',
          //   props: {
          //     name: 'reference',
          //     placeholder: '触发弹出框显示的元素',
          //   },
          //   children: [],
          // },
          {
            componentName: 'Slot',
            style: 'display:inline-block',
            props: {
              name: 'reference',
            },
            children: [],
          },
        ],
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
      nestingRule: {
        childWhitelist: ['Slot'],
      },
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: true,
      events: [
        {
          name: 'show',
          description: '显示时触发',
          template: 'function show() { console.log("显示")}',
        },
        {
          name: 'after-enter',
          description: '显示动画播放完毕后触发',
          template: 'function afterEnter() { console.log("显示动画播放完毕")}',
        },
        {
          name: 'hide',
          description: '隐藏时触发',
          template: 'function hide() { console.log("隐藏")}',
        },
        {
          name: 'after-leave',
          description: '隐藏动画播放完毕后触发',
          template: 'function afterLeave() { console.log("隐藏动画播放完毕")}',
        },
      ],
      state: [
        {
          name: 'value',
          description: '是否显示',
          type: 'boolean',
        },
      ],
    },
  },
}
