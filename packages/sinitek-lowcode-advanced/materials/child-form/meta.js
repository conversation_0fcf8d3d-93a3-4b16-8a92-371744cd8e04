import { ComponentName } from 'sinitek-lowcode-shared'
import { buttonType } from 'sinitek-lowcode-materials/element-ui/components/button/meta.js'
import { formSpan } from '../form/meta'

export default {
  componentName: 'LAChildForm',
  title: '子表单',
  category: '表单',
  modelComponent: true,
  props: [
    {
      name: 'status',
      title: '状态',
      defaultValue: 'edit',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            { label: '只读', value: 'readonly' },
            { label: '编辑', value: 'edit' },
          ],
        },
      },
    },
    {
      name: 'data-model',
      title: '数据模型',
      setter: {
        componentName: 'ModelSetter',
        props: {
          selectFields: true,
          refMain: true,
        },
      },
      supportVariable: false,
    },
    {
      type: 'group',
      title: '子表单操作设置',
      items: [
        {
          name: 'addLabel',
          title: '添加文本',
          setter: 'StringSetter',
        },
        {
          name: 'addButtonType',
          title: '添加按钮类型',
          setter: {
            componentName: 'SelectSetter',
            props: {
              options: buttonType,
            },
          },
          defaultValue: '添加',
        },
        {
          name: 'addIcon',
          title: '添加图标',
          setter: {
            componentName: 'IconSetter',
            props: {
              type: 'element-ui',
            },
          },
        },
      ],
    },
    {
      type: 'group',
      title: '操作项设置',
      items: [
        {
          name: 'useAdd',
          title: '显示新增',
          defaultValue: true,
          setter: 'BoolSetter',
        },
        {
          name: 'useDelete',
          title: '显示删除',
          defaultValue: true,
          setter: 'BoolSetter',
        },
      ],
    },
    {
      type: 'group',
      title: '表单项设置(父级是高级表单时使用)',
      condition: (_, target) => {
        return target.findParent('LAForm')
      },
      items: [
        {
          name: 'useFormItem',
          title: {
            label: '显示表单项',
            tip: '是否显示表单项，只能父级是高级表单时使用。',
          },
          setter: 'BoolSetter',
        },
        {
          name: 'span',
          title: '跨度',
          setter: {
            componentName: 'SelectSetter',
            props: {
              options: formSpan,
            },
          },
        },
        {
          title: '表单项',
          name: 'itemProps',
          condition: (props) => props.useFormItem,
          setter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: [
                  {
                    type: 'group',
                    title: '标签设置',
                    items: [
                      {
                        name: 'label',
                        title: '文本',
                        setter: 'StringSetter',
                      },
                      {
                        name: 'help',
                        title: {
                          label: '帮助图标',
                          tip: '设置帮助图标文本内容',
                        },
                        setter: 'StringSetter',
                      },
                      {
                        name: 'labelWidth',
                        title: '宽度',
                        setter: 'StringSetter',
                      },
                      {
                        name: 'labelSuffix',
                        title: '后缀',
                        setter: 'StringSetter',
                      },
                      {
                        name: 'showDefaultLabelWidth',
                        title: {
                          label: '默认宽度',
                          tip: '适用于没有“标签文本”，但仍需要“标签宽度”的场景，如表单最后的保存和重置按钮',
                        },
                        setter: 'BoolSetter',
                      },
                      {
                        name: 'showEllipsis',
                        title: '超出省略',
                        setter: 'BoolSetter',
                      },
                    ],
                  },
                  {
                    type: 'group',
                    title: '数据校验',
                    items: [
                      {
                        name: 'prop',
                        title: {
                          label: 'prop',
                          tip: '表单的“数据对象”里的属性，在使用 validate、resetFields 方法的情况下，该属性是必填的',
                        },
                        setter: 'StringSetter',
                      },
                      {
                        name: 'required',
                        title: '必填',
                        setter: 'BoolSetter',
                        defaultValue: false,
                      },
                      {
                        name: 'rules',
                        title: '验证规则',
                        setter: 'JSONSetter',
                        documentUrl:
                          'https://element.eleme.cn/#/zh-CN/component/form?prop=rules#form-item-attributes',
                      },
                      {
                        name: 'formRules',
                        title: '表单验证规则',
                        setter: 'JSONSetter',
                        documentUrl:
                          'https://element.eleme.cn/#/zh-CN/component/form?prop=rules#form-attributes',
                      },
                      {
                        name: 'error',
                        title: {
                          label: '错误信息',
                          tip: '设置该值会使表单验证状态变为error，并显示该错误信息',
                        },
                        setter: 'StringSetter',
                      },
                      {
                        name: 'showMessage',
                        title: {
                          label: '是否显示',
                          tip: '是否显示校验错误信息',
                        },
                        setter: 'BoolSetter',
                        defaultValue: true,
                      },
                    ],
                  },
                ],
              },
            },
          },
        },
      ],
    },
  ],
  overrideProps: [
    {
      name: 'LCBindState',
      title: { label: '绑定字段', tip: '这里需要绑定数组类型的' },
    },
  ],
  snippets: [
    {
      title: '子表单',
      screenshot: require('./__screenshots__/image.svg'),
      schema: {
        componentName: 'LAChildForm',
        props: {
          status: 'edit',
          addLabel: '添加一项',
          addButtonType: 'text',
          addIcon: 'el-icon-plus',
          itemProps: {
            label: '子表单',
          },
        },
        children: [
          {
            componentName: 'LAFormItem',
            props: {
              field: ComponentName.INPUT,
              itemProps: {
                label: '输入框1',
                prop: 'input1',
              },
            },
          },
          {
            componentName: 'LAFormItem',
            props: {
              field: ComponentName.INPUT,
              itemProps: {
                label: '输入框2',
                prop: 'input2',
              },
            },
          },
          {
            componentName: 'LAFormItem',
            props: {
              field: ComponentName.INPUT,
              itemProps: {
                label: '输入框3',
                prop: 'input3',
              },
            },
          },
        ],
      },
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: false,
      events: [
        {
          name: 'input',
          description: '在值改变时触发',
          template: 'function input(value) { console.log(value);}',
        },
        {
          name: 'add',
          description: '点击新增时触发',
          template: 'function add() { }',
        },
        {
          name: 'remove',
          description: '点击删除时触发',
          template: 'function remove(item,i) { console.log(item,i);}',
        },
      ],
      methods: [
        {
          name: 'validate',
          description: '校验表单',
          example: 'validate((valid) => {})',
          args: [
            {
              name: 'callback',
              description: '回调',
              type: '(valid: boolean) => void',
            },
          ],
        },
        {
          name: 'resetFields',
          description: '重置表单',
          example: 'resetFields()',
        },
        {
          name: 'clearValidate',
          description: '清除校验',
          example: 'clearValidate()',
          args: [
            {
              name: 'props',
              description: '需要检验得字段',
              type: 'string[]',
            },
          ],
        },
      ],
    },
    component: {
      isContainer: true,
      nestingRule: {
        childWhitelist: ['LAFormItem', 'LARelaForm'],
      },
    },
  },
}
