// import tabpane from './__screenshots__/tab-pane.png'
export default {
  componentName: 'LATabPane',
  title: '标签页选项卡',
  props: [
    {
      name: 'label',
      title: '选项卡标题',
      setter: 'StringSetter',
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      setter: 'BoolSetter',
    },
    {
      name: 'name',
      title: '选项卡的值',
      setter: 'StringSetter',
    },
    {
      name: 'closable',
      title: { label: '可关闭', tip: '标签是否可关闭' },
      setter: 'BoolSetter',
    },
    {
      name: 'lazy',
      title: '懒加载',
      setter: 'BoolSetter',
    },
  ],
  // snippets: [
  //   {
  //     title: '标签页选项卡',
  //     screenshot: tabpane,
  //     schema: {
  //       componentName: 'LATabPane',
  //       props: {
  //         label: '多选框文本',
  //         value: '',
  //       },
  //       children: [],
  //     },
  //   },
  // ],
  configure: {
    component: {
      isContainer: true,
      disableBehaviors: ['remove', 'copy', 'move'],
      clickCapture: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: false,
      supportBindState: false,
    },
  },
}
