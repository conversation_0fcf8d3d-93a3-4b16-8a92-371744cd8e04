import col from './col-meta'

import snippets from './snippets'

import { buttonType } from 'sinitek-lowcode-materials/element-ui/components/button/meta.js'

export default {
  componentName: 'LATable',
  title: '表格',
  category: '信息展示',
  props: [
    {
      name: 'useForm',
      title: {
        label: '支持表单验证',
        tip: '开启后可以给表单列字段里的输入组件进行表单验证。',
      },
      setter: 'BoolSetter',
    },
    {
      type: 'group',
      title: '排序',
      items: [
        {
          name: 'enableSort',
          title: {
            label: '高级排序',
            tip: '还需要开启列的排序，表格会根据排序字段进行排序。使用refreshData查询数据会清除排序。',
          },
          setter: 'BoolSetter',
          supportVariable: false,
        },
        {
          name: 'defaultOrder',
          title: {
            label: '默认排序',
            tip: '针对默认的排序功能，不影响高级排序',
          },
          setter: {
            componentName: 'StringSetter',
            props: {
              placeholder: 'eg: name:desc',
            },
          },
        },
        {
          name: 'defaultOrderName',
          title: {
            label: '默认排序字段',
            tip: '后端排序字段和列字段prop不一致时使用',
          },
          setter: {
            componentName: 'StringSetter',
            props: {
              placeholder: 'eg: name:desc',
            },
          },
        },
      ],
    },
    {
      type: 'group',
      title: '数据源',
      items: [
        {
          name: 'data',
          title: {
            label: '静态数据源',
            tip: '静态数据源,需要绑定数据列表。',
          },
          setter: 'JSONSetter',
        },
        {
          name: 'datasource',
          title: {
            label: '模型数据源',
            tip: '选择模型数据源',
          },
          setter: {
            componentName: 'BindStateSetter',
            props: {
              useDatasource: true,
              defaultLabel: '选择模型',
              hideTip: true,
              emptyText: '没有数据源',
            },
          },
        },
        {
          name: 'url',
          title: { label: '请求地址', tip: '表格数据查询后端接口 url' },
          setter: 'StringSetter',
        },
        {
          name: 'postQuery',
          title: 'post请求',
          setter: 'BoolSetter',
          condition: (props) => {
            return !!props.url
          },
        },
        {
          name: 'querymodel',
          title: {
            label: '查询条件',
            tip: '查询条件集合，根据集合中的字段查询筛选',
          },
          setter: 'JSONSetter',
          documentUrl:
            'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=querymodel#xn-table',
          condition: (props) => {
            return !!props.url
          },
        },
        {
          name: 'loadingMask',
          title: '显示 loading',
          setter: 'BoolSetter',
          defaultValue: true,
          condition: (props) => {
            return !!props.url
          },
        },
        {
          name: 'allowInit',
          title: '是否初始化',
          setter: 'BoolSetter',
          defaultValue: true,
          condition: (props) => {
            return !!props.url
          },
        },
        {
          name: 'customSourceData',
          title: 'url请求返回数据的解析路径',
          setter: 'StringSetter',
          defaultValue: 'data.datalist',
          condition: (props) => {
            return !!props.url
          },
        },
        {
          name: 'customSourceTotal',
          title: 'url请求返回数据条数的解析路径	',
          setter: 'StringSetter',
          defaultValue: 'data.totalsize',
          condition: (props) => {
            return !!props.url
          },
        },
        {
          name: 'customAfterQueryPath',
          title: '请求的解析路径',
          setter: 'StringSetter',
          defaultValue: 'data',
          condition: (props) => {
            return !!props.url
          },
        },
      ],
    },

    {
      type: 'group',
      title: '分页',
      items: [
        {
          name: 'showPagination',
          title: '表格分页',
          setter: 'BoolSetter',
          defaultValue: true,
        },
        {
          name: 'pageCountLimit',
          title: '表格最大限制页数',
          setter: 'NumberSetter',
        },
        {
          name: 'pageSize',
          title: '每页显示条数',
          setter: 'NumberSetter',
          defaultValue: 20,
        },
        {
          name: 'pageSizes',
          title: '每页显示条数集合',
          setter: 'JSONSetter',
          documentUrl:
            'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=pageSizes#xn-table',
        },
        {
          name: 'dataTotal',
          title: '数据总数',
          setter: 'NumberSetter',
        },
        {
          name: 'pagerCount',
          title: '页码按钮的数量',
          setter: 'NumberSetter',
          defaultValue: 7,
        },
      ],
    },
    {
      type: 'group',
      title: '样式',
      items: [
        {
          name: 'width',
          title: '表格宽度',
          setter: 'StringSetter',
        },
        {
          name: 'height',
          title: '表格高度',
          setter: 'StringSetter',
        },
        {
          name: 'border',
          title: '边框',
          setter: 'BoolSetter',
          defaultValue: true,
        },
        {
          name: 'rowClassName',
          title: '行的样式',
          setter: 'JSONSetter',
          documentUrl:
            'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=rowClassName#xn-table',
        },
        {
          name: 'stripe',
          title: '斑马行',
          setter: 'BoolSetter',
        },
        {
          name: 'highlightCurrentRow',
          title: '高亮当前行',
          setter: 'BoolSetter',
        },
        {
          name: 'maxHeight',
          title: '表格最大高度',
          setter: 'StringSetter',
        },
        {
          name: 'scrollCache',
          title: '缓存滚动条高度',
          setter: 'BoolSetter',
        },
        {
          name: 'spanMethod',
          title: '合并行列计算',
          setter: 'FunctionSetter',
        },
      ],
    },
    {
      type: 'group',
      title: '导出',
      items: [
        {
          name: 'allowExport',
          title: '允许导出',
          setter: 'BoolSetter',
          defaultValue: false,
        },
        {
          name: 'exportAll',
          title: '关闭部分导出',
          setter: 'BoolSetter',
          defaultValue: false,
          condition(props) {
            return props.allowExport
          },
        },
        {
          name: 'exportName',
          title: '导出文件名',
          setter: 'StringSetter',
          condition(props) {
            return props.allowExport
          },
        },
        {
          name: 'exportMaxRow',
          title: '导出最大行数',
          setter: 'NumberSetter',
          condition(props) {
            return props.allowExport
          },
        },
        {
          name: 'isProgress',
          title: '导出进度条',
          setter: 'BoolSetter',
          defaultValue: true,
          condition(props) {
            return props.allowExport
          },
        },
        {
          name: 'customExportAction',
          title: {
            label: 'bean名称',
            tip: '表格自定义导出逻辑时，对应后端自定义导出类的bean名称',
          },
          setter: 'StringSetter',
          condition(props) {
            return props.allowExport
          },
        },
      ],
    },
    // {
    //   name: 'defaultOrder',
    //   title: '排序规则',
    //   setter: 'StringSetter',
    // },
    // {
    //   name: 'afterDrag',
    //   title: 'create结束后的回调',
    //   setter: 'JSONSetter',
    // },

    // {
    //   name: 'showFilter',
    //   title: {
    //     label: '开启列筛选',
    //     tip: '是否显示筛选条件，开始后search插槽失效。\n filterModel/prop存在于querymodel决定是否展示筛选',
    //   },
    //   setter: 'BoolSetter',
    // },
    // {
    //   name: 'showConditionalView',
    //   title: '开启条件视图',
    //   setter: 'BoolSetter',
    // },
    // {
    //   name: 'useVirtual',
    //   title: '虚拟表格',
    //   setter: 'BoolSetter',
    // },
    // {
    //   name: 'rowHeight',
    //   title: '虚拟表格行高',
    //   setter: 'FunctionSetter',
    // },

    {
      type: 'group',
      title: '工具栏配置',
      items: [
        {
          name: 'dynamicCols',
          title: '开启工具栏',
          setter: 'BoolSetter',
          defaultValue: true,
        },
        {
          name: 'useDynamicCols',
          title: '自定义表格列',
          setter: 'BoolSetter',
          defaultValue: true,
        },
        {
          // 自定义工具栏
          name: 'customToolbars',
          title: '工具栏按钮',
          setter: {
            componentName: 'ArraySetter',
            props: {
              itemSetter: {
                componentName: 'ObjectSetter',
                props: {
                  textField: 'label',
                  config: {
                    items: [
                      {
                        name: 'icon',
                        title: { label: '图标类名', tip: '图标类名' },
                        setter: {
                          componentName: 'IconSetter',
                          props: {
                            type: 'element-ui',
                          },
                        },
                      },
                      {
                        name: 'label',
                        title: '按钮名称',
                        setter: 'StringSetter',
                      },
                      {
                        name: 'disabled',
                        title: '禁用',
                        setter: 'BoolSetter',
                      },
                      {
                        name: 'hidden',
                        title: '隐藏',
                        setter: 'BoolSetter',
                      },
                      {
                        name: 'showLoading',
                        title: '点击显示加载',
                        setter: 'BoolSetter',
                        defaultValue: true,
                      },
                      {
                        name: 'handler',
                        title: '定义事件名',
                        setter: {
                          componentName: 'FunctionSetter',
                          props: {
                            template: 'function click(resolve, reject) {}',
                          },
                        },
                      },
                      {
                        // 自定义工具栏
                        name: 'children',
                        title: '子菜单',
                        setter: {
                          componentName: 'ArraySetter',
                          props: {
                            itemSetter: {
                              componentName: 'ObjectSetter',
                              props: {
                                textField: 'label',
                                config: {
                                  items: [
                                    {
                                      name: 'icon',
                                      title: {
                                        label: '饿了么图标',
                                        tip: '图标类名',
                                      },
                                      setter: {
                                        componentName: 'IconSetter',
                                        props: {
                                          type: 'element-ui',
                                        },
                                      },
                                    },
                                    {
                                      name: 'label',
                                      title: '按钮名称',
                                      setter: 'StringSetter',
                                    },
                                    {
                                      name: 'disabled',
                                      title: '禁用',
                                      setter: 'BoolSetter',
                                    },
                                    {
                                      name: 'hidden',
                                      title: '隐藏',
                                      setter: 'BoolSetter',
                                    },
                                    {
                                      name: 'handler',
                                      title: '定义事件名',
                                      setter: {
                                        componentName: 'FunctionSetter',
                                        props: {
                                          template: 'function click() {}',
                                        },
                                      },
                                    },
                                  ],
                                },
                              },
                              initialValue: {
                                type: 'primary',
                                label: '按钮',
                              },
                            },
                          },
                        },
                      },
                    ],
                  },
                },
                initialValue: {
                  type: 'primary',
                  label: '按钮',
                },
              },
            },
          },
        },
        {
          name: 'toolbarSlot',
          title: '自定义插槽',
          setter: 'SlotSetter',
        },
      ],
    },
    {
      type: 'group',
      title: '选择列配置',
      items: [
        {
          name: 'useSelectionCol',
          title: '开启选择列',
          setter: 'BoolSetter',
        },
        {
          name: 'selectionType',
          title: '选择列类型',
          defaultValue: 'checkbox',
          condition: (props) => {
            return props.useSelectionCol
          },
          setter: {
            componentName: 'RadioGroupSetter',
            props: {
              options: [
                {
                  title: '多选',
                  value: 'checkbox',
                },
                {
                  title: '单选',
                  value: 'radio',
                },
              ],
            },
          },
        },
        {
          name: 'selectionFixed',
          title: '固定列',
          setter: 'BoolSetter',
          condition: (props) => {
            return props.useSelectionCol
          },
        },
        {
          name: 'selectionSelectable',
          title: {
            label: '是否可以选中',
            tip: '返回false则不可选中，禁用状态。',
          },
          setter: {
            componentName: 'FunctionSetter',
            props: {
              template: 'function selectable(row) { }',
            },
          },
          condition: (props) => {
            return (
              props.useSelectionCol &&
              (props.selectionType === 'checkbox' || !props.selectionType)
            )
          },
        },
        {
          name: 'selectionTooltip',
          title: {
            label: '气泡',
            tip: '必须是当前行数据中存在的字段',
          },
          setter: 'StringSetter',
          condition: (props) => {
            return (
              props.useSelectionCol &&
              (props.selectionType === 'checkbox' || !props.selectionType)
            )
          },
        },
        {
          name: 'selectionCrossPage',
          title: {
            label: '跨页选中',
            tip: '行数据中必须有id字段, 需要调用clearSelection来清除选中状态',
          },
          setter: 'BoolSetter',
          condition: (props) => {
            return (
              props.useSelectionCol &&
              (props.selectionType === 'checkbox' || !props.selectionType)
            )
          },
        },
      ],
    },
    {
      type: 'group',
      title: '序号列配置',
      items: [
        {
          name: 'useNumberCol',
          title: '开启序号列',
          setter: 'BoolSetter',
        },
        {
          name: 'numberFixed',
          title: '固定列',
          setter: 'BoolSetter',
          condition: (props) => {
            return props.useNumberCol
          },
        },
        {
          name: 'numberCrossPage',
          title: {
            label: '跨页序号',
            tip: '开启后根据页数累加序号',
          },
          setter: 'BoolSetter',
          condition: (props) => {
            return props.useNumberCol
          },
        },
      ],
    },

    {
      type: 'group',
      title: '操作列配置',
      items: [
        {
          name: 'useActionCol',
          title: '开启操作列',
          setter: 'BoolSetter',
        },
        {
          name: 'actionTitle',
          title: '操作列标题',
          condition: (props) => {
            return props.useActionCol
          },
          setter: 'StringSetter',
        },
        {
          name: 'actionWidth',
          title: '操作列宽度',
          condition: (props) => {
            return props.useActionCol
          },
          setter: 'StringSetter',
        },
        {
          name: 'actions',
          title: '操作列按钮',
          condition: (props) => {
            return props.useActionCol
          },
          setter: {
            componentName: 'ArraySetter',
            props: {
              itemSetter: {
                componentName: 'ObjectSetter',
                props: {
                  textField: 'text',
                  config: {
                    items: [
                      {
                        name: 'type',
                        title: '按钮类型',
                        condition: (props, target) => {
                          return !target.props.useActionText
                        },
                        setter: [
                          {
                            componentName: 'SelectSetter',
                            props: {
                              options: buttonType,
                            },
                          },
                          {
                            componentName: 'FunctionSetter',
                            props: {
                              template: 'function handler(scope) { }',
                            },
                          },
                        ],
                        supportVariable: false,
                      },
                      {
                        name: 'text',
                        title: '按钮文本',
                        setter: [
                          'StringSetter',
                          {
                            componentName: 'FunctionSetter',
                            props: {
                              template: 'function handler(scope) { }',
                            },
                          },
                        ],
                        supportVariable: false,
                      },
                      {
                        name: 'disabled',
                        title: '是否禁用',
                        setter: [
                          'BoolSetter',
                          {
                            componentName: 'FunctionSetter',
                            props: {
                              template: 'function handler(scope) { }',
                            },
                          },
                        ],
                        supportVariable: false,
                      },
                      {
                        name: 'loading',
                        title: '是否加载',
                        setter: [
                          'BoolSetter',
                          {
                            componentName: 'FunctionSetter',
                            props: {
                              template: 'function handler(scope) { }',
                            },
                          },
                        ],
                        supportVariable: false,
                      },
                      {
                        name: 'hidden',
                        title: '是否隐藏',
                        setter: [
                          'BoolSetter',
                          {
                            componentName: 'FunctionSetter',
                            props: {
                              template: 'function handler(scope) { }',
                            },
                          },
                        ],
                        supportVariable: false,
                      },
                      {
                        name: 'handler',
                        title: {
                          label: '按钮事件',
                          tip: '在开启了loading后可以调用第一个参数对象里的done方法来关闭当前加载。或者return promise自动关闭。推荐使用promise',
                        },
                        setter: {
                          componentName: 'FunctionSetter',
                          props: {
                            template: 'function handler(scope) { }',
                          },
                        },
                        supportVariable: false,
                      },
                    ],
                  },
                },
                initialValue: {
                  text: '按钮',
                  type: 'primary',
                  hidden: false,
                  disabled: false,
                },
              },
            },
          },
        },
        {
          name: 'actionFixed',
          title: '是否固定',
          condition: (props) => {
            return props.useActionCol
          },
          setter: 'BoolSetter',
        },
      ],
    },
    {
      type: 'group',
      title: '树形配置',
      items: [
        {
          name: 'defaultExpandAll',
          title: {
            tip: '是否默认展开所有行，当 Table 包含展开行存在或者为树形表格时有效',
            label: '默认展开',
          },
          setter: 'BoolSetter',
        },
        {
          name: 'expandRowKeys',
          title: {
            label: '展开的行',
            tip: '可以通过该属性设置 Table 目前的展开行，需要设置 row-key 属性才能使用，该属性为展开行的 keys 数组。',
          },
          setter: 'JSONSetter',
          documentUrl:
            'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=expandRowKeys#xn-table',
        },
        {
          name: 'rowKey',
          title: {
            label: '行key',
            tip: '行数据的 Key，用来优化 Table 的渲染；在使用 reserve-selection 功能与显示树形数据时，该属性是必填的。类型为 String 时，支持多层访问：user.info.id，但不支持 user.info[0].id，此种情况请使用 Function。',
          },
          setter: [
            'StringSetter',
            {
              componentName: 'FunctionSetter',
              props: {
                template: 'function rowKey(row) { return row.id }',
              },
            },
          ],
        },
        {
          name: 'treeProps',
          title: '字段配置',
          setter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: [
                  {
                    name: 'children',
                    title: '子节点字段',
                    defaultValue: 'children',
                    setter: 'StringSetter',
                  },
                  {
                    name: 'hasChildren',
                    title: '是否有子节点',
                    defaultValue: 'hasChildren',
                    setter: 'StringSetter',
                  },
                ],
              },
            },
          },
        },
        {
          name: 'indent',
          title: '缩进',
          defaultValue: 16,
          setter: 'NumberSetter',
        },
        {
          name: 'lazy',
          title: '懒加载',
          setter: 'BoolSetter',
        },
        {
          name: 'load',
          title: '加载方法',
          setter: {
            componentName: 'FunctionSetter',
            props: {
              template: 'function load(row, treeNode, resolve) { }',
            },
          },
          condition: (props) => {
            return props.lazy
          },
        },
      ],
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        // {
        //   name: 'toolbar',
        //   title: '表格工具栏插槽',
        //   setter: 'SlotSetter',
        // },
        {
          name: 'empty',
          title: '表格无数据插槽',
          setter: 'SlotSetter',
        },
        {
          name: 'append',
          title: '插入最后一列',
          setter: 'SlotSetter',
        },
        // sinitek-ui没有渲染，暂时不支持
        // {
        //   name: 'export',
        //   title: '自定义导出列',
        //   setter: 'SlotSetter',
        // },
        {
          name: 'search',
          title: '自定义查询条件',
          setter: 'SlotSetter',
        },
        // {
        //   name: 'conditional-view',
        //   title: '条件视图作用域插槽',
        //   setter: 'SlotSetter',
        //   supportVariable: false,
        // },
      ],
    },
  ],
  childConfig: {
    componentName: 'XnColAdvanced',
    title: '表格列',
    setter: {
      componentName: 'ArraySetter',
      props: {
        itemSetter: {
          componentName: 'ObjectSetter',
          props: {
            textField: 'label',
            config: {
              items: col.props,
            },
          },
          initialValue: {
            componentName: 'XnColAdvanced',
            props: {
              label: '表格列',
              prop: 'prop',
            },
            children: [],
          },
        },
      },
    },
  },
  priority: 2,
  snippets,
  configure: {
    component: {
      isContainer: true,
      childWrapRenderer: false,
      nestingRule: {
        allowInsert(data) {
          return data.source === 'model'
        },
      },
      getAIParams: (state) => {
        return {
          componentType: 'TABLE_COL',
          formContext: 'LIST',
          componentTitle: state.current.props.label,
        }
      },
      setAIParams: (arr) => {
        const result = {
          props: {},
        }
        arr.forEach((e) => {
          result.props[e.code] = e.value
        })
        return result
      },
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'afterQuery',
          description: '查询之后的回调事件，返回查到的datalist',
          template: 'function afterQuery(data) { console.log(data);}',
        },
        {
          name: 'select-callback',
          description: '行部分选中和全部选中回调事件',
          template:
            'function selectCallback(selection, row) { console.log(selection, row);}',
        },
        {
          name: 'size-change',
          description: '每页条数变更的回调事件',
          template:
            'function sizeChange(index, size) { console.log(index, size);}',
        },
        {
          name: 'current-page-change',
          description: '页码变更的回调事件',
          template:
            'function currentPageChange(index, size) { console.log(index, size);}',
        },
        {
          name: 'addHandler',
          description: 'toolbars的新增按钮回调事件',
          template: 'function addHandler() { }',
        },
        {
          name: 'deleteHandler',
          description: 'toolbars的删除按钮回调事件',
          template: 'function deleteHandler() { }',
        },
        {
          name: 'importHandler',
          description: 'toolbars的导入按钮回调事件',
          template: 'function importHandler() { }',
        },
      ],
      methods: [
        {
          name: 'refreshData',
          description: '刷新数据',
          example: 'refreshData(clearSelection)',
          args: [
            {
              name: 'clearSelection',
              type: 'boolean',
              description: '是否清除选中状态,默认是true',
              defaultValue: 'true',
              required: false,
            },
          ],
        },
        {
          name: 'clearSelection',
          description: '清除选中状态',
          example: 'clearSelection()',
        },
        {
          name: 'getSelection',
          description: '获取选中状态',
          example: 'getSelection()',
          returnType: 'Record<string, any>[]',
        },
        {
          name: 'initToolbars',
          description: '初始化表格工具栏',
          example: 'initToolbars()',
        },
        {
          name: 'initTableHeight',
          description: '初始化表格高度',
          example: 'initTableHeight()',
        },
        {
          name: 'clearTableSort',
          description: '清除点击排序后的排序信息',
          example: 'clearTableSort()',
        },
        {
          name: 'exportExcel',
          description: '导出方法',
          example: 'exportExcel(0)',
          args: [
            {
              name: 'type',
              type: '0|1',
              description: '传0全部导出，传1部分导出',
              required: false,
            },
          ],
        },
        {
          name: 'handleCloseFilter',
          description: 'handleCloseFilter',
          example: 'handleCloseFilter()',
        },
        {
          name: 'toggleRowSelection',
          description: '切换选中状态',
          example: 'toggleRowSelection(row, true)',
          args: [
            {
              name: 'row',
              type: 'any[]|Object',
              description: '行数据',
              required: false,
            },
            {
              name: 'selected',
              type: 'boolean',
              description: '选中状态',
              required: false,
            },
          ],
        },
        {
          name: 'toggleAllSelection',
          description: '用于多选表格，切换所有行的选中状态',
          example: 'toggleAllSelection()',
        },
        // {
        //   name: 'toggleRowExpansion',
        //   description: '用于多选表格，切换所有行的选中状态',
        //   example: 'toggleRowExpansion()',
        // },
        {
          name: 'setCurrentRow',
          description:
            '用于单选表格，设定某一行为选中行，如果调用时不加参数，则会取消目前高亮行的选中状态。',
          example: 'setCurrentRow(row)',
          args: [
            {
              name: 'row',
              type: 'Object',
              description: '行数据',
              required: false,
            },
          ],
        },
        {
          name: 'clearSort',
          description: '用于清空排序条件，数据会恢复成未排序的状态',
          example: 'clearSort()',
        },
        {
          name: 'clearFilter',
          description:
            '不传入参数时用于清空所有过滤条件，数据会恢复成未过滤的状态，也可传入由columnKey组成的数组以清除指定列的过滤条件',
          example: 'clearFilter(columnKey)',
          args: [
            {
              name: 'columnKey',
              type: 'string',
              description: '列key',
              required: false,
            },
          ],
        },
        {
          name: 'doLayout',
          description:
            '对 Table 进行重新布局。当 Table 或其祖先元素由隐藏切换为显示时，可能需要调用此方法',
          example: 'doLayout()',
        },
        {
          name: 'sort',
          description: '手动对 Table 进行排序',
          example: 'sort("prop","ascending")',
          args: [
            {
              name: 'prop',
              type: 'string',
              description: '列名',
            },
            {
              name: 'order',
              type: `'ascending'|'descending'`,
              description: '排序顺序',
            },
          ],
        },
        {
          name: 'validate',
          description: '校验表单',
          example: 'validate((valid) => {})',
          args: [
            {
              name: 'callback',
              description: '回调',
              type: '(valid: boolean) => void',
            },
          ],
        },
        {
          name: 'clearValidate',
          description: '清除校验',
          example: 'clearValidate()',
          args: [
            {
              name: 'props',
              description: '需要检验得字段',
              type: 'string[]',
              required: false,
            },
          ],
        },
        {
          name: 'resetField',
          description: '重置字段',
          example: 'resetField()',
        },
        {
          name: 'getModelData',
          description: '获取模型数据源',
          example: 'getModelData()',
          returnType: 'Promise<any>',
        },
        {
          name: 'showLoading',
          description: '显示加载',
          example: 'showLoading()',
          returnType: 'void',
        },
        {
          name: 'hideLoading',
          description: '隐藏加载',
          example: 'hideLoading()',
          returnType: 'void',
        },
        {
          name: 'tableReRender',
          description: '重新渲染表格',
          example: 'tableReRender()',
          returnType: 'void',
        },
        {
          name: 'setLoading',
          description: '设置表格加载状态',
          example: 'setLoading(true)',
          returnType: 'void',
          args: [
            {
              name: 'loading',
              description: '是否加载',
              type: 'boolean',
            },
          ],
        },
      ],
      states: [
        {
          name: 'selections',
          description: '所有选中的数据, 返回选中的行数据列表',
          example: 'refs.table.selections',
          type: 'Record<string, any>[]',
        },
        {
          name: 'isLoading',
          description: '当前表格是否在加载中，当使用url时不起作用',
          example: 'refs.table.isLoading',
          type: 'boolean',
        },
      ],
    },
  },
}
