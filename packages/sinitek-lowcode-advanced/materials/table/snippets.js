export default [
  {
    title: '高级表格',
    screenshot: require('./__screenshots__/image.svg'),
    schema: {
      componentName: 'LATable',
      props: {
        dynamicCols: true,
        border: true,
        allowInit: true,
        isProgress: true,
        showPagination: true,
        loadingMask: false,
        actionTitle: '操作',
        actionWidth: '150',
        actions: [
          { text: '编辑', type: 'primary', hidden: false, disabled: false },
          { text: '删除', type: 'primary', hidden: false, disabled: false },
        ],
      },
      children: [
        {
          componentName: 'XnColAdvanced',
          props: { prop: 'date', label: '日期' },
          children: [],
        },
        {
          componentName: 'XnColAdvanced',
          props: { prop: 'name', label: '姓名' },
          children: [],
        },
        {
          componentName: 'XnColAdvanced',
          props: { prop: 'address', label: '地址' },
          children: [],
        },
      ],
    },
  },
]
