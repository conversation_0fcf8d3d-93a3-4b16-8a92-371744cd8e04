import input from 'sinitek-lowcode-materials/element-ui/components/checkbox-group/meta.js'
import input1 from './__screenshots__/image.svg'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'LACheckboxGroup',
  title: '高级多选框',
  category: '信息输入',
  props: [
    ...input.props,
    {
      name: 'type',
      title: '类型',
      defaultValue: 'checkbox',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              title: '基础',
              value: 'checkbox',
            },
            {
              title: '按钮',
              value: 'button',
            },
          ],
        },
      },
    },
    {
      name: 'options',
      title: '选项',
      setter: {
        componentName: 'ArraySetter',
        props: {
          itemSetter: {
            componentName: 'ObjectSetter',
            props: {
              textField: 'label',
              config: {
                items: [
                  {
                    name: 'value',
                    title: '值',
                    setter: 'StringSetter',
                  },
                  {
                    name: 'label',
                    title: '文本',
                    setter: 'StringSetter',
                  },
                  {
                    name: 'disabled',
                    title: '禁用',
                    setter: 'BoolSetter',
                  },
                  {
                    name: 'hidden',
                    title: '隐藏',
                    setter: 'BoolSetter',
                  },
                  {
                    name: 'checked',
                    title: '当前是否勾选',
                    setter: 'BoolSetter',
                  },
                  {
                    name: 'indeterminate',
                    title: 'indeterminate',
                    setter: 'BoolSetter',
                  },
                  {
                    name: 'border',
                    title: '边框',
                    setter: 'BoolSetter',
                    condition: (props, target) => {
                      if (target.componetName === 'LAFormItem') {
                        return target.props.fieldProps.type !== 'button'
                      }
                      return target.getPropValue('type') !== 'button'
                    },
                  },
                ],
              },
            },
            initialValue: { label: '1', text: '选项1' },
          },
        },
      },
    },
  ],
  componentType: 'CheckboxGroup',
  formContext: 'SUBMIT',
  priority: 2,
  snippets: [
    {
      title: '高级多选框',
      screenshot: input1,
      schema: {
        componentName: 'LACheckboxGroup',
        props: {
          type: 'checkbox',
          options: [
            { label: 1, text: '选项1' },
            { label: 2, text: '选项2' },
          ],
        },
      },
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'change',
          description: '绑定值变化时触发的事件',
          template: 'function change(label ) { console.log(label);}',
        },
      ],
    },
    component: {
      isContainer: false,
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '高级多选框',
          })
        },
      },
    },
  },
}
