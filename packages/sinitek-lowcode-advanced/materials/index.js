import form from './form/meta'
import childForm from './child-form/meta'
import formItem from './form-item/meta'
import relaFrom from './rela-form/meta'
import input from './input/meta'
import autocomplete from './autocomplete/meta'
import select from './select/meta'
import employee from './employee/meta'
import dialog from './dialog/meta'
import drawer from './drawer/meta'
import tabs from './tabs/meta'
import tabPane from './tab-pane/meta'
import table from './table/meta'
import tableCol from './table/col-meta'

import radio from './radio/meta'
import checkbox from './checkbox/meta'
import download from './download/meta'
import stage from './stage/meta'
import stageItem from './stage-item/meta'
import elementIcon from './element-icon/meta'
import collapseItem from './collapse-item/meta'
import calendar from './calendar/meta'
import echarts from './echarts/meta'
import dropdown from './dropdown/meta'
import echartsEditor from './echarts-editor/meta'
import popover from './popover/meta'
import tableView from './table-view/meta'
import tableViewTd from './table-view/td-meta'
import tableViewTr from './table-view/tr-meta'
export default [
  form,
  formItem,
  childForm,
  relaFrom,
  input,
  autocomplete,
  select,
  employee,
  dialog,
  drawer,
  tabs,
  tabPane,
  table,
  tableCol,
  radio,
  checkbox,
  download,
  stage,
  stageItem,
  elementIcon,
  collapseItem,
  calendar,
  echarts,
  dropdown,
  echartsEditor,
  popover,
  tableView,
  tableViewTr,
  tableViewTd,
]
