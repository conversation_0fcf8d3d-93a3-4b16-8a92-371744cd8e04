import image from './__screenshots__/image.svg'
import echart from '../echarts/meta'
export default {
  componentName: 'LAEChartsEditor',
  title: 'echarts图表编辑器',
  category: '信息展示',
  tips: '可以全局window.LCECharts替代echarts使用',
  props: [
    {
      title: '宽度',
      name: 'width',
      setter: ['StringSetter', 'NumberSetter'],
    },
    {
      title: '高度',
      name: 'height',
      setter: ['StringSetter', 'NumberSetter'],
    },
    {
      title: '数据',
      name: 'data',
      setter: 'JSONSetter',
    },
    {
      title: '提示',
      name: 'tooltip',
      setter: {
        componentName: 'ObjectSetter',
        props: {
          config: {
            items: [
              {
                title: '显示',
                name: 'show',
                setter: 'BoolSetter',
              },
              {
                title: '格式化',
                name: 'formatter',
                setter: 'StringSetter',
              },
              {
                name: 'trigger',
                title: '触发类型',
                defaultValue: 'item',
                setter: {
                  componentName: 'SelectSetter',
                  props: {
                    options: [
                      { title: '数据项', value: 'item' },
                      { title: '坐标轴', value: 'axis' },
                      { title: '不触发', value: 'none' },
                    ],
                  },
                },
              },
            ],
          },
        },
      },
    },
    {
      title: '图表配置',
      name: 'items',
      setter: {
        componentName: 'ArraySetter',
        props: {
          itemSetter: {
            componentName: 'ObjectSetter',
            initialValue: {
              type: 'line',
              name: '图表名称',
            },
            props: {
              textField: 'name',
              config: {
                items: [
                  {
                    title: '类型',
                    name: 'type',
                    setter: {
                      componentName: 'SelectSetter',
                      props: {
                        options: [
                          { title: '折线图', value: 'line' },
                          { title: '柱状图', value: 'bar' },
                          { title: '饼图', value: 'pie' },
                          { title: '漏斗图', value: 'funnel' },
                        ],
                      },
                    },
                  },
                  {
                    title: '名称',
                    name: 'name',
                    setter: 'StringSetter',
                  },
                  {
                    title: {
                      label: '堆叠key',
                      tip: '堆叠图表时，需要设置相同堆叠的key',
                    },
                    name: 'stack',
                    setter: 'StringSetter',
                    condition: (props) => {
                      return ['line', 'bar'].includes(props.type)
                    },
                  },
                  {
                    title: '面积图',
                    name: 'isArea',
                    setter: 'BoolSetter',
                    condition: (props) => {
                      return ['line'].includes(props.type)
                    },
                  },
                  {
                    title: '平滑',
                    name: 'smooth',
                    setter: 'BoolSetter',
                    condition: (props) => {
                      return ['line', 'funnel'].includes(props.type)
                    },
                  },
                  {
                    title: 'color',
                    name: 'color',
                    setter: 'ColorSetter',
                    condition: (props) => {
                      return props.type !== 'pie' && props.type !== 'funnel'
                    },
                  },
                  {
                    title: 'x轴',
                    name: 'xAxis',
                    condition: (props) => {
                      return props.type !== 'pie' && props.type !== 'funnel'
                    },
                    setter: {
                      componentName: 'ObjectSetter',
                      props: {
                        config: {
                          items: [
                            // {
                            //   title: '数据维度',
                            //   name: 'key',
                            //   setter: 'StringSetter',
                            // },
                            {
                              title: '名称',
                              name: 'name',
                              setter: 'StringSetter',
                            },
                          ],
                        },
                      },
                    },
                  },
                  {
                    title: 'y轴',
                    name: 'yAxis',
                    condition: (props) => {
                      return props.type !== 'pie' && props.type !== 'funnel'
                    },
                    setter: {
                      componentName: 'ObjectSetter',
                      props: {
                        config: {
                          items: [
                            // {
                            //   title: '数据维度',
                            //   name: 'key',
                            //   setter: 'StringSetter',
                            // },
                            {
                              title: {
                                label: '名称',
                                tip: '多个图表时，不同的名称，会在右边生成第2个y轴',
                              },
                              name: 'name',
                              setter: 'StringSetter',
                            },
                          ],
                        },
                      },
                    },
                  },
                  {
                    title: '数据维度配置',

                    type: 'group',
                    items: [
                      {
                        title: 'x',
                        name: 'itemXDimension',
                        setter: 'StringSetter',
                        condition: (props) => {
                          return ['line', 'bar'].includes(props.type)
                        },
                      },
                      {
                        title: 'y',
                        name: 'itemYDimension',
                        setter: 'StringSetter',
                        condition: (props) => {
                          return ['line', 'bar'].includes(props.type)
                        },
                      },
                      {
                        title: '值',
                        name: 'itemValueDimension',
                        setter: 'StringSetter',
                        condition: (props) => {
                          return props.type === 'pie' || props.type === 'funnel'
                        },
                      },
                      {
                        title: '名称',
                        name: 'itemNameDimension',
                        setter: 'StringSetter',
                        condition: (props) => {
                          return props.type === 'pie' || props.type === 'funnel'
                        },
                      },
                      {
                        title: '提示值',
                        name: 'tooltipValueDimension',
                        setter: 'StringSetter',
                      },
                    ],
                  },
                ],
              },
            },
          },
        },
      },
    },
  ],
  snippets: [
    {
      title: '图表编辑',
      screenshot: image,
      schema: {
        componentName: 'LAEChartsEditor',
        props: {
          width: '100%',
          height: '300px',
          data: [
            ['Hannah Krause', 41, 'Engineer', 314, '2011-02-12'],
            ['Zhao Qian', 20, 'Teacher', 351, '2011-03-01'],
            ['Jasmin Krause ', 52, 'Musician', 287, '2011-02-14'],
            ['Li Lei', 37, 'Teacher', 219, '2011-02-18'],
            ['Karle Neumann', 25, 'Engineer', 253, '2011-04-02'],
            ['Adrian Groß', 19, 'Teacher', '-', '2011-01-16'],
            ['Mia Neumann', 71, 'Engineer', 165, '2011-03-19'],
            ['Böhm Fuchs', 36, 'Musician', 318, '2011-02-24'],
            ['Han Meimei', 67, 'Engineer', 366, '2011-03-12'],
          ],
          items: [
            {
              name: '图表名称',
              type: 'line',
              itemXDimension: '0',
              itemYDimension: '1',
            },
          ],
        },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: echart.configure.events,
      methods: echart.configure.methods,
    },
  },
}
