import formItem from 'sinitek-lowcode-materials/sinitek-ui/components/form-item/meta.js'
import { ComponentName } from 'sinitek-lowcode-shared'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'LARelaForm',
  title: '关联表单',
  category: '信息输入',
  priority: 2,
  tips: `在子表单里使用只需要设置'表单项/prop'，用来触发表单校验。\n
  在高级表单里需要多设置'绑定字段'，来绑定数据。`,
  modelComponent: true,
  props: [
    // {
    //   name: 'value',
    //   title: 'value',
    //   setter: 'StringSetter',
    // },
    {
      name: 'readonly',
      title: '只读',
      setter: 'BoolSetter',
    },
    {
      name: 'placeholder',
      title: '占位提示',
      setter: 'StringSetter',
    },
    {
      name: 'multiple',
      title: '多选',
      setter: 'BoolSetter',
    },
    {
      title: '表单项',
      name: 'itemProps',
      setter: {
        componentName: 'ObjectSetter',
        props: {
          config: {
            items: formItem.props,
          },
        },
      },
    },
    {
      title: '关联属性',
      type: 'group',
      items: [
        {
          name: 'modelAndCondition',
          title: '关联模型',
          setter: 'ModelSetter',
        },
        {
          name: 'display',
          title: '显示字段',
          setter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: [
                  {
                    name: 'primaryKey',
                    title: '主要文本key',
                    setter: 'StringSetter',
                  },
                  {
                    name: 'secondaryKey',
                    title: '次要文本key',
                    setter: 'StringSetter',
                  },
                ],
              },
            },
          },
          defaultValue: 'id',
        },
      ],
    },
    {
      title: '只读自定义内容',
      name: 'content',
      setter: 'SlotSetter',
    },
  ],
  snippets: [
    {
      title: '关联表单',
      screenshot: require('./__screenshots__/image.svg'),
      schema: {
        componentName: 'LARelaForm',
        props: {
          field: ComponentName.RELA_FORM,
          itemProps: {
            label: '关联表单',
            prop: 'relaForm',
          },
          display: {
            primaryKey: 'id',
          },
          placeholder: '请选择',
        },
        children: [],
      },
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [],
    },
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: ['LAForm', 'LAChildForm', 'XnForm'],
      },
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          // 只用主模型的当作数据源的表格可以从主模型树中拖入组件
          if (node?.source !== 'model')
            return formItemAdd(node, target, {
              label: '关联表单',
            })
          if (target.componentName == 'LATable') {
            if (target.props.datasource === 'main') {
              const props = node?.props
              const children = []
              if (props.multiple) {
                children.push({
                  componentName: 'Slot',
                  props: {
                    name: 'default',
                  },
                  children: [
                    {
                      componentName: 'LCLoop',
                      props: {
                        options: {
                          type: 'JSExpression',
                          value: `scope.row.${props.itemProps.prop}`,
                        },
                      },
                      children: [
                        {
                          componentName: 'ElTag',
                          props: {},
                          children: [
                            {
                              componentName: 'LCText',
                              props: {
                                text: {
                                  type: 'JSExpression',
                                  value: `scope.item.${props.display.primaryKey}`,
                                },
                              },
                            },
                          ],
                        },
                      ],
                    },
                  ],
                })
              }
              return {
                componentName: 'XnColAdvanced',
                props: {
                  prop: props?.itemProps?.prop,
                  label: props?.itemProps?.label,
                },
                children,
              }
            } else {
              return {
                forbidden: true,
                errorMessage: '只有使用主模型当作数据源，才能拖入主模型元素。',
              }
            }
          }
          return formItemAdd(node, target, {
            label: '关联表单',
          })
        },
      },
    },
  },
}
