import input from 'sinitek-lowcode-materials/sirmapp/components/employee/meta.js'
import input1 from 'sinitek-lowcode-materials/sirmapp/components/employee/__screenshots__/employee.svg'
import { ComponentName } from 'sinitek-lowcode-shared'
import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'
export default {
  componentName: 'LAEmployee',
  title: '组织架构',
  category: '信息输入',
  props: [
    // {
    //   name: 'value',
    //   title: '选人组件的值',
    //   setter: 'StringSetter',
    // },
    // {
    //   name: 'model',
    //   title: '控件数据来源',
    //   setter: 'StringSetter',
    // },
    {
      name: 'breakLine',
      title: '控件单行显示',
      setter: 'BoolSetter',
    },

    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'clearable',
      title: { label: '可清除', tip: '自动补全框是否可以清空' },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'mode',
      title: '可选类型',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '全部类型',
              value: 'ORG',
            },
            {
              title: '人员',
              value: 'EMPLOYEE',
            },
            {
              title: '部门',
              value: 'UNIT',
            },
            {
              title: '岗位',
              value: 'POSITION',
            },
            {
              title: '角色',
              value: 'ROLE',
            },
            {
              title: '小组',
              value: 'TEAM',
            },
          ],
        },
      },
    },
    // 面板
    {
      type: 'group',
      title: '面板',
      items: [
        {
          name: 'title',
          title: '标题',
          setter: 'StringSetter',
        },

        {
          name: 'showInserviceCheckbox',
          title: '显示离职信息',
          setter: 'BoolSetter',
        },
        {
          name: 'isCheckInservice',
          title: '是否离职选框勾选',
          setter: 'BoolSetter',
        },

        {
          name: 'isShowWorkPlace',
          title: '展示工作地',
          setter: 'BoolSetter',
        },
        {
          name: 'isShowEmail',
          title: '展示用户邮箱',
          setter: 'BoolSetter',
        },
        {
          name: 'isShowSystemCheck',
          title: '显示“系统”选框',
          setter: 'BoolSetter',
        },
        {
          name: 'showTagOrgPrefix',
          title: '显示组织结构前缀',
          setter: 'BoolSetter',
        },
        {
          name: 'showResign',
          title: '自动补全离职人员显示',
          setter: 'BoolSetter',
        },
        {
          name: 'isPopover',
          title: '弹窗展示tags列表',
          setter: 'BoolSetter',
        },
      ],
    },
    // 外观
    {
      type: 'group',
      title: '外观',
      items: [
        {
          name: 'seachType',
          title: '组件类型',
          setter: {
            componentName: 'RadioGroupSetter',
            props: {
              options: [
                {
                  title: '输入框',
                  value: 'input',
                },
                {
                  title: '按钮',
                  value: 'button',
                },
              ],
            },
          },
        },

        {
          name: 'icon',
          title: '按钮的 icon 样式',
          setter: 'StringSetter',
          condition: (props) => {
            return props.seachType === 'button'
          },
        },
        {
          name: 'label',
          title: '按钮的文本',
          setter: 'StringSetter',
          condition: (props) => {
            return props.seachType === 'button'
          },
        },
        {
          name: 'placeholder',
          title: '提示语',
          setter: 'StringSetter',
          condition: (props) => {
            return props.seachType === 'input'
          },
        },
        {
          name: 'width',
          title: '控件宽度',
          setter: 'NumberSetter',
          condition: (props) => {
            return props.seachType === 'input'
          },
        },
        widthSize,
      ],
    },
    {
      type: 'group',
      title: '多选',
      items: [
        {
          name: 'multi',
          title: '多选',
          setter: 'BoolSetter',
        },
        {
          name: 'showTag',
          title: '显示选中标签',
          setter: 'BoolSetter',
        },
        {
          name: 'collapseTag',
          title: '默认展示选择tag个数',
          setter: 'NumberSetter',
          condition(props) {
            return props.multi
          },
        },
        {
          name: 'limitCount',
          title: '选择数量限制',
          setter: 'NumberSetter',
          condition(props) {
            return props.multi
          },
        },
      ],
    },
    {
      type: 'group',
      title: '限制范围',
      items: [
        {
          name: 'excludeEmpIds',
          title: '不展示的人员',
          setter: 'JSONSetter',
          documentUrl:
            'http://*************:18056/doc/sirmapp-dev/components/employee.html?prop=excludeEmpIds#%E9%99%90%E5%88%B6%E8%A7%84%E5%88%99',
        },
        {
          name: 'selectDeptIds',
          title: '可选择的部门',
          setter: 'JSONSetter',
          documentUrl:
            'http://*************:18056/doc/sirmapp-dev/components/employee.html?prop=selectDeptIds#%E9%99%90%E5%88%B6%E8%A7%84%E5%88%99',
        },
        {
          name: 'selectPostIds',
          title: '可选择的岗位',
          setter: 'JSONSetter',
          documentUrl:
            'http://*************:18056/doc/sirmapp-dev/components/employee.html?prop=selectPostIds#%E9%99%90%E5%88%B6%E8%A7%84%E5%88%99',
        },
        {
          name: 'selectRoleIds',
          title: '可选择的角色',
          setter: 'JSONSetter',
          documentUrl:
            'http://*************:18056/doc/sirmapp-dev/components/employee.html?prop=selectRoleIds#%E9%99%90%E5%88%B6%E8%A7%84%E5%88%99',
        },
        {
          name: 'selectTeamIds',
          title: '可选择的小组',
          setter: 'JSONSetter',
          documentUrl:
            'http://*************:18056/doc/sirmapp-dev/components/employee.html?prop=selectTeamIds#%E9%99%90%E5%88%B6%E8%A7%84%E5%88%99',
        },
      ],
    },
    // {
    //   name: 'code',
    //   title: '选人控件方案代码',
    //   setter: 'StringSetter',
    // },

    // {
    //   name: 'checkStrictly',
    //   title: '树形控件级联选择器',
    //   setter: 'BoolSetter',
    // },
    // {
    //   name: 'selectWidth',
    //   title: '自动补全下拉宽度',
    //   setter: 'StringSetter',
    // },
    // {
    //   name: 'selectWidth',
    //   title: '树形控件每项最大宽度',
    //   setter: 'StringSetter',
    // },
    // {
    //   name: 'treeWidth',
    //   title: '树形控件宽度',
    //   setter: 'StringSetter',
    // },

    {
      type: 'group',
      title: '高级',
      items: [
        {
          name: 'dialogButtons',
          title: '对话框按钮数组',
          setter: 'JSONSetter',
          documentUrl:
            'http://*************:18056/doc/sirmapp-dev/components/employee.html?prop=dialogButtons#%E9%99%90%E5%88%B6%E8%A7%84%E5%88%99',
        },
        {
          name: 'groupOrRoleAsCondition',
          title: {
            label: '关联查询',
            tip: '是否开启小组、角色作为查询条件开启后，在点击小组、角色时，将查询拥有小组、角色的人员。',
          },
          setter: 'BoolSetter',
        },
        // {
        //   name: 'enableUserSelectionHistory',
        //   title: '开启选择记录功能',
        //   setter: 'BoolSetter',
        // },
        // {
        //   name: 'scene',
        //   title: '选择的场景名称',
        //   setter: 'StringSetter',
        // },
      ],
    },

    // {
    //   type: 'group',
    //   title: '插槽',
    //   items: [
    //     {
    //       name: 'employee-column',
    //       title: '扩展选人弹窗表格展示列',
    //       setter: 'SlotSetter',
    //     },
    //   ],
    // },
  ],
  componentType: 'INPUT',
  formContext: 'SUBMIT',
  priority: 2,
  snippets: [
    {
      title: '组织架构',
      screenshot: input1,
      schema: {
        componentName: 'LAEmployee',
        props: {
          mode: 'ORG',
          seachType: 'input',
        },
        children: [],
      },
    },
  ],
  configure: {
    ...input.configure,
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          if (['LAFormItem', 'LAForm'].includes(target.componentName)) {
            return {
              componentName: 'LAFormItem',
              props: {
                field: ComponentName.SIRMORG,
                itemProps: {
                  label: '组织架构',
                  prop: 'org',
                },
                fieldProps: {
                  ...node.props,
                },
              },
            }
          }
          return node
        },
      },
    },
  },
}
