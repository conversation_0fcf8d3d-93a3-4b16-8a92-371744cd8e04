import { defineConfig } from 'vite'
import Vue2JSX from '@vitejs/plugin-vue2-jsx'
import Vue2 from '@vitejs/plugin-vue2'
import UnoCSS from 'unocss/vite'
import path from 'path'
export default defineConfig(() => {
  const config = {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
      extensions: [
        '.mjs',
        '.js',
        '.mts',
        '.ts',
        '.jsx',
        '.tsx',
        '.json',
        '.vue',
      ],
    },
    build: {
      lib: {
        entry: './index.js',
        name: 'SinitekLowcodeAdvanced',
        formats: ['umd', 'esm'],
        filename: 'sinitek-lowcode-advanced',
      },
      sourcemap: process.env.NODE_ENV === 'development',
      assetsDir: 'static',
      cssCodeSplit: false,
      rollupOptions: {
        // 确保外部化处理那些你不想打包进库的依赖
        external: [
          'vue',
          'core-js',
          /^sinitek-lowcode-.*/,
          'element-ui',
          'monaco-editor',
          'sinitek-ui',
          '@iconify/vue2',
          'sirmapp',
          /^prettier.*/,
          /^@babel.*/,
        ],
        output: {
          assetFileNames: (v) => {
            if (v.name.endsWith('.css')) {
              return 'sinitek-lowcode-advanced.css'
            }
            return v.name
          },
        },
      },
    },
  }
  config.plugins = [Vue2(), Vue2JSX(), UnoCSS()]

  return config
})
