// import employee from './__screenshots__/employee.png'

import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'

// const employeeData = ''
export default {
  componentName: 'XnEmployee',
  title: '选人组件',
  category: '信息输入',
  componentType: 'SIRMORG',
  formContext: 'SUBMIT',
  props: [
    // {
    //   name: 'value',
    //   title: '选人组件的值',
    //   setter: 'StringSetter',
    // },
    {
      name: 'seachType',
      title: '组件类型',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              title: '输入框',
              value: 'input',
            },
            {
              title: '按钮',
              value: 'button',
            },
          ],
        },
      },
    },
    {
      name: 'mode',
      title: '可选类型',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '全部类型',
              value: 'ORG',
            },
            {
              title: '人员',
              value: 'EMPLOYEE',
            },
            {
              title: '部门',
              value: 'UNIT',
            },
            {
              title: '岗位',
              value: 'POSITION',
            },
            {
              title: '角色',
              value: 'ROLE',
            },
            {
              title: '小组',
              value: 'TEAM',
            },
          ],
        },
      },
    },
    {
      name: 'multi',
      title: '多选',
      setter: 'BoolSetter',
    },
    {
      name: 'showInserviceCheckbox',
      title: '显示离职信息',
      setter: 'BoolSetter',
    },
    {
      name: 'isCheckInservice',
      title: '是否离职选框勾选',
      setter: 'BoolSetter',
    },
    {
      name: 'clearable',
      title: { label: '可清除', tip: '自动补全框是否可以清空' },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'code',
      title: '选人控件方案代码',
      setter: 'StringSetter',
    },
    {
      name: 'showTag',
      title: '显示选中标签',
      setter: 'BoolSetter',
    },
    {
      name: 'width',
      title: '控件宽度',
      setter: 'NumberSetter',
    },
    {
      name: 'icon',
      title: '按钮的 icon 样式',
      setter: 'StringSetter',
    },
    {
      name: 'label',
      title: '按钮的文本',
      setter: 'StringSetter',
    },
    {
      name: 'model',
      title: '控件数据来源',
      setter: 'StringSetter',
    },
    {
      name: 'breakLine',
      title: '控件单行显示',
      setter: 'BoolSetter',
    },
    {
      name: 'title',
      title: '控件标题',
      setter: 'StringSetter',
    },
    {
      name: 'placeholder',
      title: '提示语',
      setter: 'StringSetter',
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'excludeEmpIds',
      title: '不展示的人员',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/sirmapp/employee.html?prop=excludeEmpIds#%E5%8F%82%E6%95%B0',
    },
    {
      name: 'collapseTag',
      title: '默认展示选择tag个数',
      setter: 'NumberSetter',
      condition(props) {
        return props.multi
      },
    },
    {
      name: 'isShowSystemCheck',
      title: '显示“系统”选框',
      setter: 'BoolSetter',
    },
    {
      name: 'showResign',
      title: '自动补全离职人员显示',
      setter: 'BoolSetter',
    },
    {
      name: 'checkStrictly',
      title: '树形控件级联选择器',
      setter: 'BoolSetter',
    },
    {
      name: 'selectWidth',
      title: '自动补全下拉宽度',
      setter: 'StringSetter',
    },
    {
      name: 'selectWidth',
      title: '树形控件每项最大宽度',
      setter: 'StringSetter',
    },
    {
      name: 'treeWidth',
      title: '树形控件宽度',
      setter: 'StringSetter',
    },
    {
      name: 'isShowWorkPlace',
      title: '展示工作地',
      setter: 'BoolSetter',
    },
    {
      name: 'limitCount',
      title: '选择数量限制',
      setter: 'NumberSetter',
    },
    widthSize,
    {
      name: 'isShowEmail',
      title: '展示用户邮箱',
      setter: 'BoolSetter',
    },
    {
      name: 'isPopover',
      title: '弹窗展示tags列表',
      setter: 'BoolSetter',
    },
    {
      name: 'showTagOrgPrefix',
      title: '显示组织结构前缀',
      setter: 'BoolSetter',
    },
    {
      name: 'showTagOrgPrefix',
      title: '对话框按钮数组',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/sirmapp/employee.html?prop=dialogButton#%E5%8F%82%E6%95%B0',
    },
    {
      name: 'selectRoleIds',
      title: '可选择的角色',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/sirmapp/employee.html?prop=selectRoleIds#%E5%8F%82%E6%95%B0',
    },
    {
      name: 'selectTeamIds',
      title: '可选择的小组',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/sirmapp/employee.html?prop=selectTeamIds#%E5%8F%82%E6%95%B0',
    },
    {
      name: 'enableUserSelectionHistory',
      title: '开启选择记录功能',
      setter: 'BoolSetter',
    },
    {
      name: 'scene',
      title: '选择的场景名称',
      setter: 'StringSetter',
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'employee-column',
          title: '扩展选人弹窗表格展示列',
          setter: 'SlotSetter',
        },
      ],
    },
  ],
  // snippets: [
  //   {
  //     title: '选人组件',
  //     screenshot: employee,
  //     schema: {
  //       componentName: 'XnEmployee',
  //       props: {
  //         mode: 'ORG',
  //         value: employeeData,
  //       },
  //       children: [],
  //       events: {
  //         'input-change': {
  //           type: 'JSFunction',
  //           value: 'function (data) {console.log("selectPeople", data)}',
  //         },
  //       },
  //     },
  //   },
  // ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'onCloseTag',
          description: '删除已选人员时触发',
          template: 'function onCloseTag(callback) { callback();}',
        },
        {
          name: 'inputChange',
          description: '输入框数据变化时触发',
          template: 'function inputChange(value) { console.log(value)}',
        },
        {
          name: 'getResult',
          description: '当组件参数发生变化，重新查询后触发',
          template: 'function getResult(obj) { console.log(obj)}',
        },
      ],
      methods: [
        {
          name: 'clear',
          description: '清空组件',
          example: 'refs.employee.clear()',
          returnType: 'void',
        },
        {
          name: 'getResult',
          description: '获取组件值',
          example: 'refs.employee.getResult()',
          returnType: 'void',
        },
      ],
    },
  },
}
