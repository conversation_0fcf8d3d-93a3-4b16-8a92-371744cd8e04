import image from './__screenshots__/image.svg'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'WorkflowLaunch',
  title: '工作流发起',
  category: '工作流程',
  props: [
    {
      name: 'syscode',
      title: {
        label: '流程syscode',
        tip: 'syscode\n 必填',
      },
      setter: 'StringSetter',
    },
    {
      name: 'businessData',
      title: {
        label: '业务数据',
        tip: 'businessData\n 业务数据，在使用发起时执行保存业务数据或发起组件放在提交节点处理页面时必填',
      },
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/workflow/2_0_x/workflow-launch.html#%E5%8F%82%E6%95%B0',
    },

    {
      name: 'initDate',
      title: {
        label: '流程步骤信息',
        tip: 'initDate\n 如果传入该参数，就不再向后台请求获取流程步骤信息',
      },
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/workflow/2_0_x/workflow-launch.html#%E5%8F%82%E6%95%B0',
    },
    {
      name: 'exampleId',
      title: { label: '实例id', tip: 'exampleId\n 实例 id' },
      setter: 'StringSetter',
    },
    {
      name: 'exampleStepId',
      title: { label: '步骤id', tip: 'exampleStepId\n 实例步骤 id' },
      setter: 'StringSetter',
    },
    {
      name: 'exampleownerid',
      title: {
        label: '处理人id',
        tip: 'exampleownerid\n 实例步骤处理人 id',
      },
      setter: 'StringSetter',
    },
    {
      name: 'sourceId',
      title: {
        label: '关联id',
        tip: 'sourceId\n 流程关联实体 id',
      },
      setter: 'StringSetter',
    },
    {
      name: 'sourceName',
      title: {
        label: '实体名称',
        tip: 'sourceName\n 流程关联实体 name',
      },
      setter: 'StringSetter',
    },
    {
      name: 'requiredStepOwnerFlagAble',
      title: {
        label: '必填标识',
        tip: 'requiredStepOwnerFlagAble\n 是否开启步骤处理人必填标识',
      },
      setter: 'BoolSetter',
    },
  ],
  snippets: [
    {
      title: '工作流发起',
      screenshot: image,
      schema: {
        componentName: 'WorkflowLaunch',
        props: {},
        children: [],
      },
      prePreview: false,
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'launchShowFlag',
          description: '发起组件是否显示',
          template: 'function launchShowFlag(bool) { console.log(item);}',
        },
      ],
      methods: [
        {
          name: 'validate',
          description: '验证发起组件数据是否正确使用',
          example: 'refs.workflowLaunch.validate()',
          returnType: 'boolean',
        },
        {
          name: 'getData',
          description: '获取发起组件的数据',
          example: 'refs.workflowLaunch.getData()',
          returnType: 'string',
        },
        {
          name: 'submitData',
          description: '直接使用默认处理提交数据',
          example: 'refs.workflowLaunch.submitData()',
          returnType: 'Promise<void>',
        },
        {
          name: 'saveDate',
          description: '保存发起信息',
          example: 'refs.workflowLaunch.saveDate()',
          returnType: 'Promise<void>',
        },
        {
          name: 'loadData',
          description:
            '加载保存的发起信息, 无需手动调用，但需要绑定sourceId和sourceName',
          example: 'refs.workflowLaunch.loadData()',
        },
        {
          name: 'getTmpData',
          description: '获取发起组件的步骤配置数据',
          example: 'refs.workflowLaunch.getTmpData()',
          returnType: 'string',
        },
      ],
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '工作流发起',
          })
        },
      },
    },
  },
}
