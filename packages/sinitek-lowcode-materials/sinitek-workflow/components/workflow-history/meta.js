import image from './__screenshots__/image.svg'
export default {
  componentName: 'WorkflowHistory',
  title: '工作流历史',
  category: '工作流程',
  props: [
    {
      name: 'exampleid',
      title: { label: '实例id', tip: 'exampleid\n 实例 id' },
      setter: 'StringSetter',
    },
    {
      name: 'examplestepid',
      title: { label: '步骤id', tip: 'examplestepid\n 流程实例步骤的 id' },
      setter: 'StringSetter',
    },
    {
      name: 'sourceId',
      title: {
        label: '实体id',
        tip: 'sourceId\n 流程关联实体的 id。如果没有该参数，则必须要有exampleid',
      },
      setter: 'StringSetter',
    },
    {
      name: 'sourceName',
      title: {
        label: '实体名称',
        tip: 'sourceName\n 流程关联实体的名称。和sourceId一起传递，不能只设置其中一个',
      },
      setter: 'StringSetter',
    },
    {
      name: 'showType',
      title: {
        label: '展示模式',
        tip: 'showType\n 历史流程图展示模式。1、普通模式，2、紧凑模式。7.1.59+,7.4.*版本新增参数，3、节点紧凑模式（目前支持7.3.408+）',
      },
      defaultValue: 1,
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            { label: '普通模式', value: 1 },
            { label: '紧凑模式', value: 2 },
            { label: '节点紧凑模式', value: 3 },
          ],
        },
      },
    },
    {
      name: 'tabPaneLabels',
      title: {
        label: '自定义页签名称',
        tip: 'tabPaneLabels\n 自定义页签名称，可以用来修改组件的页签名称，兼容默认名称',
      },
      setter: {
        componentName: 'ObjectSetter',
        props: {
          config: {
            items: [
              {
                name: 'processDetailLabel',
                title: {
                  label: '流转信息',
                  tip: 'processDetailLabel\n 流转信息页签名称',
                },
                setter: 'StringSetter',
              },
              {
                name: 'processsShowLabel',
                title: {
                  label: '流程图',
                  tip: 'processsShowLabel\n 流程图页签名称',
                },
                setter: 'StringSetter',
              },
              {
                name: 'associatedProcessLabel',
                title: {
                  label: '关联流程',
                  tip: 'associatedProcessLabel\n 关联流程页签名称',
                },
                setter: 'StringSetter',
              },
              {
                name: 'commentLabel',
                title: { label: '评论', tip: 'commentLabel\n 评论页签名称' },
                setter: 'StringSetter',
              },
              {
                name: 'exampleCcLable',
                title: {
                  label: '流程抄送',
                  tip: 'exampleCcLable\n 流程抄送页签名称',
                },
                setter: 'StringSetter',
              },
            ],
          },
        },
      },
    },
  ],
  snippets: [
    {
      title: '工作流历史',
      screenshot: image,
      schema: {
        componentName: 'WorkflowHistory',
        props: {},
        children: [],
      },
      prePreview: false,
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'defualtSubmitCallback',
          description: '自定义组件内按钮的处理',
          template:
            'function defualtSubmitCallback(workflowSubmitForm) { console.log(workflowSubmitForm);}',
        },
        {
          name: 'onTaskChange',
          description:
            '流程实例的任务改变会执行，比如发起征求之后，可以通过这个事件对 workflow-history 进行刷新',
          template: 'function onTaskChange() { console.log("任务已改变");}',
        },
        {
          name: 'onNextStepChange',
          description: '选择下一步步骤时会触发',
          template:
            'function onNextStepChange(processStepId) { console.log(processStepId);}',
        },
        {
          name: 'customeButtonClick',
          description:
            '自定义按钮（非流程按钮）默认点击触发事件，如果配置了自定义事件则按配置的触发',
          template:
            'function customeButtonClick({button, workflowSubmitForm}) { console.log(button, workflowSubmitForm);}',
        },
      ],
    },
  },
}
