import image from './__screenshots__/image.svg'
export default {
  componentName: 'WorkflowSubmit',
  title: '工作流审批',
  category: '工作流程',
  props: [
    {
      name: 'exampleid',
      title: { label: '实例id', tip: 'exampleid\n 实例 id' },
      setter: 'StringSetter',
    },
    {
      name: 'examplestepid',
      title: { label: '步骤id', tip: 'examplestepid\n 实例步骤 id' },
      setter: 'StringSetter',
    },
    {
      name: 'exampleownerid',
      title: {
        label: '处理人id',
        tip: 'exampleownerid\n 实例步骤处理人 id',
      },
      setter: 'StringSetter',
    },
    {
      name: 'askForId',
      title: {
        label: '征求ID',
        tip: 'askForId\n 流程征求ID，v7.4.0新增参数，有该参数表示是征求组件，没有表示是审批组件。和exampleownerid两个参数必须有一个',
      },
      setter: 'StringSetter',
    },
    {
      name: 'stepChangeOwnerFlag',
      title: {
        label: '获取默认处理人',
        tip: 'stepChangeOwnerFlag\n 选择步骤时是否获取所选择步骤的默认处理人',
      },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'launchShow',
      title: {
        label: '显示发起组件',
        tip: 'launchShow\n 是否显示发起组件',
      },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'labelWidth',
      title: {
        label: '标签宽度',
        tip: 'labelWidth\n 审批组件表单项label-width',
      },
      defaultValue: '120px',
      setter: 'StringSetter',
    },
    {
      name: 'isShowOpinion',
      title: {
        label: '显示意见输入框',
        tip: 'isShowOpinion\n 是否显示意见输入框',
      },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'maxOpinionLength',
      title: {
        label: '意见输入框长度',
        tip: 'maxOpinionLength\n 意见输入框长度',
      },
      defaultValue: 500,
      setter: 'NumberSetter',
    },
    {
      name: 'showWordLimit',
      title: {
        label: '统计字数',
        tip: 'showWordLimit\n 是否统计意见输入框的字数',
      },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'checkOpinionRequired',
      title: {
        label: '意见必填校验',
        tip: 'checkOpinionRequired\n 意见输入框是否必填',
      },
      setter: 'FunctionSetter',
    },
    {
      name: 'customizCheckOpinion',
      title: {
        label: '自定义校验审批意见',
        tip: 'customizCheckOpinion\n 自定义校验审批意见',
      },
      setter: 'FunctionSetter',
    },
    {
      name: 'custometype',
      title: {
        label: '自定义审批',
        tip: 'custometype\n 值为1时，表示使用自定义审批，配合@defualtSubmitCallback=""使用',
      },
      defaultValue: 0,
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            { label: '不使用', value: 0 },
            { label: '使用', value: 1 },
          ],
        },
      },
    },
    {
      name: 'businessData',
      title: {
        label: '业务数据',
        tip: 'businessData\n 业务数据对象，数据会在调用业务日志记录执行时作为参数传递过去',
      },
      setter: 'JSONSetter',
    },
    {
      name: 'phrasesShow',
      title: {
        label: '显示常用语',
        tip: 'phrasesShow\n 是否展示"常用语"',
      },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'opinion',
      title: {
        label: '审批意见标签',
        tip: 'opinion\n 审批意见项label名称',
      },
      defaultValue: '审批意见',
      setter: 'StringSetter',
    },
    {
      name: 'askForOpinion',
      title: {
        label: '征求意见标签',
        tip: 'askForOpinion\n 征求意见项label名称，v7.4新增参数',
      },
      defaultValue: '意见',
      setter: 'StringSetter',
    },
    {
      name: 'nextOwnerType',
      title: {
        label: '下一步处理人控件类型',
        tip: 'nextOwnerType\n 下一步处理人控件类型，默认"ORG"',
      },
      defaultValue: 'ORG',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            { label: '组织', value: 'ORG' },
            { label: '员工', value: 'EMPLOYEE' },
            { label: '单位', value: 'UNIT' },
            { label: '岗位', value: 'POSITION' },
            { label: '角色', value: 'ROLE' },
            { label: '团队', value: 'TEAM' },
          ],
        },
      },
    },
    {
      name: 'checkForm',
      title: {
        label: '自定义校验表单',
        tip: 'checkForm\n 自定义校验审批组件表单内容，返回值可以为【true/false】，也可以为Promise对象',
      },
      setter: 'FunctionSetter',
    },
    {
      name: 'pageLoadUrl',
      title: {
        label: '加载步骤信息URL',
        tip: 'pageLoadUrl\n 加载当前步骤信息的url',
      },
      defaultValue: '/frontend/api/workflow/example/submitpageload',
      setter: 'StringSetter',
    },
    {
      name: 'pageLoagParam',
      title: {
        label: '加载步骤参数',
        tip: 'pageLoagParam\n 访问获取当前步骤信息url的参数，会加上exampleid,examplestepid和exampleownerid三个属性',
      },
      setter: 'JSONSetter',
    },
    {
      name: 'isShowAttachment',
      title: {
        label: '显示附件控件',
        tip: 'isShowAttachment\n 是否显示附件控件',
      },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'attachmentLabel',
      title: {
        label: '附件标签名称',
        tip: 'attachmentLabel\n 自定义附件控件表单项的label名称',
      },
      defaultValue: '附件',
      setter: 'StringSetter',
    },
    {
      name: 'loadConfirmMsg',
      title: {
        label: '获取提示语',
        tip: 'loadConfirmMsg\n 根按钮类型获取不同的提示语。v7.3.258新增参数。参数为type，pass:通过，reject：驳回，submit：提交，withdraw：退回，ask：征求，forward：转让，sign：加签',
      },
      setter: 'FunctionSetter',
    },
    {
      name: 'confirmFunction',
      title: {
        label: '自定义确认逻辑',
        tip: 'confirmFunction\n 自定义确认逻辑，使用该参数后需要业务自己提交数据到流程接口',
      },
      setter: 'FunctionSetter',
    },
    {
      name: 'customeButtonArrays',
      title: {
        label: '自定义按钮',
        tip: 'customeButtonArrays\n 自定义按钮，使用该参数可以配置审批组件显示的按钮以及控制按钮的排序',
      },
      defaultValue: [],
      setter: 'JSONSetter',
    },
    {
      name: 'exitTypeValidOnlyPass',
      title: {
        label: '流程走向仅通过有效',
        tip: 'exitTypeValidOnlyPass\n 流程走向是否只是通过时有效',
      },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'submitButtonTxt',
      title: {
        label: '提交按钮文本',
        tip: 'submitButtonTxt\n 处理征求任务时，提交按钮的显示名称，只在askForId有值的情况下生效',
      },
      defaultValue: '提交',
      setter: 'StringSetter',
    },
    {
      name: 'associatedTableMaxHeight',
      title: {
        label: '关联流程表格高度',
        tip: 'associatedTableMaxHeight\n 关联流程表格高度',
      },
      defaultValue: 700,
      setter: {
        componentName: 'MixedSetter',
        props: {
          setters: ['StringSetter', 'NumberSetter'],
        },
      },
    },
    {
      name: 'isInitData',
      title: {
        label: '初始化数据',
        tip: 'isInitData\n 是否初始化数据',
      },
      defaultValue: true,
      setter: 'BoolSetter',
    },
  ],
  snippets: [
    {
      title: '工作流审批',
      screenshot: image,
      schema: {
        componentName: 'WorkflowSubmit',
        props: {},
        children: [],
      },
      prePreview: false,
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'defualtSubmitCallback',
          description: '自定义组件内按钮的处理',
          template:
            'function defualtSubmitCallback(workflowSubmitForm) { console.log(workflowSubmitForm);}',
        },
        {
          name: 'onTaskChange',
          description:
            '流程实例的任务改变会执行，比如发起征求之后，可以通过这个事件对 workflow-history 进行刷新',
          template: 'function onTaskChange() { console.log("任务已改变");}',
        },
        {
          name: 'onNextStepChange',
          description: '选择下一步步骤时会触发',
          template:
            'function onNextStepChange(processStepId) { console.log(processStepId);}',
        },
        {
          name: 'customeButtonClick',
          description:
            '自定义按钮（非流程按钮）默认点击触发事件，如果配置了自定义事件则按配置的触发',
          template:
            'function customeButtonClick({button, workflowSubmitForm}) { console.log(button, workflowSubmitForm);}',
        },
      ],
    },
  },
}
