import radio from './__screenshots__/radio.svg'
export default {
  componentName: 'ElRadio',
  title: '单选框',
  category: '信息输入',
  props: [
    {
      name: 'label',
      title: '单选框的 value',
      setter: 'StringSetter',
    },
    // {
    //   name: 'value',
    //   title: '单选框绑定值',
    //   setter: 'StringSetter',
    // },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'border',
      title: '显示边框',
      setter: 'BoolSetter',
    },
    {
      name: 'size',
      title: '单选框尺寸',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '正常',
              value: 'medium',
            },
            {
              title: '中等',
              value: 'small',
            },
            {
              title: '迷你',
              value: 'mini',
            },
          ],
        },
      },
      condition(props) {
        return props.border
      },
    },
    {
      name: 'name',
      title: '原生 name 属性',
      setter: 'StringSetter',
    },
  ],
  snippets: [
    {
      title: '单选框',
      screenshot: radio,
      schema: {
        componentName: 'ElRadio',
        props: {
          label: '单选框文本',
        },
        children: [
          {
            componentName: 'LCText',
            props: {
              text: '单选框文本',
            },
          },
        ],
      },
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'input',
          description: '绑定值变化时触发的事件',
          template: 'function input(label) { console.log(label);}',
        },
      ],
    },
    component: {
      isContainer: true,
      nestingRule: {
        parentBlacklist: ['ElRadioButton', 'ElRadio'],
      },
    },
  },
}
