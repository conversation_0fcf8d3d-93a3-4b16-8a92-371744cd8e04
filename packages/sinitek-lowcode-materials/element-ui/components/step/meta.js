export default {
  componentName: 'ElStep',
  title: '步骤条项',
  category: '信息展示',
  props: [
    {
      name: 'title',
      setter: 'StringSetter',
      title: '标题',
    },
    {
      name: 'description',
      setter: 'TextareaSetter',
      title: '描述性文字',
    },
    {
      name: 'icon',
      setter: {
        componentName: 'IconSetter',
        props: {
          type: 'element-ui',
        },
      },
      title: '图标',
    },
    {
      name: 'status',
      title: {
        label: '状态',
        tip: '设置当前步骤的状态，不设置则根据 steps 确定状态',
      },
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            { label: '未开始', value: 'wait' },
            { label: '进行中', value: 'process' },
            { label: '已完成', value: 'finish' },
            { label: '错误', value: 'error' },
            { label: '成功', value: 'success' },
          ],
        },
      },
    },
    {
      title: '插槽',
      type: 'group',
      items: [
        {
          name: 'icon',
          title: '自定义图标',
          setter: 'SlotSetter',
        },
        {
          name: 'title',
          title: '自定义标题',
          setter: 'SlotSetter',
        },
        {
          name: 'description',
          title: '自定义描述性文字',
          setter: 'SlotSetter',
        },
      ],
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [],
    },
  },
}
