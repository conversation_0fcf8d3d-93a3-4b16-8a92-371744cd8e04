// import screenshots1 from './__screenshots__/dialog-1.png'
export default {
  componentName: 'ElDialog',
  title: '对话框',
  category: '信息展示',
  props: [
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          title: 'title',
          name: 'title',
          setter: 'SlotSetter',
          supportVariable: false,
        },
        {
          title: 'footer',
          name: 'footer',
          setter: 'SlotSetter',
          props: {
            scopedSlot: false,
          },
          supportVariable: false,
        },
      ],
    },
  ],
  // snippets: [
  //   {
  //     title: 'Element对话框',
  //     screenshot: screenshots1,
  //     schema: {
  //       componentName: 'ElDialog',
  //       props: {},
  //       children: [
  //         {
  //           componentName: 'Slot',
  //           props: {
  //             name: 'default',
  //           },
  //           children: [],
  //         },
  //       ],
  //     },
  //   },
  // ],
  configure: {
    component: {
      isContainer: false,
      isModal: true,
      modalVisibleProp: 'visible',
      modelProps: {
        'modal-append-to-body': false,
      },
      rootSelector: '.el-dialog',
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
    },
  },
}
