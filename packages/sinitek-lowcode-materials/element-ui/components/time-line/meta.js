export default {
  componentName: 'ElTimeline',
  title: '时间线',
  category: '信息输入',
  componentType: 'TIME',
  formContext: 'SUBMIT',
  props: [
    {
      name: 'reverse',
      title: '节点排序方向',
      setter: 'BoolSetter',
      defaultValue: false,
    },
  ],
  snippets: [
    {
      title: '时间线',
      screenshot: require('./__screenshots__/image.svg'),
      schema: {
        componentName: 'ElTimeline',
        props: {},
        children: [
          {
            componentName: 'ElTimelineItem',
            props: { timestamp: '2018/4/3', placement: 'top' },
            children: [
              {
                componentName: 'ElCard',
                props: {},
                children: [
                  {
                    componentName: 'LCText',
                    props: {
                      text: '文字文字文字',
                    },
                  },
                ],
              },
            ],
          },
          {
            componentName: 'ElTimelineItem',
            props: { timestamp: '2018/4/5', placement: 'top' },
            children: [
              {
                componentName: 'ElCard',
                props: {},
                children: [
                  {
                    componentName: 'LCText',
                    props: {
                      text: '文字文字22文字',
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [],
    },
  },
}
