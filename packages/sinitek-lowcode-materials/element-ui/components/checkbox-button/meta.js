import checkboxButton from './__screenshots__/image.svg'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'ElCheckboxButton',
  title: '多选框按钮',
  category: '信息输入',
  props: [
    {
      name: 'label',
      title: 'value',
      setter: 'StringSetter',
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'name',
      title: '原生 name 属性',
      setter: 'StringSetter',
    },
  ],
  snippets: [
    {
      title: '多选框按钮',
      screenshot: checkboxButton,
      schema: {
        componentName: 'ElCheckboxButton',
        props: {
          label: '多选框按钮',
          value: '',
        },
        children: [],
      },
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [],
      supportBindState: false,
    },
    component: {
      isContainer: true,
      parentBlacklist: ['ElCheckboxButton', 'ElCheckbox'],
      parentWhitelist: ['ElCheckboxGroup'],
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '多选框按钮',
          })
        },
      },
    },
  },
}
