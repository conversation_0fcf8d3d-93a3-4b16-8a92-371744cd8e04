import screen from './__screenshots__/image.svg'
import step from '../step/meta.js'

export default {
  componentName: 'ElSteps',
  title: '步骤条',
  category: '信息展示',
  props: [
    {
      name: 'active',
      setter: 'NumberSetter',
      title: {
        label: '激活步骤',
        tip: '这里是激活步骤的下标，从0开始',
      },
    },

    {
      name: 'space',
      setter: 'StringSetter',
      title: '间距',
    },
    {
      name: 'direction',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            { label: '水平', value: 'horizontal' },
            { label: '垂直', value: 'vertical' },
          ],
        },
      },
      defaultValue: 'horizontal',
      title: '显示方向',
    },
    {
      name: 'process-status',
      title: '当前步骤的状态',
      defaultValue: 'process',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            { label: '未开始', value: 'wait' },
            { label: '进行中', value: 'process' },
            { label: '已完成', value: 'finish' },
            { label: '错误', value: 'error' },
            { label: '成功', value: 'success' },
          ],
        },
      },
    },
    {
      name: 'finish-status',
      title: '已完成的步骤状态',
      defaultValue: 'finish',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            { label: '未开始', value: 'wait' },
            { label: '进行中', value: 'process' },
            { label: '已完成', value: 'finish' },
            { label: '错误', value: 'error' },
            { label: '成功', value: 'success' },
          ],
        },
      },
    },
    {
      name: 'align-center',
      setter: 'BoolSetter',
      title: '居中',
    },
    {
      name: 'simple',
      setter: 'BoolSetter',
      title: {
        label: '简洁风格',
        tip: '该条件下 居中 / 描述 / 方向 / 间隔 都将失效。',
      },
    },
  ],
  childConfig: {
    componentName: 'ElStep',
    setter: {
      componentName: 'ArraySetter',
      props: {
        itemSetter: {
          componentName: 'ObjectSetter',
          props: {
            config: {
              items: step.props,
            },
          },
          initialValue: {
            componentName: 'ElStep',
            props: {
              title: '步骤',
            },
            children: [],
          },
        },
      },
    },
  },
  snippets: [
    {
      title: '步骤条',
      screenshot: screen,
      schema: {
        componentName: 'ElSteps',
        props: {},
        children: [
          {
            componentName: 'ElStep',
            props: {
              title: '步骤 1',
            },
            children: [],
          },
          {
            componentName: 'ElStep',
            props: {
              title: '步骤 2',
            },
            children: [],
          },
          {
            componentName: 'ElStep',
            props: {
              title: '步骤 3',
            },
            children: [],
          },
        ],
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
      childWrapRenderer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [],
    },
  },
}
