import timepicker from './__screenshots__/image.svg'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'ElTimePicker',
  title: '时间选择器',
  category: '信息输入',
  componentType: 'TIME',
  formContext: 'SUBMIT',
  props: [
    {
      name: 'defaultValue',
      title: '默认时间',
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/time-picker?prop=defaultValue#attributes',
    },
    {
      name: 'valueFormat',
      title: '绑定值格式',
      setter: {
        componentName: 'StringSetter',
        props: {
          placeholder: 'HH:mm:ss',
        },
      },
    },
    // {
    //   name: 'value',
    //   title: '时间选择器的值',
    //   setter: 'StringSetter',
    // },
    {
      name: 'readonly',
      title: '只读',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'editable',
      title: '可输入',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'clearable',
      title: { label: '可清除', tip: '是否可清除' },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    // {
    //   name: 'size',
    //   title: { label: '尺寸', tip: '设置尺寸大小' },
    //   description: '尺寸',
    //   setter: {
    //     componentName: 'SelectSetter',
    //     props: {
    //       options: [
    //         {
    //           title: '正常',
    //           value: 'medium',
    //         },
    //         {
    //           title: '中等',
    //           value: 'small',
    //         },
    //         {
    //           title: '迷你',
    //           value: 'mini',
    //         },
    //       ],
    //     },
    //   },
    // },
    {
      name: 'placeholder',
      title: '占位文本',
      setter: 'StringSetter',
    },

    {
      name: 'arrowControl',
      title: '使用箭头选择',
      setter: 'BoolSetter',
    },
    {
      name: 'align',
      title: '对齐方式',
      setter: {
        componentName: 'SelectSetter',
        defaultValue: 'left',
        props: {
          options: [
            {
              title: '靠左',
              value: 'left',
            },
            {
              title: '中间',
              value: 'center',
            },
            {
              title: '靠右',
              value: 'right',
            },
          ],
        },
      },
    },
    {
      name: 'popperClass',
      title: '下拉框类名',
      setter: 'StringSetter',
    },

    // {
    //   name: 'name',
    //   title: '原生属性',
    //   setter: 'StringSetter',
    // },
    {
      name: 'prefixIcon',
      title: '头部图标',
      defaultValue: 'el-icon-time',
      setter: {
        componentName: 'IconSetter',
        props: {
          type: 'element-ui',
        },
      },
    },
    {
      name: 'pickerOptions',
      title: '选择配置',
      setter: {
        componentName: 'ObjectSetter',
        props: {
          config: {
            items: [
              {
                name: 'selectableRange',
                title: '可选时间段',
                setter: 'JSONSetter',
                documentUrl:
                  'https://element.eleme.cn/#/zh-CN/component/time-picker?prop=selectableRange#attributes',
              },
              {
                name: 'format',
                title: '时间格式化',
                setter: 'StringSetter',
                defaultValue: 'HH:mm:ss',
              },
            ],
          },
        },
      },
    },
    {
      type: 'group',
      title: '范围选择',
      items: [
        {
          name: 'isRange',
          title: '开启',
          setter: 'BoolSetter',
        },
        {
          name: 'rangeSeparator',
          title: '分隔符',
          setter: 'StringSetter',
          defaultValue: '-',
        },
        {
          name: 'startPlaceholder',
          title: '开始时间占位文本',
          setter: 'StringSetter',
        },
        {
          name: 'endPlaceholder',
          title: '结束时间的占位文本',
          setter: 'StringSetter',
        },
      ],
    },
    // {
    //   name: 'clearIcon',
    //   title: '自定义清空图标',
    //   defaultValue: 'el-icon-circle-close',
    //   setter: {
    //     componentName: 'IconSetter',
    //     props: {
    //       type: 'element-ui',
    //     },
    //   },
    // },
  ],
  snippets: [
    {
      title: '时间选择器',
      screenshot: timepicker,
      schema: {
        componentName: 'ElTimePicker',
        props: { editable: true, clearable: true },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'change',
          description: '用户确认选定的值时触发',
          template: 'function change(value) { console.log(value);}',
        },
        {
          name: 'blur',
          description: '在失去焦点时触发的事件',
          template: 'function blur(event) { console.log(event);}',
        },
        {
          name: 'focus',
          description: '在获得焦点时触发',
          template: 'function focus(event) { console.log(event);}',
        },
      ],
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '时间选择器',
          })
        },
      },
    },
  },
}
