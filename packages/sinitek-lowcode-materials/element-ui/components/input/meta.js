// import input1 from './__screenshots__/input-1.png'
export default {
  componentName: 'ElInput',
  title: '输入框',
  category: '信息输入',
  componentType: 'INPUT',
  formContext: 'SUBMIT',
  props: [
    // {
    //   name: 'value',
    //   title: '输入框的值',
    //   setter: 'StringSetter',
    // },
    {
      name: 'type',
      title: { label: '输入框类型', tip: '' },
      defaultValue: 'text',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              title: '文本',
              value: 'text',
            },
            {
              title: '文本域',
              value: 'textarea',
            },
          ],
        },
      },
    },
    {
      name: 'maxlength',
      title: { label: '最大输入长度', tip: '' },
      setter: 'NumberSetter',
      condition(props) {
        return props.showWordLimit
      },
    },
    {
      name: 'minlength',
      title: { label: '最小输入长度', tip: '' },
      setter: 'NumberSetter',
    },
    {
      name: 'showWordLimit',
      title: { label: '字数限制', tip: '是否开启字数限制' },
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'placeholder',
      title: '占位文本',
      setter: 'StringSetter',
    },
    {
      name: 'clearable',
      title: { label: '可清除', tip: '是否可清除' },
      setter: 'BoolSetter',
    },
    {
      name: 'showPassword',
      title: { label: '密码框', tip: '是否切换密码框' },
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'size',
      title: { label: '输入框尺寸', tip: '设置输入框尺寸大小' },
      description: '尺寸',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '正常',
              value: 'medium',
            },
            {
              title: '中等',
              value: 'small',
            },
            {
              title: '迷你',
              value: 'mini',
            },
          ],
        },
      },
      condition(props) {
        return props.type !== 'textarea'
      },
    },
    {
      name: 'prefixIcon',
      title: { label: '头部图标', tip: '输入框头部图标' },
      setter: 'StringSetter',
      defaultValue: '',
    },
    {
      name: 'suffixIcon',
      title: { label: '尾部图标', tip: '输入框尾部图标' },
      setter: 'StringSetter',
      defaultValue: '',
    },
    {
      name: 'rows',
      title: { label: '输入框行数', tip: '自适应输入框行数' },
      setter: 'NumberSetter',
      condition(props) {
        return props.type === 'textarea'
      },
    },
    {
      name: 'autosize',
      title: '自适应内容高度',
      setter: 'BoolSetter',
      condition(props) {
        return props.type === 'textarea'
      },
    },
    {
      name: 'name',
      title: '原生属性',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'readonly',
      title: { label: '只读', tip: '是否只读' },
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'max',
      title: '最大值',
      setter: 'NumberSetter',
    },
    {
      name: 'min',
      title: '最小值',
      setter: 'NumberSetter',
    },
    {
      name: 'step',
      title: '字段数字间隔',
      setter: 'NumberSetter',
    },
    {
      name: 'resize',
      title: { label: '缩放', tip: '控制是否能被用户缩放' },
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '无',
              value: 'none',
            },
            {
              title: '水平和垂直',
              value: 'both',
            },
            {
              title: '水平',
              value: 'horizontal',
            },
            {
              title: '垂直',
              value: 'vertical',
            },
          ],
        },
      },
    },
    {
      name: 'autofocus',
      title: { label: '获取焦点', tip: '能否自动获取焦点' },
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'form',
      title: 'form原生属性',
      setter: 'StringSetter',
    },
    {
      name: 'label',
      title: { label: '文字', tip: '输入框关联的label文字' },
      setter: 'StringSetter',
    },
    {
      name: 'tabindex',
      title: { label: 'tabindex', tip: '输入框的tabindex' },
      setter: 'StringSetter',
    },
    {
      name: 'validateEvent',
      title: { label: '校验', tip: '输入时是否触发表单的校验' },
      setter: 'BoolSetter',
      default: true,
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'prefix',
          title: '输入框头部',
          setter: 'SlotSetter',
          supportVariable: false,
          condition(props) {
            return !props.type || props.type === 'text'
          },
        },
        {
          name: 'suffix',
          title: '输入框尾部',
          setter: 'SlotSetter',
          supportVariable: false,
          condition(props) {
            return !props.type || props.type === 'text'
          },
        },
        {
          name: 'prepend',
          title: '输入框前置内容',
          setter: 'SlotSetter',
          supportVariable: false,
          condition(props) {
            return !props.type || props.type === 'text'
          },
        },
        {
          name: 'append',
          title: '输入框后置内容',
          setter: 'SlotSetter',
          supportVariable: false,
          condition(props) {
            return !props.type || props.type === 'text'
          },
        },
      ],
    },
  ],
  // snippets: [
  //   {
  //     title: '输入框',
  //     screenshot: input1,
  //     schema: {
  //       componentName: 'XnInput',
  //       props: { type: 'text' },
  //     },
  //   },
  // ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      condition: true,
      style: true,
      events: [
        {
          name: 'blur',
          description: '在 Input 失去焦点时触发的事件',
          template: 'function blur(event) { console.log(event);}',
        },
        {
          name: 'focus',
          description: '在 Input 获得焦点时触发',
          template: 'function focus(event) { console.log(event);}',
        },
        {
          name: 'change',
          description: '仅在输入框失去焦点或用户按下回车时触发',
          template: 'function change(value) { console.log(value);}',
        },
        {
          name: 'input',
          description: '在 Input 值改变时触发',
          template: 'function input(value) { console.log(value);}',
        },
        {
          name: 'clear',
          description: '在点击由 clearable 属性生成的清空按钮时触发',
          template: 'function clear() { console.log("cleared");}',
        },
      ],
    },
  },
}
