import inputNumber from './__screenshots__/image.svg'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'ElInputNumber',
  title: '计数器',
  category: '信息输入',
  componentType: 'NUMBER',
  formContext: 'SUBMIT',
  props: [
    // {
    //   name: 'value',
    //   title: '计数器的绑定值',
    //   setter: 'NumberSetter',
    // },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'placeholder',
      title: '占位文本',
      setter: 'StringSetter',
    },
    {
      name: 'min',
      title: '最小值',
      setter: 'NumberSetter',
    },
    {
      name: 'max',
      title: '最大值',
      setter: 'NumberSetter',
    },
    {
      name: 'step',
      title: '步长',
      setter: 'NumberSetter',
      defaultValue: 1,
    },
    {
      name: 'stepStrictly',
      title: '步长的倍数',
      setter: 'BoolSetter',
    },
    {
      name: 'precision',
      title: '数值精度',
      setter: 'NumberSetter',
    },
    {
      name: 'size',
      title: '尺寸',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            { label: '大', value: 'large' },
            { label: '小', value: 'small' },
          ],
        },
      },
    },

    {
      name: 'controlsPosition',
      title: {
        label: '控制按钮',
        tip: '控制按钮位置，只能设置right或者空字符串',
      },
      setter: 'StringSetter',
    },
    // {
    //   name: 'controls',
    //   title: '控制按钮',
    //   defaultValue: true,
    //   setter: 'BoolSetter',
    // },
    // {
    //   name: 'name',
    //   title: '原生属性',
    //   setter: 'StringSetter',
    // },
    // {
    //   name: 'label',
    //   title: '输入框文字',
    //   setter: 'StringSetter',
    // },
  ],
  snippets: [
    {
      title: '计数器',
      screenshot: inputNumber,
      schema: {
        componentName: 'ElInputNumber',
        props: {},
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'change',
          description: '绑定值变化时触发的事件',
          template:
            'function change(currentValue, oldValue) { console.log(currentValue, oldValue);}',
        },
        {
          name: 'blur',
          description: '在组件 Input 失去焦点时触发',
          template: 'function blur(event) { console.log(event);}',
        },
        {
          name: 'focus',
          description: '在组件 Input 获得焦点时触发',
          template: 'function focus(event) { console.log(event);}',
        },
      ],
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '计数器',
          })
        },
      },
    },
  },
}
