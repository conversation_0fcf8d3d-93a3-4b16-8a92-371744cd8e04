import switch1 from './__screenshots__/image.svg'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'ElSwitch',
  title: '按钮',
  category: '基础',
  props: [
    {
      name: 'disabled',
      title: '是否禁用',
      setter: 'BoolSetter',
    },
    // {
    //   name: 'width',
    //   title: '宽度（像素）',
    //   defaultValue: 40,
    //   setter: 'NumberSetter',
    // },
    {
      type: 'group',
      title: '开启状态',
      items: [
        {
          name: 'active-value',
          title: '值',
          defaultValue: true,
          setter: ['BoolSetter', 'StringSetter', 'NumberSetter'],
        },
        {
          name: 'active-icon-class',
          title: {
            label: '图标',
            tip: '打开时所显示图标的类名，设置此项会忽略文字描述',
          },
          setter: {
            componentName: 'IconSetter',
            props: {
              type: 'element-ui',
            },
          },
        },
        {
          name: 'active-text',
          title: '文字描述',
          setter: 'StringSetter',
        },
        {
          name: 'active-color',
          title: '背景色',
          setter: 'ColorSetter',
        },
      ],
    },
    {
      type: 'group',
      title: '关闭状态',
      items: [
        {
          name: 'inactive-value',
          title: '值',
          setter: ['BoolSetter', 'StringSetter', 'NumberSetter'],
        },
        {
          name: 'inactive-icon-class',
          title: {
            label: '饿了么图标',
            tip: '关闭时所显示图标的类名，设置此项会忽略文字描述',
          },
          setter: {
            componentName: 'IconSetter',
            props: {
              type: 'element-ui',
            },
          },
        },

        {
          name: 'inactive-text',
          title: '文字描述',
          setter: 'StringSetter',
        },

        {
          name: 'inactive-color',
          title: '背景色',
          setter: 'ColorSetter',
        },
      ],
    },
  ],
  snippets: [
    {
      title: '开关',
      screenshot: switch1,
      schema: {
        componentName: 'ElSwitch',
        props: {},
      },
    },
  ],

  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'change',
          description: '状态发生变化时的回调函数',
          template: 'function change(v) { console.log(v);}',
        },
      ],
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '开关',
          })
        },
      },
    },
  },
}
