export default {
  componentName: 'ElCascader',
  title: '级联选择器',
  category: '基础',
  props: [
    {
      name: 'options',
      title: { label: 'options', tip: '数据源' },
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/cascader?prop=options#cascader-attributes',
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
    },
  },
}
