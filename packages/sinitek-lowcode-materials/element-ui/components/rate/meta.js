import rate from './__screenshots__/image.svg'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'ElRate',
  title: '评分',
  category: '信息输入',
  componentType: 'RATE',
  formContext: 'SUBMIT',
  props: [
    // {
    //   name: 'value',
    //   title: '评分的值',
    //   setter: 'NumberSetter',
    //   defaultValue: 0,
    // },
    {
      name: 'max',
      title: '最大分值',
      setter: 'NumberSetter',
      defaultValue: 5,
    },
    {
      name: 'allowHalf',
      title: '允许半选',
      setter: 'BoolSetter',
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'lowThreshold',
      title: {
        label: '低分界限值',
        tip: '低分和中等分数的界限值，值本身被划分在低分中',
      },
      setter: 'NumberSetter',
      defaultValue: 2,
    },
    {
      name: 'highThreshold',
      title: {
        label: '高分界限值',
        tip: '高分和中等分数的界限值，值本身被划分在高分中',
      },
      setter: 'NumberSetter',
      defaultValue: 4,
    },

    {
      type: 'group',
      title: '样式',
      items: [
        {
          name: 'voidColor',
          title: '颜色',
          setter: 'ColorSetter',
          defaultValue: '#C6D1DE',
        },
        {
          name: 'voidIconClass',
          title: '图标',
          defaultValue: 'el-icon-star-off',
          setter: {
            componentName: 'IconSetter',
            props: {
              type: 'element-ui',
            },
          },
        },
        {
          name: 'disabledVoidColor',
          title: '只读时颜色',
          setter: 'ColorSetter',
          defaultValue: '#EFF2F7',
        },
        {
          name: 'disabledVoidIconClass',
          title: '只读时图标',
          defaultValue: 'el-icon-star-on',
          setter: {
            componentName: 'IconSetter',
            props: {
              type: 'element-ui',
            },
          },
        },
        {
          name: 'colors',
          title: '选中颜色数组',
          setter: 'JSONSetter',
          defaultValue: ['#F7BA2A', '#F7BA2A', '#F7BA2A'],
          documentUrl:
            'https://element.eleme.cn/#/zh-CN/component/rate?prop=colors#attributes',
        },
        {
          name: 'iconClasses',
          title: '选中图标数组',
          setter: 'JSONSetter',
          defaultValue: [
            'el-icon-star-on',
            'el-icon-star-on',
            'el-icon-star-on',
          ],
          documentUrl:
            'https://element.eleme.cn/#/zh-CN/component/rate?prop=iconClasses#attributes',
        },
      ],
    },
    {
      type: 'group',
      title: '辅助文字',
      items: [
        {
          name: 'showScore',
          title: {
            label: '当前分数',
            tip: '是否显示当前分数，show-score 和 show-text 不能同时为真',
          },
          setter: 'BoolSetter',
          condition: (props) => {
            return !props.showText
          },
        },
        {
          name: 'scoreTemplate',
          title: '分数显示模板',
          setter: {
            componentName: 'StringSetter',
            props: {
              placeholder: '当前分数：{value}',
            },
          },
          condition: (props) => {
            return props.showScore
          },
        },
        {
          name: 'showText',
          title: {
            label: '辅助文字',
            tip: '是否显示辅助文字，若为真，则会从 texts 数组中选取当前分数对应的文字内容',
          },
          setter: 'BoolSetter',
          condition: (props) => {
            return !props.showScore
          },
        },

        {
          name: 'texts',
          title: '辅助文字数组',
          setter: 'JSONSetter',
          defaultValue: ['极差', '失望', '一般', '满意', '惊喜'],
          condition: (props) => {
            return props.showText
          },
          documentUrl:
            'https://element.eleme.cn/#/zh-CN/component/rate?prop=texts#attributes',
        },
        {
          name: 'textColor',
          title: '文字的颜色',
          setter: 'ColorSetter',
          defaultValue: '#1F2D3D',
        },
      ],
    },
  ],
  snippets: [
    {
      title: '评分',
      screenshot: rate,
      schema: {
        componentName: 'ElRate',
        props: {},
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'change',
          description: '分值改变时触发',
          template: 'function change(rate) { console.log(rate);}',
        },
      ],
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '评分',
          })
        },
      },
    },
  },
}
