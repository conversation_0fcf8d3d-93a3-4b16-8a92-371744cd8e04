import checkboxGroup from './__screenshots__/image.svg'
import checkboxButton1 from '../checkbox-button/__screenshots__/image.svg'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'ElCheckboxGroup',
  title: '多选框组',
  category: '信息输入',
  componentType: 'CHECKBOX_GROUP',
  formContext: 'SUBMIT',
  props: [
    // {
    //   name: 'value',
    //   title: '多选框绑定值',
    //   setter: 'StringSetter',
    // },
    {
      name: 'size',
      title: '多选框尺寸',
      setter: 'StringSetter',
      condition(props) {
        return props.border
      },
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'min',
      title: '可选最小数量',
      setter: 'NumberSetter',
    },
    {
      name: 'max',
      title: '可选最大数量',
      setter: 'NumberSetter',
    },
    {
      name: 'textColor',
      title: '激活时的文本颜色',
      setter: 'ColorSetter',
      defaultValue: '#ffffff',
    },
    {
      name: 'fill',
      title: '填充色和边框色',
      setter: 'ColorSetter',
      defaultValue: 'var(--xn-color-primary,#409EFF)',
    },
  ],
  snippets: [
    {
      title: '多选框组',
      screenshot: checkboxGroup,
      schema: {
        componentName: 'ElCheckboxGroup',
        props: {
          value: ['多选框文本'],
        },
        children: [
          {
            componentName: 'ElCheckbox',
            props: {
              label: '多选框文本',
            },
            children: [
              {
                componentName: 'LCText',
                props: {
                  text: '多选框文本',
                },
              },
            ],
          },
          {
            componentName: 'ElCheckbox',
            props: {
              label: '多选框文本2',
            },
            children: [
              {
                componentName: 'LCText',
                props: {
                  text: '多选框文本2',
                },
              },
            ],
          },
        ],
      },
    },
    {
      title: '多选框按钮组',
      screenshot: checkboxButton1,
      schema: {
        componentName: 'ElCheckboxGroup',
        props: {
          value: ['多选框文本'],
        },
        children: [
          {
            componentName: 'ElCheckboxButton',
            props: {
              label: '多选框文本',
            },
            children: [
              {
                componentName: 'LCText',
                props: {
                  text: '多选框文本',
                },
              },
            ],
          },
          {
            componentName: 'ElCheckboxButton',
            props: {
              label: '多选框文本2',
            },
            children: [
              {
                componentName: 'LCText',
                props: {
                  text: '多选框文本2',
                },
              },
            ],
          },
        ],
      },
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'change',
          description: '绑定值变化时触发的事件',
          template: 'function change(value) { console.log(value);}',
        },
      ],
    },
    component: {
      isContainer: true,
      nestingRule: {
        parentBlacklist: ['ElCheckboxButton', 'ElCheckbox', 'ElCheckboxGroup'],
      },
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '多选框组',
          })
        },
      },
    },
  },
}
