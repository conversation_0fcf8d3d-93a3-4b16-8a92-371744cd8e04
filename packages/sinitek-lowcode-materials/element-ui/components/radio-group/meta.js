import radioGroup from './__screenshots__/image.svg'
import radioButton from '../radio-button/__screenshots__/image.svg'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'ElRadioGroup',
  title: '单选框组',
  category: '信息输入',
  componentType: 'RADIO_GROUP',
  formContext: 'SUBMIT',
  props: [
    // {
    //   name: 'value',
    //   title: '单选框绑定值',
    //   setter: 'StringSetter',
    // },
    {
      name: 'size',
      title: '单选框尺寸',
      setter: 'StringSetter',
      condition(props) {
        return props.border
      },
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'textColor',
      title: '激活时的文本颜色',
      setter: 'ColorSetter',
      defaultValue: '#ffffff',
    },
    {
      name: 'fill',
      title: '填充色和边框色',
      setter: 'ColorSetter',
      defaultValue: 'var(--xn-color-primary,#409EFF)',
    },
  ],
  snippets: [
    {
      title: '单选框组',
      screenshot: radioGroup,
      schema: {
        componentName: 'ElRadioGroup',
        props: {},
        children: [
          {
            componentName: 'ElRadio',
            props: {
              label: '单选框文本',
            },
            children: [
              {
                componentName: 'LCText',
                props: {
                  text: '单选框文本',
                },
              },
            ],
          },
          {
            componentName: 'ElRadio',
            props: {
              label: '单选框文本2',
            },
            children: [
              {
                componentName: 'LCText',
                props: {
                  text: '单选框文本2',
                },
              },
            ],
          },
        ],
      },
    },
    {
      title: '单选框按钮组',
      screenshot: radioButton,
      schema: {
        componentName: 'ElRadioGroup',
        props: {},
        children: [
          {
            componentName: 'ElRadioButton',
            props: {
              label: '单选框文本',
            },
            children: [],
          },
          {
            componentName: 'ElRadioButton',
            props: {
              label: '单选框文本2',
            },
            children: [],
          },
        ],
      },
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'input',
          description: '绑定值变化时触发的事件',
          template: 'function input(label ) { console.log(label);}',
        },
      ],
    },
    component: {
      isContainer: true,
      nestingRule: {
        parentBlacklist: ['ElRadioButton', 'ElRadio', 'ElRadioGroup'],
      },
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '单选框组',
          })
        },
      },
    },
  },
}
