import slider from './__screenshots__/image.svg'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'ElSlider',
  title: '滑块',
  category: '信息输入',
  componentType: 'SLIDER',
  formContext: 'SUBMIT',
  props: [
    // {
    //   name: 'value',
    //   title: '滑块的绑定值',
    //   setter: 'NumberSetter',
    //   defaultValue: 0,
    // },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'min',
      title: '最小值',
      setter: 'NumberSetter',
      defaultValue: 0,
    },
    {
      name: 'max',
      title: '最大值',
      setter: 'NumberSetter',
      defaultValue: 100,
    },

    {
      name: 'step',
      title: '步长',
      setter: 'NumberSetter',
      defaultValue: 1,
    },
    {
      name: 'showInput',
      title: '显示输入框',
      setter: 'BoolSetter',
    },
    {
      name: 'debounce',
      title: '去抖延迟',
      setter: 'NumberSetter',
      defaultValue: 300,
      condition(props) {
        return props.showInput
      },
    },
    // {
    //   name: 'showInputControls',
    //   title: '显示输入框控制按钮',
    //   setter: 'BoolSetter',
    //   defaultValue: true,
    // },
    // {
    //   name: 'inputSize',
    //   title: { label: '尺寸', tip: '设置尺寸大小' },
    //   description: '尺寸',
    //   setter: {
    //     componentName: 'RadioGroupSetter',
    //     props: {
    //       options: [
    //         {
    //           title: '最大',
    //           value: 'large',
    //         },
    //         {
    //           title: '中等',
    //           value: 'medium',
    //         },
    //         {
    //           title: '较小',
    //           value: 'small',
    //         },
    //         {
    //           title: '迷你',
    //           value: 'mini',
    //         },
    //       ],
    //     },
    //   },
    // },
    {
      name: 'showStops',
      title: '显示间断点',
      setter: 'BoolSetter',
    },
    {
      name: 'range',
      title: '范围选择',
      setter: 'BoolSetter',
    },
    {
      name: 'showTooltip',
      title: '显示提示',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'formatTooltip',
      title: '格式化提示',
      defaultValue: false,
      setter: 'FunctionSetter',
    },
    {
      name: 'tooltipClass',
      title: '提示类名',
      setter: 'StringSetter',
    },
    {
      name: 'vertical',
      title: '竖向模式',
      setter: 'BoolSetter',
    },
    {
      name: 'height',
      title: '竖向高度',
      setter: 'StringSetter',
    },

    {
      name: 'marks',
      title: '标记',
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/slider?prop=marks#attributes',
    },
  ],
  snippets: [
    {
      title: '滑块',
      screenshot: slider,
      schema: {
        componentName: 'ElSlider',
        props: {},
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'change',
          description: '绑定值变化时触发的事件',
          template: 'function change(value) { console.log(value);}',
        },
        {
          name: 'input',
          description: '数据改变时触发的事件',
          template: 'function input(value) { console.log(value);}',
        },
      ],
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '滑块',
          })
        },
      },
    },
  },
}
