import card from './__screenshots__/image.svg'

export default {
  componentName: 'ElCard',
  title: '卡片',
  category: '基础',
  props: [
    {
      name: 'header',
      title: '设置 header',
      setter: 'StringSetter',
    },
    {
      name: 'bodyStyle',
      title: 'body 的样式',
      setter: 'JSONSetter',
    },
    {
      name: 'shadow',
      title: '阴影',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '始终',
              value: 'always',
            },
            {
              title: '鼠标移入',
              value: 'hover',
            },
            {
              title: '隐藏',
              value: 'never',
            },
          ],
        },
      },
    },
  ],
  snippets: [
    {
      title: '卡片',
      screenshot: card,
      schema: {
        componentName: 'ElCard',
        props: {
          shadow: 'always',
        },
        children: [
          {
            componentName: 'LCText',
            props: {
              text: '文字',
            },
          },
        ],
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [],
    },
  },
}
