export default {
  componentName: 'ElDropdownItem',
  title: '按钮菜单下拉',
  category: '信息展示',
  props: [
    {
      name: 'command',
      title: '指令',
      setter: ['StringSetter', 'NumberSetter', 'JSONSetter'],
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/dropdown?prop=command#dropdown-menu-item-attributes',
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'divided',
      title: '显示分割线',
      setter: 'BoolSetter',
    },
    {
      name: 'icon',
      title: '图标类名',
      setter: {
        componentName: 'IconSetter',
        props: {
          type: 'element-ui',
        },
      },
    },
  ],
  // snippets: [
  //   {
  //     title: '按钮菜单下拉',
  //     schema: {
  //       componentName: 'ElDropdownItem',
  //       props: {},
  //       children: [
  //         {
  //           componentName: 'LCText',
  //           props: { text: '按钮菜单' },
  //         },
  //       ],
  //     },
  //   },
  // ],
  configure: {
    component: {
      isContainer: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [],
    },
  },
}
