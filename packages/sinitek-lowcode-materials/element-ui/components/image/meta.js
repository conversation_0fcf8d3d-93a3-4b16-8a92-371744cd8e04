// import image from './__screenshots__/image.png'
export default {
  componentName: 'ElImage',
  title: '图片',
  category: '基础',
  props: [
    {
      name: 'src',
      title: '图片源',
      setter: 'StringSetter',
    },
    {
      name: 'fit',
      title: { label: '缩放类型', tip: '确定图片如何适应容器框' },
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: 'fill',
              value: 'fill',
            },
            {
              title: 'contain',
              value: 'contain',
            },
            {
              title: 'cover',
              value: 'cover',
            },
            {
              title: 'none',
              value: 'none',
            },
            {
              title: 'scale-down',
              value: 'scale-down',
            },
          ],
        },
      },
    },
    {
      name: 'alt',
      title: '原生alt',
      setter: 'StringSetter',
    },
    {
      name: 'referrerPolicy',
      title: '原生 referrerPolicy',
      setter: 'StringSetter',
    },
    {
      name: 'lazy',
      title: '开启懒加载',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'scrollContainer',
      title: '懒加载监听',
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/image?prop=scrollContainer#attributes',
    },
    {
      name: 'previewSrcList',
      title: '图片预览列表',
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/image?prop=previewSrcList#attributes',
    },
    {
      name: 'zIndex',
      title: '层级',
      setter: 'NumberSetter',
      defaultValue: 2000,
    },
    {
      name: 'initial-index',
      title: '初始层级',
      setter: 'NumberSetter',
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'placeholder',
          title: '图片未加载的占位内容',
          setter: 'SlotSetter',
        },
        {
          name: 'error',
          title: '加载失败的内容',
          setter: 'SlotSetter',
        },
      ],
    },
  ],
  snippets: [
    {
      title: '图片',
      screenshot: require('./__screenshots__/image.svg'),
      schema: {
        componentName: 'ElImage',
        props: {
          src: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
          previewSrcList: [
            'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
          ],
        },
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'load',
          description: '图片加载成功触发',
          template: 'function load(event) { console.log(event);}',
        },
        {
          name: 'error',
          description: '图片加载失败触发',
          template: 'function error(error) { console.log(error);}',
        },
      ],
    },
  },
}
