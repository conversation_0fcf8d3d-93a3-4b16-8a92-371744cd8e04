export default {
  componentName: 'ElTableColumn',
  title: '表格',
  category: 'ElementUI',
  componentType: 'RELA_LIST',
  formContext: 'SUBMIT',
  props: [
    {
      name: 'label',
      title: '显示的标题',
      setter: 'StringSetter',
    },
    {
      name: 'prop',
      title: '对应列内容的字段名',
      setter: 'StringSetter',
    },
    {
      name: 'type',
      title: '列的类型',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              title: '选择',
              value: 'selection',
            },
            {
              title: '排序',
              value: 'index',
            },
            {
              title: '可展开',
              value: 'expand',
            },
          ],
        },
      },
    },
    {
      name: 'index',
      title: '索引',
      setter: 'NumberSetter',
      condition(props) {
        return props.type === 'index'
      },
    },
    {
      name: 'columnKey',
      title: 'column 的 key',
      setter: 'StringSetter',
    },
    {
      name: 'width',
      title: '列的宽度',
      setter: 'StringSetter',
    },
    {
      name: 'minWidth',
      title: {
        label: '列的最小宽度',
        tip: '与 width 的区别是 width 是固定的，min-width 会把剩余宽度按比例分配给设置了 min-width 的列',
      },
      setter: 'StringSetter',
    },
    {
      name: 'fixed',
      title: {
        label: '列是否固定',
        tip: '列是否固定在左侧或者右侧，true 表示固定在左侧',
      },
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              title: '固定',
              value: true,
            },
            {
              title: '左',
              value: 'left',
            },
            {
              title: '右',
              value: 'right',
            },
          ],
        },
      },
    },
    {
      name: 'renderHeader',
      title: 'Label 区域渲染使用的 Function',
      setter: 'FunctionSetter',
    },
    {
      name: 'sortable',
      title: {
        label: '排序',
        tip: '如果设置为 custom，则代表用户希望远程排序，需要监听 Table 的 sort-change 事件',
      },
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              title: '是',
              value: true,
            },
            {
              title: '否',
              value: false,
            },
            {
              title: '远程排序',
              value: 'custom',
            },
          ],
        },
      },
    },
    {
      name: 'sortMethod',
      title: '排序方法',
      setter: 'FunctionSetter',
      condition(props) {
        return props.sortable === true
      },
    },
    {
      name: 'sortBy',
      title: '按照哪个属性进行排序',
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/table?prop=sortBy#table-column-attributes',
      condition(props) {
        return props.sortable === true && !props.sortMethod
      },
    },
    {
      name: 'sortOrders',
      title: '排序策略的轮转顺序',
      setter: 'JSONSetter',
      condition(props) {
        return props.sortable === true
      },
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/table?prop=sortOrders#table-column-attributes',
    },
    {
      name: 'resizable',
      title: '拖动改变宽度',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'formatter',
      title: '格式化内容方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'showOverflowTooltip',
      title: '显示 tooltip',
      setter: 'BoolSetter',
    },
    {
      name: 'align',
      title: '对齐方式',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              title: '左',
              value: 'left',
            },
            {
              title: '中',
              value: 'center',
            },
            {
              title: '右',
              value: 'right',
            },
          ],
        },
      },
      defaultValue: 'left',
    },
    {
      name: 'className',
      title: '列的 className',
      setter: 'StringSetter',
    },
    {
      name: 'labelClassName',
      title: '当前列标题的自定义类名',
      setter: 'StringSetter',
    },
    {
      name: 'selectable',
      title: '是否可以勾选',
      setter: 'FunctionSetter',
    },
    {
      name: 'reserveSelection',
      title: '反选',
      condition(props) {
        return props.type === 'selection '
      },
      setter: 'BoolSetter',
    },
    {
      name: 'filters',
      title: '数据过滤的选项',
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/table?prop=filters#table-column-attributes',
    },
    {
      name: 'filterPlacement',
      title: '过滤弹出框的定位',
      setter: 'StringSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/table?prop=filterPlacement#table-column-attributes',
    },
    {
      name: 'filterMultiple',
      title: '过滤的选项是否多选',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'filterMethod',
      title: '数据过滤使用的方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'filteredValue',
      title: '选中的数据过滤项',
      setter: 'JSONSetter',
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'header',
          title: { label: '头部插槽', tip: '自定义表头的内容' },
          setter: 'SlotSetter',
        },
        {
          name: 'default',
          title: { label: '内容插槽', tip: '自定义列的内容' },
          setter: 'SlotSetter',
        },
      ],
    },
  ],
  configure: {
    component: {
      isContainer: false,
      clickCapture: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'select',
          description: '当用户手动勾选数据行的 Checkbox 时触发的事件',
          template:
            'function onSelect(selection, row) { console.log(selection, row);}',
        },
        {
          name: 'cell-click',
          description: '当某个单元格被点击时会触发该事件',
          template:
            'function onCellClick(row, column, cell, event) { console.log(row, column, cell, event);}',
        },
      ],
    },
  },
}
