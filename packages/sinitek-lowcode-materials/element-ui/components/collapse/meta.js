import image from './__screenshots__/image.svg'
import collapse from '../collapse-item/meta'
export default {
  componentName: 'ElCollapse',
  title: '折叠面板',
  category: '容器',
  props: [
    {
      name: 'accordion',
      title: '手风琴模式',
      defaultValue: false,
      setter: 'BoolSetter',
    },
  ],
  childConfig: {
    componentName: 'LACollapseItem',
    title: '折叠面板项配置',
    setter: {
      componentName: 'ArraySetter',
      props: {
        itemSetter: {
          componentName: 'ObjectSetter',
          props: {
            textField: 'title',
            config: {
              items: collapse.props,
            },
          },
          initialValue(i) {
            return {
              componentName: 'LACollapseItem',
              props: {
                title: '折叠面板' + i,
              },
              children: [],
            }
          },
        },
      },
    },
  },
  snippets: [
    {
      title: '折叠面板',
      screenshot: image,
      schema: {
        componentName: 'ElCollapse',
        props: {},
        children: [
          {
            componentName: 'LACollapseItem',
            props: {
              title: '折叠面板1',
            },
            children: [{ componentName: 'Slot', props: {}, children: [] }],
          },
          {
            componentName: 'LACollapseItem',
            props: {
              title: '折叠面板2',
            },
            children: [{ componentName: 'Slot', props: {}, children: [] }],
          },
        ],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
      clickCapture: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'change',
          description:
            '当前激活面板改变时触发(如果是手风琴模式，参数 activeNames 类型为string，否则为array)',
          template: 'function change(activeNames) { console.log("clicked");}',
        },
      ],
    },
  },
}
