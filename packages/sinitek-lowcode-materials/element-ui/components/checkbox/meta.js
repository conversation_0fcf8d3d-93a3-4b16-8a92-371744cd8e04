import checkbox from './__screenshots__/checkbox.svg'
export default {
  componentName: 'ElCheckbox',
  title: '多选框',
  category: '信息输入',
  componentType: 'CHECKBOX',
  formContext: 'SUBMIT',
  props: [
    {
      name: 'label',
      title: '多选框的 value',
      setter: 'StringSetter',
    },
    // {
    //   name: 'value',
    //   title: '多选框绑定值',
    //   setter: 'BoolSetter',
    // },
    {
      name: 'trueLabel',
      title: '选中时的值',
      setter: 'StringSetter',
    },
    {
      name: 'trueLabel',
      title: '未选中时的值',
      setter: 'StringSetter',
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'border',
      title: '显示边框',
      setter: 'BoolSetter',
    },
    {
      name: 'size',
      title: '多选框尺寸',
      condition(props) {
        return props.border
      },
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '正常',
              value: 'medium',
            },
            {
              title: '中等',
              value: 'small',
            },
            {
              title: '迷你',
              value: 'mini',
            },
          ],
        },
      },
    },
    {
      name: 'name',
      title: '原生 name 属性',
      setter: 'StringSetter',
    },
    {
      name: 'checked',
      title: '勾选',
      setter: 'BoolSetter',
    },
    {
      name: 'indeterminate',
      title: '设置即刻状态',
      setter: 'BoolSetter',
    },
  ],
  snippets: [
    {
      title: '多选框',
      screenshot: checkbox,
      schema: {
        componentName: 'ElCheckbox',
        props: {
          label: '多选框文本',
        },
        children: [
          {
            componentName: 'LCText',
            props: {
              text: '多选框文本',
            },
          },
        ],
      },
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'change',
          description: '绑定值变化时触发的事件',
          template: 'function change(value) { console.log(value);}',
        },
      ],
    },
    component: {
      isContainer: true,
      nestingRule: {
        // parentBlacklist: ['ElCheckboxButton', 'ElCheckbox'],
      },
    },
  },
}
