import radioButton from './__screenshots__/image.svg'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'ElRadioButton',
  title: '单选框按钮',
  category: '信息输入',
  props: [
    {
      name: 'label',
      title: 'value',
      setter: 'StringSetter',
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'name',
      title: '原生 name 属性',
      setter: 'StringSetter',
    },
  ],
  snippets: [
    {
      title: '单选框按钮',
      screenshot: radioButton,
      schema: {
        componentName: 'ElRadioButton',
        props: {
          label: '单选框按钮',
        },
      },
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: false,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [],
      supportBindState: false,
    },
    component: {
      isContainer: true,
      nestingRule: {
        parentBlacklist: ['ElRadioButton', 'ElRadio'],
        parentWhitelist: ['ElRadioGroup'],
      },
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '单选框按钮',
          })
        },
      },
    },
  },
}
