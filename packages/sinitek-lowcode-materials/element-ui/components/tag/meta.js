import tag from './__screenshots__/tag.svg'
export default {
  componentName: 'ElTag',
  title: '标签',
  category: '基础',
  props: [
    {
      name: 'type',
      title: '类型',
      setter: 'StringSetter',
    },
    {
      name: 'color',
      title: '颜色',
      setter: 'StringSetter',
    },
    {
      name: 'size',
      title: '尺寸',
      setter: 'StringSetter',
    },
    {
      name: 'effect',
      title: '效果',
      setter: 'StringSetter',
    },
    {
      name: 'hit',
      title: '是否可点击',
      setter: 'BooleanSetter',
    },
    {
      name: 'closable',
      title: '是否可关闭',
      setter: 'BooleanSetter',
    },
    {
      name: 'disableTransitions',
      title: '是否禁用渐变动画',
      setter: 'BooleanSetter',
    },
  ],
  snippets: [
    {
      title: '标签',
      screenshot: tag,
      schema: {
        componentName: 'ElTag',
        props: {},
        children: [
          {
            componentName: 'LCText',
            props: {
              text: '标签',
            },
          },
        ],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'click',
          description: '点击按钮触发的事件',
          template: 'function click(event) { console.log(event);}',
        },
        {
          name: 'close',
          description: '关闭按钮触发的事件',
          template: 'function close(event) { console.log(event);}',
        },
      ],
      supportBindState: false,
    },
  },
}
