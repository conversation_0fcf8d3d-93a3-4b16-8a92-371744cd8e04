// import btn1 from './__screenshots__/button-1.png'
export default {
  componentName: 'ElCollapseItem',
  title: '折叠面板项',
  category: '容器',
  props: [
    {
      name: 'name',
      title: '唯一标志符',
      setter: 'StringSetter',
    },
    {
      name: 'title',
      title: '面板标题',
      setter: 'StringSetter',
    },
    {
      name: 'disabled',
      title: '是否禁用',
      setter: 'BoolSetter',
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'title',
          title: '标题',
          setter: 'SlotSetter',
        },
      ],
    },
  ],
  configure: {
    component: {
      isContainer: true,
      clickCapture: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: false,
      events: [],
      supportBindState: false,
    },
  },
}
