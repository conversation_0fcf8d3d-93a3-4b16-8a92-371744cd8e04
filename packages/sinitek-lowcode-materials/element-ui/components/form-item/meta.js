import snippets from './snippets'

export default {
  componentName: 'ElFormItem',
  title: '表单元素容器',
  category: '表单',
  props: [
    {
      name: 'prop',
      title: {
        label: 'prop',
        tip: '表单域 model 字段，在使用 validate、resetFields 方法的情况下，该属性是必填的',
      },
      setter: 'StringSetter',
    },
    {
      name: 'label',
      title: '标签文本',
      setter: 'StringSetter',
    },
    {
      name: 'labelWidth',
      title: '标签宽度',
      setter: 'StringSetter',
    },
    {
      name: 'required',
      title: '必填',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'rules',
      title: '验证规则',
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/form?prop=rules#form-item-attributes',
    },
    {
      name: 'error',
      title: {
        label: '错误信息',
        tip: '设置该值会使表单验证状态变为error，并显示该错误信息',
      },
      setter: 'StringSetter',
    },
    {
      name: 'showMessage',
      title: '是否显示校验错误信息',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    // {
    //   name: 'inlineMessage',
    //   title: '以行内形式展示校验信息',
    //   setter: 'BoolSetter',
    //   defaultValue: false,
    // },
    {
      name: 'size',
      title: { label: '尺寸', tip: '设置尺寸大小' },
      description: '尺寸',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '正常',
              value: 'medium',
            },
            {
              title: '中等',
              value: 'small',
            },
            {
              title: '迷你',
              value: 'mini',
            },
          ],
        },
      },
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'label',
          title: '标签文本的内容',
          setter: 'SlotSetter',
          supportVariable: false,
        },
        {
          name: 'error',
          title: {
            label: '自定义表单校验信息',
            tip: '自定义表单校验信息的显示方式，参数为 { error }',
          },
          setter: 'SlotSetter',
          supportVariable: false,
        },
      ],
    },
  ],
  snippets,
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
    },
    component: {
      isContainer: true,
      clickCapture: false,
      nestingRule: {
        parentWhitelist: ['ElForm'],
      },
    },
  },
}
