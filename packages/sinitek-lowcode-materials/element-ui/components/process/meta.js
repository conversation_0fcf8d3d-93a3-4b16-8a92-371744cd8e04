import image from './__screenshots__/image.svg'

export default {
  componentName: 'ElProgress',
  title: '进度条',
  category: '基础',
  props: [
    {
      name: 'type',
      title: '类型',
      defaultValue: 'line',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            { label: '线形', value: 'line' },
            { label: '圆环', value: 'circle' },
            { label: '仪表盘', value: 'dashboard' },
          ],
        },
      },
    },
    {
      name: 'percentage',
      title: '百分比',
      defaultValue: 0,
      setter: 'NumberSetter',
    },
    {
      name: 'status',
      title: '状态',
      defaultValue: '',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            { label: '默认', value: '' },
            { label: '成功', value: 'success' },
            { label: '异常', value: 'exception' },
            { label: '警告', value: 'warning' },
          ],
        },
      },
    },
    {
      name: 'strokeWidth',
      title: '进度条宽度',
      defaultValue: 6,
      setter: 'NumberSetter',
    },
    {
      name: 'textInside',
      title: '文字内置',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'color',
      title: '进度条颜色',
      setter: 'ColorSetter',
    },
    {
      name: 'showText',
      title: '显示文字',
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'strokeLinecap',
      title: '进度条端点形状',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            { label: '圆形', value: 'round' },
            { label: '方形', value: 'square' },
          ],
        },
      },
      defaultValue: 'round',
    },
    {
      name: 'format',
      title: '文字格式',
      setter: 'FunctionSetter',
    },
    {
      name: 'defineBackColor',
      title: '背景色',
      setter: 'ColorSetter',
    },
    {
      name: 'textColor',
      title: '文字颜色',
      setter: 'ColorSetter',
    },
  ],
  snippets: [
    {
      title: '进度条',
      screenshot: image,
      schema: {
        componentName: 'ElProgress',
        props: {
          percentage: 50,
        },
        children: [],
      },
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
    component: {
      isContainer: true,
    },
  },
}
