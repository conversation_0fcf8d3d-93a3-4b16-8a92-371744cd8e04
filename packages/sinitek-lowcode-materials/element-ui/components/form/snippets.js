import form1 from './__screenshots__/form-1.png'
export default [
  {
    title: 'Element表单',
    screenshot: form1,
    schema: {
      componentName: 'ElForm',

      props: {
        labelWidth: '80px',
      },
      children: [
        {
          componentName: 'ElFormItem',

          props: {
            label: '活动名称',
          },
          children: [
            {
              componentName: 'ElInput',
              props: {},
            },
          ],
        },
        {
          componentName: 'ElFormItem',

          props: {
            label: '活动区域',
          },
          children: [
            {
              componentName: 'ElSelect',
              props: {
                placeholder: '请选择活动区域',
              },
              children: [
                {
                  componentName: 'ElOption',

                  props: {
                    label: '区域一',
                    value: 'shanghai',
                  },
                },
                {
                  componentName: 'ElOption',

                  props: {
                    label: '区域二',
                    value: 'beijing',
                  },
                },
              ],
            },
          ],
        },
        {
          componentName: 'ElFormItem',

          props: {
            label: '活动时间',
          },
          children: [
            {
              componentName: 'ElCol',

              props: {
                span: 11,
              },
              children: [
                {
                  componentName: 'ElDatePicker',

                  style: 'width: 100%;',
                  props: {
                    type: 'date',
                    placeholder: '选择日期',
                  },
                },
              ],
            },
            {
              componentName: 'ElCol',

              props: {
                span: 2,
              },
              className: 'line',
              children: [
                {
                  componentName: 'LCText',

                  props: {
                    text: '-',
                  },
                },
              ],
            },
            {
              componentName: 'ElCol',

              props: {
                span: 11,
              },
              children: [
                {
                  componentName: 'ElTimePicker',

                  style: 'width: 100%;',
                  props: {
                    type: 'date',
                    placeholder: '选择时间',
                  },
                },
              ],
            },
          ],
        },
        {
          componentName: 'ElFormItem',

          props: {
            label: '即时配送',
          },
          children: [
            {
              componentName: 'ElSwitch',

              props: {},
            },
          ],
        },
        {
          componentName: 'ElFormItem',

          props: {
            label: '活动性质',
          },
          children: [
            {
              componentName: 'ElCheckboxGroup',

              props: {},
              children: [
                {
                  componentName: 'ElCheckbox',

                  props: {
                    label: '美食/餐厅线上活动',
                    name: 'type',
                  },
                },
                {
                  componentName: 'ElCheckbox',

                  props: {
                    label: '地推活动',
                    name: 'type',
                  },
                },
                {
                  componentName: 'ElCheckbox',

                  props: {
                    label: '线下主题活动',
                    name: 'type',
                  },
                },
                {
                  componentName: 'ElCheckbox',

                  props: {
                    label: '单纯品牌曝光',
                    name: 'type',
                  },
                },
              ],
            },
          ],
        },
        {
          componentName: 'ElFormItem',

          props: {
            label: '特殊资源',
          },
          children: [
            {
              componentName: 'ElRadioGroup',

              props: {},
              events: {},
              children: [
                {
                  componentName: 'ElRadio',

                  props: {
                    label: '线上品牌商赞助',
                  },
                },
                {
                  componentName: 'ElRadio',

                  props: {
                    label: '线下场地免费',
                  },
                },
              ],
            },
          ],
        },
        {
          componentName: 'ElFormItem',

          props: {
            label: '活动形式',
          },
          children: [
            {
              componentName: 'ElInput',

              props: {
                type: 'textarea',
              },
            },
          ],
        },
        {
          componentName: 'ElFormItem',

          children: [
            {
              componentName: 'ElButton',

              props: {
                type: 'primary',
              },
              events: {},
              children: [
                {
                  componentName: 'LCText',

                  props: {
                    text: '立即创建',
                  },
                },
              ],
            },
            {
              componentName: 'ElButton',

              children: [
                {
                  componentName: 'LCText',

                  props: {
                    text: '取消',
                  },
                },
              ],
            },
          ],
        },
      ],
    },
  },
]
