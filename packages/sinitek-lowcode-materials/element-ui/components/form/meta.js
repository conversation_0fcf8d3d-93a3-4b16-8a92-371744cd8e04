import snippets from './snippets'
export default {
  componentName: 'ElForm',
  title: '表单容器',
  category: '表单',
  snippets,
  props: [
    {
      name: 'model',
      title: { label: '表单数据对象', tip: '表单数据对象' },
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/form?prop=model#form-attributes',
    },
    {
      name: 'labelSuffix',
      title: '标签后缀',
      setter: 'StringSetter',
    },
    {
      name: 'inline',
      title: { label: '行内表单模式', tip: '行内表单模式' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'labelPosition',
      title: {
        label: '标签的位置',
        tip: '表单域标签的位置，如果值为 “左” 或者 “右” 时，则需要设置 “标签宽度”',
      },
      defaultValue: 'right',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              title: '左',
              value: 'left',
            },
            {
              title: '右',
              value: 'right',
            },
            {
              title: '上',
              value: 'top',
            },
          ],
        },
      },
    },
    {
      name: 'hideRequiredAsterisk',
      title: '星号隐藏',
      setter: 'BoolSetter',
    },
    {
      name: 'statusIcon',
      title: {
        label: '反馈图标',
        tip: '是否在输入框中显示校验结果反馈图标',
      },
      setter: 'BoolSetter',
    },
    {
      name: 'size',
      title: { label: '尺寸', tip: '设置尺寸大小' },
      description: '尺寸',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '正常',
              value: 'medium',
            },
            {
              title: '中等',
              value: 'small',
            },
            {
              title: '迷你',
              value: 'mini',
            },
          ],
        },
      },
    },
    {
      name: 'disabled',
      title: {
        label: '禁用表单',
        tip: '是否禁用该表单内的所有组件。若设置为 true，则表单内组件上的 disabled 属性不再生效',
      },
      setter: 'BoolSetter',
    },
    {
      type: 'group',
      title: '校验',
      items: [
        {
          name: 'rules',
          title: { label: '表单验证规则', tip: '表单验证规则' },
          setter: 'JSONSetter',
          documentUrl:
            'https://element.eleme.cn/#/zh-CN/component/form?prop=rules#form-attributes',
        },
        {
          name: 'show-message',
          title: { label: '显示校验', tip: '是否显示校验错误信息' },
          defaultValue: true,
          setter: 'BoolSetter',
        },
        {
          name: 'inline-message',
          title: { label: '行内展示', tip: '是否以行内形式展示校验信息' },
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'validateOnRuleChange',
          title: {
            label: 'rules改变验证',
            tip: '是否在 rules 属性改变后立即触发一次验证',
          },
          setter: 'BoolSetter',
        },
      ],
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'validate',
          description: '任一表单项被校验后触发',
          template: 'function validate(prop) { console.log("validate");}',
        },
      ],
    },
    component: {
      isContainer: true,
      clickCapture: false,
    },
  },
}
