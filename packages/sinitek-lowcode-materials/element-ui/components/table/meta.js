import tableColumn from '../table-column/meta'
export default {
  componentName: 'ElTable',
  title: '表格',
  category: '信息展示',
  componentType: 'RELA_LIST',
  formContext: 'SUBMIT',
  props: [
    {
      name: 'data',
      title: { label: 'data', tip: '数据源' },
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/table?prop=data#table-attributes',
    },
    {
      name: 'height',
      title: {
        label: '高度',
        tip: '如果 height 为 number 类型，单位 px；如果 height 为 string 类型，则这个高度会设置为 Table 的 style.height 的值',
      },
      setter: 'StringSetter',
    },
    {
      name: 'maxHeight',
      title: {
        label: '最大高度',
        tip: '合法的值为数字或者单位为 px 的高度',
      },
      setter: 'StringSetter',
    },
    {
      name: 'stripe',
      title: {
        label: '斑马纹',
        tip: '是否为斑马纹 table',
      },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'border',
      title: {
        label: '边框',
        tip: '是否带有纵向边框',
      },
      setter: 'BoolSetter',
    },
    {
      name: 'size',
      title: { label: '尺寸', tip: 'Table 的尺寸' },
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '正常',
              value: 'medium',
            },
            {
              title: '中等',
              value: 'small',
            },
            {
              title: '迷你',
              value: 'mini',
            },
          ],
        },
      },
    },
    {
      name: 'fit',
      title: '宽度是否自撑开',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'showHeader',
      title: '显示表头',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'highlightCurrentRow',
      title: '高亮当前行',
      setter: 'BoolSetter',
    },
    {
      name: 'highlightSelectionRow',
      title: '高亮复选框选中行',
      setter: 'BoolSetter',
    },
    {
      name: 'currentRowKey',
      title: '当前行的 key',
      setter: 'StringSetter',
    },
    {
      name: 'rowClassName',
      title: '行的 className 的回调方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'rowStyle',
      title: '行的 style 的回调方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'cellClassName',
      title: '单元格的 className 的回调方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'cellStyle',
      title: '单元格的 style 的回调方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'headerRowClassName',
      title: '表头行的 className 的回调方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'headerRowStyle',
      title: '表头行的 style 的回调方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'headerCellClassName',
      title: '表头单元格的 className 的回调方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'headerCellStyle',
      title: '表头单元格的 style 的回调方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'rowKey',
      title: '行数据的 Key',
      setter: 'StringSetter',
    },
    {
      name: 'emptyText',
      title: '空数据时显示的文本内容',
      setter: 'StringSetter',
    },
    {
      name: 'defaultExpandAll',
      title: '默认展开所有行',
      setter: 'BoolSetter',
    },
    {
      name: 'expandRowKeys',
      title: '当前展开行',
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/table?prop=expandRowKeys#table-attributes',
    },
    {
      name: 'defaultSort',
      title: '默认的排序',
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/table?prop=defaultSort#table-attributes',
    },
    {
      name: 'tooltipEffect',
      title: 'tooltip effect 属性',
      setter: 'StringSetter',
    },
    {
      name: 'showSummary',
      title: '显示合计行',
      setter: 'BoolSetter',
    },
    {
      name: 'sumText',
      title: '合计行第一列的文本',
      setter: 'StringSetter',
    },
    {
      name: 'summaryMethod',
      title: '合计计算方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'spanMethod',
      title: '合并行或列的计算方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'selectOnIndeterminate',
      title: '全选',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'indent',
      title: '树节点的缩进',
      setter: 'NumberSetter',
    },
    {
      name: 'lazy',
      title: '懒加载子节点数据',
      setter: 'BoolSetter',
    },
    {
      name: 'load',
      title: '加载子节点数据的函数',
      setter: 'FunctionSetter',
    },
    {
      name: 'treeProps',
      title: '渲染嵌套数据配置',
      setter: 'ObjectSetter',
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'append',
          title: {
            label: '插入至表格最后一行之后的内容',
            tip: '如果需要对表格的内容进行无限滚动操作，可能需要用到这个 slot。若表格有合计行，该 slot 会位于合计行之上',
          },
          setter: 'SlotSetter',
        },
      ],
    },
  ],
  childConfig: {
    componentName: 'ElTableColumn',
    title: '表格列',
    setter: {
      componentName: 'ArraySetter',
      props: {
        itemSetter: {
          componentName: 'ObjectSetter',
          props: {
            config: {
              items: tableColumn.props,
            },
          },
          initialValue: {
            componentName: 'ElTableColumn',
            props: {
              prop: 'prop',
              label: 'label',
              filterMultiple: true,
              resizable: true,
            },
            children: [],
          },
        },
      },
    },
  },
  configure: {
    component: {
      isContainer: false,
      childWrapRenderer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'select',
          description: '当勾选数据行的 Checkbox 时触发的事件',
          template:
            'function onSelect(selection, row) { console.log(selection, row);}',
        },
        {
          name: 'selectAll',
          description: '当勾选全选 Checkbox 时触发的事件',
          template: 'function selectAll(selection) { console.log(selection);}',
        },
        {
          name: 'selectionChange',
          description: '当选择项发生变化时会触发该事件',
          template:
            'function selectionChange(selection) { console.log(selection);}',
        },
        {
          name: 'cellMouseEnter',
          description: '当单元格 hover 进入时会触发该事件',
          template:
            'function cellMouseEnter(row, column, cell, event) { console.log(row, column, cell, event);}',
        },
        {
          name: 'cellMouseLeave',
          description: '当单元格 hover 退出时会触发该事件',
          template:
            'function cellMouseLeave(row, column, cell, event) { console.log(row, column, cell, event);}',
        },
        {
          name: 'cellClick',
          description: '当某单元格被点击时会触发该事件',
          template:
            'function cellClick(row, column, cell, event) { console.log(row, column, cell, event);}',
        },
        {
          name: 'cellDblclick',
          description: '当某单元格被双击击时会触发该事件',
          template:
            'function cellDblclick(row, column, cell, event) { console.log(row, column, cell, event);}',
        },
        {
          name: 'rowClick',
          description: '当某单元格被双击击时会触发该事件',
          template:
            'function rowClick(row, column, event) { console.log(row, column, event);}',
        },
        {
          name: 'rowContextmenu',
          description: '当某行被点击时会触发该事件',
          template:
            'function rowClick(row, column, event) { console.log(row, column, event);}',
        },
        {
          name: 'rowDblclick',
          description: '当某行被双击时会触发该事件',
          template:
            'function rowClick(row, column, event) { console.log(row, column, event);}',
        },
        {
          name: 'headerClick',
          description: '当某列的表头被点击时会触发该事件',
          template:
            'function headerClick(row, column) { console.log(row, column);}',
        },
        {
          name: 'headerContextmenu',
          description: '当某列的表头被鼠标右键点击时触发该事件',
          template:
            'function headerContextmenu(row, column) { console.log(row, column);}',
        },
        {
          name: 'sortChange',
          description: '当表格的排序条件发生变化的时候会触发该事件',
          template: 'function sortChange(obj) { console.log(obj);}',
        },
        {
          name: 'filterChange',
          description: '当表格的排序条件发生变化的时候会触发该事件',
          template: 'function filterChange(filters) { console.log(filters);}',
        },
        {
          name: 'currentChange',
          description: '当表格的当前行发生变化的时候会触发该事件',
          template:
            'function currentChange(currentRow, oldCurrentRow) { console.log(currentRow, oldCurrentRow);}',
        },
        {
          name: 'headerDragend',
          description: '当表格的当前行发生变化的时候会触发该事件',
          template:
            'function headerDragend(newWidth, oldWidth, column, event) { console.log(newWidth, oldWidth, column, event);}',
        },
        {
          name: 'expandChange',
          description: '当用户对某行展开或者关闭的时候会触发该事件',
          template:
            'function expandChange(row, expandedRows) { console.log(row, expandedRows);}',
        },
      ],
    },
  },
}
