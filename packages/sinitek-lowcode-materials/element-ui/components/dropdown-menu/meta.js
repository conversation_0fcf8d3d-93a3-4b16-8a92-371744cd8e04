export default {
  componentName: 'ElDropdownMenu',
  title: '按钮菜单容器',
  category: '信息展示',
  props: [],
  // snippets: [
  //   {
  //     title: '按钮菜单组',
  //     schema: {
  //       componentName: 'ElDropdownMenu',
  //       props: {},
  //       children: [
  //         {
  //           componentName: 'ElDropdownItem',
  //           props: {},
  //           children: [
  //             {
  //               componentName: 'LCText',
  //               props: { text: '按钮菜单' },
  //             },
  //           ],
  //         },
  //       ],
  //     },
  //   },
  // ],
  configure: {
    component: {
      isContainer: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [],
    },
  },
}
