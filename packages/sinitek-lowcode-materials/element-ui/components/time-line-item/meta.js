import timeLineItem from './__screenshots__/image.svg'
export default {
  componentName: 'ElTimelineItem',
  title: '时间线子选项',
  category: '信息输入',
  componentType: 'TIME',
  formContext: 'SUBMIT',
  props: [
    {
      name: 'timestamp',
      title: '时间戳',
      setter: 'StringSetter',
    },
    {
      name: 'hideTimestamp',
      title: '隐藏时间戳',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'type',
      title: '节点类型',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '正常',
              value: 'primary',
            },
            {
              title: '成功',
              value: 'success ',
            },
            {
              title: '警告',
              value: 'warning',
            },
            {
              title: '危险',
              value: 'danger',
            },
            {
              title: '提示',
              value: 'info',
            },
          ],
        },
      },
    },
    {
      name: 'color',
      title: '节点颜色',
      setter: 'StringSetter',
    },
    {
      name: 'size',
      title: '节点尺寸',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '正常',
              value: 'normal',
            },
            {
              title: '较大',
              value: 'large',
            },
          ],
        },
      },
    },
    {
      name: 'icon',
      title: '节点图标',
      setter: {
        componentName: 'IconSetter',
        props: {
          type: 'element-ui',
        },
      },
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          title: '自定义节点',
          name: 'dot',
          setter: 'SlotSetter',
          supportVariable: false,
        },
      ],
    },
  ],
  snippets: [
    {
      title: '时间线子选项',
      screenshot: timeLineItem,
      schema: {
        componentName: 'ElTimelineItem',
        props: { editable: true, clearable: true },
        children: [
          {
            componentName: 'ElCard',
            props: {},
            children: [
              {
                componentName: 'LCText',
                props: {
                  text: '文字文字文字',
                },
              },
            ],
          },
        ],
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [],
    },
  },
}
