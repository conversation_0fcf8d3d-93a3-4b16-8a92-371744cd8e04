import backtop from './__screenshots__/image.svg'
export default {
  componentName: 'ElBacktop',
  title: '回到顶部',
  category: '基础',
  props: [
    {
      name: 'target',
      title: '触发滚动的对象',
      setter: 'StringSetter',
    },
    {
      name: 'visibilityHeight',
      title: '滚动高度达到此参数值才出现',
      setter: 'NumberSetter',
      defaultValue: 200,
    },
    {
      name: 'right',
      title: '页面右边距',
      setter: 'NumberSetter',
      defaultValue: 40,
    },
    {
      name: 'bottom',
      title: '页面底部距离',
      setter: 'NumberSetter',
      defaultValue: 40,
    },
  ],
  snippets: [
    {
      title: '回到顶部',
      screenshot: backtop,
      prePreview: false,
      schema: {
        componentName: 'ElBacktop',
        props: {},
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'click',
          description: '点击按钮触发的事件',
          template: 'function click(event) { console.log(event);}',
        },
      ],
      supportBindState: false,
    },
  },
}
