import link from './__screenshots__/image.svg'

import { buttonType } from '../button/meta.js'

export default {
  componentName: 'ElLink',
  title: '链接',
  category: '基础',
  props: [
    {
      name: 'type',
      title: '类型',
      setter: {
        componentName: 'SelectSetter',
        // primary / success / warning / danger / info / text
        props: {
          options: buttonType,
        },
      },
    },
    {
      name: 'underline',
      title: '下划线',
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'disabled',
      title: '禁用',
      setter: 'BoolSetter',
    },
    {
      name: 'href',
      title: '链接地址',
      setter: 'StringSetter',
    },
    {
      name: 'icon',
      title: '图标类名',
      setter: {
        componentName: 'IconSetter',
        props: {
          type: 'element-ui',
        },
      },
    },
  ],
  snippets: [
    {
      title: '链接',
      screenshot: link,
      schema: {
        componentName: 'ElLink',
        props: {
          type: 'primary',
        },
        children: [
          {
            componentName: 'LCText',
            props: {
              text: '链接',
            },
          },
        ],
      },
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'click',
          description: '点击事件',
          template: 'function click(event) { console.log(event);}',
        },
      ],
    },
    component: {
      isContainer: true,
    },
  },
}
