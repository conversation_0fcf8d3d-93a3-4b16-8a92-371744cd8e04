import datepicker from './__screenshots__/date-picker.svg'
export default {
  componentName: 'ElDatePicker',
  title: '日期选择器',
  category: '信息输入',
  componentType: 'DATE',
  formContext: 'SUBMIT',
  props: [
    // {
    //   name: 'value',
    //   title: '日期组件的值',
    //   setter: 'StringSetter',
    // },
    {
      name: 'readonly',
      title: '完全只读',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'editable',
      title: '文本框可输入',
      setter: 'BoolSetter',
    },
    {
      name: 'clearable',
      title: { label: '可清除', tip: '是否可清除' },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'size',
      title: { label: '尺寸', tip: '设置尺寸大小' },
      description: '尺寸',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '正常',
              value: 'medium',
            },
            {
              title: '中等',
              value: 'small',
            },
            {
              title: '迷你',
              value: 'mini',
            },
          ],
        },
      },
    },
    {
      name: 'placeholder',
      title: '占位内容',
      setter: 'StringSetter',
    },
    {
      name: 'startPlaceholder',
      title: '开始日期占位内容',
      setter: 'StringSetter',
    },
    {
      name: 'endPlaceholder',
      title: '结束日期的占位内容',
      setter: 'StringSetter',
    },
    {
      name: 'format',
      title: { label: '日期格式', tip: '输入日期格式' },
      setter: 'StringSetter',
      defaultValue: 'yyyy-MM-dd',
    },
    {
      name: 'align',
      title: '对齐方式',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              title: '靠左',
              value: 'left',
            },
            {
              title: '中间',
              value: 'center',
            },
            {
              title: '靠右',
              value: 'right',
            },
          ],
        },
      },
    },
    {
      name: 'popperClass',
      title: '下拉框的类名',
      setter: 'StringSetter',
    },
    {
      name: 'pickerOptions',
      title: '时间选择配置',
      setter: {
        componentName: 'ObjectSetter',
        props: {
          config: {
            items: [
              {
                name: 'shortcuts',
                title: '设置快捷选项',
                setter: 'JSONSetter',
                documentUrl:
                  'https://element.eleme.cn/#/zh-CN/component/date-picker#shortcuts',
              },
              {
                name: 'disabledDate',
                title: '设置禁用状态',
                setter: 'FunctionSetter',
              },
              {
                name: 'cellClassName',
                title: '日期的 className',
                setter: 'FunctionSetter',
              },
              {
                name: 'firstDayOfWeek',
                title: '周起始日',
                setter: 'NumberSetter',
              },
              {
                name: 'onPick',
                title: '选中日期后会执行的回调',
                setter: 'FunctionSetter',
              },
            ],
          },
        },
      },
    },
    {
      name: 'rangeSeparator',
      title: '分隔符',
      setter: 'StringSetter',
    },
    {
      name: 'defaultValue',
      title: '默认显示的时间',
      setter: 'JSONSetter',
      documentUrl:
        'http://192.168.1.121:18056/doc/sirmapp-dev/components/datepicker.html#%E5%8F%82%E6%95%B0?prop=defaultValue',
    },
    {
      name: 'name',
      title: '原生属性',
      setter: 'StringSetter',
    },
    {
      name: 'unlinkPanels',
      title: '取消联动',
      setter: 'BoolSetter',
    },
    {
      name: 'prefixIcon',
      title: '自定义头部图标',
      setter: 'StringSetter',
    },
    {
      name: 'clearIcon',
      title: '自定义清空图标',
      setter: 'StringSetter',
    },
    {
      name: 'validateEvent',
      title: '表单的校验',
      setter: 'BoolSetter',
    },
    {
      name: 'appendToBody',
      title: '插入至 body',
      setter: 'BoolSetter',
    },
    {
      name: 'picker-options',
      title: { label: 'picker-options' },
      setter: {
        componentName: 'ObjectSetter',
        props: {
          config: {
            items: [
              {
                name: 'shortcuts',
                title: '设置快捷选项',
                setter: 'JSONSetter',
                documentUrl:
                  'https://element.eleme.cn/#/zh-CN/component/date-picker#shortcuts',
              },
              {
                name: 'disabledDate',
                title: '设置禁用状态',
                setter: 'FunctionSetter',
              },
              {
                name: 'cellClassName',
                title: '日期的 className',
                setter: 'FunctionSetter',
              },
              {
                name: 'firstDayOfWeek',
                title: '周起始日',
                setter: 'NumberSetter',
              },
              {
                name: 'onPick',
                title: '选中日期后会执行的回调',
                setter: 'FunctionSetter',
              },
            ],
          },
        },
      },
    },
  ],
  snippets: [
    {
      title: '时间选择框',
      screenshot: datepicker,
      schema: {
        componentName: 'ElDatePicker',
        props: {
          pickerOptions: {
            shortcuts: [],
            firstDayOfWeek: 7,
            clearable: true,
          },
        },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'change',
          description: '用户确认选定的值时触发',
        },
        {
          name: 'blur',
          description: '在失去焦点时触发的事件',
        },
        {
          name: 'focus',
          description: '在获得焦点时触发',
        },
      ],
    },
  },
}
