export const formItemAdd = (node, target, opts = {}) => {
  if (opts.laForm && target.findParent('LAForm')) {
    return {
      componentName: 'LAFormItem',
      props: {
        field: opts.laForm.field,
        itemProps: {
          label: opts.label || '输入框',
        },
        fieldProps: node.props || {},
      },
    }
  }
  const formItem = target.findParent('XnFormItem')
  if (!formItem) {
    const form = target.findParent('XnForm')
    if (form) {
      return {
        componentName: 'XnFormItem',
        props: {
          label: opts.label || '输入框',
        },
        children: [node],
      }
    }
  }

  return node
}
