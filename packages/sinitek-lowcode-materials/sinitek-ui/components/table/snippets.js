import table from './__screenshots__/table.png'
export default [
  {
    title: '表格',
    screenshot: table,
    schema: {
      componentName: 'XnTable',
      props: {
        data: [
          {
            date: '2016-05-02',
            name: '测试姓名111',
            address: '上海市普陀区金沙江路 1518 弄',
          },
          {
            date: '2016-05-04',
            name: '测试姓名222',
            address: '上海市普陀区金沙江路 1517 弄',
          },
          {
            date: '2016-05-01',
            name: '测试姓名333',
            address: '上海市普陀区金沙江路 1519 弄',
          },
          {
            date: '2016-05-03',
            name: '测试姓名444',
            address: '上海市普陀区金沙江路 1516 弄',
          },
        ],
        border: true,
        allowInit: true,
        isProgress: true,
        loadingMask: true,
        showPagination: true,
      },
      children: [
        {
          componentName: 'xn-col',
          props: { prop: 'date', label: '日期', width: '180' },
          children: [],
        },
        {
          componentName: 'xn-col',
          props: { prop: 'name', label: '姓名', width: '180' },
          children: [],
        },
        {
          componentName: 'xn-col',
          props: { prop: 'address', label: '地址', width: '180' },
          children: [],
        },
      ],
    },
  },
]
