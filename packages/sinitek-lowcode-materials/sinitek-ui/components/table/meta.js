// import snippets from './snippets'
import col from '../xn-col/meta'
export default {
  componentName: 'XnTable',
  title: '表格',
  category: '信息展示',
  componentType: 'DETAIL',
  formContext: 'SUBMIT',
  priority: 2,
  props: [
    {
      name: 'data',
      title: { label: '表格数据源', tip: '数据源' },
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=data#参数',
    },
    {
      name: 'url',
      title: { label: '请求地址', tip: '表格数据查询后端接口 url' },
      setter: 'StringSetter',
    },
    {
      name: 'querymodel',
      title: {
        label: 'querymodel',
        tip: '查询条件集合，根据集合中的字段查询筛选',
      },
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=querymodel#参数',
    },
    {
      name: 'border',
      title: '边框',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'pageSize',
      title: '每页显示条数',
      setter: 'NumberSetter',
      defaultValue: 20,
    },
    {
      name: 'pageSizes',
      title: '每页显示条数集合',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=pageSizes#参数',
    },
    {
      name: 'width',
      title: '表格宽度',
      setter: 'StringSetter',
    },
    {
      name: 'height',
      title: '表格高度',
      setter: 'StringSetter',
    },
    {
      name: 'maxHeight',
      title: '表格最大高度',
      setter: 'StringSetter',
    },
    {
      name: 'defaultOrder',
      title: '排序规则',
      setter: 'StringSetter',
    },
    {
      name: 'allowExport',
      title: '允许导出',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'exportAll',
      title: '导出全部',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'exportName',
      title: '导出文件名',
      setter: 'StringSetter',
    },
    {
      name: 'exportMaxRow',
      title: '导出最大行数',
      setter: 'NumberSetter',
    },
    {
      name: 'rowClassName',
      title: '行的样式',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=rowClassName#参数',
    },
    {
      name: 'allowInit',
      title: '是否初始化',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'afterDrag',
      title: 'create结束后的回调',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=afterDrag#参数',
    },
    {
      name: 'isProgress',
      title: '导出进度条',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'loadingMask',
      title: '显示 loading',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'showPagination',
      title: '表格分页',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'postQuery',
      title: 'post请求',
      setter: 'BoolSetter',
    },
    {
      name: 'dataTotal',
      title: '数据总数',
      setter: 'NumberSetter',
    },
    {
      name: 'pageCountLimit',
      title: '表格最大限制页数',
      setter: 'NumberSetter',
    },
    {
      name: 'customSourceData',
      title: 'url请求返回数据的解析路径',
      setter: 'StringSetter',
      defaultValue: 'data.datalist',
    },
    {
      name: 'customSourceTotal',
      title: 'url请求返回数据条数的解析路径	',
      setter: 'StringSetter',
      defaultValue: 'data.totalsize',
    },
    {
      name: 'customAfterQueryPath',
      title: '请求的解析路径',
      setter: 'StringSetter',
      defaultValue: 'data',
    },
    {
      name: 'stripe',
      title: '斑马行',
      setter: 'BoolSetter',
    },
    {
      name: 'highlightCurrentRow',
      title: '高亮当前行',
      setter: 'BoolSetter',
    },
    {
      name: 'scrollCache',
      title: '缓存滚动条高度',
      setter: 'BoolSetter',
    },
    {
      name: 'dynamicCols',
      title: '开启动态列',
      setter: 'BoolSetter',
    },
    {
      name: 'spanMethod',
      title: '合并行列计算',
      setter: 'FunctionSetter',
    },
    {
      name: 'pagerCount',
      title: '页码按钮的数量',
      setter: 'NumberSetter',
      defaultValue: 7,
    },
    {
      name: 'showFilter',
      title: {
        label: '开启列筛选',
        tip: '是否显示筛选条件，开始后search插槽失效。\n filterModel/prop存在于querymodel决定是否展示筛选',
      },
      setter: 'BoolSetter',
    },
    {
      name: 'showConditionalView',
      title: '开启条件视图',
      setter: 'BoolSetter',
    },
    {
      name: 'useVirtual',
      title: '虚拟表格',
      setter: 'BoolSetter',
    },
    {
      name: 'rowHeight',
      title: '虚拟表格行高',
      setter: 'FunctionSetter',
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        // {
        //   name: 'toolbar',
        //   title: '表格工具栏插槽',
        //   setter: 'SlotSetter',
        // },
        {
          name: 'empty',
          title: '表格无数据插槽',
          setter: 'SlotSetter',
        },
        {
          name: 'append',
          title: '插入最后一列',
          setter: 'SlotSetter',
        },
        // sinitek-ui没有渲染，暂时不支持
        // {
        //   name: 'export',
        //   title: '自定义导出列',
        //   setter: 'SlotSetter',
        // },
        {
          name: 'search',
          title: '自定义查询条件',
          setter: 'SlotSetter',
        },
        // {
        //   name: 'conditional-view',
        //   title: '条件视图作用域插槽',
        //   setter: 'SlotSetter',
        //   supportVariable: false,
        // },
      ],
    },
  ],
  // snippets,
  childConfig: {
    componentName: 'xn-col',
    title: '表格列',
    setter: {
      componentName: 'ArraySetter',
      props: {
        itemSetter: {
          componentName: 'ObjectSetter',
          props: {
            textField: 'label',
            config: {
              items: col.props,
            },
          },
          initialValue: {
            componentName: 'xn-col',
            props: {
              label: '表格列',
              prop: 'prop',
            },
            children: [],
          },
        },
      },
    },
  },
  configure: {
    component: {
      isContainer: false,
      childWrapRenderer: false,
      useWrap: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'afterQuery',
          description: '查询之后的回调事件，返回查到的datalist',
          template: 'function afterQuery(data) { console.log(data);}',
        },
        {
          name: 'select-callback',
          description: '行部分选中和全部选中回调事件',
          template:
            'function selectCallback(selection, row) { console.log(selection, row);}',
        },
        {
          name: 'size-change',
          description: '每页条数变更的回调事件',
          template:
            'function sizeChange(index, size) { console.log(index, size);}',
        },
        {
          name: 'current-page-change',
          description: '页码变更的回调事件',
          template:
            'function currentPageChange(index, size) { console.log(index, size);}',
        },
      ],
    },
  },
}
