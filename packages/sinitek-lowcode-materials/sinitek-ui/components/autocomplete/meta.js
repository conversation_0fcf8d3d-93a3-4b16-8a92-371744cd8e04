// import autocomplete from './__screenshots__/autocomplete.png'
import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'XnAutocomplete',
  title: '自动完成',
  category: '信息输入',
  props: [
    // {
    //   name: 'value',
    //   title: '自动完成组件的值',
    //   setter: 'StringSetter',
    // },
    {
      name: 'valueKey',
      title: {
        label: 'value的 key',
        tip: '该 key 的值用来作为控件的返回值',
      },
      setter: 'StringSetter',
    },
    {
      name: 'displayKey',
      title: {
        label: '显示的 key',
        tip: '该 key 的值用来作为控件的输入部分和下拉部分的显示',
      },
      setter: 'StringSetter',
      defaultValue: 'name',
    },
    {
      name: 'icon',
      title: { label: '图标的样式', tip: '' },
      defaultValue: 'el-icon-search',
      setter: {
        componentName: 'IconSetter',
        props: {
          type: 'element-ui',
        },
      },
    },
    {
      name: 'url',
      title: { label: '请求接口', tip: '后台请求接口的值' },
      defaultValue: '',
      setter: 'StringSetter',
    },
    {
      name: 'params',
      title: { label: '请求参数', tip: '后台请求参数' },
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/autocomplete.html?prop=params#%E5%8F%82%E6%95%B0',
    },
    {
      name: 'width',
      title: '宽度',
      setter: 'StringSetter',
    },
    {
      name: 'selectWidth',
      title: '下拉框的宽度',
      setter: 'StringSetter',
    },
    {
      name: 'delay',
      title: { label: '延迟时间', tip: '下拉框数据显示延迟时间' },
      setter: 'NumberSetter',
      defaultValue: 100,
    },
    {
      name: 'placeholder',
      title: { label: 'placeholder', tip: '提示文字' },
      setter: 'StringSetter',
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'triggerOnFocus',
      title: { label: '点击显示下拉框', tip: '点击时就显示下拉框' },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'hideLoading',
      title: { label: '隐藏加载', tip: '是否隐藏远程加载时的加载图标' },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'maxlength',
      title: { label: '最大输入长度', tip: '' },
      setter: 'NumberSetter',
      defaultValue: 100,
      condition(props) {
        return props.showWordLimit
      },
    },
    {
      name: 'clearable',
      title: { label: '可清除', tip: '是否可清除' },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'initIsPost',
      title: { label: '初始化是否为post', tip: '初始化请求是否为post' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'initFieldPath',
      title: {
        label: '初始化解析路径',
        tip: '初始化请求接口值的解析路径',
      },
      defaultValue: 'data',
      setter: 'StringSetter',
    },
    {
      name: 'fetchSuggestionUrl',
      title: { label: '输入时的请求接口', tip: '默认为初始化请求' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'fetchFieldPath',
      title: {
        label: '输入解析路径',
        tip: '输入时请求接口的返回值解析路径',
      },
      defaultValue: 'data',
      setter: 'StringSetter',
    },
    {
      name: 'fetchIsPost',
      title: {
        label: '输入是否为post',
        tip: '输入时请求接口的请求方式是否为post',
      },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    widthSize,
  ],
  // snippets: [
  //   {
  //     title: '自动完成',
  //     screenshot: autocomplete,
  //     schema: {
  //       componentName: 'XnAutocomplete',
  //       props: {
  //         placeholder: '请输入',
  //         triggerOnFocus: true,
  //         hideLoading: true,
  //         clearable: true,
  //         icon: 'el-icon-search',
  //       },
  //       children: [],
  //     },
  //   },
  // ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'select',
          description: '选中选项时触发',
          template: 'function select(item) { console.log(item);}',
        },
        {
          name: 'change',
          description: '值改变时触发触发',
          template: 'function change(event) { console.log(event);}',
        },
        {
          name: 'focus',
          description: '获得焦点时触发',
          template: 'function focus(event) { console.log(event);}',
        },
        {
          name: 'blur',
          description: '失去焦点时触发的事件',
          template: 'function blur(event) { console.log(event);}',
        },
        {
          name: 'iconClick',
          description: '点击图表时触发',
          template: 'function iconClick(event) { console.log(event);}',
        },
      ],
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '自动完成',
          })
        },
      },
    },
  },
}
