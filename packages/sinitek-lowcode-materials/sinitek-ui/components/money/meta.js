import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'
import money from './__screenshots__/money.svg'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'XnMoney',
  title: '金额输入框',
  category: '信息输入',
  componentType: 'MONEY',
  formContext: 'SUBMIT',
  props: [
    // {
    //   name: 'value',
    //   title: '金额输入框的值',
    //   setter: 'StringSetter',
    // },
    {
      name: 'placeholder',
      title: { label: '输入框内的提示文字', tip: '请输入金额' },
      setter: 'StringSetter',
    },
    {
      name: 'divided',
      title: { label: '千分位分割', tip: '是否以千分位分割' },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'decimalPlaces',
      title: '保留小数点几位',
      defaultValue: 2,
      setter: 'NumberSetter',
    },
    {
      name: 'isRound',
      title: { label: '四舍五入', tip: '是否四舍五入' },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'width',
      title: { label: '宽度', tip: '金额输入框宽度' },
      setter: 'StringSetter',
      defaultValue: '100%',
    },
    {
      name: 'showBigTip',
      title: { label: '显示大写', tip: '数字大写中文的显示' },
      setter: 'BoolSetter',
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'clearable',
      title: { label: '可清除', tip: '是否可清除' },
      setter: 'BoolSetter',
    },
    // {
    //   name: 'bigTipContainerRef',
    //   title: { label: '容器 ref', tip: '数字大写中文所在容器 ref 值	' },
    //   setter: 'StringSetter',
    // },
    {
      name: 'size',
      title: { label: '尺寸', tip: '设置尺寸大小' },
      description: '尺寸',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '正常',
              value: 'medium',
            },
            {
              title: '中等',
              value: 'small',
            },
            {
              title: '迷你',
              value: 'mini',
            },
          ],
        },
      },
    },
    {
      name: 'formatTip',
      title: '自定义显示 tips',
      setter: 'FunctionSetter',
    },
    widthSize,
  ],
  snippets: [
    {
      title: '金额输入框',
      screenshot: money,
      schema: {
        componentName: 'XnMoney',
        props: {
          divided: true,
          isRound: true,
          showBigTip: true,
          clearable: true,
        },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'blur',
          description: '在失去焦点时触发的事件',
          template: 'function blur(event) { console.log(event);}',
        },
        {
          name: 'clear',
          description: '在点击由 clearable 属性生成的清空按钮时触发',
          template: 'function clear(event) { console.log(event);}',
        },
        {
          name: 'change',
          description: '在金额输入框值改变时触发',
          template: 'function change(event) { console.log(event);}',
        },
        {
          name: 'focus',
          description: '在获得焦点时触发',
          template: 'function focus(event) { console.log(event);}',
        },
      ],
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '金额输入框',
          })
        },
      },
    },
  },
}
