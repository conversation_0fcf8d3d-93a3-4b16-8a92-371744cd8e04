import { buttonType } from '../../../element-ui/components/button/meta.js'

export default {
  componentName: 'XnColAction',
  title: '表格操作列',
  category: 'SinitekUI',
  props: [
    {
      name: 'size',
      title: { label: '尺寸', tip: '设置尺寸大小' },
      description: '尺寸',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '正常',
              value: 'medium',
            },
            {
              title: '中等',
              value: 'small',
            },
            {
              title: '迷你',
              value: 'mini',
            },
          ],
        },
      },
    },
    {
      name: 'type',
      title: { label: '类型', tip: '设置按钮不同的类型' },
      description: '类型',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: buttonType,
        },
      },
    },
    {
      name: 'plain',
      title: { label: '朴素按钮', tip: '是否朴素按钮' },
      defaultValue: false,
      setter: 'BoolSetter',
      condition(props) {
        return props.type === 'primary'
      },
    },
    {
      name: 'round',
      title: { label: '圆角按钮', tip: '是否圆角按钮' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'circle',
      title: { label: '圆形按钮', tip: '是否圆形按钮' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'loading',
      title: { label: '加载状态', tip: '是否加载状态' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      description: '圆角按钮',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'icon',
      title: { label: '图标类名', tip: '图标类名' },
      description: '图标类名',
      defaultValue: false,
      setter: 'IconSetter',
    },
    {
      name: 'autofocus',
      title: { label: '默认聚焦', tip: '默认聚焦' },
      description: '默认聚焦',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'native-type',
      title: { label: '原生 type 属性', tip: '原生 type 属性' },
      description: '原生 type 属性',
      defaultValue: 'button',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              title: '按钮',
              value: 'button',
            },
            {
              title: '提交',
              value: 'submit',
            },
            {
              title: '重置',
              value: 'reset',
            },
          ],
        },
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'click',
          description: '点击时触发',
          template: 'function click() { console.log("clicked");}',
        },
      ],
      supportBindState: false,
    },
  },
}
