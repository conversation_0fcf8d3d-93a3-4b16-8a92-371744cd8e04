import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'
import calendar from './__screenshots__/richbox.svg'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'XnRichbox',
  title: '富文本组件',
  category: '信息输入',
  componentType: 'RICHBOX',
  formContext: 'SUBMIT',
  props: [
    {
      name: 'content',
      title: '富文本内容',
      setter: 'StringSetter',
    },
    {
      name: 'config',
      title: 'config',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/richbox.html?prop=config#参数',
    },
    {
      name: 'options',
      title: '自定义工具栏',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/richbox.html?prop=options#参数',
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    widthSize,
  ],
  snippets: [
    {
      title: '富文本组件',
      screenshot: calendar,
      schema: {
        componentName: 'XnRichbox',
        props: {
          options: {
            modules: {
              toolbar: [
                ['bold', 'italic', 'underline', 'strike'], // toggled buttons

                [{ header: 1 }, { header: 2 }], // custom button values
                [{ color: [] }, { background: [] }], // dropdown with defaults from theme
                [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
                [
                  { list: 'ordered' },
                  { list: 'bullet' },
                  { indent: '-1' },
                  { indent: '+1' },
                ], // outdent/indent
                [{ align: [] }, { direction: 'rtl' }], // text direction

                ['blockquote', 'code-block'],

                ['link', 'image'],
                ['previous', 'next'],

                [{ header: [] }, { size: [] }, { font: [] }],
                [{ 'line-height': [] }],

                ['clean', 'html'], // remove formatting button
              ],
            },
          },
        },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '富文本组件',
          })
        },
      },
    },
  },
}
