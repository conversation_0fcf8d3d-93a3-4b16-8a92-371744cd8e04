import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'
import formItem from './__screenshots__/form-item.svg'

export default {
  componentName: 'XnFormItem',
  title: '表单元素容器',
  category: '表单',
  componentType: 'FORMITEM',
  formContext: 'SUBMIT',
  treeKey: 'label',
  props: [
    {
      type: 'group',
      title: '布局',
      items: [
        {
          name: 'flex',
          title: 'flex布局',
          setter: 'BoolSetter',
        },
        {
          name: 'size',
          title: { label: '尺寸', tip: '设置尺寸大小' },
          description: '尺寸',
          setter: {
            componentName: 'SelectSetter',
            props: {
              options: [
                {
                  title: '正常',
                  value: 'medium',
                },
                {
                  title: '中等',
                  value: 'small',
                },
                {
                  title: '迷你',
                  value: 'mini',
                },
              ],
            },
          },
        },
        widthSize,
      ],
    },
    {
      type: 'group',
      title: '标签设置',
      items: [
        {
          name: 'label',
          title: '文本',
          setter: 'StringSetter',
          showAI: true,
        },
        {
          name: 'help',
          title: {
            label: '帮助图标',
            tip: '设置帮助图标文本内容',
          },
          setter: 'StringSetter',
        },
        {
          name: 'labelWidth',
          title: '宽度',
          setter: 'StringSetter',
        },
        {
          name: 'labelSuffix',
          title: '后缀',
          setter: 'StringSetter',
        },
        {
          name: 'showDefaultLabelWidth',
          title: {
            label: '默认宽度',
            tip: '适用于没有“标签文本”，但仍需要“标签宽度”的场景，如表单最后的保存和重置按钮',
          },
          setter: 'BoolSetter',
        },
        {
          name: 'showEllipsis',
          title: '超出省略',
          setter: 'BoolSetter',
        },
      ],
    },
    {
      type: 'group',
      title: '数据校验',
      items: [
        {
          name: 'prop',
          title: {
            label: 'prop',
            tip: '表单的“数据对象”里的属性，在使用 validate、resetFields 方法的情况下，该属性是必填的',
          },
          setter: 'StringSetter',
        },
        {
          name: 'required',
          title: '必填',
          setter: 'BoolSetter',
          defaultValue: false,
        },
        {
          name: 'rules',
          title: '验证规则',
          setter: 'JSONSetter',
          documentUrl:
            'https://element.eleme.cn/#/zh-CN/component/form?prop=rules#form-item-attributes',
        },
        {
          name: 'error',
          title: {
            label: '错误信息',
            tip: '设置该值会使表单验证状态变为error，并显示该错误信息',
          },
          setter: 'StringSetter',
        },
        {
          name: 'showMessage',
          title: {
            label: '是否显示',
            tip: '是否显示校验错误信息',
          },
          setter: 'BoolSetter',
          defaultValue: true,
        },
      ],
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'label',
          title: '标签文本的内容',
          setter: 'SlotSetter',
          supportVariable: false,
        },
        {
          name: 'error',
          title: {
            label: '自定义表单校验信息',
            tip: '自定义表单校验信息的显示方式，参数为 { error }',
          },
          setter: 'SlotSetter',
          supportVariable: false,
        },
      ],
    },
  ],
  snippets: [
    {
      title: '表单元素容器',
      screenshot: formItem,
      schema: {
        componentName: 'XnFormItem',
        props: {
          label: '年龄',
        },
        children: [
          {
            componentName: 'XnInput',
            props: {
              type: 'text',
            },
          },
        ],
      },
      prePreview: false,
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
    component: {
      isContainer: true,
      clickCapture: false,
      nestingRule: {
        upRecursiveParent: true,
        parentWhitelist: ['XnForm', 'LAForm', 'XnFlex'],
      },
      getAIParams: (state) => {
        return {
          componentType:
            state.current.props?.field?.toUpperCase?.() ?? 'FORMITEM',
          formContext:
            state.parent.props?.status === 'readonly' ? 'DETAIL' : 'SUBMIT',
          componentTitle: state.current.props.label,
        }
      },
      setAIParams: (arr) => {
        const result = {
          props: {},
        }
        arr.forEach((e) => {
          result.props[e.code] = e.value
        })
        return result
      },
    },
  },
}
