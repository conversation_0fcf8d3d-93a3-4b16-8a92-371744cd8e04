import buttonSvg from './__screenshots__/button.svg'

import { buttonType } from '../../../element-ui/components/button/meta.js'

export default {
  componentName: 'XnButton',
  title: '按钮',
  category: '基础',
  props: [
    {
      name: 'showLoading',
      title: {
        label: '显示加载',
        tip: '点击后是否显示加载动画，需要在按钮回调里手动关闭。',
      },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    // {
    //   name: 'action',
    //   title: { label: 'action', tip: '按钮类型' },
    //   setter: 'StringSetter',
    // },
    {
      name: 'async',
      title: {
        label: '异步',
        tip: '是否异步处理点击事件',
      },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      type: 'group',
      title: '按钮样式',
      items: [
        {
          name: 'icon',
          title: { label: '饿了么图标', tip: '图标类名' },
          description: '图标类名',
          defaultValue: false,
          setter: 'IconSetter',
        },
        {
          name: 'type',
          title: { label: '类型', tip: '设置按钮不同的类型' },
          description: '类型',
          defaultValue: 'default',
          setter: {
            componentName: 'SelectSetter',
            // primary / success / warning / danger / info / text
            props: {
              options: buttonType,
            },
          },
        },
        {
          name: 'plain',
          title: { label: '朴素按钮', tip: '是否朴素按钮' },
          defaultValue: false,
          setter: 'BoolSetter',
          condition(props) {
            return props.type === 'primary'
          },
        },
        {
          name: 'round',
          title: { label: '圆角按钮', tip: '是否圆角按钮' },
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'circle',
          title: { label: '圆形按钮', tip: '是否圆形按钮' },
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'size',
          title: { label: '尺寸', tip: '设置尺寸大小' },
          description: '尺寸',
          setter: {
            componentName: 'SelectSetter',
            props: {
              options: [
                {
                  title: '正常',
                  value: 'medium',
                },
                {
                  title: '中等',
                  value: 'small',
                },
                {
                  title: '迷你',
                  value: 'mini',
                },
              ],
            },
          },
        },
      ],
    },
    {
      title: 'svg图标设置',
      type: 'group',
      items: [
        {
          name: 'svgIcon',
          title: '图标名称',
          setter: {
            componentName: 'IconSetter',
            props: {
              type: 'svg-icon',
            },
          },
        },
        {
          name: 'svgVerticalAlign',
          title: '图标对齐方式',
          setter: 'StringSetter',
        },
        {
          name: 'svgIconSize',
          title: '图标大小',
          setter: 'StringSetter',
        },
      ],
    },
    // {
    //   name: 'loading',
    //   title: { label: '加载状态', tip: '是否加载状态' },
    //   defaultValue: false,
    //   setter: 'BoolSetter',
    // },
    // {
    //   name: 'autofocus',
    //   title: { label: '默认聚焦', tip: '默认聚焦' },
    //   description: '默认聚焦',
    //   defaultValue: false,
    //   setter: 'BoolSetter',
    // },
    // {
    //   name: 'native-type',
    //   title: { label: '原生 type 属性', tip: '原生 type 属性' },
    //   description: '原生 type 属性',
    //   defaultValue: 'button',
    //   setter: {
    //     componentName: 'RadioGroupSetter',
    //     props: {
    //       options: [
    //         {
    //           title: '按钮',
    //           value: 'button',
    //         },
    //         {
    //           title: '提交',
    //           value: 'submit',
    //         },
    //         {
    //           title: '重置',
    //           value: 'reset',
    //         },
    //       ],
    //     },
    //   },
    // },
  ],
  snippets: [
    {
      title: '按钮',
      screenshot: buttonSvg,
      schema: {
        componentName: 'XnButton',
        props: {
          showLoading: false,
        },
        children: [
          {
            componentName: 'LCText',
            props: {
              text: '按钮',
            },
          },
        ],
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'click',
          description: '点击按钮时触发',
          template:
            'function click(resolve, reject, event) { console.log("clicked");}',
        },
      ],
    },
  },
}
