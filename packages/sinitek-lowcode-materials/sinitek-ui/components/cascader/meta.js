import snippets from './snippets'
import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'XnCascader',
  title: '级联选择',
  category: '信息输入',
  props: [
    // {
    //   name: 'value',
    //   title: '级联选择器的值',
    //   setter: 'StringSetter',
    // },
    widthSize,
    {
      name: 'options',
      title: { label: 'options', tip: '设置数据源' },
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/cascader?prop=options#cascader-attributes',
    },
    {
      name: 'props',
      title: '下拉框配置',
      setter: {
        componentName: 'ObjectSetter',
        props: {
          config: {
            items: [
              {
                name: 'expandTrigger',
                title: '次级菜单的展开方式',
                defaultValue: '请选择',
                setter: {
                  componentName: 'RadioGroupSetter',
                  props: {
                    options: [
                      {
                        title: 'click',
                        value: 'click',
                      },
                      {
                        title: 'hover',
                        value: 'hover',
                      },
                    ],
                  },
                },
              },
              {
                name: 'multiple',
                title: '是否多选',
                defaultValue: false,
                setter: 'BoolSetter',
              },
              {
                name: 'checkStrictly',
                title: {
                  label: '节点不互相关联',
                  tip: '是否严格的遵守父子节点不互相关联	',
                },
                defaultValue: false,
                setter: 'BoolSetter',
              },
              {
                name: 'emitPath',
                title: {
                  label: '返回路径',
                  tip: '在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值',
                },
                defaultValue: true,
                setter: 'BoolSetter',
              },
              {
                name: 'lazy',
                title: {
                  label: '动态加载子节点',
                  tip: '是否动态加载子节点，需与 lazyLoad 方法结合使用',
                },
                defaultValue: false,
                setter: 'BoolSetter',
              },
              {
                name: 'lazyLoad',
                title: {
                  label: '加载动态数据方法',
                  tip: '仅在 lazy 为 true 时有效',
                },
                setter: 'FunctionSetter',
                condition(props) {
                  return props.lazy
                },
              },
              {
                name: 'value',
                title: {
                  label: 'value的键名',
                  tip: '指定选项的值为选项对象的某个属性值',
                },
                defaultValue: 'value',
                setter: 'StringSetter',
              },
              {
                name: 'label',
                title: {
                  label: 'label的键名',
                  tip: '指定选项标签为选项对象的某个属性值',
                },
                defaultValue: 'label',
                setter: 'StringSetter',
              },
              {
                name: 'children',
                title: {
                  label: 'children的键名',
                  tip: '指定选项的子选项为选项对象的某个属性值',
                },
                defaultValue: 'children',
                setter: 'StringSetter',
              },
              {
                name: 'disabled',
                title: {
                  label: 'disabled的键名',
                  tip: '指定选项的禁用为选项对象的某个属性值',
                },
                defaultValue: 'disabled',
                setter: 'StringSetter',
              },
              {
                name: 'leaf',
                title: {
                  label: 'leaf的键名',
                  tip: '指定选项的叶子节点的标志位为选项对象的某个属性值',
                },
                defaultValue: 'leaf',
                setter: 'StringSetter',
              },
            ],
          },
        },
      },
    },
    {
      name: 'size',
      title: { label: '原生尺寸', tip: '设置原生尺寸大小' },
      description: '尺寸',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '中等',
              value: 'medium',
            },
            {
              title: '较小',
              value: 'small',
            },
            {
              title: '迷你',
              value: '最小',
            },
          ],
        },
      },
    },
    {
      name: 'placeholder',
      title: '填充内容',
      defaultValue: '请选择',
      setter: 'StringSetter',
    },
    {
      name: 'disabled',
      title: { label: '禁用', tip: '是否禁用' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'clearable',
      title: { label: '可清除', tip: '是否可清除' },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'showAllLevels',
      title: { label: '完整路径', tip: '输入框中是否显示选中值的完整路径' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'collapseTags',
      title: { label: '折叠Tag', tip: '多选模式下是否折叠Tag' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'separator',
      title: '选项分隔符',
      defaultValue: '/',
      setter: 'StringSetter',
    },
    {
      name: 'filterable',
      title: '是否可搜索选项',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'filterMethod',
      title: '自定义搜索逻辑',
      setter: 'FunctionSetter',
    },
    {
      name: 'debounce',
      title: '延迟',
      setter: 'NumberSetter',
    },
    {
      name: 'beforeFilter',
      title: '筛选之前的钩子',
      setter: 'FunctionSetter',
    },
    {
      name: 'popperClass',
      title: '自定义浮层类名',
      setter: 'StringSetter',
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'empty',
          title: { label: 'empty', tip: '无选项列表' },
          setter: 'SlotSetter',
          supportVariable: false,
        },
      ],
    },
  ],
  snippets,
  configure: {
    component: {
      isContainer: true,
      childWrapRenderer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'change',
          description: '当选中节点变化时触发',
          template: 'function change(value) { console.log(value);}',
        },
        {
          name: 'expandChange',
          description: '当展开节点发生变化时触发',
          template: 'function expandChange(arr) { console.log(arr);}',
        },
        {
          name: 'blur',
          description: '在 Input 失去焦点时触发的事件',
          template: 'function blur(event) { console.log(event);}',
        },
        {
          name: 'focus',
          description: '在 Input 获得焦点时触发',
          template: 'function focus(event) { console.log(event);}',
        },
        {
          name: 'visibleChange',
          description: '下拉框出现/隐藏时触发',
          template: 'function visibleChange(visible) { console.log(visible);}',
        },
        {
          name: 'removeTag',
          description: '在多选模式下，移除Tag时触发',
          template: 'function removeTag(tag) { console.log(tag);}',
        },
      ],
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '级联选择',
          })
        },
      },
    },
  },
}
