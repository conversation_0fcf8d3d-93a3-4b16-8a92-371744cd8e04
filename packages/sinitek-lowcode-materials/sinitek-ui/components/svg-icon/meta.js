import svgIconSvg from './__screenshots__/svg.svg'

export default {
  componentName: 'SvgIcon',
  title: '图标',
  category: '基础',
  props: [
    {
      name: 'iconClass',
      title: { label: 'svg图标名', tip: '自定义引入时是图标的文件名' },
      setter: {
        componentName: 'IconSetter',
        props: {
          type: 'svg-icon',
        },
      },
    },
    {
      name: 'className',
      title: '自定义class',
      setter: 'StringSetter',
    },
    {
      name: 'iconColor',
      title: { label: '颜色', tip: '设置图标颜色' },
      description: '颜色',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              title: '激活',
              value: 'active',
            },
            {
              title: '静态',
              value: 'inactive',
            },
            {
              title: '反转',
              value: 'invert',
            },
            {
              title: '默认',
              value: 'default',
            },
          ],
        },
      },
    },
    {
      name: 'verticalAlign',
      title: '对齐方式',
      setter: 'StringSetter',
      defaultValue: 'baseline',
    },
    {
      name: 'cursor',
      title: '鼠标悬浮样式',
      setter: 'StringSetter',
      defaultValue: '',
    },
    {
      name: 'iconSize',
      title: 'svg图标大小',
      setter: 'StringSetter',
    },
    {
      name: 'title',
      title: '鼠标悬浮tips',
      setter: 'StringSetter',
    },
  ],
  snippets: [
    {
      title: 'svg图标',
      screenshot: svgIconSvg,
      schema: {
        componentName: 'SvgIcon',
        props: { iconClass: 'wenhao', iconSize: 24 },
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
      useWrap: true,
      wrapType: 'inline',
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
  },
}
