import {
  generateBindMethodTemplate,
  JSExpression,
  JSFunction,
} from 'sinitek-lowcode-shared'
export default [
  {
    title: '表单容器',
    screenshot: require('./__screenshots__/form.svg'),
    pageData: (id) => {
      return {
        state: {
          [`form_${id}`]: {
            name: '',
            region: '',
            date1: '',
            date2: '',
            desc: '',
          },
        },
        methods: {
          [`submit_${id}`]: {
            type: JSFunction,
            value: `function submit_${id} () {console.log(this.state.form_${id})}`,
          },
        },
      }
    },
    schema: (id) => {
      return {
        componentName: 'XnForm',
        props: {
          widthSize: 'medium',
          labelWidth: '100px',
          model: {
            type: JSExpression,
            value: `this.state.form_${id}`,
          },
        },
        children: [
          {
            componentName: 'XnFormItem',
            props: {
              label: '活动名称',
              required: true,
              prop: `name`,
            },
            children: [
              {
                componentName: 'XnInput',
                LCBindState: `form_${id}.name`,
                props: {},
                events: {},
              },
            ],
          },
          {
            componentName: 'XnFormItem',
            props: {
              label: '活动区域',
              required: true,
              prop: `region`,
            },
            children: [
              {
                componentName: 'LASelect',
                LCBindState: `form_${id}.region`,
                props: {
                  placeholder: '请选择活动区域',
                  options: [
                    { label: '区域一', value: '区域一' },
                    { label: '区域二', value: '区域二' },
                  ],
                },
                children: [],
              },
            ],
          },
          {
            componentName: 'XnFormItem',

            props: {
              label: '活动形式',
              prop: `desc`,
            },
            children: [
              {
                componentName: 'XnInput',
                LCBindState: `form_${id}.desc`,
                props: {
                  type: 'textarea',
                },
              },
            ],
          },
          {
            componentName: 'XnFormItem',
            children: [
              {
                componentName: 'XnButton',
                props: {
                  type: 'primary',
                  showLoading: false,
                },
                events: {
                  click: {
                    type: JSFunction,
                    value: generateBindMethodTemplate(`submit_${id}`),
                  },
                },
                children: [
                  {
                    componentName: 'LCText',
                    props: {
                      text: '立即创建',
                    },
                  },
                ],
              },
              {
                componentName: 'XnButton',
                props: {
                  showLoading: false,
                },
                children: [
                  {
                    componentName: 'LCText',

                    props: {
                      text: '取消',
                    },
                  },
                ],
              },
            ],
            props: {
              showDefaultLabelWidth: true,
            },
          },
        ],
      }
    },
  },
]
