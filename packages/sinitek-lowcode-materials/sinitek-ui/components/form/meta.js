import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'
import snippets from './snippets'
export default {
  componentName: 'XnForm',
  title: '表单容器',
  category: '表单',
  componentType: 'FORM',
  formContext: 'SUBMIT',
  snippets,
  props: [
    {
      name: 'model',
      title: { label: '表单数据', tip: '表单数据对象，动态时请使用绑定变量。' },
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/form?prop=model#form-attributes',
    },
    {
      name: 'disabled',
      title: {
        label: '禁用表单',
        tip: '是否禁用该表单内的所有组件。若设置为 true，则表单内组件上的 disabled 属性不再生效',
      },
      setter: 'BoolSetter',
    },
    {
      title: '标签',
      type: 'group',
      items: [
        {
          name: 'inline',
          title: { label: '行内模式', tip: '行内表单模式' },
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'labelPosition',
          title: {
            label: '标签的位置',
            tip: '表单域标签的位置，如果值为 “左” 或者 “右” 时，则需要设置 “标签宽度”',
          },
          defaultValue: 'right',
          setter: {
            componentName: 'RadioGroupSetter',
            props: {
              options: [
                {
                  title: '左',
                  value: 'left',
                },
                {
                  title: '右',
                  value: 'right',
                },
                {
                  title: '上',
                  value: 'top',
                },
              ],
            },
          },
        },
        {
          name: 'labelWidth',
          title: {
            label: '标签宽度',
            tip: '优先使用标签宽度',
          },
          defaultValue: '100px',
          setter: 'StringSetter',
        },
        {
          name: 'defaultLabelWidth',
          title: {
            label: '标签默认宽度',
            tip: '优先使用标签宽度',
          },
          defaultValue: '100px',
          setter: 'StringSetter',
        },
        {
          name: 'labelSuffix',
          title: '标签后缀',
          setter: 'StringSetter',
        },

        {
          name: 'showEllipsis',
          title: {
            label: '超出省略',
            tip: '标签文字长度超过设置宽度时，显示省略号。',
          },
          defaultValue: false,
          setter: 'BoolSetter',
        },

        {
          name: 'hideRequiredAsterisk',
          title: '隐藏星号',
          setter: 'BoolSetter',
        },
        {
          name: 'statusIcon',
          title: {
            label: '反馈图标',
            tip: '是否在输入框中显示校验结果反馈图标',
          },
          setter: 'BoolSetter',
        },
      ],
    },
    {
      title: '尺寸',
      type: 'group',
      items: [
        widthSize,

        {
          name: 'size',
          title: { label: '组件尺寸', tip: '设置组件尺寸大小' },
          description: '尺寸',
          setter: {
            componentName: 'SelectSetter',
            props: {
              options: [
                {
                  title: '正常',
                  value: 'medium',
                },
                {
                  title: '中等',
                  value: 'small',
                },
                {
                  title: '迷你',
                  value: 'mini',
                },
              ],
            },
          },
        },
      ],
    },

    {
      type: 'group',
      title: '校验',
      items: [
        {
          name: 'rules',
          title: { label: '验证规则', tip: '表单验证规则' },
          setter: 'JSONSetter',
          documentUrl:
            'https://element.eleme.cn/#/zh-CN/component/form?prop=rules#form-attributes',
        },
        {
          name: 'show-message',
          title: { label: '显示校验', tip: '是否显示校验错误信息' },
          defaultValue: true,
          setter: 'BoolSetter',
        },
        {
          name: 'inline-message',
          title: { label: '行内展示', tip: '是否以行内形式展示校验信息' },
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'validateOnRuleChange',
          title: {
            label: '规则变更',
            tip: '是否在 “验证规则” 属性改变后立即触发一次验证',
          },
          setter: 'BoolSetter',
        },
        {
          name: 'scrollToError',
          title: {
            label: '跳转错误',
            tip: '校验失败后是否跳转到第一个错误字段',
          },
          setter: 'BoolSetter',
        },
      ],
    },
  ],
  configure: {
    design: {
      excludeProps: ['model'],
      excludeAttrs: ['model'],
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'validate',
          description: '任一表单项被校验后触发',
          template: 'function validate(prop) { console.log("validate");}',
        },
      ],
      supportBindState: false,
    },
    component: {
      isContainer: true,
      clickCapture: false,
      useWrap: true,
    },
  },
}
