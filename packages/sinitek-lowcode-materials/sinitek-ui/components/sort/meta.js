import sortSvg from './__screenshots__/sort.svg'

export default {
  componentName: 'XnSort',
  title: '排序',
  category: '信息展示',
  props: [
    {
      name: 'sortData',
      title: '排序数据',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/sort.html?prop=sortData#参数',
    },
    {
      name: 'title',
      title: '对话框标题',
      setter: 'StringSetter',
      defaultValue: '对话框你标题',
    },
    {
      name: 'show',
      title: '对话框显示控制标识',
      setter: 'BoolSetter',
    },
    {
      name: 'buttons',
      title: '对话框的按钮配置',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/sort.html?prop=buttons#参数',
    },
    {
      name: 'saveMethod',
      title: '节点个性保存方法',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/sort.html?prop=saveMethod#参数',
    },
    {
      name: 'closeMethod',
      title: '节点个性关闭方法',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/sort.html?prop=closeMethod#参数',
    },
  ],
  snippets: [
    {
      title: '排序',
      screenshot: sortSvg,
      schema: {
        componentName: 'XnSort',
        props: {
          show: true,
          sortData: [
            { id: 3, name: 'demo3' },
            { id: 4, name: 'demo4' },
          ],
          buttons: [
            { label: '保存', type: 'primary', event: 'save', action: 'save' },
            { label: '取消', type: 'info', event: 'cancel', action: 'cancel' },
          ],
          saveMethod() {
            this.show = false
          },
          closeMethod() {
            this.show = false
          },
        },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
      isModal: true,
      modalVisibleProp: 'visible',
      modelProps: {
        'modal-append-to-body': false,
      },
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [],
      supportBindState: false,
    },
  },
}
