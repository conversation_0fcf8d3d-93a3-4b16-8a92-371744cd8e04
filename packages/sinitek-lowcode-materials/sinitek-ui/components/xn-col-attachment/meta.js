export default {
  componentName: 'XnColAttachment',
  title: '表格附加列',
  category: 'SinitekUI',
  props: [
    {
      name: 'count',
      title: {
        label: '附件数量',
        tip: '是否（存在附件）展示回形针图标，值大于 0 时展示',
      },
      setter: 'NumberSetter',
    },
    {
      name: 'sourceId',
      title: { label: 'sourceId', tip: '与 sourceEntity 联合生效' },
      setter: 'StringSetter',
    },
    {
      name: 'sourceEntity',
      title: { label: '唯一值', tip: '可用于dom更新' },
      setter: 'StringSetter',
    },
    {
      name: 'types',
      title: '值为[(title, type)]的集合',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=types#xn-col-attachment',
    },
    {
      name: 'icon',
      title: '图标样式',
      setter: 'StringSetter',
    },
    {
      name: 'width',
      title: '点击图标后弹框的宽度',
      setter: ['StringSetter', 'NumberSetter'],
    },
    {
      name: 'height',
      title: '图标弹框最小高度',
      setter: ['StringSetter', 'NumberSetter'],
    },
    {
      name: 'showSize',
      title: '是否展示大小',
      setter: 'BoolSetter',
    },
    {
      name: 'downloadUrl',
      title: '下载指定的url',
      setter: 'StringSetter',
    },
    {
      name: 'downloadIsPost',
      title: '默认post请求',
      setter: 'BoolSetter',
    },
    {
      name: 'downloadParams',
      title: '下载的额外参数',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=downloadParams#xn-col-attachment',
    },
  ],
  configure: {
    component: {
      isContainer: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
  },
}
