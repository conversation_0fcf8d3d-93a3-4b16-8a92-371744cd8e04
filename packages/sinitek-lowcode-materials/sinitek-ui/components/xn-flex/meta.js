import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'

export default {
  componentName: 'XnFlex',
  title: '弹性分栏容器',
  category: '容器',
  props: [
    widthSize,
    {
      name: 'width',
      title: '宽度',
      defaultValue: '',
      setter: ['StringSetter', 'NumberSetter'],
    },
    {
      name: 'showDashed',
      title: '显示虚线',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'isColumn',
      title: '默认分栏',
      defaultValue: false,
      setter: 'BoolSetter',
    },
  ],
  snippets: [
    {
      title: '弹性分栏容器',
      screenshot: require('./__screenshots__/img.svg'),
      schema: {
        componentName: 'XnFlex',
        props: {},
        children: [],
      },
      prePreviewSchema: {
        componentName: 'XnFlex',
        props: {},
        children: [
          {
            componentName: 'LCText',
            props: { text: '这是一串文本' },
            hidden: false,
            id: '63664926',
            ref: 'LCText_1426',
          },
          {
            componentName: 'LCText',
            props: { text: '这是一串文本' },
            hidden: false,
            id: '32414643',
            ref: 'LCText_5b45',
          },
        ],
        hidden: false,
        id: '13564633',
        ref: 'XnFlex_8484',
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
      useWrap: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [],
      supportBindState: false,
    },
  },
}
