import scheduleSvg from './__screenshots__/schedule.svg'

export default {
  componentName: 'XnSchedule',
  title: '日程组件',
  category: '信息展示',
  props: [
    {
      name: 'calendarViewShow',
      title: '日程视图展示，默认只展示月视图',
      setter: 'ArraySetter',
    },
    {
      name: 'calendarTypeLocation',
      title: '日程类型展示位置',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              title: '左边',
              value: 'left',
            },
            {
              title: '右边',
              value: 'right',
            },
          ],
        },
      },
      defaultValue: 'left',
    },
    {
      name: 'calendarTodayButton',
      title: '日程今日按钮展示',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'calendarSearch',
      title: '日程搜索框展示',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'customAddCalendar',
      title: '自定义添加日程的方式',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'customAddCalendar',
      title: '自定义展示页面',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'searchUrl',
      title: '自定义查询接口url',
      setter: 'StringSetter',
    },
    {
      name: 'customSearchData',
      title: '自定义查询的数据',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/schedule.html?prop=customSearchData#参数',
    },
  ],
  snippets: [
    {
      title: '日程表',
      screenshot: scheduleSvg,
      schema: {
        componentName: 'XnSchedule',
        props: {
          calendarViewShow: ['day', 'week', 'month'],
        },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
      isModal: true,
      modalVisibleProp: 'visible',
      modelProps: {
        'modal-append-to-body': false,
      },
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [],
    },
  },
}
