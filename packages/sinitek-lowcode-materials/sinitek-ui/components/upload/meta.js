import { ComponentName } from 'sinitek-lowcode-shared'
import upload1 from './__screenshots__/upload.svg'
import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'XnUpload',
  title: '上传文件',
  category: '信息输入',
  props: [
    // {
    //   name: 'value',
    //   title: '上传组件的值',
    //   setter: 'StringSetter',
    // },
    {
      name: 'mode',
      title: { label: '模式', tip: '上传模式' },
      description: '上传模式',
      defaultValue: 'upload',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              title: '按钮上传',
              value: 'upload',
            },
            {
              title: '拖拽上传',
              value: 'dragger',
            },
            {
              title: '头像上传',
              value: 'avatar',
            },
          ],
        },
      },
    },

    widthSize,
    {
      name: 'readOnly',
      title: '只读',
      defaultValue: false,
      setter: 'BoolSetter',
    },

    {
      name: 'tip',
      title: { label: '提示信息', tip: '控件提示信息' },
      defaultValue: '',
      setter: 'StringSetter',
    },

    {
      name: 'showFileList',
      title: { label: '上传列表', tip: '是否显示上传列表' },
      defaultValue: true,
      setter: 'BoolSetter',
    },

    {
      type: 'group',
      title: '上传配置',
      items: [
        {
          name: 'action',
          title: { label: '后端接口', tip: '上传附件的后端接口' },
          setter: {
            componentName: 'StringSetter',
            props: {
              removeProp: true,
            },
          },
        },
        {
          name: 'autoStart',
          title: '自动上传',
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'sourceId',
          title: '来源ID',
          setter: 'StringSetter',
        },
        {
          name: 'sourceEntity',
          title: '来源实体',
          setter: 'StringSetter',
        },
        {
          name: 'type',
          title: { label: '上传类型', tip: '不传默认返回所有类型' },
          setter: 'StringSetter',
        },
        {
          name: 'isChunkUpload',
          title: {
            label: '分片上传',
            tip: '组件未设置该属性的情况下，以sinitekUI全局配置$SINITEKUI.chunkUpload为准，默认为true',
          },
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'requestNum',
          title: {
            label: '分片并发数',
            tip: '默认值为3，自定义传值不能小于1',
          },
          defaultValue: 3,
          setter: 'NumberSetter',
        },
        {
          name: 'chunkSize',
          title: {
            label: '分片大小',
            tip: '默认值为1，单位为M',
          },
          defaultValue: 1,
          setter: 'NumberSetter',
        },
      ],
    },
    {
      type: 'group',
      title: '限制',
      items: [
        {
          name: 'multi',
          title: '多选',
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'allowEmpty',
          title: { label: '大小为0', tip: '是否允许上传大小为0的文件' },
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'limit',
          title: { label: '个数', tip: '最大允许上传的个数' },
          defaultValue: 1,
          setter: 'NumberSetter',
        },
        {
          name: 'allowOverlay',
          title: {
            label: '允许覆盖',
            tip: 'limit为1时，是否允许再次上传时覆盖前一次上传的文件',
          },

          setter: 'BoolSetter',
        },
        {
          name: 'accept',
          title: { label: '文件类型', tip: '接受上传的文件扩展名' },
          setter: 'StringSetter',
        },
        {
          name: 'uploadMaxSize',
          title: '大小限制',
          setter: 'StringSetter',
        },
        {
          name: 'fileNameMaxLength',
          title: {
            label: '文件名长度',
            tip: '文件名长度限制。注意： 若要大于300长度，表sirm_attachment.name 字段长度要加长',
          },
          setter: 'NumberSetter',
        },
      ],
    },

    {
      type: 'group',
      title: '高级',
      items: [
        {
          name: 'requestConfig',
          title: '上传接口自定义配置',
          setter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: [
                  {
                    name: 'download',
                    title: '附件下载请求配置',
                    setter: {
                      componentName: 'ObjectSetter',
                      props: {
                        config: {
                          items: [
                            {
                              name: 'url',
                              title: 'url配置',
                              setter: {
                                componentName: 'StringSetter',
                                props: {
                                  removeProp: true,
                                },
                              },
                            },
                            {
                              name: 'isPost',
                              title: 'post请求',
                              setter: 'BoolSetter',
                            },
                            {
                              name: 'params',
                              title: '请求参数',
                              setter: 'JSONSetter',
                              documentUrl:
                                'http://192.168.1.121:18056/doc/sirmapp-dev/components/upload.html?prop=requestConfig.download.params#requestconfig属性',
                            },
                          ],
                        },
                      },
                    },
                  },
                  {
                    name: 'uploadList',
                    title: '附件列表回显接口配置',
                    setter: {
                      componentName: 'ObjectSetter',
                      props: {
                        config: {
                          items: [
                            {
                              name: 'url',
                              title: 'url配置',
                              setter: {
                                componentName: 'StringSetter',
                                props: {
                                  removeProp: true,
                                },
                              },
                            },
                            {
                              name: 'isPost',
                              title: 'post请求',
                              setter: 'BoolSetter',
                            },
                            {
                              name: 'fieldPath',
                              title: '请求路径',
                              setter: {
                                componentName: 'StringSetter',
                                props: {
                                  removeProp: true,
                                },
                              },
                            },
                          ],
                        },
                      },
                    },
                  },
                  {
                    name: 'action',
                    title: '普通上传接口配置',
                    setter: {
                      componentName: 'ObjectSetter',
                      props: {
                        config: {
                          items: [
                            {
                              name: 'url',
                              title: 'url配置',
                              setter: {
                                componentName: 'StringSetter',
                                props: {
                                  removeProp: true,
                                },
                              },
                            },
                            {
                              name: 'fieldPath',
                              title: '请求路径',
                              setter: {
                                componentName: 'StringSetter',
                                props: {
                                  removeProp: true,
                                },
                              },
                            },
                          ],
                        },
                      },
                    },
                  },
                  {
                    name: 'uploadChunk',
                    title: '分片上传接口配置',
                    setter: {
                      componentName: 'ObjectSetter',
                      props: {
                        config: {
                          items: [
                            {
                              name: 'url',
                              title: 'url配置',
                              setter: {
                                componentName: 'StringSetter',
                                props: {
                                  removeProp: true,
                                },
                              },
                            },
                            {
                              name: 'params',
                              title: '请求参数',
                              setter: 'JSONSetter',
                              documentUrl:
                                'http://192.168.1.121:18056/doc/sirmapp-dev/components/upload.html?prop=requestConfig.download.params#requestconfig属性',
                            },
                            {
                              name: 'fieldPath',
                              title: '请求路径',
                              setter: {
                                componentName: 'StringSetter',
                                props: {
                                  removeProp: true,
                                },
                              },
                            },
                          ],
                        },
                      },
                    },
                  },
                  {
                    name: 'uploadChunkCheck',
                    title: '分片上传校验接口配置',
                    setter: {
                      componentName: 'ObjectSetter',
                      props: {
                        config: {
                          items: [
                            {
                              name: 'url',
                              title: 'url配置',
                              setter: {
                                componentName: 'StringSetter',
                                props: {
                                  removeProp: true,
                                },
                              },
                            },
                            {
                              name: 'isPost',
                              title: 'post请求',
                              setter: 'BoolSetter',
                            },
                            {
                              name: 'fieldPath',
                              title: '请求路径',
                              setter: {
                                componentName: 'StringSetter',
                                props: {
                                  removeProp: true,
                                },
                              },
                            },
                            {
                              name: 'params',
                              title: '请求参数',
                              setter: 'JSONSetter',
                              documentUrl:
                                'http://192.168.1.121:18056/doc/sirmapp-dev/components/upload.html?prop=requestConfig.download.params#requestconfig属性',
                            },
                          ],
                        },
                      },
                    },
                  },
                  {
                    name: 'avatarList',
                    title: '头像上传回显接口配置',
                    setter: {
                      componentName: 'ObjectSetter',
                      props: {
                        config: {
                          items: [
                            {
                              name: 'url',
                              title: 'url配置',
                              setter: {
                                componentName: 'StringSetter',
                                props: {
                                  removeProp: true,
                                },
                              },
                            },
                            {
                              name: 'isPost',
                              title: 'post请求',
                              setter: 'BoolSetter',
                            },
                            {
                              name: 'fieldPath',
                              title: '请求路径',
                              setter: {
                                componentName: 'StringSetter',
                                props: {
                                  removeProp: true,
                                },
                              },
                            },
                            {
                              name: 'params',
                              title: '请求参数',
                              setter: 'JSONSetter',
                              documentUrl:
                                'http://192.168.1.121:18056/doc/sirmapp-dev/components/upload.html?prop=requestConfig.download.params#requestconfig属性',
                            },
                          ],
                        },
                      },
                    },
                  },
                ],
              },
            },
          },
        },
        {
          name: 'filePreview',
          title: '文件预览自定义配置',
          setter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: [
                  {
                    name: 'isEnable',
                    title: '开启预览',
                    setter: 'BoolSetter',
                  },
                  {
                    name: 'isShowWatermark',
                    title: '展示水印',
                    setter: 'BoolSetter',
                  },
                  {
                    name: 'watermarkContent',
                    title: '返回水印内容',
                    setter: 'FunctionSetter',
                  },
                  {
                    name: 'isInternalOpening',
                    title: {
                      label: '预览打开方式',
                      tip: '为true时打开一个tab页，为false时打开一个新窗口，上传下载组件在xn-dialog里时只能为false',
                    },
                    setter: 'BoolSetter',
                  },
                  {
                    name: 'handle',
                    title: '自定义预览函数',
                    setter: 'FunctionSetter',
                  },
                ],
              },
            },
          },
        },

        {
          name: 'beforeUpload',
          title: '上传文件之前的钩子',
          setter: 'FunctionSetter',
        },
        {
          name: 'beforeRemove',
          title: '删除文件之前的钩子',
          setter: 'FunctionSetter',
        },
        {
          name: 'onExceed',
          title: '超过自定义校验',
          setter: 'FunctionSetter',
        },
        {
          name: 'onPreview',
          title: '预览函数',
          setter: 'FunctionSetter',
        },
        {
          name: 'customUploadItemClass',
          title: '返回上传列表子项的样式类名',
          setter: 'FunctionSetter',
        },
        {
          name: 'sources',
          title: '多来源配置',
          setter: {
            componentName: 'ArraySetter',
            props: {
              itemSetter: {
                componentName: 'ObjectSetter',
                props: {
                  config: {
                    items: [
                      {
                        name: 'sourceEntity',
                        title: 'sourceEntity',
                        setter: 'StringSetter',
                      },
                      {
                        name: 'sourceId',
                        title: 'sourceId',
                        setter: 'StringSetter',
                      },
                      {
                        type: 'types',
                        title: 'types',
                        setter: 'JSONSetter',
                        documentUrl:
                          'http://192.168.1.121:18056/doc/sirmapp-dev/components/upload.html?prop=sources#参数',
                      },
                    ],
                  },
                },
                initialValue: {
                  componentName: 'ElTableColumn',
                  props: {
                    prop: 'prop',
                    label: 'label',
                  },
                  children: [],
                },
              },
            },
          },
        },
      ],
    },

    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'upload-slot',
          title: '触发文件选择框的内容',
          setter: 'SlotSetter',
        },
        {
          name: 'manual-slot',
          title: '自定义选择框',
          setter: 'SlotSetter',
        },
        {
          name: 'draggerForCustom-slot',
          title: '拖拽上传选择框内容',
          setter: 'SlotSetter',
        },
        {
          name: 'upload-item-slot',
          title: '额外自定义内容',
          setter: 'SlotSetter',
        },
      ],
    },
  ],
  snippets: [
    {
      title: '上传文件',
      screenshot: upload1,
      schema: {
        componentName: 'XnUpload',
        props: { showFileList: true },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'beforeUpload',
          description: '上传文件之前的钩子',
          template:
            'function beforeUpload(file) { console.log(file);return true;}',
        },
        {
          name: 'beforeRemove',
          description: '删除文件之前的钩子',
          template:
            'function beforeRemove(file,fileList) { console.log(file,fileList);return true;}',
        },
        {
          name: 'onExceed',
          description: '文件超过允许个数的钩子',
          template:
            'function onExceed(file,fileList) { console.log(file,fileList);return fileList;}',
        },
        {
          name: 'onPreview',
          description: '文件超过允许个数的钩子',
          template: 'function onPreview(file) { console.log(file,fileList);}',
        },
        {
          name: 'input',
          description: '输入事件，输入值变化事触发',
          template:
            'function input(uploadFileList, model, type, removeFileList) { console.log(uploadFileList);}',
        },
        {
          name: 'onRemove',
          description: '附件删除时触发',
          template:
            'function onRemove(file, filelist) { console.log(file, filelist);}',
        },
        {
          name: 'onSuccess',
          description: '附件上传成功时触发',
          template:
            'function onSuccess(res, file, filelist) { console.log(file, filelist);}',
        },
        {
          name: 'onError',
          description: '附件上传成功时触发',
          template:
            'function onError(err, file, filelist) { console.log(err);}',
        },
        {
          name: 'onProgress',
          description: '附件上传中触发',
          template:
            'function onProgress(event, file, filelist) { console.log(event);}',
        },
        {
          name: 'onRemoveAll',
          description: '附件上传中触发',
          template:
            'function onRemoveAll(removeFileList) { console.log(removeFileList);}',
        },
        {
          name: 'onRemoveAll',
          description: '附件上传中触发',
          template:
            'function onRemoveAll(removeFileList) { console.log(removeFileList);}',
        },
        {
          name: 'clear',
          description: '清空上传附件的各个值',
          template: 'function clear() { console.log("clear");}',
        },
        {
          name: 'clearAvatar',
          description: '清空头像上传列表',
          template: 'function clearAvatar() { console.log("clear avatar");}',
        },
        {
          name: 'clearError',
          description: '点击事件，清空错误信息',
          template: 'function clearError() { console.log("clear error");}',
        },
        {
          name: 'uploadFiles',
          description: '文件手动上传',
          template: 'function uploadFiles(fileList) { console.log(fileList);}',
        },
        {
          name: 'downloadFile',
          description: '文件手动下载',
          template: 'function downloadFile(file) { console.log(file);}',
        },
      ],
    },
    advanced: {
      callbacks: {
        onNodeAdd: (node, target) => {
          return formItemAdd(node, target, {
            label: '上传附件',
            laForm: {
              field: ComponentName.FILE,
            },
          })
        },
      },
    },
  },
}
