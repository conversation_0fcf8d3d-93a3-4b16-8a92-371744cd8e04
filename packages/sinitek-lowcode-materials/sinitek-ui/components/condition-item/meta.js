import conditionItemSvg from './__screenshots__/condition-item.svg'
import ElFormItem from '../../../sinitek-ui/components/form-item/meta'
export default {
  componentName: 'XnConditionItem',
  title: '查询元素',
  category: '信息输入',
  componentType: 'CONDITIONITEM',
  formContext: 'SEARCH',
  props: ElFormItem.props,
  snippets: [
    {
      title: '条件',
      screenshot: conditionItemSvg,
      schema: {
        componentName: 'XnConditionItem',
        props: {
          label: '年龄',
        },
        children: [
          {
            componentName: 'XnInput',
            props: {
              type: 'text',
              value: '',
            },
            events: {},
          },
        ],
      },
      prePreview: false,
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
    component: {
      isContainer: true,
      clickCapture: false,
      nestingRule: {
        parentWhitelist: ['XnCondition'],
      },
    },
  },
}
