export default {
  componentName: 'XnTransfer',
  title: '穿梭框',
  category: '信息输入',
  componentType: 'TRANSFER',
  formContext: 'SUBMIT',
  props: [
    {
      name: 'sortData',
      title: { label: '排序数据', tip: '穿梭框右侧数据' },
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/transfer.html?prop=sortData#参数',
    },
    {
      name: 'moveData',
      title: { label: '移动数据', tip: '穿梭框左侧数据' },
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/transfer.html?prop=moveData#参数',
    },
    {
      name: 'buttons',
      title: '按钮配置',
      setter: {
        componentName: 'ArraySetter',
        props: {
          itemSetter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: [
                  {
                    name: 'label',
                    title: '按钮文本',
                    setter: 'StringSetter',
                  },
                  {
                    name: 'type',
                    title: '按钮类型',
                    setter: 'StringSetter',
                  },
                  {
                    type: 'action',
                    title: '按钮效果',
                    setter: 'StringSetter',
                  },
                  {
                    type: 'event',
                    title: '按钮事件',
                    setter: 'StringSetter',
                  },
                  {
                    type: 'showLoading',
                    title: '显示加载',
                    setter: 'BoolSetter',
                  },
                ],
              },
            },
            initialValue: {
              componentName: 'ElTableColumn',
              props: {
                prop: 'prop',
                label: 'label',
              },
              children: [],
            },
          },
        },
      },
    },
    {
      name: 'saveMethod',
      title: '节点个性保存方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'closeMethod',
      title: '节点个性关闭方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'title',
      title: '对话框标题',
      setter: 'StringSetter',
    },
    {
      name: 'listTitleLeft',
      title: '穿梭框左侧小标题',
      setter: 'StringSetter',
    },
    {
      name: 'listTitleRight',
      title: '穿梭框右侧小标题',
      setter: 'StringSetter',
    },
    {
      name: 'show',
      title: '对话框显示',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'showRightToolbar',
      title: '右侧按钮显示',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'dialogWidth',
      title: '穿梭框宽度',
      setter: 'StringSetter',
    },
    {
      name: 'showManageIcon',
      title: '数据管理按钮',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'isLoading',
      title: '是否loading',
      setter: 'BoolSetter',
      defaultValue: false,
    },
  ],
  snippets: [
    {
      title: '穿梭框',
      screenshot: require('./__screenshots__/transfer.svg'),
      schema: {
        componentName: 'XnTransfer',
        props: {
          title: '表单标题',
          moveData: [
            { id: 1, name: 'demo1' },
            { id: 2, name: 'demo2' },
          ],
          sortData: [
            { id: 3, name: 'demo3' },
            { id: 4, name: 'demo4' },
          ],
          buttons: [
            { label: '保存', type: 'primary', event: 'save', action: 'save' },
            { label: '取消', type: 'info', event: 'cancel', action: 'cancel' },
          ],
          to: false,
          show: true,
          showRightToolbar: true,
        },
        children: [],
      },
      prePreview: false,
    },
  ],
  configure: {
    component: {
      isContainer: false,
      isModal: true,
      modalVisibleProp: 'show',
      modelProps: {
        'modal-append-to-body': false,
      },
      rootSelector: '.el-dialog',
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'edit',
          description: '点击保存按钮时触发',
          template: 'function edit() {}',
        },
        {
          name: 'delete',
          description: '点击取消按钮时触发',
          template: 'function delete() {}',
        },
      ],
    },
  },
}
