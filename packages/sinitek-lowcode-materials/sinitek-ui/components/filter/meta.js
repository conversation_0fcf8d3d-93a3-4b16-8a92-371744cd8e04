import filterSvg from './__screenshots__/filter.svg'

export default {
  componentName: 'XnFilter',
  title: '筛选器',
  category: '信息输入',
  componentType: 'FILTER',
  formContext: 'SEARCH',
  props: [
    {
      name: 'value',
      title: '筛选器的值',
      setter: 'StringSetter',
    },
    {
      name: 'options',
      title: '筛选的选项数组',
      setter: {
        componentName: 'ArraySetter',
        props: {
          itemSetter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: [
                  {
                    name: 'label',
                    title: '文本',
                    setter: 'StringSetter',
                  },
                  {
                    name: 'value',
                    title: '值',
                    setter: 'StringSetter',
                  },
                ],
              },
            },
            initialValue: {},
          },
        },
      },
    },
    {
      name: 'multiple',
      title: { label: '多选', tip: '	是否默认显示为多选' },
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'maxLength',
      title: '默认展示的数量',
      setter: 'NumberSetter',
      defaultValue: 10,
    },
    {
      name: 'collapse',
      title: {
        label: '折叠',
        tip: '是否开启选项数超过maxLength时以"更多""收起"的方式展示',
      },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'inline',
      title: {
        label: '行内显示',
        tip: '全选按钮是否与选项同在行内，false为换行',
      },
      setter: 'BoolSetter',
      defaultValue: true,
    },
  ],
  snippets: [
    {
      title: '过滤器',
      screenshot: filterSvg,
      schema: {
        componentName: 'XnFilter',
        props: {
          options: [
            {
              label: '选项1',
              value: 1,
            },
            {
              label: '选项2',
              value: 2,
            },
            {
              label: '选项3',
              value: 3,
            },
            {
              label: '选项4',
              value: 4,
            },
            {
              label: '选项5',
              value: 5,
            },
            {
              label: '选项6',
              value: 6,
            },
            {
              label: '选项7',
              value: 7,
            },
            {
              label: '选项8',
              value: 8,
            },
            {
              label: '选项9',
              value: 9,
            },
            {
              label: '选项10',
              value: 10,
            },
            {
              label: '选项11',
              value: 11,
            },
          ],
          collapse: true,
          inline: true,
        },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'change',
          description: '在筛选器值改变时触发',
          template: 'function change(value) { console.log(value);}',
        },
        {
          name: 'switch',
          description: '单选、多选切换时触发',
          template: 'function focus() { console.log("switched");}',
        },
      ],
    },
  },
}
