import snippets from './snippets'
export default {
  componentName: 'XnTree',
  title: '树组件',
  category: '信息展示',
  props: [
    {
      name: 'width',
      title: '宽度',
      setter: 'StringSetter',
    },
    {
      name: 'virtual',
      title: '是否切换为虚拟树',
      setter: 'BoolSetter',
      defaultValue: false,
      condition(props) {
        return props.height || props.dynamicHeight
      },
    },
    {
      name: 'height',
      title: '高度',
      setter: 'StringSetter',
    },
    {
      name: 'dynamicHeight',
      title: '动态高度',
      setter: 'FunctionSetter',
    },
    {
      name: 'data',
      title: '数据来源',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/tree.html?prop=data#参数',
    },
    {
      name: 'url',
      title: { label: '请求接口', tip: '后台请求接口的值' },
      defaultValue: '/mock/example/tree',
      setter: 'StringSetter',
    },
    {
      name: 'param',
      title: { label: '请求参数', tip: '后台请求参数' },
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/tree.html?prop=param#参数',
    },
    {
      name: 'showFilter',
      title: { label: '展示搜索', tip: '是否显示搜索框' },
      setter: 'BoolSetter',
    },
    {
      name: 'checkStrictly',
      title: {
        label: '不互相关联',
        tip: '在显示复选框的情况下，是否严格的遵循父子不互相关联的做法',
      },
      setter: 'BoolSetter',
    },
    {
      name: 'showCheckbox',
      title: { label: '可被选择', tip: '节点是否可被选择' },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'defaultCheckedKeys',
      title: { label: '默认勾选', tip: '	默认勾选的节点数组' },
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/tree.html?prop=defaultCheckedKeys#参数',
    },
    {
      name: 'defaultExpandedKeys',
      title: { label: '默认勾默认展开选', tip: '	默认展开的节点的 key 的数组' },
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/tree.html?prop=defaultExpandedKeys#参数',
    },
    {
      name: 'filterNodeMethod',
      title: {
        label: '选择节点方法',
        tip: '对树节点进行筛选时执行的方法，返回 true 表示这个节点可以显示，返回 false 则表示这个节点会被隐藏',
      },
      setter: 'FunctionSetter',
    },
    {
      name: 'accordion',
      title: '是否每次只打开一个同级树节点',
      setter: 'BoolSetter',
    },
    {
      name: 'indent',
      title: { label: '水平缩进', tip: '相邻级节点间的水平缩进，单位为像素' },
      defaultValue: 16,
      setter: 'NumberSetter',
    },
    {
      name: 'lazy',
      title: '懒加载子节点',
      setter: 'BoolSetter',
    },
    {
      name: 'onNodeClick',
      title: '节点被点击时的回调',
      setter: 'FunctionSetter',
    },
    {
      name: 'deleteNode',
      title: '删除节点',
      setter: 'FunctionSetter',
    },
    {
      name: 'addNode',
      title: '新增节点',
      setter: 'FunctionSetter',
    },
    {
      name: 'editNode',
      title: '编辑节点',
      setter: 'FunctionSetter',
    },
    {
      name: 'selectId',
      title: { label: '节点 Id', tip: '初始化时默认显示的节点 Id' },
      setter: 'StringSetter',
      defaultValue: -1,
    },
    {
      name: 'defaultExpandAll',
      title: '默认展开所有节点',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'expandLevel',
      title: '默认展开层级',
      setter: 'NumberSetter',
      defaultValue: 0,
    },
    {
      name: 'highlightCurrent',
      title: '高亮当前选中',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'renderContent',
      title: '自定义树内容',
      setter: 'FunctionSetter',
    },
    {
      name: 'renderAfterExpand',
      title: {
        label: '展开后渲染',
        tip: '是否在第一次展开某个树节点后才渲染其子节点',
      },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'expandOnClickNode',
      title: {
        label: '点击节点展开收缩',
        tip: '是否在点击节点的时候展开或者收缩节点，如果为 false，则只有点箭头图标的时候才会展开或者收缩节点',
      },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'checkOnClickNode',
      title: {
        label: '点击时选中',
        tip: '是否在点击节点的时候选中节点',
      },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'autoExpandParent',
      title: {
        label: '父节点自动展开',
        tip: '展开子节点的时候是否自动展开父节点',
      },
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'emptyText',
      title: {
        label: '无数据提示',
        tip: '列表节点空时的提示文字',
      },
      setter: 'StringSetter',
      defaultValue: '无数据',
    },
    {
      name: 'nodeKey',
      title: {
        label: '节点id',
        tip: '每个树节点用来作为唯一标识的属性',
      },
      setter: 'StringSetter',
      defaultValue: 'id',
    },
    {
      name: 'itemMaxWidth',
      title: {
        label: '最大宽度',
        tip: '自定义的树列表的树节点最大宽度',
      },
      setter: 'StringSetter',
    },
    {
      name: 'props',
      title: '节点配置选项',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/tree.html?prop=props#参数',
    },
    {
      name: 'isPost',
      title: { label: '请求方式', tip: '请求方式是否为post' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'initFieldPath',
      title: {
        label: 'response解析路径',
        tip: '请求的response解析路径，默认为data',
      },
      defaultValue: 'data',
      setter: 'StringSetter',
    },
    {
      name: 'handleNodeError',
      title: {
        label: '节点error图标',
        tip: '节点error图标的tip文字，返回值为string时展示图标，默认具有node和data两个参数',
      },
      setter: 'FunctionSetter',
    },
    {
      name: 'allowInt',
      title: 'allowInt',
      defaultValue: true,
      setter: 'BoolSetter',
    },
  ],
  snippets,
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'nodeClick',
          description: '节点被点击时的回调',
          template:
            'function nodeClick(obj,node,element) { console.log(obj,node,element);}',
        },
        {
          name: 'nodeContextmenu',
          description: '当某一节点被鼠标右键点击时触发',
          template:
            'function nodeContextmenu(event,node,selected,childNodeSelected) { console.log(node);}',
        },
        {
          name: 'checkChange',
          description: '节点选中状态发生变化时的回调',
          template:
            'function checkChange(obj,selected,childNodeSelecteds) { console.log(obj,selected,childNodeSelecteds);}',
        },
        {
          name: 'check',
          description: '当复选框被点击的时候触发',
          template:
            'function check(obj,selectedObj) { console.log(obj,selectedObj);}',
        },
        {
          name: 'currentChange',
          description: '当前选中节点变化时触发的事件',
          template: 'function iconClick(data,obj) { console.log(data,obj);}',
        },
        {
          name: 'nodeExpand',
          description: '节点被展开时触发的事件',
          template:
            'function nodeExpand(obj,node,element) { console.log(obj,node,element);}',
        },
        {
          name: 'nodeCollapse',
          description: '节点被关闭时触发的事件',
          template:
            'function nodeCollapse(obj,node,element) { console.log(obj,node,element);}',
        },
      ],
    },
  },
}
