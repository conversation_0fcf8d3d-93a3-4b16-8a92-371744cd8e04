import condition from './__screenshots__/condition.svg'
export default [
  {
    title: '条件查询组件',
    screenshot: condition,
    schema: {
      componentName: 'XnCondition',
      props: {
        model: {
          type: 'JSExpression',
          value: 'this.state.form',
        },
      },
      children: [
        {
          componentName: 'LCRow',
          children: [
            {
              componentName: 'XnConditionItem',
              props: {
                label: '姓名',
              },
              children: [
                {
                  componentName: 'XnInput',
                  props: {
                    value: {
                      type: 'JSExpression',
                      value: 'this.state.form.name',
                    },
                  },
                  events: {
                    input: {
                      type: 'JSFunction',
                      value: 'function (e) {this.state.form.name=e}',
                    },
                  },
                },
              ],
            },
            {
              componentName: 'XnConditionItem',
              props: {
                label: '性别',
              },
              children: [
                {
                  componentName: 'LASelect',
                  props: {
                    placeholder: '请选择性别',
                    value: 1,
                    options: [
                      { label: '女', value: 1 },
                      { label: '男', value: 0 },
                    ],
                  },
                  events: {},
                  children: [],
                },
              ],
            },
            {
              componentName: 'XnConditionItem',
              props: {
                label: '年龄',
              },
              children: [
                {
                  componentName: 'XnInput',
                  props: {
                    type: 'text',
                    value: '',
                  },
                  events: {},
                },
              ],
            },
          ],
        },
        {
          componentName: 'LCRow',
          children: [
            {
              componentName: 'XnConditionItem',
              props: {
                label: '电话',
              },
              children: [
                {
                  componentName: 'XnInput',
                  props: {
                    type: 'text',
                    value: '',
                  },
                  events: {},
                },
              ],
            },
            {
              componentName: 'XnConditionItem',
              props: {
                label: '住址',
              },
              children: [
                {
                  componentName: 'XnInput',
                  props: {
                    type: 'text',
                    value: '',
                  },
                  events: {},
                },
              ],
            },
            {
              componentName: 'XnConditionItem',
              props: {
                label: '身高',
              },
              children: [
                {
                  componentName: 'XnInput',
                  props: {
                    type: 'text',
                    value: '',
                  },
                  events: {},
                },
              ],
            },
          ],
        },
      ],
    },
  },
]
