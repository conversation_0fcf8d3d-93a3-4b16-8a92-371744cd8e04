import snippets from './snippets'
import ElFrom from '../../../element-ui/components/form/meta'

export default {
  componentName: 'XnCondition',
  title: '条件查询组件',
  category: '信息输入',
  componentType: 'CONDITION',
  formContext: 'SEARCH',
  snippets,
  props: [
    {
      name: 'showNum',
      title: '默认控件行数',
      setter: 'NumberSetter',
      defaultValue: 3,
    },
    {
      name: 'overlays',
      title: '以浮层方式展开',
      setter: 'BoolSetter',
    },
    ...ElFrom.props,
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'query',
          description: '点击查询按钮时触发	',
          template: 'function query() { console.log("query");}',
        },
        {
          name: 'clear',
          description:
            '当存在自定义的组件需要清空内容时，例如 xn-employee，点击重置按钮时触发',
          template: 'function clear() { console.log("clear");}',
        },
        ...ElFrom.configure.supports.events,
      ],
    },
    component: {
      isContainer: true,
      clickCapture: false,
    },
  },
}
