export default {
  componentName: 'XnContainer',
  title: '居中对齐容器',
  category: '容器',
  props: [
    {
      name: 'align',
      title: '对齐方式',
      defaultValue: 'center',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '居中',
              value: 'center',
            },
            {
              title: '左对齐',
              value: 'left',
            },
          ],
        },
      },
    },
    {
      name: 'isPercent',
      title: {
        label: '是否百分比',
        tip: '容器下的表单width-size是否为百分比（主要是配合表单使用）',
      },
      defaultValue: false,
      setter: 'BoolSetter',
    },
  ],
  snippets: [
    {
      title: '居中对齐容器',
      screenshot: require('./__screenshots__/img.svg'),
      schema: {
        componentName: 'XnContainer',
        props: {},
        children: [],
      },
      prePreviewSchema: {
        componentName: 'XnContainer',
        props: {},
        children: [
          {
            componentName: 'LCText',
            props: { text: '这是一串文本' },
            hidden: false,
            id: '8e3c6454',
            ref: 'LCText_3634',
          },
        ],
        hidden: false,
        id: '16524246',
        ref: 'XnContainer_a346',
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
      useWrap: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [],
      supportBindState: false,
    },
  },
}
