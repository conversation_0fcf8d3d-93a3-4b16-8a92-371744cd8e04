// import screenshots1 from './__screenshots__/dialog-1.png'
import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'
import { buttonType } from '../../../element-ui/components/button/meta.js'

export default {
  componentName: 'XnDialog',
  title: '对话框',
  category: '信息展示',
  props: [
    {
      name: 'show',
      title: { label: '弹窗显示', tip: '设计时不会生效' },
      setter: 'BoolSetter',
    },
    {
      name: 'limitToTab',
      title: '限制在tab内',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'title',
      title: '弹窗标题',
      setter: 'StringSetter',
    },
    {
      name: 'to',
      title: '挂载的dom选择器',
      setter: 'StringSetter',
    },
    {
      name: 'top',
      title: '上边距',
      setter: 'StringSetter',
      defaultValue: '15vh',
    },
    {
      name: 'innerTop',
      title: '限制在tab内时的margin-top',
      setter: 'StringSetter',
      defaultValue: '5vh',
    },
    {
      name: 'width',
      title: '宽度',
      setter: 'StringSetter',
    },
    {
      name: 'height',
      title: '高度',
      setter: 'StringSetter',
    },
    {
      name: 'buttons',
      title: '按钮配置',
      setter: {
        componentName: 'ArraySetter',
        props: {
          itemSetter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: [
                  {
                    name: 'label',
                    title: '按钮文本',
                    setter: 'StringSetter',
                  },
                  {
                    name: 'type',
                    title: '按钮类型',
                    setter: {
                      componentName: 'SelectSetter',
                      // primary / success / warning / danger / info / text
                      props: {
                        options: buttonType,
                      },
                    },
                  },
                  {
                    name: 'action',
                    title: '按钮效果',
                    setter: {
                      componentName: 'SelectSetter',
                      // primary / success / warning / danger / info / text
                      props: {
                        options: [
                          {
                            title: '新增',
                            value: 'add',
                          },
                          {
                            title: '编辑',
                            value: 'edit',
                          },
                          {
                            title: '删除',
                            value: 'delete',
                          },
                        ],
                      },
                    },
                  },
                  {
                    name: 'event',
                    title: '按钮事件',
                    setter: 'FunctionSetter',
                  },
                  {
                    name: 'showLoading',
                    title: '显示加载',
                    setter: 'BoolSetter',
                  },
                ],
              },
            },
            initialValue: {
              label: '按钮',
              type: 'primary',
              action: '',
              event: '',
            },
          },
        },
      },
    },
    {
      name: 'loadingNotAllowClose',
      title: 'loading时不允许关闭',
      setter: 'BoolSetter',
    },
    {
      name: 'showFooter',
      title: '显示弹窗底部',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    widthSize,
    {
      name: 'showFullscreenIcon',
      title: '全屏按钮显示',
      setter: 'BoolSetter',
    },
    {
      name: 'fullscreen',
      title: '是否全屏',
      setter: 'BoolSetter',
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          title: '标题',
          name: 'title',
          setter: 'SlotSetter',
          supportVariable: false,
        },
        {
          title: '底部',
          name: 'footer',
          setter: 'SlotSetter',
          supportVariable: false,
        },
      ],
    },
  ],
  // snippets: [
  //   {
  //     title: '对话框',
  //     screenshot: screenshots1,
  //     schema: {
  //       componentName: 'XnDialog',
  //       props: {
  //         title: '表单标题',
  //         to: false,
  //         buttons: [
  //           { label: '保存', type: 'primary', action: 'save', event: 'save' },
  //           { label: '取消', type: 'info', action: 'cancel', event: 'cancel' },
  //         ],
  //         limitToTab: true,
  //         loadingNotAllowClose: true,
  //         showFooter: true,
  //         showFullscreenIcon: true,
  //       },
  //       children: [
  //         {
  //           componentName: 'Slot',
  //           props: {
  //             name: 'default',
  //           },
  //           children: [],
  //         },
  //       ],
  //     },
  //   },
  // ],
  configure: {
    component: {
      isContainer: false,
      isModal: true,
      clickCapture: false,
      clickCaptureBlackList: ['el-button', 'el-dialog__header'],
      modalVisibleProp: 'show',
      // modelProps: {
      //   to: false,
      // },
      rootSelector: '.el-dialog',
      nestingRule: {
        childWhitelist: ['Slot'],
      },
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'save',
          description: '点击保存按钮时触发',
          template: 'function save(resolve, reject) { resolve()}',
        },
        {
          name: 'cancel',
          description: '点击取消按钮时触发',
          template: 'function cancel() { console.log("cancel");}',
        },
      ],
    },
  },
}
