import treeselect from './__screenshots__/treeselect.svg'
import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'
export default {
  componentName: 'XnTreeselect',
  title: '树状下拉框',
  category: '信息输入',
  props: [
    {
      name: 'model',
      title: '分组id的值',
      setter: 'StringSetter',
      defaultValue: 0,
    },
    {
      name: 'width',
      title: '下拉框宽度',
      setter: 'NumberSetter',
    },
    {
      name: 'maxHeight',
      title: '最大高度',
      setter: 'NumberSetter',
    },
    {
      name: 'multi',
      title: '多选',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'options',
      title: { label: 'options', tip: '数据源' },
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/treeselect.html?prop=options#参数',
    },
    {
      name: 'searchAble',
      title: '可否查询',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'showCount',
      title: '是否展示每层个数',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'defaultExpandLevel',
      title: '默认展示层级',
      setter: 'NumberSetter',
      defaultValue: 2,
    },
    {
      name: 'title',
      title: '对话框标题',
      setter: 'StringSetter',
    },
    {
      name: 'show',
      title: '对话框显示',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'limit',
      title: '多选时一行显示的个数',
      setter: 'NumberSetter',
    },
    {
      name: 'openEvent',
      title: '打开下拉框触发的事件',
      setter: 'FunctionSetter',
    },
    widthSize,
  ],
  snippets: [
    {
      title: '树状下拉框',
      screenshot: treeselect,
      schema: {
        componentName: 'XnTreeselect',
        props: {
          options: [
            {
              id: 1,
              label: '一级 1',
              children: [
                {
                  id: 2,
                  label: '二级 1',
                  children: [
                    {
                      id: 5,
                      label: '三级 1',
                      children: [],
                    },
                  ],
                },
                {
                  id: 3,
                  label: '二级 2',
                  children: [],
                },
              ],
            },
            {
              id: 4,
              label: '一级 2',
              children: [],
            },
          ],
          model: 0,
          maxHeight: 250,
          defaultExpandLevel: 2,
        },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
    },
  },
}
