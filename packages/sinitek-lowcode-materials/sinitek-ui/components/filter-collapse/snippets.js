import filterCollapse from './__screenshots__/filter-collapse.svg'
export default [
  {
    title: '折叠筛选',
    screenshot: filterCollapse,
    schema: {
      componentName: 'XnFilterCollapse',
      props: { height: 170, collapse: true, border: true, button: true },
      children: [
        {
          componentName: 'XnForm',
          props: { labelPosition: 'right' },
          children: [
            {
              componentName: 'XnFormItem',
              props: {
                label: '输入条件',
              },
              children: [
                {
                  componentName: 'XnInput',
                  props: {},
                },
              ],
            },
            {
              componentName: 'XnFormItem',
              props: {
                label: '筛选条件1',
              },
              children: [
                {
                  componentName: 'XnFilter',
                  props: {
                    collapse: false,
                    options: [
                      {
                        label: '选项1',
                        value: 1,
                      },
                      {
                        label: '选项2',
                        value: 2,
                      },
                      {
                        label: '选项3',
                        value: 3,
                      },
                    ],
                  },
                },
              ],
            },
            {
              componentName: 'XnFormItem',
              props: {
                label: '筛选条件2',
              },
              children: [
                {
                  componentName: 'XnFilter',
                  props: {
                    multiple: true,
                    options: [
                      {
                        label: '选项1',
                        value: 1,
                      },
                      {
                        label: '选项2',
                        value: 2,
                      },
                      {
                        label: '选项3',
                        value: 3,
                      },
                      {
                        label: '选项4',
                        value: 4,
                      },
                    ],
                  },
                },
              ],
            },
          ],
        },
      ],
    },
  },
]
