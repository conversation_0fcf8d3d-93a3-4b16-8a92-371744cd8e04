import snippets from './snippets'
export default {
  componentName: 'XnFilterCollapse',
  title: '折叠筛选',
  category: '信息输入',
  snippets,
  props: [
    {
      name: 'height',
      title: '容器高度',
      setter: 'StringSetter',
    },
    {
      name: 'collapse',
      title: '是否使用折叠展开',
      setter: 'BoolSetter',
      defaultValue: true,
    },
    {
      name: 'border',
      title: '显示底部边线',
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'button',
      title: {
        label: '使用按钮',
        tip: '是否使用顶部查询、重置按钮',
      },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'inline',
      title: {
        label: '行内',
        tip: '顶部按钮是否在第一行行内右侧显示',
      },
      defaultValue: false,
      setter: 'BoolSetter',
    },
  ],
  configure: {
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'query',
          description: '点击查询按钮时触发',
          template: 'function query() { console.log("查询");}',
        },
        {
          name: 'reset',
          description: '点击重置按钮时触发',
          template: 'function reset() { console.log("重置");}',
        },
      ],
    },
    component: {
      isContainer: true,
    },
  },
}
