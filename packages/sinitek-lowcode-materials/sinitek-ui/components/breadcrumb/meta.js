import snippets from './snippets'
export default {
  componentName: 'XnBreadcrumb',
  title: '面包屑',
  category: '信息展示',
  componentType: 'BREADCRUMB',
  formContext: 'DETAIL',
  props: [
    {
      name: 'separatorClass',
      title: '图标分隔符',
      setter: 'StringSetter',
      defaultValue: 'el-icon-arrow-right',
    },
    {
      name: 'separator',
      title: '分隔符',
      setter: 'StringSetter',
    },
  ],
  snippets,
  configure: {
    component: {
      isContainer: true,
      supportBindState: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
    },
  },
}
