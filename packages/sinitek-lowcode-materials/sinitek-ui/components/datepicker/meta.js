import { widthSize } from 'sinitek-lowcode-materials/comm/width-size'
import datepicker1 from '../../../element-ui/components/date-picker/__screenshots__/date-picker.svg'
import { formItemAdd } from 'sinitek-lowcode-materials/utils/node-add'
export default {
  componentName: 'XnDatePicker',
  title: '日期选择框',
  category: '信息输入',
  props: [
    // {
    //   name: 'value',
    //   title: '日期组件的值',
    //   setter: 'StringSetter',
    // },
    // {
    //   name: 'defaultTime',
    //   title: '默认选择时间',
    //   setter: 'JSONSetter',
    // },
    {
      name: 'format',
      title: { label: '显示格式', tip: '输入日期格式' },
      setter: {
        componentName: 'StringSetter',
        placeholder: 'yyyy-MM-dd HH:mm:ss',
      },
    },
    {
      name: 'valueFormat',
      title: { label: '值格式', tip: '输入日期格式' },
      setter: {
        componentName: 'StringSetter',
        placeholder: 'yyyy-MM-dd HH:mm:ss',
      },
    },

    {
      name: 'type',
      title: { label: '类型', tip: '设置日期类型' },
      description: '类型',
      defaultValue: 'date',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '年',
              value: 'year',
            },
            {
              title: '月',
              value: 'month',
            },
            {
              title: '日期',
              value: 'date',
            },
            {
              title: '多日期',
              value: 'dates',
            },
            {
              title: '周',
              value: 'week',
            },
            {
              title: '多年',
              value: 'years',
            },
            {
              title: '日期时间',
              value: 'datetime',
            },
            {
              title: '日期时间区间',
              value: 'datetimerange',
            },
            {
              title: '日期区间',
              value: 'daterange',
            },
            {
              title: '月度区间',
              value: 'monthrange',
            },
          ],
        },
      },
    },
    // {
    //   name: 'width',
    //   title: { label: '宽度', tip: '日期选择框宽度' },
    //   setter: 'StringSetter',
    // },

    widthSize,
    {
      name: 'readonly',
      title: '只读',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'disabled',
      title: '禁用',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'editable',
      title: '可输入',
      setter: 'BoolSetter',
    },
    {
      name: 'clearable',
      title: '可清除',
      setter: 'BoolSetter',
    },
    // {
    //   name: 'size',
    //   title: { label: '尺寸', tip: '设置尺寸大小' },
    //   description: '尺寸',
    //   setter: {
    //     componentName: 'SelectSetter',
    //     props: {
    //       options: [
    //         {
    //           title: '正常',
    //           value: 'medium',
    //         },
    //         {
    //           title: '中等',
    //           value: 'small',
    //         },
    //         {
    //           title: '迷你',
    //           value: 'mini',
    //         },
    //       ],
    //     },
    //   },
    // },
    {
      name: 'placeholder',
      title: '占位内容',
      setter: 'StringSetter',
    },

    {
      name: 'align',
      title: '对齐方式',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              title: '靠左',
              value: 'left',
            },
            {
              title: '中间',
              value: 'center',
            },
            {
              title: '靠右',
              value: 'right',
            },
          ],
        },
      },
      defaultValue: 'left',
    },
    {
      name: 'popperClass',
      title: '下拉框的类名',
      setter: 'StringSetter',
    },

    // {
    //   name: 'defaultValue',
    //   title: '默认显示的时间',
    //   setter: 'JSONSetter',
    // },
    // {
    //   name: 'name',
    //   title: '原生属性',
    //   setter: 'StringSetter',
    // },
    {
      name: 'prefixIcon',
      title: '自定义头部图标',
      defaultValue: 'el-icon-time',
      setter: {
        componentName: 'IconSetter',
        props: {
          type: 'element-ui',
        },
      },
    },
    {
      title: '范围选择',
      type: 'group',
      items: [
        {
          name: 'unlinkPanels',
          title: '独立切换月份',
          setter: 'BoolSetter',
        },
        {
          name: 'separate',
          title: '自定义关联',
          setter: 'BoolSetter',
        },
        {
          name: 'rangeSeparator',
          title: '分隔符',
          setter: 'StringSetter',
          defaultValue: '-',
        },
        {
          name: 'startPlaceholder',
          title: '开始日期占位内容',
          setter: 'StringSetter',
        },
        {
          name: 'endPlaceholder',
          title: '结束日期的占位内容',
          setter: 'StringSetter',
        },
      ],
    },
    // {
    //   name: 'clearIcon',
    //   title: '自定义清空图标',
    //   defaultValue: 'el-icon-circle-close',
    //   setter: {
    //     componentName: 'IconSetter',
    //     props: {
    //       type: 'element-ui',
    //     },
    //   },
    // },
    // {
    //   name: 'validateEvent',
    //   title: '表单的校验',
    //   setter: 'BoolSetter',
    //   defaultValue: true,
    // },
    {
      title: '高级',
      type: 'group',
      items: [
        {
          name: 'showHoliday',
          title: { label: '节假日', tip: '是否展示节假日' },
          setter: 'BoolSetter',
          defaultValue: false,
        },
        {
          name: 'holidayUrl',
          title: '节假日请求接口',
          setter: 'StringSetter',
        },
        {
          name: 'pickerOptions',
          title: '时间选择配置',
          setter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: [
                  {
                    name: 'shortcuts',
                    title: '设置快捷选项',
                    setter: 'JSONSetter',
                    documentUrl:
                      'https://element.eleme.cn/#/zh-CN/component/date-picker?prop=shortcuts#shortcuts',
                  },
                  {
                    name: 'disabledDate',
                    title: '设置禁用状态',
                    setter: 'FunctionSetter',
                  },
                  {
                    name: 'cellClassName',
                    title: '日期的 className',
                    setter: 'FunctionSetter',
                  },
                  {
                    name: 'firstDayOfWeek',
                    title: '周起始日',
                    setter: 'NumberSetter',
                  },
                  {
                    name: 'onPick',
                    title: '选中日期后会执行的回调',
                    setter: 'FunctionSetter',
                  },
                ],
              },
            },
          },
        },
        {
          name: 'appendToBody',
          title: '插入至 body',
          setter: 'BoolSetter',
          defaultValue: true,
        },
      ],
    },
  ],
  snippets: [
    {
      title: '日期选择框',
      screenshot: datepicker1,
      schema: {
        componentName: 'XnDatePicker',
        props: {
          pickerOptions: {
            shortcuts: [],
            firstDayOfWeek: 7,
            clearable: true,
            showHoliday: true,
          },
        },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
      useWrap: true,
      wrapType: 'inline',
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'change',
          description: '用户确认选定的值时触发',
          template: 'function change(value) { console.log(value);}',
        },
        {
          name: 'blur',
          description: '在失去焦点时触发的事件',
          template: 'function blur(event) { console.log(event);}',
        },
        {
          name: 'focus',
          description: '在获得焦点时触发',
          template: 'function focus(event) { console.log(event);}',
        },
      ],
    },
    advanced: {
      callbacks: {
        onNodeAdd(node, target) {
          return formItemAdd(node, target, {
            label: '日期选择框',
          })
        },
      },
    },
  },
}
