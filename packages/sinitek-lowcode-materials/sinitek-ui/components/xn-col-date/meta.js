export default {
  componentName: 'xn-col-date',
  title: '表格列日期',
  category: 'SinitekUI',
  props: [
    {
      name: 'prop',
      title: 'prop',
      setter: 'StringSetter',
    },
    {
      name: 'label',
      title: 'label',
      setter: 'StringSetter',
    },
    {
      name: 'width',
      title: '列宽度',
      setter: 'NumberSetter',
    },
    {
      name: 'fixed',
      title: '列是否固定及位置',
      setter: 'StringSetter',
    },
    {
      name: 'format',
      title: '日期格式化方式',
      setter: 'StringSetter',
    },
    {
      name: 'align',
      title: '列对齐方式',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '靠左',
              value: 'left',
            },
            {
              title: '居中',
              value: 'center',
            },
            {
              title: '靠右',
              value: 'right',
            },
          ],
        },
      },
    },
    {
      name: 'showOverflowTooltip',
      title: '内容超出提示',
      setter: 'BoolSetter',
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
  },
}
