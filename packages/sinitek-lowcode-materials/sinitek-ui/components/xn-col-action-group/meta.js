export default {
  componentName: 'XnColAction',
  title: '表格操作列',
  category: 'SinitekUI',
  props: [
    {
      name: 'showIcon',
      title: { label: '展示图标', tip: '是否展示图标' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'onlyIcon',
      title: { label: '仅展示图标', tip: '是否仅展示图标' },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'keys',
      title: { label: '唯一值', tip: '可用于dom更新' },
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=keys#参数-5',
    },
    {
      name: 'justify',
      title: '内部排列',
      defaultValue: 'center',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: 'start',
              value: 'start',
            },
            {
              title: 'end',
              value: 'end',
            },
            {
              title: 'center',
              value: 'center',
            },
            {
              title: 'space-around',
              value: 'space-around',
            },
            {
              title: 'space-between',
              value: 'space-between',
            },
          ],
        },
      },
    },
    {
      name: 'boxJustify',
      title: '内部排列',
      defaultValue: 'center',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: 'start',
              value: 'start',
            },
            {
              title: 'end',
              value: 'end',
            },
            {
              title: 'center',
              value: 'center',
            },
            {
              title: 'space-around',
              value: 'space-around',
            },
            {
              title: 'space-between',
              value: 'space-between',
            },
          ],
        },
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
    },
    supportBindState: false,
  },
}
