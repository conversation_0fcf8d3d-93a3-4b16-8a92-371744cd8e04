export default {
  componentName: 'xn-col',
  title: '表格列',
  category: 'SinitekUI',
  treeKey: 'label',
  scope: [
    {
      name: 'rows',
      detail: '行数据',
    },
    {
      name: 'column',
      detail: '列配置',
    },
    {
      name: '$index',
      detail: '行索引',
    },
  ],
  props: [
    {
      name: 'CWRCondition',
      title: '展示列',
      setter: 'BoolSetter',
      defaultValue: true,
      condition: (_, target) => {
        return target.componentName === 'LATable'
      },
    },
    {
      name: 'label',
      title: '列标题',
      setter: 'StringSetter',
    },
    {
      name: 'type',
      title: '列类型',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '默认',
              value: 'default',
            },
            // {
            //   title: '多选框',
            //   value: 'selection',
            // },
            {
              title: '拖动行排序',
              value: 'draggable',
            },
            // {
            //   title: '单选',
            //   value: 'radio',
            // },
          ],
        },
      },
    },
    {
      name: 'className',
      title: '列的 className',
      setter: 'StringSetter',
    },
    {
      name: 'labelClassName',
      title: '列标题的自定义类名',
      setter: 'StringSetter',
    },
    {
      name: 'prop',
      title: 'prop',
      setter: {
        componentName: 'StringSetter',
        props: {
          suggestions: async (target) => {
            if (target.props.datasource) {
              return target.getModelFields(target.props.datasource)
            }
            return []
          },
        },
      },
    },
    {
      name: 'width',
      title: '列宽度',
      setter: 'NumberSetter',
    },
    {
      name: 'minWidth',
      title: '列最小宽度',
      setter: 'NumberSetter',
    },
    {
      name: 'renderHeader',
      title: 'Label渲染方法',
      setter: {
        componentName: 'FunctionSetter',
        props: {
          template: `function renderHeader(h, { column, $index }) {
            return <div>
              <span>
                {column.label}
              </span>
            </div>
          }`,
        },
      },
    },
    {
      name: 'sortable',
      title: '是否排序',
      setter: [
        {
          componentName: 'SelectSetter',
          props: {
            options: [
              {
                title: '后端排序',
                value: 'custom',
              },
              {
                title: '前端排序',
                value: true,
              },
            ],
          },
        },
      ],
    },
    {
      name: 'sortBy',
      title: '按属性排序',
      setter: 'StringSetter',
    },
    {
      name: 'resizable',
      title: '能否拖拽改变宽度',
      setter: 'BoolSetter',
    },
    {
      name: 'columnKey',
      title: '能否拖拽改变宽度',
      setter: 'BoolSetter',
    },
    {
      name: 'align',
      title: '列对齐方式',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '靠左',
              value: 'left',
            },
            {
              title: '居中',
              value: 'center',
            },
            {
              title: '靠右',
              value: 'right',
            },
          ],
        },
      },
    },
    {
      name: 'headerAlign',
      title: '表头对齐方式',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '靠左',
              value: 'left',
            },
            {
              title: '居中',
              value: 'center',
            },
            {
              title: '靠右',
              value: 'right',
            },
          ],
        },
      },
    },
    {
      name: 'showOverflowTooltip',
      title: '内容超出提示',
      setter: 'BoolSetter',
    },
    {
      name: 'fixed',
      title: '列是否固定及位置',
      setter: 'StringSetter',
    },
    {
      name: 'formatter',
      title: '格式化内容',
      setter: 'FunctionSetter',
    },
    {
      name: 'selectable',
      title: '此行可否勾选',
      setter: 'FunctionSetter',
      condition(props) {
        return props.type === 'selection'
      },
    },
    {
      name: 'tooltip',
      title: '提示内容',
      setter: 'StringSetter',
    },
    {
      name: 'filterMethod',
      title: '过滤方法',
      setter: 'FunctionSetter',
    },
    {
      name: 'filteredValue',
      title: '选中的数据过滤项',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=filteredValue#参数-2',
    },
    {
      name: 'filters',
      title: '数据过滤项',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=filters#参数-2',
    },
    {
      name: 'filterPlacement',
      title: '过滤弹出框定位',
      setter: 'StringSetter',
    },
    {
      name: 'filterMultiple',
      title: '过滤的选项是否多选',
      setter: 'BoolSetter',
    },
    {
      name: 'index',
      title: '自定义索引',
      setter: 'FunctionSetter',
      condition(props) {
        return props.type === 'index'
      },
    },
    {
      name: 'dataType',
      title: '数据类型',
      defaultValue: 'text',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              label: '文本',
              value: 'text',
            },
            {
              label: '日期',
              value: 'date',
            },
            {
              label: '数字',
              value: 'number',
            },
          ],
        },
      },
      condition: (props) => {
        return props.type === 'default' || !props.type
      },
      extraProps: {
        setValue: (target, v, childTarget) => {
          if (!v || !childTarget) return
          setTimeout(() => {
            if (v === 'number') {
              childTarget.setPropValue('align', 'right')
            } else if (v === 'date') {
              childTarget.setPropValue('align', 'center')
            }
          }, 50)
        },
      },
    },
    {
      name: 'format',
      title: '日期格式化方式',
      setter: {
        componentName: 'StringSetter',
        props: {
          placeholder: 'yyyy-MM-dd HH:mm:ss',
        },
      },
    },
    {
      name: 'pointPlace',
      title: '小数保留位数',
      setter: 'StringSetter',
      condition(props) {
        return props.datatType === 'number'
      },
    },
    {
      name: 'isMoney',
      title: '千分位格式化',
      setter: 'BoolSetter',
      condition(props) {
        return props.datatType === 'number'
      },
    },
    {
      name: 'export',
      title: '是否需要导出',
      setter: 'BoolSetter',
    },
    {
      name: 'dynamicShow',
      title: '动态列是否展示',
      setter: 'BoolSetter',
    },
    {
      name: 'isBase64Decode',
      title: 'Base64解码',
      setter: 'BoolSetter',
    },
    {
      name: 'isSensitiveInfo',
      title: '敏感信息解码',
      setter: 'BoolSetter',
    },
    {
      name: 'formatNullValue',
      title: '空值时的展示文本',
      setter: 'StringSetter',
    },
    {
      name: 'filterModel',
      title: '筛选查询字段',
      setter: 'StringSetter',
    },
    {
      name: 'fieldName',
      title: '字段key键名',
      setter: 'StringSetter',
    },
    {
      name: 'filterComponentProp',
      title: '筛选弹窗默认prop对象',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/table.html?prop=filterComponentProp#参数-2',
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'default',
          title: { label: '自定义列插槽', tip: '自定义列的内容' },
          setter: 'SlotSetter',
        },
        {
          name: 'default-filter',
          title: {
            label: '默认组件插槽',
            tip: '筛选弹窗内默认组件插槽',
          },
          setter: 'SlotSetter',
          props: {
            scopedSlot: true,
          },
          supportVariable: false,
        },
        {
          name: 'custom-filter',
          title: {
            label: '额外查询条件插槽',
            tip: '筛选弹窗内额外查询条件插槽',
          },
          setter: 'SlotSetter',
          props: {
            scopedSlot: true,
          },
          supportVariable: false,
        },
      ],
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
  },
}
