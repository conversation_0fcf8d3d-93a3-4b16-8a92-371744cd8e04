import breadcrumb from '../breadcrumb/__screenshots__/image.svg'
export default {
  componentName: 'XnBreadcrumbItem',
  title: '面包屑组件',
  category: '信息展示',
  componentType: 'BREADCRUMBITEM',
  formContext: 'DETAIL',
  props: [
    {
      name: 'isLink',
      title: '是否可点击',
      setter: 'BoolSetter',
    },
    {
      name: 'effect',
      title: 'tooltip的主题样式',
      setter: 'StringSetter',
      defaultValue: 'dark',
    },
    {
      name: 'placement',
      title: 'tooltip的定位',
      setter: 'StringSetter',
      defaultValue: 'bottom-start',
    },
    {
      name: 'maxWidth',
      title: '文本的最大宽度',
      setter: 'StringSetter',
      defaultValue: 'auto',
    },
    {
      name: 'to',
      title: '路由跳转对象',
      setter: 'JSONSetter',
      documentUrl:
        'https://element.eleme.cn/#/zh-CN/component/breadcrumb?prop=to#breadcrumb-item-attributes',
    },
    {
      name: 'replace',
      title: {
        label: '添加history记录',
        tip: '在使用 to 进行路由跳转时，启用 replace 将不会向 history 添加新记录',
      },
      setter: 'BoolSetter',
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          title: 'tooltip提示文本',
          name: 'tooltip',
          setter: 'SlotSetter',
          props: {
            scopedSlot: false,
          },
          supportVariable: false,
        },
      ],
    },
  ],
  snippets: [
    {
      title: '面包屑子项',
      screenshot: breadcrumb,
      schema: {
        componentName: 'XnBreadcrumbItem',
        props: {},
        children: [
          {
            componentName: 'LCText',
            props: { text: '第一级' },
          },
        ],
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'click',
          description: '文本点击事件，当is-link为true时才会触发',
          template: 'function click(event) { console.log(event);}',
        },
      ],
    },
  },
}
