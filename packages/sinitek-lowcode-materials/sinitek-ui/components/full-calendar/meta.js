import snippets from './snippets'
export default {
  componentName: 'XnFullCalendar',
  title: '大型日历',
  category: '信息展示',
  componentType: 'FULLCALEDAR',
  formContext: 'DETAIL',
  props: [
    {
      name: 'value',
      title: '日历组件的值',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/full-calendar.html?prop=value#参数',
    },
    {
      name: 'config',
      title: '组件配置信息',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/full-calendar.html?prop=config#参数',
    },
    {
      name: 'url',
      title: '请求地址',
      setter: 'StringSetter',
    },
    {
      name: 'data',
      title: '事件数据',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/full-calendar.html?prop=data#参数',
    },
    {
      name: 'params',
      title: 'url请求参数',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/full-calendar.html?prop=params#参数',
    },
    {
      name: 'fetchIsPost',
      title: 'post请求',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'fetchFieldPath',
      title: 'url请求返回值解析路径',
      setter: 'StringSetter',
      defaultValue: 'data',
    },
  ],
  snippets,
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'dayClick',
          description: '点击日期的时候触发',
          template: 'function dayClick(info) { console.log(info);}',
        },
        {
          name: 'eventClick',
          description: '点击日期上面的事件的时候触发	',
          template: 'function eventClick(obj) { console.log(obj.event);}',
        },
        // {
        //   name: 'eventMouseEnter',
        //   description: '鼠标移入事件时触发',
        //   template: 'function eventMouseEnter(info) { console.log(info);}',
        // },
        // {
        //   name: 'eventMouseLeave',
        //   description: '鼠标移出事件时触发',
        //   template: 'function eventMouseLeave(info) { console.log(info);}',
        // },
      ],
    },
  },
}
