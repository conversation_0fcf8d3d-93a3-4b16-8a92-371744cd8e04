import fullCalendar from './__screenshots__/image.svg'
export default [
  {
    title: '大型日历',
    screenshot: fullCalendar,
    schema: {
      componentName: 'Div',
      children: [
        {
          componentName: 'XnFullCalendar',
          props: {
            config: {
              headerToolbar: {
                left: 'prev,next,today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay',
              },
              nextDayThreshold: '00:00',
              // 'zh-cn'、'en-gb'、'zh-tw'
              locale: 'zh-cn',
              initialView: 'dayGridMonth',
              editable: false,
              selectable: true,
              timeZone: 'Asia/Chongqing',
              titleFormat: 'YYYY年MM月DD日',
              allDayText: '全天',
              slotLabelFormat: 'H:mm',
              listDayFormat: 'YYYY年MM月DD日',
              nowIndicator: true,
              selectMirror: true,
              dayMaxEventRows: true,
              dayPopoverFormat: 'YYYY年MM月DD日',
              moreLinkContent: '展示更多',
              noEventsContent: '暂无相关日程',
            },
            data: [
              {
                id: 1,
                title: '出差',
                start: '2019-04-08',
                end: '2019-05-09',
              },
              {
                id: 2,
                title: '春游',
                start: '2019-04-08',
                end: '2019-04-09',
              },
            ],
          },
          children: [],
        },
      ],
      style: 'display: table;margin: 0;',
    },
  },
]
