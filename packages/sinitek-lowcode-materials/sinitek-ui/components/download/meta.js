// import download from './__screenshots__/download.png'
export default {
  componentName: 'XnDownload',
  title: '下载文件',
  category: '信息展示',
  props: [
    {
      name: 'sourceId',
      title: { label: '来源 id', tip: '' },
      setter: 'StringSetter',
    },
    {
      name: 'sourceEntity',
      title: { label: '来源实体', tip: '' },
      setter: 'StringSetter',
    },
    {
      name: 'downloadUrl',
      title: '下载指定的URL',
      setter: 'StringSetter',
    },
    {
      name: 'downloadIsPost',
      title: 'post请求',
      setter: 'BoolSetter',
    },
    {
      name: 'downloadParams',
      title: '下载的额外参数',
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/download.html?prop=downloadParams#参数',
    },
    {
      name: 'showSize',
      title: '展示文件大小',
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'type',
      title: { label: '指定下载的附件的类型', tip: '未设置时下载所有' },
      setter: {
        componentName: 'StringSetter',
        props: {
          removeProp: true,
        },
      },
      propType: 'number',
    },
    {
      name: 'types',
      title: {
        label: '类型集合',
        tip: '值为[(title, type)]的集合，其中 title 是折叠面板的标题，type 是附件所属类型',
      },
      setter: 'JSONSetter',
      documentUrl:
        'http://*************:18056/doc/sirmapp-dev/components/download.html?prop=types#参数',
    },
    {
      title: '高级',
      type: 'group',
      items: [
        {
          name: 'onPreview',
          title: '预览函数',
          setter: 'FunctionSetter',
        },
        {
          name: 'filePreview',
          title: '文件预览自定义配置',
          setter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: [
                  {
                    name: 'isEnable',
                    title: '开启预览',
                    setter: 'BoolSetter',
                  },
                  {
                    name: 'isShowWatermark',
                    title: '展示水印',
                    setter: 'BoolSetter',
                  },
                  {
                    name: 'watermarkContent',
                    title: '返回水印内容',
                    setter: 'FunctionSetter',
                  },
                  {
                    name: 'isInternalOpening',
                    title: {
                      label: '预览打开方式',
                      tip: '为true时打开一个tab页，为false时打开一个新窗口，上传下载组件在xn-dialog里时只能为false',
                    },
                    setter: 'BoolSetter',
                  },
                  {
                    name: 'handle',
                    title: '自定义预览函数',
                    setter: 'FunctionSetter',
                  },
                ],
              },
            },
          },
        },
      ],
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'attachment-item',
          title: '子项额外自定义内容插槽',
          setter: 'SlotSetter',
        },
      ],
    },
  ],
  snippets: [
    // {
    //   title: '下载文件',
    //   screenshot: download,
    //   schema: {
    //     componentName: 'XnDownload',
    //     props: { showSize: true },
    //     children: [],
    //   },
    // },
  ],
  configure: {
    component: {
      isContainer: true,
      placeholder: {
        height: '30px',
        text: '下载文件',
      },
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'onPreview',
          description: '点击已下载列表中文件时的钩子',
          template: 'function onPreview(file) { console.log(file,fileList);}',
        },
      ],
      methods: [
        {
          name: 'downloadFile',
          description: '下载文件',
          example: 'this.$refs.download.downloadFile(file)',
          args: [
            {
              name: 'file',
              description: '接口返回的file对象',
              type: 'object',
            },
          ],
        },
      ],
    },
  },
  screenshot: require('./__screenshots__/download.svg'),
}
