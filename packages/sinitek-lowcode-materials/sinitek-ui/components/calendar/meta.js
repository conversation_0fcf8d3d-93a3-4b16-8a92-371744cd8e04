import calendarSvg from './__screenshots__/calendar.svg'

export default {
  componentName: 'XnCalendar',
  title: '日历组件',
  category: '信息输入',
  props: [
    // {
    //   name: 'value',
    //   title: '日历组件的值',
    //   setter: 'StringSetter',
    // },
    {
      name: 'fullscreen',
      title: '是否全屏显示',
      setter: 'BoolSetter',
    },
    {
      name: 'mode',
      title: '初始模式',
      defaultValue: false,
      setter: {
        componentName: 'month',
        props: {
          options: [
            {
              title: '月',
              value: 'month',
            },
            {
              title: '年',
              value: 'year',
            },
          ],
        },
      },
    },
    {
      name: 'supportMark',
      title: '是否开启标记功能',
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'markList',
      title: '标记日期列表',
      setter: 'JSONSetter',
      defaultValue: [],
      documentUrl:
        'http://192.168.1.121:18056/doc/sirmapp-dev/components/calendar.html?prop=markList#参数',
    },
    {
      name: 'defaultDate',
      title: '默认展示日期',
      setter: 'JSONSetter',
      documentUrl:
        'http://192.168.1.121:18056/doc/sirmapp-dev/components/calendar.html?prop=defaultDate#参数',
    },
    {
      name: 'disabledDate',
      title: '不可选日期',
      setter: {
        componentName: 'FunctionSetter',
        props: {
          template: 'function disabledDate(currentDate) { return false}',
        },
      },
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          title: '日期渲染插槽',
          name: 'dateCellRender',
          setter: 'SlotSetter',
          props: {
            scopedSlot: false,
          },
          supportVariable: false,
        },
      ],
    },
  ],
  snippets: [
    {
      title: '日历',
      screenshot: calendarSvg,
      schema: {
        componentName: 'XnCalendar',
        props: { supportMark: true },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      events: [
        {
          name: 'panelChange',
          description: '日期面板变化回调',
          template:
            'function panelChange(date,mode) { console.log(date,mode);}',
        },
        {
          name: 'select',
          description: '点击选择日期回调',
          template: 'function select(date) { console.log(date);}',
        },
        {
          name: 'change',
          description: '日期变化时的回调',
          template: 'function change(date) { console.log(date);}',
        },
      ],
    },
  },
}
