import loop1 from './__screenshots__/loop.svg'

export default {
  componentName: 'LCLoop',
  title: '循环',
  category: '基础',
  componentType: 'Loop',
  formContext: 'DETAIL',
  props: [
    {
      name: 'options',
      title: { label: '数据源', tip: '数据源需要是一个数组' },
      setter: 'JSONSetter',
    },
    {
      name: 'loopVarName',
      title: '迭代变量名',
      setter: 'StringSetter',
      defaultValue: 'item',
      supportVariable: false,
    },
    {
      name: 'loopIndexName',
      title: '索引变量名',
      setter: 'StringSetter',
      defaultValue: 'index',
      supportVariable: false,
    },
    {
      type: 'group',
      title: '插槽',
      items: [
        {
          name: 'empty',
          title: '空数据',
          setter: 'SlotSetter',
          supportVariable: false,
        },
      ],
    },
  ],
  snippets: [
    {
      title: '循环',
      screenshot: loop1,
      schema: {
        componentName: 'LCLoop',
        props: {
          options: [
            { name: '张三', age: 18 },
            { name: '李四', age: 20 },
          ],
        },
        children: [
          {
            componentName: 'LCRow',
            props: {},
            children: [
              {
                componentName: 'LCText',
                props: {
                  text: {
                    type: 'JSExpression',
                    value: '`姓名：${this.scope.item.name}`',
                  },
                },
              },
            ],
          },
        ],
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
      clickCapture: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
  },
}
