// import row1 from './__screenshots__/row1.png'
import row from './__screenshots__/row.svg'

export default {
  componentName: 'LCRow',
  title: '水平容器',
  category: '容器',
  props: [
    {
      name: 'gap',
      title: { label: 'gap', tip: '间隔' },
      setter: 'NumberSetter',
      defaultValue: 10,
    },
    {
      name: 'wrap',
      title: '换行',
      setter: 'BoolSetter',
    },
    {
      name: 'basis',
      title: { label: 'basis', tip: '主轴尺寸' },
      setter: 'StringSetter',
    },
    {
      name: 'width',
      title: { label: '宽度', tip: '不设置为自动' },
      setter: 'StringSetter',
    },
  ],
  snippets: [
    {
      title: '水平容器',
      screenshot: row,

      schema: {
        componentName: 'LCRow',
        props: {
          gap: 10,
        },
        children: [
          {
            componentName: 'div',
            props: {
              gap: 10,
            },
            children: [],
          },
          {
            componentName: 'div',
            props: {
              gap: 10,
            },
            children: [],
          },
        ],
      },
      prePreviewSchema: {
        componentName: 'LCRow',
        props: { gap: 10 },
        children: [
          {
            componentName: 'LCFlex',
            props: { gap: 10 },
            children: [
              {
                componentName: 'LCText',
                props: { text: '这是一串文本' },
                hidden: false,
                id: '28313642',
                ref: 'LCText_2315',
              },
            ],
            hidden: false,
            id: '56462432',
            ref: 'LCFlex_26a6',
          },
          {
            componentName: 'LCFlex',
            props: {},
            children: [
              {
                componentName: 'LCText',
                props: { text: '这是一串文本' },
                hidden: false,
                id: '4d245564',
                ref: 'LCText_248b',
              },
            ],
            id: '1276d662',
            ref: 'LCFlex_1326',
          },
        ],
        hidden: false,
        ref: 'LCFlex_26a6',
        id: '55565259',
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
      clickCapture: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
  },
}
