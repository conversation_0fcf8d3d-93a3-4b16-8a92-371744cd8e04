// import col1 from './__screenshots__/col1.png'
import col from './__screenshots__/column.svg'

export default {
  componentName: 'LCCol',
  title: '垂直容器',
  category: '容器',
  props: [
    {
      name: 'gap',
      title: { label: '间隔', tip: 'gap, 设置为0时，不显示间隔' },
      setter: 'NumberSetter',
      defaultValue: 10,
    },
    {
      name: 'height',
      title: { label: '高度', tip: '不设置为自动' },
      setter: 'StringSetter',
    },
    {
      name: 'basis',
      title: { label: 'basis', tip: '主轴尺寸' },
      setter: 'StringSetter',
    },
  ],
  snippets: [
    {
      title: '垂直容器',
      screenshot: col,
      schema: {
        componentName: 'LCCol',
        props: {
          gap: 10,
        },
        children: [
          {
            componentName: 'div',
            props: {},
            children: [],
          },
          {
            componentName: 'div',
            props: {},
            children: [],
          },
        ],
      },
      prePreviewSchema: {
        componentName: 'LCCol',
        props: { gap: 10 },
        children: [
          {
            componentName: 'div',
            props: {},
            children: [
              {
                componentName: 'LCText',
                props: { text: '这是一串文本' },
                hidden: false,
                id: '85245763',
                ref: 'LCText_2f55',
              },
            ],
            id: '363451b3',
            ref: 'Div_4632',
          },
          {
            componentName: 'div',
            props: {},
            children: [
              {
                componentName: 'LCText',
                props: { text: '这是一串文本' },
                hidden: false,
                id: '55444521',
                ref: 'LCText_14bf',
              },
            ],
            id: '539c646f',
            ref: 'Div_552f',
          },
        ],
        hidden: false,
        id: '15651222',
        ref: 'LCCol_3321',
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
      clickCapture: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
  },
}
