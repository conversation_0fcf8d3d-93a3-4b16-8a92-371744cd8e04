import div1 from './__screenshots__/box.svg'
export default {
  componentName: 'div',
  title: 'div',
  category: '容器',
  props: [
    {
      title: 'tag',
      name: 'tag',
      setter: 'StringSetter',
      defaultValue: 'div',
    },
  ],
  snippets: [
    {
      title: '容器',
      screenshot: div1,
      prePreview: false,
      schema: {
        componentName: 'div',
        props: {
          tag: 'div',
        },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
      clickCapture: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
  },
}
