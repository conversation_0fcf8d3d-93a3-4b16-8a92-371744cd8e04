import custom1 from './__screenshots__/custom.svg'

export default {
  componentName: 'LCCustom',
  title: '动态组件',
  category: '基础',
  props: [
    {
      name: 'name',
      title: { label: 'slot-name', tip: '插槽名称，唯一。' },
      setter: 'StringSetter',
    },
  ],
  snippets: [
    {
      title: '动态组件',
      screenshot: custom1,
      schema: {
        componentName: 'LCCustom',
        props: {
          name: 'slot',
        },
        children: [],
      },
      prePreview: false,
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: false,
      supportBindState: false,
    },
  },
}
