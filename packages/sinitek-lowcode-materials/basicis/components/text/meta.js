import text from './__screenshots__/text.svg'

export default {
  componentName: 'LCText',
  title: '文本',
  category: '基础',
  componentType: 'TEXT',
  formContext: 'SUBMIT',
  treeKey: 'text',
  props: [
    {
      name: 'type',
      title: '文本格式',
      defaultValue: 'text',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            { label: '普通文本', value: 'text' },
            { label: '富文本（没有格式）', value: 'richText' },
            { label: '富文本（有格式）', value: 'richText2' },
            { label: '支持换行符号', value: 'wrap' },
          ],
        },
      },
    },
    {
      name: 'shortStyle',
      title: '快捷样式',
      defaultValue: 'lc-font--normal',
      setter: {
        componentName: 'SelectStyleSetter',
        props: {
          options: [
            {
              label: '正文',
              value: 'lc-font--normal',
            },
            { label: '菜单', value: 'lc-font--menu' },
            { label: '标题', value: 'lc-font--title' },
            { label: '内容', value: 'lc-font--content' },
            { label: '超链接', value: 'lc-font--link' },
          ],
        },
      },
      condition(props) {
        return props.type === 'text' || props.type === void 0
      },
    },
    {
      name: 'text',
      title: '文本',
      setter: 'TextareaSetter',
      condition(props) {
        return props.type !== 'richText' || props.type === void 0
      },
    },
    {
      name: 'text',
      title: '文本',
      setter: 'RichTextSetter',
      condition(props) {
        return props.type === 'richText' || props.type === 'richText2'
      },
    },
    {
      name: 'overflowTooltip',
      title: {
        label: '溢出显示提示',
        tip: '当文本内容超出容器宽度时，是否显示提示',
      },
      defaultValue: false,
      setter: 'BoolSetter',
    },
    // {
    //   name: 'mockText',
    //   title: {
    //     label: '假数据',
    //     tip: '用于编辑区中显示假数据，运行时将显示实际文本内容',
    //   },
    //   setter: 'TextareaSetter',
    // },
  ],
  snippets: [
    {
      title: '文本',
      screenshot: text,
      schema: {
        componentName: 'LCText',
        props: {
          text: '这是一串文本',
          shortStyle: 'lc-font--normal',
        },
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
  },
}
