import flex from './__screenshots__/flex.svg'

export default {
  componentName: 'LCFlex',
  title: 'Flex容器',
  category: '容器',
  props: [
    {
      name: 'flexJustify',
      title: '水平对齐',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '左',
              value: 'left',
            },
            {
              title: '中',
              value: 'center',
            },
            {
              title: '右',
              value: 'right',
            },
            {
              title: '等分',
              value: 'round',
            },
            {
              title: '两端',
              value: 'between',
            },
            {
              title: '均分',
              value: 'evenly',
            },
          ],
        },
      },
      defaultValue: 'left',
    },
    {
      name: 'flexAlign',
      title: '垂直对齐',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              title: '上',
              value: 'top',
            },
            {
              title: '中',
              value: 'center',
            },
            {
              title: '下',
              value: 'bottom',
            },
            {
              title: '填满',
              value: 'stretch',
            },
            {
              title: '基线',
              value: 'baseline',
            },
          ],
        },
      },
      defaultValue: 'stretch',
    },
    {
      name: 'gap',
      title: { label: '间隔', tip: 'gap, 设置为0时，不显示间隔' },
      defaultValue: 10,
      setter: {
        componentName: 'NumberSetter',
        props: {
          min: 0,
        },
      },
    },
    {
      name: 'basis',
      title: {
        label: '主轴尺寸',
        tip: '可以设置像素值和百分比，和其他有效值。',
      },
      defaultValue: 'auto',
      setter: 'StringSetter',
    },
    {
      name: 'height',
      title: '高度',
      setter: 'StringSetter',
      condition(props, target) {
        return !!target.findParent('LCRow')
      },
    },
    // {
    //   name: 'grow',
    //   title: {
    //     label: '增长',
    //     tip: 'grow, 设置为0时，不增长。数值越大，占比越多',
    //   },
    //   defaultValue: 1,
    //   setter: {
    //     componentName: 'NumberSetter',
    //     props: {
    //       min: 0,
    //     },
    //   },
    //   condition(props) {
    //     return !props.basis || props.basis === 'auto'
    //   },
    // },
    // {
    //   name: 'shrink',
    //   title: {
    //     label: '收缩',
    //     tip: 'shrink, 设置为0时，不收缩。数值越大，占比越小',
    //   },
    //   defaultValue: 1,
    //   setter: {
    //     componentName: 'NumberSetter',
    //     props: {
    //       min: 0,
    //     },
    //   },
    // },
  ],
  snippets: [
    {
      title: 'Flex容器',
      screenshot: flex,
      schema: {
        componentName: 'LCFlex',
        props: {
          gap: 10,
        },
        children: [],
      },
      prePreviewSchema: {
        componentName: 'LCRow',
        props: { gap: 10 },
        children: [
          {
            componentName: 'LCFlex',
            props: { gap: 10 },
            children: [
              {
                componentName: 'LCText',
                props: { text: '这是一串文本' },
                hidden: false,
                id: '28313642',
                ref: 'LCText_2315',
              },
            ],
            hidden: false,
            id: '56462432',
            ref: 'LCFlex_26a6',
          },
          {
            componentName: 'LCFlex',
            props: {},
            children: [
              {
                componentName: 'LCText',
                props: { text: '这是一串文本' },
                hidden: false,
                id: '4d245564',
                ref: 'LCText_248b',
              },
            ],
            id: '1276d662',
            ref: 'LCFlex_1326',
          },
        ],
        hidden: false,
        ref: 'LCFlex_26a6',
        id: '55565259',
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
      clickCapture: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
  },
}
