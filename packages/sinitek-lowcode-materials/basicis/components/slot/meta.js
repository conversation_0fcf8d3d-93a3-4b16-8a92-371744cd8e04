import slot1 from './__screenshots__/slot.svg'
export default {
  componentName: 'Slot',
  title: 'Slot',
  category: '容器',
  props: [
    {
      name: 'name',
      title: 'name',
      setter: 'StringSetter',
    },
  ],
  snippets: [
    {
      title: 'Slot',
      screenshot: slot1,
      prePreview: false,
      schema: {
        componentName: 'Slot',
        props: {
          name: 'default',
        },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
      disableBehaviors: ['*'],
      clickCapture: false,
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
  },
}
