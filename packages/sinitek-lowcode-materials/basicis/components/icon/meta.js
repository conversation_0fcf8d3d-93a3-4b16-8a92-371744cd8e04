import icon1 from './__screenshots__/icon.svg'

export default {
  componentName: 'LCIcon',
  title: '图标',
  category: '基础',
  props: [
    {
      name: 'icon',
      title: { label: '图标', tip: '使用https://icon-sets.iconify.design/' },
      setter: {
        componentName: 'IconSetter',
        props: {
          type: 'iconify',
        },
      },
    },
    {
      name: 'width',
      title: 'width',
      setter: 'NumberSetter',
    },
    {
      name: 'height',
      title: 'height',
      setter: 'NumberSetter',
    },
    {
      name: 'color',
      title: 'color',
      setter: 'ColorSetter',
    },
    {
      name: 'inline',
      title: 'inline',
      setter: 'BoolSetter',
    },
    {
      name: 'horizontalFlip',
      title: '水平翻转',
      setter: 'BoolSetter',
    },
    {
      name: 'verticalFlip',
      title: '垂直翻转',
      setter: 'BoolSetter',
    },
    {
      name: 'rotate',
      title: '旋转',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              label: '0°',
              value: '0',
            },
            {
              label: '90°',
              value: '1',
            },
            {
              label: '180°',
              value: '2',
            },
            {
              label: '270°',
              value: '3',
            },
          ],
        },
      },
    },
  ],
  snippets: [
    {
      title: '图标',
      screenshot: icon1,
      schema: {
        componentName: 'LCIcon',
        props: {
          icon: 'uil:icons',
          height: 24,
          width: 24,
        },
      },
    },
  ],
  configure: {
    component: {
      isContainer: false,
      useWrap: true,
      wrapType: 'inline',
    },
    supports: {
      // 支持条件设置
      condition: true,
      // 将现在的宽高和自定义样式集合到一起控制。
      style: true,
      supportBindState: false,
    },
  },
}
