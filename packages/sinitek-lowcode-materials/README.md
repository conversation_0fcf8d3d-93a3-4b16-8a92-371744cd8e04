# `sinitek-lowcode-materials`

物料文件定义说明

## 目录结构

以element-ui为例子

├─element-ui element-ui库vue@2版本UI库
│ └─index.js 导出组件
│ └─components 组件文件夹
│&nbsp;&nbsp;&nbsp;&nbsp;├─button 组件
│&nbsp;&nbsp;&nbsp;&nbsp;│&nbsp;&nbsp;└─\_\_screenshots\_\_ 截图文件夹
│&nbsp;&nbsp;&nbsp;&nbsp;│&nbsp;&nbsp;└─ meta.js 组件描述
│&nbsp;&nbsp;&nbsp;&nbsp;│&nbsp;&nbsp;└─ snippets.js 组件片段

## 属性定义说明

下面是基础的属性定义
| 属性 | 说明 | 类型 | 必填 |
| ------------- | -------------------------------------------------------------- | ------------- | ------ |
| componentName | 组件名称，对应 vue 里注册的组件名称**唯一** | String | `true` |
| title | 标题 | String | `true` |
| lowcodeComponent | 低代码组件 | Boolean | `false` |
| category | 在组件库中的分类 | String | `true` |
| props | 在属性面板中可以设置的属性 | Array | `true` |
| childConfig | 子元素配置，在属性面板中可以设置的属性 | Object\|Array | `true` |
| configure | 组件的一些交互配置 | Object | `true` |
| snippets | 在组件库中可以拖拽的元素，不设置当前组件不会在组件库中出现 | Array | `true` |
| componentType | 组件类型 | string | `true` |
| formContext | 表单上下文。SUBMIT-提交，LIST-表格， DETAIL-详情， SEARCH-搜索 | String | `true` |
| priority | 在物料面板里显示的顺序，数值越大越靠前 | Number | 1 |
| tips | 组件 tips 可以关闭。 | string | false |
| parentWhiteList | 限制组件拖入时的父级组件。 | String[] | false |
| scope | 组件的scope，用于在表达式中获取插槽的属性 | Array | false |
| treeKey | 在大纲树中展示对应的props的key | String|String[] | false |

### scope 说明

scope 是组件的scope，用于在表达式中获取插槽的属性。
| 属性            | 说明                                                                                               | 类型                | 必填    |
| --------------- | -------------------------------------------------------------------------------------------------- | ------------------- | ------- |
| name            | 属性名称                                                                      | String              | `true`  |
| detail | 属性描述                                                                      | String              | `true`  |

### props 说明

| 属性            | 说明                                                                                               | 类型                | 必填    |
| --------------- | -------------------------------------------------------------------------------------------------- | ------------------- | ------- |
| name            | 对应组件的props属性键**唯一**                                                                      | String              | `true`  |
| title           | 属性面板中展示的文本                                                                               | String\|{label,tip} | `true`  |
| setter          | 属性面板输入组件[setter文档](../sinitek-lowcode-simulator/src/settings/props/src/setter/README.md) | String\|Object      | `true`  |
| supportVariable | 绑定变量，默认true                                                                                 | Boolean             | `false` |
| condition       | 是否展示                                                                                           | (props, target) => boolean            | `false` |
| propType        | 属性类型，用于数据转换，目前只支持number                                                                             | String              | `false` |
| extraProps      | 额外属性，用于设置组件的属性                                                                         | Object              | `false` |
| extraProps.setValue      | setter 内容修改时调用，开发者可在该函数内部修改节点 schema 或者进行其他操作	                                                                         | (target, value: any, childTarget?) => void              | `false` |
| documentUrl | 文档地址,目前只用在`JSONSetter`里 |String | `false` |

```js
// props 当前的属性
condition(props, target) {
  return props.type === 'primary'
}
```

#### target 属性说明

1. props 当前选中物料的属性
2. componentName 当前选中物料的组件名称
3. configure 当前选中物料的组件配置
4. parent 当前选中的父级
5. getPropValue: propsName=>any 获取属性
6. getParentPropValue: propsName=>any 获取父级属性
7. setPropValue: (propName, value)=> void 设置当前组件属性
8. setParentPropValue: (propName, value)=> void 设置当前组件父级的属性
9. findParent: componentName => null | component 找到指定父级组件
10. root: 当前的页面schema数据
11. log: (...args) => void 打印日志

### overrideProps 说明

根据name查找默认的属性，进行属性覆盖。
| 属性 | 说明 | 类型 | 必填 |
| --------------- | -------------------------------------------------------------------------------------------------- | ------------------- | ------- |
| name | 对应组件的props属性键**唯一** | String | `true` |
| title | 属性面板中展示的文本 | String\|{label,tip} | `true` |
| setter | 属性面板输入组件[setter文档](../sinitek-lowcode-simulator/src/settings/props/src/setter/README.md) | String\|Object | `true` |
| supportVariable | 绑定变量，默认true | Boolean | `false` |
| condition | 是否展示 | Function | `false` |

### childConfig 子元素配置

比如`element-ui`的`el-table`它的子元素是`el-table-column`, `el-table-column`无法拖入的情况，又需要设置子元素的配置，就使用改参数。
| 属性 | 说明 | 类型 | 必填 |
| ------------- | ----------------- | ------------------ | ------ |
| componentName | 对应组件名称 | String | `true` |
| title | 标题 | Sting\|{label,tip} | `true` |
| setter | 必须是ArraySetter | Object | `true` |

#### 例子

```js
childConfig: {
    componentName: 'ElTableColumn',
    setter: {
      componentName: 'ArraySetter',
      props: {
        itemSetter: {
          componentName: 'ObjectSetter',
          props: {
            config: {
              // 如果子组件有多种或者不想设置items，可以使用auto:true自动从物料库获取
              auto: true,
              items: [
                {
                  name: 'prop',
                  title: 'prop',
                  setter: 'StringSetter',
                },
                {
                  name: 'label',
                  title: 'label',
                  setter: 'StringSetter',
                },
              ],
            },
          },
          initialValue: {
            componentName: 'ElTableColumn',
            props: {
              prop: 'prop',
              label: 'label',
            },
            children: [],
          },
        },
      },
    },
  },
```

```js
childConfig: [
    {
      componentName: 'ElRadio',
      title: '单选框选项',
      setter: {
        componentName: 'ArraySetter',
        props: {
          itemSetter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: radio.props,
              },
            },
            initialValue: {
              componentName: 'ElRadio',
              props: {
                label: '单选框文本',
              },
              children: [],
            },
          },
        },
      },
    },
    {
      componentName: 'ElRadioButton',
      title: '单选框按钮选项',
      setter: {
        componentName: 'ArraySetter',
        props: {
          itemSetter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: radioButtonMeta.props,
              },
            },
            initialValue: {
              componentName: 'ElRadioButton',
              props: {
                label: '单选框文本',
              },
              children: [],
            },
          },
        },
      },
    },
  ],
```

### snippets 说明

| 属性       | 说明                                                          | 类型             | 必填   |
| ---------- | ------------------------------------------------------------- | ---------------- | ------ |
| name       | 对应组件的props属性键**唯一**                                 | String           | `true` |
| title      | 在组件库中的名称                                              | String           | `true` |
| screenshot | 截图                                                          |                  | `true` |
| schema     | json数据                                                      | Object\|Function | `true` |
| pageData   | 会合并到当前页面数据中，主要用来处理state,和methods的功能合并 | Function         | `true` |

schema传递函数，可以给组件添加一些数据绑定的逻辑

```js
schema: (id) => {
  return {
    componentName: 'LCText',
    props: {
      text: {
        type: 'JSExpression',
        value: `this.state.text_${id}`
      }
    }
  }
},
pageData: (id, target) => {
  return {
    state: {
      [`text_${id}`]: 'test'
    }
  }
}
```

#### schema 结构

| 属性          | 说明           | 类型   | 必填    |
| ------------- | -------------- | ------ | ------- |
| componentName | 对应的组件名称 | String | `true`  |
| props         | 属性对象       | Object | `true`  |
| children      | 子组件列表     | Array  | `false` |

#### 例子

```js
import btn1 from './__screenshots__/button-1.png'
snippets: [
  {
    title: '主按钮',
    screenshot: btn1,
    schema: {
      componentName: 'ElButton',
      props: {
        type: 'primary',
      },
      children: [
        {
          componentName: 'LCText',
          props: {
            text: '主按钮',
          },
        },
      ],
    },
  },
]
```

### configure 说明

| 字段      | 字段描述               | 字段类型 | 备注                                                                                                                     |
| --------- | ---------------------- | -------- | ------------------------------------------------------------------------------------------------------------------------ |
| supports  | 通用扩展配置能力支持性 | Object   | 用于通用扩展面板能力描述                                                                                                 |
| component | 组件能力配置           | Object   | 与组件相关的能力、约束、行为等描述，有些信息可从组件视图实例上直接获取                                                   |
| advanced  | 高级配置               | Object   | 用户可以在这些配置通过引擎上下文控制组件在设计器中的表现，例如自动初始化组件的子组件、截获组件的操作事件进行个性化处理等 |
| design | 针对设计区域的设置| Object |扩展设计区域能力

#### supports

| 属性                           | 说明                                  | 类型    | 必填    |
| ------------------------------ | ------------------------------------- | ------- | ------- |
| condition                      | 支持展示条件设置                      | Boolean | `true`  |
| style                          | 支持样式设置                          | Boolean | `true`  |
| supportBindState               | 设置为false，则不支持绑定状态管理的值 | Boolean | `false` |
| events                         | 事件                                  | Array   | `false` |
| events[0].name                 | 事件名称                              | String  | `true`  |
| events[0].description          | 事件描述                              | String  | `true`  |
| events[0].template             | 事件模板                              | String  | `true`  |
| methods                        | 可调用方法，主要用于js提示            | Array   | `false` |
| methods[0].name                | 方法名称                              | String  | `true`  |
| methods[0].description         | 方法描述                              | String  | `true`  |
| methods[0].example             | 方法例子                              | String  | `false` |
| methods[0].type                | 不是函数时设置，例如'string'|'number'                              | String  | `false` |
| methods[0].returnType          | 方法返回值                            | String  | `false` |
| methods[0].args[0].name        | 参数名称                              | String  | `false` |
| methods[0].args[0].description | 参数描述                              | String  | `false` |
| methods[0].args[0].type        | 参数类型                              | String  | `false` |
| methods[0].args[0].required    | 参数是否必填                              | Boolean  | `false` |
| states[0].name                 | 字段名称                              | String  | `true`  |
| states[0].description          | 字段描述                              | String  | `true`  |
| states[0].example              | 字段例子                              | String  | `false` |
| states[0].type                 | 字段类型                              | String  | `false` |

```js
{
  events: [
    {
      name: 'input',
      description: '绑定值变化时触发的事件',
      template: 'function input(label) { console.log(label);}',
    },
  ],
  methods: [
    {
      name: 'input',
      description: '绑定值变化时触发的事件',
      args: [
        {
        name: 'getX',
        description:'获取x'
      }
      ],
    },
  ],
  states: [
    {
      name: 'isValid',
      description: '表单是否验证通过',
      example: 'refs.form.isValid',
      type: 'boolean'
    },
  ]
}
```

#### component

| 属性                        | 说明                                                                                        | 类型           | 默认值  |
| --------------------------- | ------------------------------------------------------------------------------------------- | -------------- | ------- |
| isContainer                 | 是否是容器                                                                                  | Boolean        | `true`  |
| isModal                     | 是否是弹框                                                                                  | Boolean        | `false` |
| clickCapture                | 是否需要捕获点击，用在模拟器上。所有组件默认是不能进行鼠标交互的，需要时设置false，例如tabs组件 | Boolean        | `true`  |
| clickCaptureBlackList | 设置了clickCapture为false，会导致组组件内的所有元素都可以交互，单独设置拥有className的元素不可以交互 | Array | -- |
| nestingRule                 | 嵌套规则                                                                                    | Object         | -       |
| nestingRule.childWhitelist  | 子节点组件白名单                                                                            | Array          | -       |
| nestingRule.childBlacklist  | 子节点组件黑名单                                                                            | Array          | -       |
| nestingRule.parentWhitelist | 父节点组件白名单                                                                            | Array          | -       |
| nestingRule.parentBlacklist | 父节点组件黑名单                                                                            | Array          | -       |
| nestingRule.allowInsert     | 返回true可以添加组件                                                                        | Function(node) | -       |
| nestingRule.upRecursiveParent     | 使向上递归查找父级用来匹配parent rule                                                                        | Boolean | -       |

| rootSelector | 组件选中框的cssSelector | String | - |
| modalVisibleProp | 弹框控制隐藏显示的属性 | String | - |
| modelProps | 在模拟器中给弹框添加的属性 | Object | - |
| getAIParams | 获取ai参数 | (state) => {返回ai参数} | -- |
| setAIParams | 设置ai参数 | (multipleSelection) => {返回设置得属性对象} | -- |
| disableBehaviors | 用于屏蔽在设计器中选中组件时提供的操作项，默认操作项有 copy、move、remove | String[] | -- |
| useWrap | 用于适配设计器，当组件使用了inheritAttrs:false，选中框异常的情况。 | Boolean | -- |
| wrapType | 可选值'inline' 'block'，默认是block | String | -- |
| childWrapRenderer | 默认使用renderer组件包裹。子组件可以使用CWRCondition实现控制是否展示 | Boolean | -- |
| placeholder | 当组件拖入时没有宽高，导致组件无法选中时使用 | Object | -- |
| placeholder.width | 宽度 | String | -- |
| placeholder.height | 高度 | String | -- |
| placeholder.text | 文本 | String | -- |

##### getAIParams的state说明

- current 当前选中组件的schema
- parent 当前选中组件父级的schema

##### setAIParams

```js
setAIParams: (multipleSelection) => {
  const result = {
    props: {
      itemProps: {},
      fieldProps: {},
    },
  }
  const props = {}
  multipleSelection.forEach((e) => {
    props[e.code] = e.value
  })
  result.props.fieldProps = Object.assign({}, props)
  result.props.itemProps = Object.assign({}, props)
  return result
},
```

### advanced 高级功能配置

| 字段 | 用途 | 类型 | 备注
|
| --------------------------- | ------------------------------------------------------------------------------------------- | -------------- | ------- |
| callbacks | 配置 callbacks 可捕获引擎抛出的一些事件，例如 onNodeAdd | Callback | - |
| callbacks.onNodeAdd | 在容器中拖入组件时触发的事件回调 | Function | (node, target) => node |
| callbacks.onNodeRemove | 在容器删除组件时触发的事件回调 | Function | (node, target) => node |

### design
| 字段 | 用途 | 类型 | 备注
|
| --------------------------- | ------------------------------------------------------------------------------------------- | -------------- | ------- |
| excludeProps | 设计时，不应用props到设计区域 | String[] | - |
| excludeAttrs | 设计时，不应用attrs到设计区域 | String[] | - |

## 设计器自定义物料添加

以xn-button组件为例：

**新增物料文件夹**

打开sinitek-lowcode-materials/component文件夹，新建一个button文件夹：
![loadingag1109](./pics/47.png)

在button文件夹里新建一个screenshots文件夹和meta.js文件，screenshots文件夹用来存放物料按钮图标图片：
![loadingag1111](./pics/48.png)

在screenshots文件夹里放入button图标的缩略图，缩略图的名称要和组件文件名一致。
![loadingag1115](./pics/50.png)

打开meta.js文件，引入缩略图：
![loadingag1115](./pics/49.png)

**导出物料**

收起component文件夹，点击index.js文件，引入button并导出：
![151](./pics/151.png)

**配置物料meta文件**

在export default中配置以下属性：
![150](./pics/150.png)

设置snippets属性如图。title为组件在物料导入面板显示的名称，screenshots为组件的缩略图，schema为组件导入模拟器后默认显示的页面结构。
![152](./pics/152.png)

运行项目，在sinitek ui中可以看到刚添加的button物料：
![153](./pics/153.png)

打开sinitek ui文档，找到button组件的属性，开始配置props。
![154](./pics/154.png)

打开模拟器，此时右侧物料配置栏已经出现了按钮类型属性。
![155](./pics/155.png)

以第一个属性为例，依次导入文档中所有的属性。若属性的值为字符串，则使用stringSetter，值为数字，则使用numberSetter，若值为布尔值，则使用boolSetter，值为自定义数组/对象，则使用JSONSetter。
![156](./pics/156.png)
![157](./pics/157.png)
