import defineConfig from '../../config/rspack/base.mjs'
import path from 'path'
export default function () {
  return defineConfig({
    context: path.resolve(),
    unoConfigPath: path.resolve('uno.config.js'),
    library: {
      type: 'umd',
      name: 'sinitek-lowcode-render',
      entry: './index.js',
    },
    css: {
      extract: {
        filename: '[name].css',
      },
    },
    assetsDir: 'static', // 静态资源目录名称
    productionSourceMap: process.env.NODE_ENV === 'development', // 去掉打包的时候生成的map文件
    configureWebpack: {
      // entry: {
      //   example: './example/main.js',
      // },
      resolve: {
        alias: {
          'sinitek-lowcode-render': path.resolve('./index.js'),
        },
      },
      externals: [
        {
          vue: 'vue',
          'core-js': 'core-js',
          lodash: 'lodash',
          'element-ui': 'element-ui',
          'monaco-editor': 'monaco-editor',
          'sinitek-ui': 'sinitek-ui',
          '@iconify/vue2': '@iconify/vue2',
          sirmapp: 'sirmapp',
          'sinitek-lowcode-materials': 'sinitek-lowcode-materials',
          'sinitek-lowcode-shared': 'sinitek-lowcode-shared',
          echarts: 'echarts',
        },
        /^lodash\//,
      ],
    },
  })
}
