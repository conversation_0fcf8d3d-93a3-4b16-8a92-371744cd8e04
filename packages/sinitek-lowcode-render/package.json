{"name": "sinitek-lowcode-render", "version": "1.0.0-SNAPSHOT.135", "description": "> TODO: description", "author": "wei.peng <<EMAIL>>", "homepage": "", "license": "ISC", "main": "dist/sinitek-lowcode-render.umd.min.js", "private": false, "files": ["dist"], "directories": {"test": "__tests__"}, "repository": {"url": "http://*************:8088/repository/sinitek-npm/"}, "scripts": {"dev": "vue-cli-service serve", "build": "rspack build", "build2": "vue-cli-service build --target lib --formats umd-min --name sinitek-lowcode-render --dest dist ./index.js", "lint": "eslint . --fix", "format": "prettier --write \"{src,example}/**/*.{vue,js,jsx}\""}, "devDependencies": {"@babel/core": "catalog:", "@babel/eslint-parser": "catalog:", "@iconify/vue2": "catalog:", "@unocss/webpack": "catalog:", "element-ui": "catalog:", "eslint": "catalog:", "eslint-config-prettier": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-vue": "catalog:", "lint-staged": "catalog:", "prettier": "catalog:", "unocss": "catalog:", "vue": "catalog:", "vue-template-compiler": "catalog:"}, "volta": {"node": "18.0.0"}, "peerDependencies": {"@iconify/vue2": "catalog:", "core-js": "catalog:", "element-ui": "catalog:", "sinitek-lowcode-shared": "workspace:^", "vue": "catalog:"}, "gitHooks": {"pre-commit": "lint-staged"}, "dependencies": {"mitt": "^3.0.1", "sinitek-lowcode-flow-execute": "workspace:^"}}