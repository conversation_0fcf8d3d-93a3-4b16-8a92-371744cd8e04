import Placeholder from './placeholder'
import { createComponent } from '../shared/createComponent'
export default createComponent({
  name: 'LCSlot',
  components: {
    Placeholder,
  },
  props: {
    name: {
      type: String,
      default: '',
    },
  },
  render() {
    return (
      // 这个div是为了保证设计时和运行时一致
      // 因为有的组件使用inheritAttrs: false，导致设计时无法选中需要包裹一层
      // 如果运行时去掉了div可能和设计时不一致。
      <div>
        {this.slots('default') ? this.slots('default') : <Placeholder />}
      </div>
    )
  },
})
