import { createComponent } from '../shared/createComponent'
import {
  DatasourceType,
  DesignMode,
  getCtxUtils,
  isDesignMode,
  addPageCtx,
} from 'sinitek-lowcode-shared'

import {
  setDatasource,
  setDatasourceByModel,
  setDatasourceByEnum,
} from '../shared'
import {
  parseJSFunction,
  parseData,
  getCtxMaterial,
} from 'sinitek-lowcode-shared'
import { exec } from 'sinitek-lowcode-flow-execute'
import cloneDeep from 'lodash/cloneDeep'

function callLifecycle(mode, lifecycle, name) {
  if (isDesignMode(mode)) return
  if (lifecycle && lifecycle[name]) {
    lifecycle[name]()
  }
}

const autLoadTask = new Set()

const runTask = function () {
  if (autLoadTask.size === 0) return
  Promise.resolve().then(() => {
    Promise.allSettled([...autLoadTask].map((e) => e())).then(() => {
      this.callLifecycle('datasourceLoaded')
    })
    autLoadTask.clear()
  })
}

// 将页面状态限制page里，外部无法访问，保持独立
// 多个page嵌套互不影响
export default createComponent({
  name: 'Page',
  inject: {
    fetcher: { default: null },
    mode: { default: DesignMode.DESIGN },
    root: { default: null },
    // 低代码组件传递的
    lowcodeProps: { default: {} },
    lowcodeEmit: { default: null },
    lowcodeScope: { default: {} },
    isLCComponent: { default: false },
  },
  provide() {
    return {
      setRef: this.setRef,
      pageScope: this._getScope,
    }
  },
  data() {
    return {
      datasource: {},
      dsFetcher: {},
      refs: {},
      state: {},
      methods: {},
      showParentList: false,
      parentList: [],
      popupDir: 'top',
    }
  },
  created() {
    this.state = this.$parent.schema.state
    this.cloneState = cloneDeep(this.state)
    this.methods = this.$parent.schema.methods
    if (this.isLCComponent) {
      if (this.lowcodeScope.componentName) {
        const props =
          getCtxMaterial(this.lowcodeScope.componentName)?.props?.filter?.(
            (e) => e.valueChange
          ) ?? []
        props.forEach((e) => {
          this.$watch(`lowcodeProps.${e.name}`, (...args) => {
            const fn = parseJSFunction(e.valueChange, this._getScope())
            fn(...args)
          })
        })
      }
    }

    this.initDataSource()
    this.callLifecycle('created')
  },
  mounted() {
    this.callLifecycle('mounted')
  },
  activated() {
    this.callLifecycle('activated')
  },
  deactivated() {
    this.callLifecycle('activated')
  },
  beforeDestroy() {
    this.callLifecycle('beforeDestroy')
  },
  destroyed() {
    this.callLifecycle('destroyed')
  },
  methods: {
    initDataSource() {
      if (Array.isArray(this.$parent.schema?.datasource)) {
        this.$parent.schema.datasource.forEach((e) => {
          this.setDatasource(e)
        })
      }
      // 主模型的处理
      if (
        this.$parent.schema?.mainModel &&
        this.$parent.schema.datasource.every((e) => e.id !== 'main')
      ) {
        this.setDatasource({
          // 保留main关键字，其他数据源不能定义main
          id: 'main',
          type: DatasourceType.MODEL,
          condition: this.$parent.schema.mainModel.condition,
          modelCode: this.$parent.schema.mainModel.modelCode,
          auto: true,
        })
      }
    },
    setDatasource(e) {
      const fetcher = this.fetcher || window.parent.__LCConfig__?.fetcher || {}
      let http = fetcher.fetcher
      let _setDatasource = setDatasource
      if (e.type === DatasourceType.MODEL) {
        http = fetcher.modelFetchers
        _setDatasource = setDatasourceByModel
      } else if (e.type === DatasourceType.ENUM) {
        http = fetcher.getEnum
        _setDatasource = setDatasourceByEnum
      }
      if (!http) {
        throw Error('sinitek-lowcode-render fetcher is required')
      }
      const scope = this._getScope()
      const ds = _setDatasource(e, http, scope, (res) => {
        this.datasource[e.id] = res
      })
      this.$set(this.dsFetcher, e.id, ds)
      this.$set(this.datasource, e.id, e.type === DatasourceType.ENUM ? [] : {})
      if (e.auto && ds.auto) {
        // auto调用一次后会被清除，后面只能调用send
        autLoadTask.add(ds.auto)
        runTask.bind(this)()
      }
    },
    setRef(key, ref) {
      this.refs[key] = ref
    },
    _getScope() {
      const scope = {
        // TODO: methods在使用时才处理，保持统一。
        methods: this.methods,
        state: this.state,
        cloneState: this.cloneState,
        datasource: this.datasource,
        refs: this.refs,
        fetcher: this.dsFetcher,
        vm: this.root,
        props: this.lowcodeProps,
        emit: this.lowcodeEmit ?? (() => {}),
        doAction: this._userDoAction(),
        _renderDoAction: this._renderDoAction(),
      }
      const utils = Object.entries(getCtxUtils()).reduce(
        (acc, [key, value]) => {
          acc[key] = value.bind({
            ...scope,
            ...getCtxUtils(),
          })
          return acc
        },
        {}
      )
      return {
        ...scope,
        utils,
      }
    },
    // 给渲染时调用的action的方法
    _renderDoAction() {
      // 绑定this
      return (name) => {
        // render时绑定到对应的event上
        return async (scope) => {
          // 最后事件的触发
          return this.baseDoAction(name, scope)
        }
      }
    },
    baseDoAction(name, componentScope) {
      // 取graphData
      const actions = this.$parent.schema.actions
      if (!actions) {
        throw new Error('没有逻辑编辑的数据')
      }
      if (!actions[name]) {
        throw new Error(`没有找到${name}对应的逻辑编排`)
      }
      const scope = this._getScope()
      return exec({
        context: {
          ...scope,
          scope: componentScope,
          methods: parseData(scope.methods, scope),
        },
        graphData: actions[name],
      })
    },
    // 给用户调用的action的方法
    _userDoAction() {
      // 绑定this
      return async (name) => {
        return this.baseDoAction(name)
      }
    },
    callLifecycle(name) {
      const methods = parseData(this.methods, this._getScope())
      callLifecycle(this.mode, methods, name)
    },
  },
  render() {
    const className = ['lc-page']
    if (!this.slots('default')) {
      className.push('h-full')
    }
    return (
      <div class={className}>
        {this.slots('default') ? (
          this.slots('default')
        ) : isDesignMode(this.mode) ? (
          <div class="drag-placeholder">请从左侧拖拽组件到此处</div>
        ) : null}
      </div>
    )
  },
  watch: {
    '$parent.schema.datasource'() {
      this.initDataSource()
    },
    '$parent.schema.state'(v) {
      this.state = v
    },
    '$parent.schema.methods'(v) {
      this.methods = v
    },
    '$parent.schema': {
      deep: true,
      handler() {
        this.$nextTick(() => {
          // 低代码组件的page不添加pageCtx
          if (this.lowcodeEmit) return
          addPageCtx(this._getScope.bind(this))
        })
      },
      immediate: true,
    },
  },
  computed: {
    computedStyle() {
      let showParentList = this.showParentList
      let parentList = this.parentList
      let style = {
        height: showParentList ? parentList.length * 25 + 'px' : 0,
      }
      if (this.popupDir === 'top' && showParentList) {
        // 上方弹出时整体向上偏移
        style.transform = `translateY(-${(parentList.length - 1) * 24}px)`
      } else {
        style.transform = ''
      }
      return style
    },
  },
})
