import { createComponent } from '../../shared/createComponent'
import Placeholder from '../placeholder'
import { styleToObject } from 'sinitek-lowcode-shared'
// import { justifyMap, alignMap } from './flex'
export default createComponent({
  name: 'LCCol',
  inheritAttrs: false,
  components: {
    Placeholder,
  },
  props: {
    gap: {
      type: Number,
      default: 10,
    },
    width: {
      type: String,
    },
    height: {
      type: String,
    },
    flexAlign: {
      type: String,
      default: 'stretch',
    },
    flexJustify: {
      type: String,
      default: 'left',
    },
    basis: {
      type: String,
      default: '',
    },
  },
  computed: {
    attrsOmitStyle() {
      return Object.keys(this.$attrs).reduce((acc, key) => {
        if (key !== 'style') {
          acc[key] = this.$attrs[key]
        }
        return acc
      }, {})
    },
  },
  render(h) {
    const gap = this.gap + 'px'
    const style = {
      gap,
      flexBasis: this.basis,
      height: this.$attrs.height,
      width: this.$attrs.width,
      // alignItems: alignMap[this.flexAlign],
      // justifyContent: justifyMap[this.flexJustify],
      ...styleToObject(this.$attrs.style),
    }
    if (this.isFlexCol) {
      style.height = this.$attrs.basis
    } else {
      style.width = this.$attrs.basis
    }
    return h(
      'div',
      {
        class:
          'lc-col flex flex-col' +
          (this.$attrs.basis && this.$attrs.basis !== 'auto'
            ? ' flex-col-valid-basis'
            : ''),
        style,
        attrs: {
          ...this.attrsOmitStyle,
        },
      },
      [this.slots('default') ? this.slots('default') : <Placeholder />]
    )
  },
})
