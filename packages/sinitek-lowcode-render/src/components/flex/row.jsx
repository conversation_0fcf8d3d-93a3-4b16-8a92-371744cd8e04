import { createComponent } from '../../shared/createComponent'
import Placeholder from '../placeholder'
import { styleToObject } from 'sinitek-lowcode-shared'
// import { justifyMap, alignMap } from './flex'

export default createComponent({
  name: 'LCRow',
  inheritAttrs: false,
  components: {
    Placeholder,
  },
  props: {
    gap: {
      type: Number,
      default: 10,
    },
    width: {
      type: String,
    },
    height: {
      type: String,
    },
    flexAlign: {
      type: String,
      default: 'stretch',
    },
    flexJustify: {
      type: String,
      default: 'left',
    },
    wrap: Boolean,
    basis: {
      type: String,
      default: '',
    },
  },
  computed: {
    attrsOmitStyle() {
      return Object.keys(this.$attrs).reduce((acc, key) => {
        if (key !== 'style') {
          acc[key] = this.$attrs[key]
        }
        return acc
      }, {})
    },
  },
  render(h) {
    const gap = this.gap + 'px'
    const style = {
      gap,
      flexBasis: this.basis,
      width: this.$attrs.width,
      height: this.$attrs.height,
      ...styleToObject(this.$attrs.style),
    }
    return h(
      'div',
      {
        class:
          'lc-row flex flex-row' +
          (this.$attrs.basis && this.$attrs.basis !== 'auto'
            ? ' flex-row-valid-basis'
            : ''),
        style,
        attrs: {
          ...this.attrsOmitStyle,
        },
      },
      [this.slots('default') ? this.slots('default') : <Placeholder />]
    )
  },
})
