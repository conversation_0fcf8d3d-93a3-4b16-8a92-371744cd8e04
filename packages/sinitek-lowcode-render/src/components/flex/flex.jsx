import { styleToObject } from 'sinitek-lowcode-shared'
import { createComponent } from '../../shared/createComponent'
import Placeholder from '../placeholder'

export const justifyMap = {
  left: 'flex-start',
  center: 'center',
  right: 'flex-end',
  round: 'space-around',
  between: 'space-between',
  evenly: 'space-evenly',
}
export const alignMap = {
  top: 'flex-start',
  center: 'center',
  bottom: 'flex-end',
  stretch: 'stretch',
  baseline: 'baseline',
}
export default createComponent({
  name: 'LCFlex',
  inheritAttrs: false,
  components: {
    Placeholder,
  },
  props: {
    gap: {
      type: Number,
      default: 10,
    },
    flexAlign: {
      type: String,
      default: 'stretch',
    },
    flexJustify: {
      type: String,
      default: 'left',
    },
    grow: {
      type: Number,
      default: 1,
    },
    shrink: {
      type: Number,
      default: 1,
    },
    basis: {
      type: String,
      default: 'auto',
    },
    height: {
      type: String,
      default: 'auto',
    },
  },
  computed: {
    computedStyle() {
      const result = {
        gap: this.gap + 'px',
        alignItems: alignMap[this.flexAlign],
        justifyContent: justifyMap[this.flexJustify],
        height: this.height,
        ...styleToObject(this.$attrs.style),
      }
      if (this.basis !== 'auto') {
        result.flexGrow = 0
        result.flexShrink = 0
        result.flexBasis = this.basis
      }

      return result
    },
    attrsOmitStyle() {
      return Object.keys(this.$attrs).reduce((acc, key) => {
        if (key !== 'style') {
          acc[key] = this.$attrs[key]
        }
        return acc
      }, {})
    },
  },
  render(h) {
    return h(
      'div',
      {
        class: 'lc-flex flex',
        style: this.computedStyle,
        attrs: {
          ...this.attrsOmitStyle,
        },
      },
      [this.slots('default') ? this.slots('default') : <Placeholder />]
    )
  },
})
