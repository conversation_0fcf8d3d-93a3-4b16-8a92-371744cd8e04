import Box from './box'
import Slot from './slot'
import LCFlex from './flex/flex'
import LCRow from './flex/row'
import LCCol from './flex/col'
import LCIcon from './icon'
import LCLoop from './loop'
import LCPage from './page'
import LCText from './text'
import LCCustom from './custom'
import LCPlaceholder from './placeholder'
import { setByPath } from '../shared/setByPath'
import LCWrap from './wrap'
import {
  Design,
  JSExpression,
  DesignMode,
  getCtxComponent,
  getCtxMaterial,
  parseData,
  isDesignMode,
  isJSExpression,
} from 'sinitek-lowcode-shared'
import cloneDeep from 'lodash/cloneDeep'
// let stateKey = 0

const stopEvent = (event) => {
  event.preventDefault?.()
  event.stopPropagation?.()
  return false
}

export const getMaterial = (componentName) => {
  return getCtxMaterial(componentName)
}

const clickCapture = (componentName) => {
  const c = getMaterial(componentName)
  return c?.configure?.component?.clickCapture !== false
}
const childWrapRenderer = (componentName) => {
  const c = getMaterial(componentName)
  return c?.configure?.component?.childWrapRenderer === false
}
const isContainer = (componentName) => {
  const c = getMaterial(componentName)
  return c?.configure?.component?.isContainer === true
}
function getBindProps(schema, scope, mode = DesignMode.DESIGN) {
  if (schema.componentName === 'LCPlaceholder') return {}
  if (!schema || !Object.keys(schema).length) return null
  let extendsAttrs = {}
  // TODO XnButton不传递属性会导致button的type被设置。都是高级组件，不想把所有的属性都写出来，直接继承所有属性
  if (schema.componentName != 'XnButton') {
    extendsAttrs = { ...(schema.props || {}) }
  }
  // 删除title属性，防止被设置后，使浏览器的title生效
  delete extendsAttrs.title
  const bindProps = {
    attrs: {
      style: schema?.style,
      ...extendsAttrs,
    },
    ref: schema.ref,
    props: { ...(schema.props || {}) },
    class: [schema.className ?? ''],
    // 通过修改key来重新渲染组件
    // slot切换时，需要重新渲
    key: schema?.__key__,
  }

  if (isDesignMode(mode)) {
    const c = getMaterial(schema.componentName)
    bindProps.class.push(schema.hidden ? ' lc-hidden' : '')
    bindProps.attrs[Design.NODE_UID] = schema.id
    bindProps.attrs[Design.NODE_TAG] = schema.componentName
    if (!isCondition(schema.condition, scope)) {
      bindProps.attrs['data-visible'] = 'false'
      bindProps.attrs['data-hide-text'] = '<隐藏状态>'
    } else if (schema.trackCode) {
      bindProps.attrs['data-visible'] = 'false'
      bindProps.attrs['data-hide-text'] = '<埋点>'
    } else if (c?.configure?.component?.placeholder) {
      bindProps.attrs['data-visible'] = 'false'

      let style = bindProps.attrs?.style || ''
      if (style && !style.endsWith(';')) {
        style += ';'
      }
      if (c?.configure?.component?.placeholder?.width) {
        style += `;width: ${c?.configure?.component?.placeholder?.width};`
      }
      if (c?.configure?.component?.placeholder?.height) {
        style += `height: ${c?.configure?.component?.placeholder?.height};`
      }
      bindProps.attrs.style = style
      bindProps.attrs['data-hide-text'] =
        `<${c?.configure?.component?.placeholder?.text ?? '占位'}>`
    }

    bindProps.on = {
      mouseover: stopEvent,
      focus: stopEvent,
    }
    bindProps.nativeOn = {
      '!focus': stopEvent,
    }
    // 默认阻止事件点击
    // 如果需要点击穿透，在meta里设置clickCapture为false
    if (
      clickCapture(schema.componentName) ||
      c?.configure?.component?.clickCaptureBlackList
    ) {
      bindProps.nativeOn['!click'] = (event) => {
        // 如果配置了黑名单，则只阻止黑名单的元素进行交互
        // 比如弹框的底部按钮，需要在设计时禁止点击，又要保证内容可以交互
        if (c?.configure?.component?.clickCaptureBlackList) {
          const blackList = c?.configure?.component?.clickCaptureBlackList
          if (
            event
              .composedPath()
              .some((e) => blackList.some((a) => e?.classList?.contains(a)))
          ) {
            return stopEvent(event)
          }
          return
        }
        return stopEvent(event)
      }
    }

    if (!c?.configure?.component?.isModal) {
      bindProps.attrs.draggable = true
    } else {
      bindProps.props[c?.configure?.component?.modalVisibleProp] = true
      if (c?.configure?.component?.modelProps) {
        Object.assign(bindProps.props, c?.configure?.component.modelProps)
      }
    }
    // 处理design配置
    const design = c?.configure?.design
    if (design) {
      // 设计器不应用对应的属性
      if (design.excludeProps) {
        design.excludeProps.forEach((e) => {
          delete bindProps.props[e]
        })
      }
      if (design.excludeAttrs) {
        design.excludeAttrs.forEach((e) => {
          delete bindProps.attrs[e]
        })
      }
    }
  } else {
    // 设置事件
    if (schema.events && Object.keys(schema.events).length) {
      bindProps.on = schema.events
    }
    bindProps.directives = bindProps.directives || []
    if (schema.directiveLoading) {
      bindProps.directives = [
        {
          name: 'loading',
          value: schema.directiveLoading,
        },
      ]
    }
    // 运行时才使用埋点指令
    if (schema.trackCode) {
      bindProps.directives = [
        {
          name: 'has',
          value: schema.trackCode,
        },
      ]
    }
  }

  bindProps.on = bindProps.on || {}
  // 自动绑定值
  if (schema.LCBindState) {
    if (isJSExpression(schema.LCBindState)) {
      bindProps.props.value = schema.LCBindState
    } else if (typeof schema.LCBindState === 'string') {
      bindProps.props.value = {
        type: JSExpression,
        value: `this.state.${schema.LCBindState}`,
      }
    } else {
      bindProps.props.value = {
        type: JSExpression,
        value: `this${schema.LCBindState?.isState !== false ? '.state' : ''}.${schema.LCBindState.value}`,
      }
    }
    bindProps.attrs.value = bindProps.props.value
  }
  const result = parseData(cloneDeep(bindProps), scope, mode)
  if (!isDesignMode(mode)) {
    // 自动绑定值 处理事件
    // TODO: 处理事件要转换后在处理
    if (schema.LCBindState) {
      const _input = result?.on?.input
      result.on = result.on || {}
      result.on.input = (...args) => {
        if (
          typeof schema.LCBindState === 'string' ||
          schema.LCBindState?.isState !== false
        ) {
          // 处理表达式，同时处理
          // this.state.xx
          // state.xx
          setByPath(
            scope.state,
            schema.LCBindState?.value?.replace(/(this\.)?(state\.)?/, '') ??
              schema.LCBindState,
            args?.[0]
          )
        } else {
          setByPath(scope, schema.LCBindState.value, args?.[0])
        }
        if (_input) {
          _input(...args)
        }
      }
    }
    if (
      ['LAForm', 'XnForm'].includes(schema.componentName) &&
      bindProps.attrs.model
    ) {
      // 当调用resetFields时会发送resetModel事件
      result.on['resetModel'] = () => {
        const path = bindProps.attrs.model.value.replace('this.state.', '')
        scope.state[path] = scope.cloneState[path]
      }
    }
  }
  return result
}

const isCondition = (condition, scope) => {
  return typeof condition === 'object'
    ? !!parseData(condition, scope)
    : condition !== false
}

// const isLoop = (schema) => {
//   return !!schema.loop
// }

const Mapper = {
  div: Box,
  slot: Slot,
  Slot,
  LCRow,
  LCCol,
  LCFlex,
  LCIcon,
  LCLoop,
  LCPage,
  LCText,
  Page: LCPage,
  LCCustom,
  LCPlaceholder,
}

const getComponent = (name) => {
  return getCtxComponent(name) || Mapper[name] || name
}

const renderDefault = (h, children, scope, parent) => {
  if (!children || !children.length) return null

  return children.map((schema) => {
    const props = {
      props: {
        schema,
        scope,
        parent,
      },
      key: schema.id,
    }
    if (schema.componentName === 'Slot') {
      props.slot = schema.props.name
    }
    return h(renderer, props)
  })
}
const renderSlot = (h, schema, scope, parent) => {
  const props = {
    props: {
      schema,
      scope,
      parent,
    },
  }
  return h(renderer, props, renderDefault(schema.children, scope, parent))
}

// 有的组件禁用了attrs继承导致选中框错误
// 需要在meta配置里的component.useWrap为true时，使用wrap组件包裹
const wrapRender = (h, schema, props, el) => {
  const c = getMaterial(schema.componentName)
  if (c?.configure?.component?.useWrap) {
    const attrs = props.attrs
    props.attrs = null
    return h(
      LCWrap,
      {
        attrs,
        props: {
          type: c?.configure?.component?.wrapType ?? 'block',
        },
      },
      [el]
    )
  } else {
    return el
  }
}

const injectPlaceHolder = (isContainer, children) => {
  const isEmptyArr = Array.isArray(children) && !children.length

  if (isContainer && (!children || isEmptyArr)) {
    return [
      {
        componentName: 'LCPlaceholder',
      },
    ]
  }

  return children
}

export const getChildren = (h, schema, mergeScope) => {
  // const { componentName } = schema
  let renderChildren =
    schema.componentName !== 'Page'
      ? injectPlaceHolder(isContainer(schema.componentName), schema?.children)
      : schema.children
  if (!renderChildren?.length || schema.componentName === 'LCLoop') return null
  if (Array.isArray(renderChildren) && renderChildren.length) {
    return renderDefault(h, renderChildren, mergeScope, schema)
  } else {
    return parseData(renderChildren, mergeScope)
  }
}

const renderer = {
  name: 'VirtualRenderer',
  props: ['schema', 'scope', 'parent'],
  inject: {
    // render/index.jsx
    mode: { default: DesignMode.DESIGN },
    // render/index.jsx
    emit: { default: () => undefined },
    // page.jsx
    setRef: { default: () => undefined },
    // page.jsx
    pageScope: { default: () => undefined },
  },
  inheritAttrs: false,
  mounted() {
    // 设置ref
    if (this.schema.ref && this.$refs[this.schema.ref]) {
      this?.setRef?.(this.schema.ref, this.$refs[this.schema.ref])
    }
    // 设计时禁用input获取焦点，防止必填触发
    if (
      ['LAFormItem', 'XnFormItem', 'ElFormItem'].includes(
        this.schema.componentName
      ) &&
      DesignMode.PUBLISH !== this.mode
    ) {
      const input = this.$el.querySelector('input')
      // 只处理element-ui的input
      if (!input || !input.classList.contains('el-input__inner')) return
      input.parentElement.classList.add('relative')
      const div = document.createElement('div')
      if (input.getAttribute(Design.NODE_UID)) {
        div.setAttribute(Design.NODE_UID, input.getAttribute(Design.NODE_UID))
        div.setAttribute(Design.NODE_TAG, input.getAttribute(Design.NODE_TAG))
      }
      div.classList.add('absolute', 'top-0', 'left-0', 'w-full', 'h-full')
      input.parentElement.insertBefore(div, input)
    }
  },
  render(h) {
    const schema = this.schema

    const mergeScope = {
      ...this.scope,
      ...this?.pageScope?.(),
    }

    const props = getBindProps(schema, mergeScope, this.mode)
    if (props) {
      // 渲染slot，对应vue@2
      // vue3 这里的代码需要删除，修改getChildren方法
      if (schema?.children?.length) {
        props.scopedSlots = {}
        schema.children.forEach((e) => {
          if (e.componentName === 'Slot') {
            const slot = e?.props?.name ?? 'default'
            if (!slot) {
              throw Error(e.componentName + '缺少slot属性')
            }
            props.scopedSlots[slot] = (scope) => {
              return renderSlot(h, e, { ...mergeScope, scope }, schema)
            }
          }
        })
      }
    }
    // 根据循环参数渲染
    if (schema.componentName === 'LCLoop') {
      props.props.schema = schema
      props.props.state = this?.state
      props.attrs[Design.NODE_LOOP] = schema.id
    }

    const Component = schema.componentName

    this.emit('renderNode', {
      schema,
      parent: this.parent,
      scope: this.scope,
    })
    if (
      !isCondition(schema.condition, mergeScope) &&
      this.mode !== DesignMode.DESIGN
    )
      return null

    // 不使用renderer包裹子元素
    // 目前遇到的el-tabs比较特殊，tabs是通过$slots.default来获取tab导航的文本，
    // 针对这种组件，不能使用renderer包裹会呆滞获取不到导航，需要直接渲染子元素
    // 这也意味着失去了对子组件的通用控制，比如事件绑定，样式绑定等
    // 所以这种组件，不能对子组件进行控制，配置material时需要注意，不要定义对应的snippet
    // 可以使用childConfig来控制子组件，获得部分属性控制
    if (childWrapRenderer(Component)) {
      const el = h(
        getComponent(Component),
        props,
        schema.children
          .filter((e) => {
            return isCondition(e.props.CWRCondition, mergeScope)
          })
          .map((e) => {
            this.emit('renderNode', {
              schema: e,
              parent: this.schema,
              scope: this.scope,
            })

            const props2 = getBindProps(e, mergeScope, this.mode)

            // 不使用renderer嵌套渲染时，添加slot属性
            if (e.componentName === 'Slot') {
              props2.slot = e.props.name
            }
            // 不用renderer包裹得单独处理scopedSlots
            if (e?.children?.length) {
              props2.scopedSlots = {}
              e.children.forEach((a) => {
                if (a.componentName === 'Slot') {
                  const slot = a?.props?.name
                  if (!slot) {
                    throw Error(a.componentName + '缺少slot属性')
                  }
                  props2.scopedSlots[slot] = (scope) => {
                    return renderSlot(h, a, { ...mergeScope, scope }, e)
                  }
                }
              })
            }

            if (e.componentName === 'LCCustom') {
              props2.props.scope = mergeScope
            }

            return h(
              getComponent(e.componentName),
              props2,
              getChildren(
                h,
                {
                  ...e,
                  // table组件下的children排除Slot,防止获取不到对应的scope
                  children: (e?.children ?? []).filter(
                    (e) => e.componentName !== 'Slot'
                  ),
                },
                mergeScope
              )
            )
          })
      )
      if (this.mode === DesignMode.DESIGN) {
        return wrapRender(h, schema, props, el)
      }
      return el
    }

    // 保持数据干净
    if (schema.__key__) {
      requestAnimationFrame(() => {
        delete schema.__key__
      })
    }
    if (Component === 'LCCustom') {
      // 自定义组件，将scope也传入进去
      props.props.scope = mergeScope
    }

    const renderEl = h(
      getComponent(Component),
      props,
      getChildren(h, schema, mergeScope)
    )
    if (this.mode === DesignMode.DESIGN) {
      return wrapRender(h, schema, props, [renderEl])
    }
    // 在这里加wrap
    return renderEl
  },
}

export default renderer
