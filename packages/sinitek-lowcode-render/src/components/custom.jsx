import { createComponent } from '../shared/createComponent'
import Placeholder from './placeholder'
import { DesignMode } from 'sinitek-lowcode-shared'
export default createComponent({
  name: 'LCCustom',
  props: {
    name: String,
    scope: Object,
  },
  inject: ['root', 'mode'],
  render() {
    const getSlot = (name, ...args) => {
      let slots = window.parent?.__LCScopedSlots__
      if (this.mode === DesignMode.PUBLISH) {
        slots = this.root
      }
      return (
        slots?.$slots?.[name] ||
        (slots?.$scopedSlots?.[name] && slots?.$scopedSlots?.[name](...args))
      )
    }
    const el = getSlot(this.name, this.scope || {})
    if (this.mode === DesignMode.DESIGN) {
      return <Placeholder placeholder="在渲染组件里，使用对应的slot。" />
    } else if (el) {
      return el
    } else {
      throw Error('动态组件对应的slot未定义！')
    }
  },
})
