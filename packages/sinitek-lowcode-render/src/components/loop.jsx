import Placeholder from './placeholder'
import { createComponent } from '../shared/createComponent'
import renderer from './renderer'
import { DesignMode } from 'sinitek-lowcode-shared'
export default createComponent({
  name: 'LCLoop',
  components: {
    Placeholder,
  },
  inject: ['mode'],
  props: {
    options: {
      type: Array,
    },
    schema: Object,
    state: Object,
    loopVarName: {
      type: String,
      default: 'item',
    },
    loopIndexName: {
      type: String,
      default: 'index',
    },
  },
  render(h) {
    // 设计时，没有数据源时，则默认生成一个空数据
    const options =
      this.mode === DesignMode.PUBLISH || this?.options?.length
        ? this.options
        : [{}]
    if (!options?.length) return this.slots('empty')
    const children = this.schema.children || []
    if (!children.length) return <Placeholder />
    return (
      <div>
        {options.map((e, i) => {
          return children.map((schema) => {
            return h(renderer, {
              props: {
                schema,
                parent: this.schema,
                state: this.state,
                scope: {
                  scope: { [this.loopVarName]: e, [this.loopIndexName]: i },
                },
              },
              ref: schema.ref,
              refInFor: true,
            })
          })
        })}
      </div>
    )
  },
})
