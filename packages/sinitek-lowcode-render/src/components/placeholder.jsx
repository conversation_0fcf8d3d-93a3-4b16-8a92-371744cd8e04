import { createComponent } from '../shared/createComponent'
import { DesignMode } from 'sinitek-lowcode-shared'
export default createComponent({
  name: 'LCPlaceholder',
  props: {
    placeholder: {
      type: String,
      default: '请将元素拖放到这里',
    },
  },
  inject: ['mode'],
  render() {
    return this.mode === DesignMode.DESIGN ? (
      <div class="lc-placeholder min-h-12 wh-full f-c-c text-bgGray text-gray fs-14">
        {this.slots('default') ? this.slots('default') : this.placeholder}
      </div>
    ) : (
      this.slots('default')
    )
  },
})
