import { createComponent } from '../shared/createComponent'
import { Icon } from '@iconify/vue2'
export default createComponent({
  name: 'LCIcon',
  props: {
    icon: String,
    width: {
      type: Number,
      default: 12,
    },
    height: {
      type: Number,
      default: 12,
    },
    color: String,
    rotate: Number,
    horizontalFlip: Number,
    verticalFlip: Number,
    inline: Number,
  },
  render() {
    return (
      <Icon
        icon={this.icon}
        rotate={this.rotate || 0}
        inline={this.inline}
        horizontalFlip={this.horizontalFlip}
        verticalFlip={this.verticalFlip}
        style={{
          width: this.width + 'px',
          height: this.height + 'px',
          color: this.color,
        }}
      />
    )
  },
})
