import { DesignMode, isEmpty } from 'sinitek-lowcode-shared'
import { createComponent } from '../shared/createComponent'
export default createComponent({
  name: 'LCText',
  props: {
    text: null,
    mockText: null,
    type: String,
    shortStyle: String,
    overflowTooltip: Boolean,
  },
  inject: ['mode'],
  data() {
    return {
      isOverflow: false,
    }
  },
  mounted() {
    // 根据当前元素的宽度，判断是否溢出
    const el = this.$el
    if (this.overflowTooltip && el) {
      this.isOverflow = el.scrollWidth > el.clientWidth
    }
  },
  render() {
    const text =
      this.mode !== DesignMode.PUBLISH && this.mockText
        ? this.mockText
        : this.text
    let extraClass = [this.$attrs.class, this.shortStyle]
    // 文本占位，如果不占位，文本没有值设计时就选不了这个组件
    if (this.mode !== DesignMode.PUBLISH && isEmpty(text)) {
      return (
        <span data-visible="false" data-hide-text="<文本>" class="lc-text">
          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        </span>
      )
    } else if (this.type === 'richText') {
      return <span inline-block domPropsInnerHTML={text}></span>
    } else if (this.type === 'richText2') {
      return (
        <span class="ql-snow">
          <span class="ql-editor">
            <span domPropsInnerHTML={text}></span>
          </span>
        </span>
      )
    }
    if (this.type === 'wrap') {
      return (
        <span class={['lc-text', 'whitespace-pre-wrap', ...extraClass]}>
          {text}
        </span>
      )
    }
    if (this.isOverflow) {
      return (
        <el-tooltip effect="dark" content={text} placement="top-start">
          <span class={['lc-text', ...extraClass]}>{text}</span>
        </el-tooltip>
      )
    }
    return <span class={['lc-text', ...extraClass]}>{text}</span>
  },
})
