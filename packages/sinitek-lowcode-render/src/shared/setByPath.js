import vue from 'vue'
export function setByPath(obj, path, value) {
  if (!path || typeof path !== 'string') return
  // eslint-disable-next-line no-useless-escape
  const keys = path.split(/[\.\[\]]/).filter(Boolean)
  const lastKey = keys.pop()
  let target = obj

  for (let key of keys) {
    if (!isNaN(key)) {
      // 如果 key 是数字字符串，则将其转换为数字索引
      let index = Number(key)
      if (!Array.isArray(target)) {
        target = vue.set(target, index, [])
      } else if (!(index in target)) {
        target[index] = {}
      }
    } else if (!(key in target)) {
      target = vue.set(target, key, {})
    } else {
      target = target[key]
    }
  }
  vue.set(target, lastKey, value)
}
