import { createFetcher } from './datasource'
const initContext = () => ({
  datasource: {},
})
const context = initContext()
export const getContext = () => {
  return context
}

export const setDatasource = (ds, fetcher, scope, callback) => {
  const id = ds.id
  if (!id) {
    throw Error(new Error('datasource 缺少id！'))
  }
  const load = createFetcher(ds, fetcher, scope, callback)
  return (context.datasource[id] = {
    type: ds.type,
    auto: () => {
      // auto只能执行一次
      context.datasource[id].auto = null
      return load()
    },
    send: load,
  })
}

export const setDatasourceByModel = (ds, fetchers, scope, callback) => {
  const id = ds.id
  if (!id) {
    throw Error(new Error('datasource 缺少id！'))
  }

  const read = createFetcher(ds, fetchers.list, scope, callback)
  const create = createFetcher(ds, fetchers.create, scope)
  const update = createFetcher(ds, fetchers.update, scope)
  const _delete = createFetcher(ds, fetchers.delete, scope)
  const single = createFetcher(ds, fetchers.single, scope)
  const lazyList = createFetcher(ds, fetchers.lazyList, scope)
  const lazySingle = createFetcher(ds, fetchers.lazySingle, scope)
  const readWrap = (params = {}) => {
    params.modelCode = ds.modelCode
    if (!params.condition) {
      params.condition = ds.condition
    }
    return read(params)
  }
  return (context.datasource[id] = {
    type: ds.type,
    create: (params, otherOptions) => {
      if (!params) throw Error('create函数缺少参数')
      params.modelCode = ds.modelCode
      return create(params, otherOptions).then((res) => {
        scope.datasource[id].create = res
        return res
      })
    },
    auto: () => {
      // auto只能执行一次
      context.datasource[id].auto = null
      return readWrap()
    },
    update: (params, otherOptions) => {
      if (!params) throw Error('update函数缺少参数')
      params.modelCode = ds.modelCode
      return update(params, otherOptions).then((res) => {
        scope.datasource[id].update = res
        return res
      })
    },
    list: (params = {}, otherOptions) => {
      return readWrap(params, otherOptions)
    },
    delete: (params, otherOptions) => {
      if (!params) throw Error('delete函数缺少参数')
      params.modelCode = ds.modelCode
      return _delete(params, otherOptions).then((res) => {
        scope.datasource[id].delete = res
        return res
      })
    },
    send: (params = {}, otherOptions) => {
      return readWrap(params, otherOptions)
    },
    single: (params = {}, otherOptions) => {
      params.modelCode = ds.modelCode
      return single(params, otherOptions).then((res) => {
        scope.datasource[id].single = res
        return res
      })
    },
    lazyList: (params = {}, otherOptions) => {
      params.modelCode = ds.modelCode
      params.condition = ds.condition
      return lazyList(params, otherOptions).then((res) => {
        scope.datasource[id].lazyList = res
        return res
      })
    },
    lazySingle: (params = {}, otherOptions) => {
      params.modelCode = ds.modelCode
      return lazySingle(params, otherOptions).then((res) => {
        scope.datasource[id].lazySingle = res
        return res
      })
    },
  })
}

export const setDatasourceByEnum = (ds, fetcher, scope, callback) => {
  const id = ds.id
  if (!id) {
    throw Error(new Error('datasource 缺少id！'))
  }
  const load = createFetcher(ds, fetcher, scope, callback)
  const readWrap = () => {
    return load({ catalog: ds.enum[0], name: '', type: ds.enum[1] })
  }
  return (context.datasource[id] = {
    type: ds.type,
    auto: () => {
      // auto只能执行一次
      context.datasource[id].auto = null
      return readWrap()
    },
    send: readWrap,
  })
}

export const cleanContext = () => {
  context.datasource = {}
}
