// datasource
// id
// auto
// method
// url
// data
// headers
// willFetch
// shouldFetch
// errorHandler

import { newFn, parseData } from 'sinitek-lowcode-shared'

// dataHandler
export const createFetcher = (
  datasource,
  fetcher,
  scope,
  callback = () => {}
) => {
  return (data, otherOptions) => {
    let options = {
      url: datasource.url,
      method: datasource.method,
      data: Array.isArray(data)
        ? data
        : parseData({ ...datasource.data, ...data }, scope),
      headers: parseData(datasource.headers, scope),
    }
    let isShouldFetch = true
    if (datasource.shouldFetch) {
      // 判断是否可以发送请求
      isShouldFetch = newFn(`return (${datasource.shouldFetch}) `)().call(
        scope,
        options
      )
    }
    if (!isShouldFetch) return Promise.resolve()
    if (datasource.willFetch) {
      // 判断是否可以发送请求
      options = newFn(`return (${datasource.willFetch}) `)().call(
        scope,
        options
      )
    }
    return fetcher(options, otherOptions)
      .then((res) => {
        if (datasource.dataHandler) {
          return newFn(`return (${datasource.dataHandler}) `)().call(scope, res)
        }
        return res
      })
      .then((res) => {
        callback(res)
        return Promise.resolve(res)
      })
      .catch((e) => {
        let err = e
        if (datasource.errorHandler) {
          err = newFn(`return (${datasource.errorHandler}) `)().call(scope, e)
        }
        return Promise.reject(err ?? e)
      })
  }
}
