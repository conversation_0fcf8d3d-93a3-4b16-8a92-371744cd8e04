import Renderer from '../components/renderer.jsx'
import { cleanContext } from '../shared/context'
import './index.scss'
import { DesignMode, parseData } from 'sinitek-lowcode-shared'
const prefix = 'page-css-'
let cleanCssQueue = []

const setPageCss = (css = '', _uid) => {
  if (cleanCssQueue.length) {
    cleanCssQueue.forEach((uid) => {
      removePageCss(uid)
    })
    cleanCssQueue = []
  }
  let element = document.getElementById(prefix + _uid)
  const head = document.querySelector('head')

  document.body.setAttribute('style', '')

  if (!element) {
    element = document.createElement('style')
    element.setAttribute('type', 'text/css')
    element.setAttribute('id', prefix + _uid)

    element.innerHTML = css
    head.appendChild(element)
  } else {
    element.innerHTML = css
  }
}

const removePageCss = (_uid) => {
  const element = document.getElementById(prefix + _uid)
  if (element) {
    document.head.removeChild(element)
  }
}

export default {
  name: 'SinitekLowcodeRender',
  provide() {
    return {
      mode: this.mode,
      // 用来给renderer发送 渲染节点的事件
      emit: this.emit,
      // 和simulator的fetcher不是一个东西
      // 当render组件是在设计器里时，fetcher和simulator是一致的
      // 单独使用时，结构和simulator的fetcher一样
      fetcher: this.fetcher,
      // 目前给custom用来获取slots
      root: this,
      isLCComponent: this.isLCComponent,
    }
  },
  props: {
    config: {
      type: Object,
      required: true,
    },
    fetcher: Object,
    mode: {
      type: String,
      default: DesignMode.PUBLISH,
    },
    isLCComponent: Boolean,
  },
  destroyed() {
    this._destroy()
  },
  activated() {
    this._register()
  },
  deactivated() {
    this._destroy()
  },
  methods: {
    _register() {
      setPageCss(this.config.css, this._uid)
      // setVM(this)
    },
    _destroy() {
      cleanCssQueue.push(this._uid)
      cleanContext()
    },
    emit(type, ...argv) {
      this.$emit(type, ...argv)
    },
    // 获取的是page的scope
    // 获取路径固定的，每个render下面的第一个子元素一定是renderer嵌套page
    getScope() {
      return this.$children[0].$children[0]._getScope()
    },
    getMethods() {
      if (!this.isLCComponent) {
        throw new Error('getMethods只能在低代码组件中使用')
      }
      const scope = this.getScope()
      if (!Object.keys(scope.methods).length) {
        return {}
      }
      return Object.entries(parseData(scope.methods, scope)).reduce(
        (cur, [key, v]) => {
          if (
            !(
              key.startsWith('_') ||
              [
                'created',
                'mounted',
                'activated',
                'deactivated',
                'destroyed',
                'beforeDestroy',
              ].includes(key)
            )
          ) {
            cur[key] = v
          }
          return cur
        },
        {}
      )
    },
  },
  created() {
    this._register()
    this.$watch('config.css', (newVal) => {
      setPageCss(newVal, this._uid)
    })
  },
  render(h) {
    return h(Renderer, { props: { schema: this.config, scope: {} } })
  },
}
