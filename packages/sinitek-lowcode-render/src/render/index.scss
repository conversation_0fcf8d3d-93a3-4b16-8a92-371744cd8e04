.lc-hidden {
  display: none;
}
.lc-wrap {
  &.inline {
    display: inline-block;
  }
}
.lc-placeholder {
  background-color: #e0e0e0
}
*[data-uid][data-visible=false] {
  opacity: .6;
  position: relative;
  &::before,&:after {
    align-items: center;
    display: flex;
    font-size: 12px;
    justify-content: center;
    left: 0;
    overflow: hidden;
    pointer-events: none;
    position: absolute;
    top: 0;
    z-index: 10;
  }
  &:before {
    height: 100%;
    width: 100%;
    background: rgba(151, 173, 170, 0.1);
    border: 1px dashed #ced0d3;
  }
  &:after {
    color: #333;
    content: attr(data-hide-text);
    background-color: #fff;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    word-break: keep-all;
    white-space: nowrap;
  }
}
.lc-row,.lc-col{
  > .lc-flex,.lc-col,.lc-row {
    flex: 1 1 0;
  }
}
.lc-row,.lc-col{
  > .lc-col.flex-col-valid-basis,.lc-row.flex-row-valid-basis {
    flex: 0 0 auto;
  }
}
.lc-page:empty{
  width: 100%;
  height: 100%;
  min-height: 48px;
  font-size: 14px;
  color: #8c8c8c;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0e0e0;
  &:before {
    content: '请将元素拖放到这里'
  }
}
.lc-font--normal {
  font-size: 14px;
  color: #333;
}
.lc-font--menu {
  font-size: 14px;
  font-weight: 600;
  color: #666
}
.lc-font--title {
  font-size: 16px;
  font-weight: 600;
  color: #666
}
.lc-font--content {
  font-size: 14px;
  color: #666
}

.lc-font--link {
  font-size: 14px;
  color: var(--lc-color-primary, #409EFF);
  font-weight: 600;
}

.drag-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #94a3b8; /* 提示文字颜色 */
  font-size: 1.1rem;
  border: 2px dashed #cbd5e1; /* 虚线边框 */
  padding: 30px 50px;
  border-radius: 8px;
  pointer-events: none; /* 不影响下方元素点击 */
}
