import { createComponent } from 'sinitek-lowcode-render'

export default createComponent({
  name: 'XBlock',
  data() {
    return {
      slot1: 'slot1',
    }
  },
  render() {
    return (
      <div>
        <div>
          block,{' '}
          <button type="button" onClick={this.onClick}>
            修改slot数据
          </button>
        </div>
        <div>default: {this._t('default')}</div>
        <div>slot1: {this._t('slot1', null, { slot1: this.slot1 })}</div>
      </div>
    )
  },
  methods: {
    onClick() {
      this.slot1 = '修改后的slot1' + Math.random()
    },
  },
})
