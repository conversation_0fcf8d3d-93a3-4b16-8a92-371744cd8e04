import Vue from 'vue'
import App from './App.vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
// import 'uno.css'

// Vue.config.productionTip = false

import render from '../index.js'
import Input from './components/input'
import Text from './components/text'
import Form from './components/form'
import Button from './components/button'
import Block from './components/block'

Vue.use(ElementUI)
Vue.use(render)
Vue.component(Input.name, Input)
Vue.component(Text.name, Text)
Vue.component(Form.name, Form)
Vue.component(Button.name, Button)
Vue.component(Block.name, Block)

new Vue({
  render: (h) => h(App),
}).$mount('#app')
