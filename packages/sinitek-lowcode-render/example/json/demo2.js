export default {
  componentName: 'Page',
  name: '',
  style: '', // 自定义样式
  props: {},
  key: '[age]',
  id: 'sdfsd',
  state: {
    text1: 'text1',
    text2: 'text2',
    text3: 'text3',
    text4: 'text4',
    form2: {
      a1: 'a1',
      a2: 'a2',
    },
  },
  methods: {
    testFn: {
      type: 'JSFunction',
      value: 'function (e,args) {console.log("fn",this,e, args)}',
      params: {
        a: 1,
      },
    },
  },

  children: [
    {
      componentName: 'XForm',
      key: 'key_0',
      id: 'sdaaafsd12',
      children: [
        {
          componentName: 'Input',
          name: 's0',
          title: '输入框',
          ref: 'ref_1',
          key: 'key_12',
          id: 'sdaaafsd',
          events: {
            onInput: {
              type: 'JSFunction',
              value:
                'function (e) {console.log(this, "Tt"); this.state.text1=e;this.state.text2=e}',
            },
          },
          condition: false,
        },
        {
          componentName: 'Input',
          title: '输入框',
          ref: 'ref_1',
          key: 'key_11',
          id: 'sdfs44d',
          props: {
            value: {
              type: 'JSExpression',
              value: 'this.state.text2',
            },
          },
          events: {
            onInput: {
              type: 'JSFunction',
              value:
                'function (e) {console.log(this, "Tt"); this.state.text1=e;this.state.text2=e}',
            },
          },
        },
        {
          componentName: 'XText',
          title: '',
          ref: 'ref_2',
          key: 'key_1',
          id: 'sdfsasdd',
          props: {
            value: {
              type: 'JSExpression',
              value: 'this.state.text1',
            },
          },
        },
        {
          componentName: 'XText',
          title: '',
          ref: 'ref_2',
          key: 'key_2',
          id: 'sdfdssasd',
          props: {
            value: {
              type: 'JSExpression',
              value: 'this.state.text2',
            },
          },
        },
        {
          componentName: 'XText',
          title: '',
          ref: 'ref_2',
          key: 'key_32',
          id: 'sdf431sd',
          props: {
            value: {
              type: 'JSExpression',
              value: 'this.state.text3',
            },
          },
        },
        {
          componentName: 'XText',
          title: '',
          ref: 'ref_2',
          key: 'key_4',
          id: 's1231dfsd',
          props: {
            value: {
              type: 'JSExpression',
              value: 'this.state.text4',
            },
          },
        },
        {
          componentName: 'XForm',
          key: 'key_012',
          id: 'sdaaafsd1232',
          children: [
            {
              componentName: 'Input',
              title: '输入框',
              ref: 'ref_1',
              key: 'key_12',
              id: 'sdaaafsd',
              events: {
                onInput: {
                  type: 'JSFunction',
                  value:
                    'function (e) {console.log(this, "Tt"); this.state.text1=e;this.state.text2=e}',
                },
              },
              condition: false,
            },
            {
              componentName: 'XText',
              title: '',
              ref: 'ref_02',
              key: 'key_32004',
              id: 'sdf431s00d5',
              props: {
                value: 'text--0000',
              },
            },
            {
              componentName: 'XText',
              title: '',
              ref: 'ref_2',
              key: 'key_324',
              id: 'sdf431sd5',
              props: {
                value: {
                  type: 'JSExpression',
                  value: '`${this.state.text1}_${this.state.text2}`',
                },
              },
            },
            {
              componentName: 'Input',
              title: '输入框',
              ref: 'ref_1',
              key: 'key_1143',
              id: 'sdfs44d3',
              props: {
                value: {
                  type: 'JSExpression',
                  value: 'this.state.text2',
                },
              },
              events: {
                onInput: {
                  type: 'JSFunction',
                  value:
                    'function (e) {console.log(this, "Tt"); this.state.text1=e;this.state.text2=e}',
                },
              },
            },
            {
              componentName: 'XButton',
              title: '按钮',
              key: 'key_113',
              id: 'sdf34s44d3',
              style: 'background:red;color: white;',
              className: 'btn2',
              props: {
                text: '修改text2按钮',
              },
              events: {
                onClick: {
                  type: 'JSFunction',
                  value: 'function () {this.state.text2="修改text2按钮"}',
                },
              },
            },
            {
              componentName: 'XBlock',
              ref: 'key_41113',
              id: 'sdf34s4466d3',
              children: [
                {
                  componentName: 'XForm',
                  title: '',
                  key: 'key_11sd23',
                  id: 'sdf34aa1s444d3',
                  slot: 'default',
                  children: [
                    {
                      componentName: 'Input',
                      title: '输入框',
                      ref: 'ref_sa11',
                      key: 'key_11a243',
                      id: 'sdfdss44d3',
                      props: {
                        value: {
                          type: 'JSExpression',
                          value: 'this.state.form2.a1',
                        },
                      },
                    },
                    {
                      componentName: 'Input',
                      title: '输入框',
                      ref: 'ref_dsa11',
                      key: 'key_1s1a243',
                      id: 'sdfadss44d3',
                      props: {
                        value: {
                          type: 'JSExpression',
                          value: 'this.state.form2.a2',
                        },
                      },
                      events: {
                        onInput: {
                          type: 'JSFunction',
                          value: 'function (v) {this.state.form2.a2 = v}',
                        },
                      },
                    },
                  ],
                },
                {
                  componentName: 'XButton',
                  title: '按钮',
                  id: 'sdf34s144d333',
                  slot: 'slot1',
                  props: {
                    text: {
                      type: 'JSExpression',
                      value: '$scope.slot1',
                    },
                  },
                },
              ],
            },
          ],
        },
        {
          componentName: 'XButton',
          title: '按钮',
          id: 'sdf34s144d333',
          slot: 'slot1',
          props: {
            text: '点击调用全局函数',
          },
          events: {
            onClick: {
              type: 'JSExpression',
              value: 'this.testFn',
            },
          },
        },
      ],
    },
  ],
}
