export default {
  componentName: 'Page',
  name: '',
  style: '', // 自定义样式
  props: {},
  key: '[age]',
  state: {
    text1: 'df',
    text21: 'df',
  },
  methods: {},

  children: [
    // 弹窗，插入到page/children最前面
    {
      componentName: 'Input',
      ref: 'ref_1',
      key: 'key_1',
      events: {
        onInput: {
          type: 'JSFunction',
          value:
            'function (e) {console.log(this, e, "Tt"); this.state.text1=e}',
        },
      },
      props: {
        value: {
          type: 'JSExpression',
          value: '$scope.item',
        },
      },
      loop: {
        type: 'JSExpression',
        value: '["a1", 2, 3, 4]',
      },
      loopArgs: ['item', 'index'],
    },
    {
      componentName: 'XText',
      ref: 'ref_2',
      key: 'key_2',
      props: {
        value: {
          type: 'JSExpression',
          value: 'this.state.text1',
        },
      },
    },
  ],
}
