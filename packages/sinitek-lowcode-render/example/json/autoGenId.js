const guid = () => {
  return 'xxxxxxxx'.replace(/[x]/g, (c) => {
    const random = parseFloat(
      '0.' + crypto.getRandomValues(new Uint32Array(1))[0]
    )
    const r = (random * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
export function autoGenId(schemas) {
  schemas.id = guid()
  if (schemas.hidden === void 0) {
    schemas.hidden = false
  }
  ;(schemas?.children ?? []).forEach((e) => {
    autoGenId(e)
  })
  return schemas
}
