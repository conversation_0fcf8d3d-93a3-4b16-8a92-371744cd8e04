import { autoGenId } from './autoGenId'
export default autoGenId({
  componentName: 'Page',
  name: '',
  style: '',
  props: {},
  ref: 'a1',
  id: '64c5d242',
  css: '.line{text-align:center;}',
  state: {
    form: {
      name: '产品名称',
      productTypeName: '产品类型',
      investTypeName: '投资',
    },
    d1: 1,
    form_yy3k: {
      name: '12',
      region: '',
      date1: '',
      date2: '',
      desc: '',
    },
    form_s2lu: {
      field1: '',
      field2: '',
      field3: '',
    },
  },
  datasource: [
    {
      url: 'https://mock.apipark.cn/m1/594011-2734521-default/mock',
      method: 'get',
      id: 'mock1',
      auto: true,
      data: {},
      shouldFetch:
        "function shouldFetch() {\n        console.log('should fetch.....');\n        return true;\n      }",
      errorHandler: 'function errorHandler(err) {}',
      dataHandler:
        'function dataHandler(res) { console.log(res);return res.data }',
      willFetch:
        'function willFetch(options) {\n        options.b = 2\n        return options; }',
    },
  ],
  methods: {
    submit: {
      type: 'JSFunction',
      value: 'function submit(e) {\n  console.log("fn", this, this.state);\n}',
    },
    a: {
      type: 'JSFunction',
      value:
        'function a(...args) {\n  console.log(this, "this");\n  console.log(...args, "a1");\n}',
    },
    close: {
      type: 'JSFunction',
      value: 'function close(v) {\n  this.state.d1 = v;\n}',
    },
    submit_yy3k: {
      type: 'JSFunction',
      value: 'function submit_yy3k () {console.log(this.state.form_yy3k)}',
    },
    submit_s2lu: {
      type: 'JSFunction',
      value:
        "function submit_s2lu () {\n              this.refs.form_s2lu.validate((valid) => {\n                if (valid) {\n                  console.log(this.state.form_s2lu)\n                } else {\n                  console.log('error submit!!');\n                  return false;\n                }\n              });\n              \n              }",
    },
  },
  children: [
    {
      componentName: 'ElLink',
      props: {
        type: 'primary',
        href: 'ssss',
      },
      children: [
        {
          componentName: 'LCText',
          props: {
            text: '链接',
          },
          id: '35214415',
          ref: 'LCText1',
          hidden: false,
        },
      ],
      hidden: false,
      id: '44c963b2',
      ref: 'ElLink1',
      events: {
        click: {
          type: 'JSFunction',
          value:
            'function(){return this.methods.close.apply(this, Array.prototype.slice.call(arguments)) }',
        },
      },
    },
    {
      componentName: 'LCText',
      props: {
        text: {
          type: 'JSExpression',
          value: '`test: ${this.state.d1}`',
        },
      },
      hidden: false,
      id: '64467235',
      ref: 'LCText1',
    },
    {
      componentName: 'LCText',
      props: {
        text: {
          type: 'JSExpression',
          value: '`test: ${this.state.d1}`',
        },
      },
      hidden: false,
      id: '45445237',
      ref: 'LCText1',
    },
    {
      componentName: 'XnButton',
      props: {
        showLoading: false,
      },
      children: [
        {
          componentName: 'LCText',
          props: {
            text: '按钮',
          },
          id: '54558264',
          ref: 'LCText2',
          hidden: false,
        },
      ],
      hidden: false,
      id: 'a535c3e6',
      ref: 'XnButton1',
      events: {
        click: {
          type: 'JSAction',
          value: 'ssss',
        },
      },
    },
    {
      componentName: 'LAForm',
      ref: 'form_s2lu',
      props: {
        column: '2',
        labelPosition: 'left',
        status: 'edit',
        model: {
          type: 'JSExpression',
          value: 'this.state.form_s2lu',
        },
      },
      children: [
        {
          componentName: 'LAFormItem',
          LCBindState: 'form_s2lu.field1',
          props: {
            field: 'INPUT',
            itemProps: {
              label: '输入框',
              required: true,
              prop: 'field1',
            },
          },
          id: '55574254',
          ref: 'LAFormItem4',
          hidden: false,
        },
        {
          componentName: 'LAFormItem',
          LCBindState: 'form_s2lu.field2',
          props: {
            field: 'SELECT',
            itemProps: {
              label: '选择框',
              required: true,
              prop: 'field2',
            },
            fieldProps: {
              options: [
                {
                  label: '区域一',
                  value: '区域一',
                },
                {
                  label: '区域二',
                  value: '区域二',
                },
              ],
            },
          },
          id: '43146369',
          ref: 'LAFormItem5',
          hidden: false,
        },
        {
          componentName: 'LAFormItem',
          LCBindState: 'form_s2lu.field3',
          props: {
            field: 'DATE',
            itemProps: {
              label: '时间',
              required: true,
              prop: 'field3',
            },
            fieldProps: {
              type: 'datetime',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
            },
          },
          id: '4b564315',
          ref: 'LAFormItem6',
          hidden: false,
        },
        {
          componentName: 'XnFormItem',
          style: 'grid-column-start: span 2;text-align: right;',
          children: [
            {
              componentName: 'XnButton',
              props: {
                type: 'primary',
                showLoading: false,
              },
              events: {
                click: {
                  type: 'JSAction',
                  value: 'save',
                },
              },
              children: [
                {
                  componentName: 'LCText',
                  props: {
                    text: '立即创建',
                  },
                  id: 'd2894e45',
                  ref: 'LCText9',
                  hidden: false,
                },
              ],
              id: '514516d3',
              ref: 'XnButton7',
              hidden: false,
            },
            {
              componentName: 'XnButton',
              props: {
                showLoading: false,
              },
              children: [
                {
                  componentName: 'LCText',
                  props: {
                    text: '取消',
                  },
                  id: 'b4331524',
                  ref: 'LCText10',
                  hidden: false,
                },
              ],
              id: '6369b343',
              ref: 'XnButton8',
              hidden: false,
            },
          ],
          id: '14224554',
          ref: 'XnFormItem12',
          hidden: false,
        },
      ],
      hidden: false,
      id: 'd3521465',
    },
    {
      componentName: 'LCRow',
      props: {
        gap: 10,
        basis: '',
        width: '',
      },
      children: [
        {
          componentName: 'LCFlex',
          props: {
            gap: 10,
          },
          children: [],
          hidden: false,
          id: '42693156',
          ref: 'LCFlex1',
        },
        {
          componentName: 'LCFlex',
          props: {},
          children: [
            {
              componentName: 'LATable',
              props: {
                dynamicCols: true,
                border: true,
                allowInit: true,
                isProgress: true,
                showPagination: true,
                loadingMask: false,
                actionTitle: '操作',
                actionWidth: '150',
                actions: [
                  {
                    text: '编辑',
                    type: 'primary',
                    hidden: false,
                    disabled: false,
                  },
                  {
                    text: '删除',
                    type: 'primary',
                    hidden: false,
                    disabled: false,
                  },
                ],
              },
              children: [
                {
                  componentName: 'xn-col',
                  props: {
                    prop: 'date',
                    label: '日期',
                  },
                  children: [],
                  id: '612fd65d',
                  ref: 'XnCol1',
                },
                {
                  componentName: 'xn-col',
                  props: {
                    prop: 'name',
                    label: '姓名',
                  },
                  children: [],
                  id: 'f3533423',
                  ref: 'XnCol2',
                },
                {
                  componentName: 'xn-col',
                  props: {
                    prop: 'address',
                    label: '地址',
                  },
                  children: [],
                  id: 'f4f63137',
                  ref: 'XnCol3',
                },
              ],
              hidden: false,
              id: '6d492661',
              ref: 'LATable1',
            },
          ],
          id: '1241665a',
          ref: 'LCFlex2',
        },
      ],
      hidden: false,
      ref: 'LCFlex1',
      id: '25442666',
      style: '',
    },
    {
      componentName: 'LAStage',
      props: {
        options: [
          {
            label: '未开始',
            color: 'rgb(156,163,175)',
            icon: 'el-icon-check',
          },
          {
            label: '就绪',
            color: 'rgb(96,165,250)',
            icon: 'el-icon-check',
          },
          {
            label: '处理中',
            color: 'rgb(251,146,60)',
            icon: 'el-icon-check',
          },
          {
            label: '已完成',
            color: 'rgb(74,222,128)',
            icon: 'el-icon-check',
          },
          {
            label: '关闭',
            color: 'rgb(254,113,113)',
            icon: 'el-icon-close',
          },
        ],
      },
      children: [
        {
          componentName: 'LAStageItem',
          props: {
            label: '未开始',
            color: 'rgb(156,163,175)',
            icon: 'el-icon-check',
            children: [],
          },
          children: [],
          id: '92853833',
          ref: 'LAStageItem1',
          hidden: false,
        },
        {
          componentName: 'LAStageItem',
          props: {
            label: '就绪',
            color: '#FB923C',
            icon: 'el-icon-check',
            children: [],
          },
          children: [],
          id: '2c254168',
          ref: 'LAStageItem2',
          hidden: false,
        },
        {
          componentName: 'LAStageItem',
          props: {
            label: '处理中',
            color: '#FB923C',
            icon: 'el-icon-check',
            children: [],
          },
          children: [],
          id: '36436151',
          ref: 'LAStageItem3',
          hidden: false,
        },
        {
          componentName: 'LAStageItem',
          props: {
            label: '已完成',
            color: '#4ade80',
            icon: 'el-icon-check',
            children: [],
          },
          children: [],
          id: '53144364',
          ref: 'LAStageItem4',
          hidden: false,
        },
        {
          componentName: 'LAStageItem',
          props: {
            label: '关闭',
            color: '#FE7171',
            icon: 'el-icon-close',
            children: [],
          },
          children: [],
          id: '64925221',
          ref: 'LAStageItem5',
          hidden: false,
        },
      ],
      hidden: false,
      id: '65833256',
      ref: 'LAStage1',
      style: '',
    },
    {
      componentName: 'LATable',
      props: {
        dynamicCols: true,
        border: true,
        allowInit: true,
        isProgress: true,
        showPagination: true,
        loadingMask: false,
        actionTitle: '操作',
        actionWidth: '150',
        actions: [
          {
            text: '编辑',
            type: 'primary',
            hidden: false,
          },
          {
            text: '删除',
            type: 'primary',
            hidden: false,
            disabled: false,
          },
        ],
        datasource: '',
        data: [
          {
            id: 1,
            date: '2016-05-02',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1518 弄',
          },
          {
            id: 2,
            date: '2016-05-04',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1517 弄',
          },
          {
            id: 3,
            date: '2016-05-01',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1519 弄',
            children: [
              {
                id: 31,
                date: '2016-05-01',
                name: '王小虎',
                address: '上海市普陀区金沙江路 1519 弄',
              },
              {
                id: 32,
                date: '2016-05-01',
                name: '王小虎',
                address: '上海市普陀区金沙江路 1519 弄',
              },
            ],
          },
          {
            id: 4,
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
          },
        ],
        defaultExpandAll: true,
        rowKey: 'id',
        useSelectionCol: false,
        useActionCol: true,
      },
      children: [
        {
          componentName: 'xn-col',
          props: {
            prop: 'date',
            label: '日期',
            dataType: 'date',
          },
          children: [],
          id: '5655d624',
          ref: 'XnCol1',
          hidden: false,
        },
        {
          componentName: 'xn-col',
          props: {
            prop: 'name',
            label: '姓名',
          },
          children: [],
          id: '59526816',
          ref: 'XnCol2',
          hidden: false,
        },
        {
          componentName: 'xn-col',
          props: {
            prop: 'address',
            label: '地址',
          },
          children: [],
          id: '25125134',
          ref: 'XnCol3',
          hidden: false,
        },
      ],
      hidden: false,
      id: '33415c22',
      ref: 'LATable1',
      setterIndex: {},
    },
  ],
  hidden: false,
  actions: {
    ssss: {
      nodes: [
        {
          id: 'node_id_1',
          type: 'start-node',
          x: 0,
          y: 50,
          properties: {
            validateMsgs: [],
          },
        },
        {
          id: 'node_id_2',
          type: 'end-node',
          x: 0,
          y: 300,
          properties: {
            validateMsgs: [],
          },
        },
        {
          id: 'pbm7',
          type: 'assign-node',
          x: 4,
          y: 175,
          properties: {
            color: '#60a5fa',
            validateMsgs: [],
            assignments: [
              {
                target: 'd1',
                value: '123123',
              },
            ],
          },
        },
      ],
      edges: [
        {
          id: '91b3a6cf-9075-4891-97b3-fee6cfcf1ff1',
          type: 'logic-line',
          properties: {},
          sourceNodeId: 'node_id_1',
          targetNodeId: 'pbm7',
          startPoint: {
            x: 0,
            y: 81.5,
          },
          endPoint: {
            x: 4,
            y: 143.5,
          },
        },
        {
          id: 'e5698b94-f78c-40fb-9de6-f36f5a4b642a',
          type: 'logic-line',
          properties: {},
          sourceNodeId: 'pbm7',
          targetNodeId: 'node_id_2',
          startPoint: {
            x: 4,
            y: 206.5,
          },
          endPoint: {
            x: 0,
            y: 268.5,
          },
        },
      ],
    },
    save: {
      nodes: [
        {
          id: 'start',
          type: 'start-node',
          properties: {
            width: 50,
            height: 50,
          },
        },
        {
          id: 'end',
          type: 'end-node',
          properties: {
            width: 50,
            height: 50,
          },
        },
        {
          id: 'gzwn',
          type: 'component-node',
          properties: {
            target: 'form_s2lu',
            method: 'validate',
            width: 50,
            height: 50,
          },
          text: 'form_s2lu',
        },
      ],
      edges: [
        {
          id: '841548eb-0399-45b1-a50f-4d2b898371b3',
          type: 'logic-line',
          sourceNodeId: 'start',
          targetNodeId: 'gzwn',
        },
        {
          id: '1a29de6a-6556-4fee-80f7-9f62f84bb30d',
          type: 'logic-line',
          sourceNodeId: 'gzwn',
          targetNodeId: 'end',
        },
      ],
    },
  },
  stateComment: {},
})
