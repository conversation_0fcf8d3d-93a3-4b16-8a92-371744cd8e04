{"name": "sinitek-lowcode-flow-execute", "type": "module", "version": "1.0.0-SNAPSHOT.135", "description": "", "author": "", "license": "ISC", "keywords": [], "sideEffects": false, "main": "lib/index.js", "module": "es/index.js", "types": "es/index.d.ts", "files": ["dist", "es", "lib"], "scripts": {"build": "run-p build:esm build:cjs build:umd", "build:esm": "tsc --module esnext --target es6 --outDir ./es", "build:cjs": "tsc --module commonjs --target es6 --outDir ./lib", "build:umd": "rollup -c ./rollup.config.js --bundleConfigAsCjs", "dev": "vite", "lint": "eslint ."}, "dependencies": {"@nyariv/sandboxjs": "0.8.23", "uuid": "10.0.0"}, "devDependencies": {"@antfu/eslint-config": "3.8.0", "@babel/core": "catalog:", "@rollup/plugin-typescript": "12.1.1", "@types/node": "22.9.0", "@types/uuid": "10.0.0", "eslint": "catalog:", "rollup": "4.25.0", "sinitek-lowcode-shared": "workspace:^", "tslib": "2.8.1", "typescript": "5.6.3", "vite": "5.4.11", "vitest": "2.1.4", "vue": "catalog:"}, "lint-staged": {"*": "eslint --fix"}, "volta": {"node": "18.0.0"}, "gitHooks": {"pre-commit": "lint-staged"}}