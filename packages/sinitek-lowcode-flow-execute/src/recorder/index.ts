import type { Engine } from '..'
import { storage } from '../utils'

export const MAX_RECORDER = 100
export const MAX_INSTANCE = 100
export const LOGICFLOW_ENGINE_INSTANCES = 'LOGICFLOW_ENGINE_INSTANCES'

export class Recorder implements Recorder.Base {
  instanceId: Engine.Key
  maxRecorder: number

  constructor({ instanceId }: { instanceId: Engine.Key }) {
    this.instanceId = instanceId
    this.maxRecorder = MAX_RECORDER

    const instances = this.getItem(LOGICFLOW_ENGINE_INSTANCES) || []
    if (!instances.includes(instanceId)) {
      instances.push(instanceId)
    }
    if (instances.length > MAX_INSTANCE) {
      const clearInstance = instances.shift()
      if (clearInstance) {
        this.clearInstance(clearInstance)
      }
    }
    this.setItem(LOGICFLOW_ENGINE_INSTANCES, instances)
  }

  setMaxRecorderNumber(max: number): void {
    this.maxRecorder = max
  }

  // 将存储 storage 的方法收敛到此处，并在此处做异常处理 - setItem
  setItem(key: Engine.Key, value: unknown): void {
    try {
      storage.setItem(key, value)
    }
    catch (error) {
      console.error('[setItem] error:', error)
      storage.clear()
      storage.setItem(key, value)
    }
  }

  // getItem 方法
  getItem<T = Engine.Key[]>(key: Engine.Key): T {
    return storage.getItem<T>(key)
  }

  async getExecutionActions(executionId: Engine.Key): Promise<Engine.Key[]> {
    return Promise.resolve(this.getItem(executionId) || [])
  }

  async getExecutionList(): Promise<Engine.Key[]> {
    return this.getItem(this.instanceId) || []
  }

  private addExecution(executionId: Engine.Key): void {
    const instanceExecutions = this.getItem(this.instanceId) || []
    if (instanceExecutions.length >= this.maxRecorder) {
      const toBeRemovedItem = instanceExecutions.shift()
      if (toBeRemovedItem) {
        this.popExecution(toBeRemovedItem)
      }
    }
    instanceExecutions.push(executionId)
    this.setItem(this.instanceId, instanceExecutions)
  }

  private popExecution(executionId: Engine.Key): void {
    const instanceData = this.getItem(executionId) || []
    instanceData.forEach((actionId) => {
      storage.removeItem(actionId)
    })
    storage.removeItem(executionId)
  }

  private pushActionToExecution(executionId: Engine.Key, actionId: Engine.Key): void {
    const actions = this.getItem(executionId) || []
    actions.push(actionId)
    this.setItem(executionId, actions)
  }

  /**
   * @param {object} action
   * {
   *   actionId: '',
   *   nodeId: '',
   *   executionId: '',
   *   nodeType: '',
   *   timestamp: '',
   *   properties: {},
   * }
   */
  async addActionRecord(action: Recorder.Info): Promise<void> {
    const { executionId, actionId } = action
    const instanceData = await this.getExecutionActions(executionId)

    if (!instanceData) {
      this.addExecution(executionId)
    }
    this.pushActionToExecution(executionId, actionId)
    this.setItem(actionId, action)
  }

  async getActionRecord(actionId: Engine.Key): Promise<Recorder.Info> {
    return this.getItem<Recorder.Info>(actionId)
  }

  clear(): void {
    this.clearInstance(this.instanceId)
  }

  clearInstance(instanceId: Engine.Key): void {
    const instanceExecutions = this.getItem(instanceId) || []
    // TODO: 完善类型定义
    instanceExecutions.forEach((executionId) => {
      storage.removeItem(executionId)
      const instanceData = this.getItem(executionId) || []
      instanceData.forEach((actionId) => {
        storage.removeItem(actionId)
      })
    })

    storage.removeItem(instanceId)
  }
}

export namespace Recorder {
  export interface Base {
    addActionRecord: (action: Info) => Promise<void>
    getActionRecord: (actionId: Engine.Key) => Promise<Info>
    getExecutionActions: (executionId: Engine.Key) => Promise<Engine.Key[]>
    clear: () => void
  }

  export type Info = {
    timestamp: number
  } & Engine.NextActionParam
}

export default Recorder
