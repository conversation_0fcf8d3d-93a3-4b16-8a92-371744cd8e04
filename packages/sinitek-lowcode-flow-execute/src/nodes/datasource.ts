import type { IBaseNodeProps } from './base'
import { log } from 'sinitek-lowcode-shared'
import { ActionStatus } from '..'
import { runEval } from '../utils/eval'
import { BaseNode } from './base'

interface Properties {
  datasourceID: string
  type?: string
  params?: { key: string, value: unknown }[]
  [key: string]: any
}
export default class DatasourceNode extends BaseNode {
  static nodeTypeName = 'datasource-node'

  datasourceID: string = ''
  type: string = 'send'
  params: any[] = []
  constructor({ nodeConfig, context }: IBaseNodeProps<Properties>) {
    super({ nodeConfig, context })
    if (nodeConfig.properties) {
      this.datasourceID = nodeConfig.properties?.datasourceID ?? ''
      this.params = nodeConfig.properties?.params ?? []
      this.type = nodeConfig.properties?.type ?? 'send'
    }
  }

  public async action(): Promise<BaseNode.ActionResult | undefined> {
    let msg = ''
    log('执行数据源节点', '--参数datasourceID:', this.datasourceID, '--参数type:', this.type, '--参数params:', this.params)
    try {
      const newParams: Record<string, unknown> = {}
      // 把参数计算一遍
      for (const item of this.params) {
        const value = await runEval(this.context, item.value)
        newParams[item.key] = value
      }
      if (!this.context?.fetcher?.[this.datasourceID]) {
        throw new Error(`找不到请求${this.datasourceID}对应的${this.type}方法`)
      }
      await this.context.fetcher[this.datasourceID][this.type](newParams)
    }
    catch (e: any) {
      console.error(e)
      msg = e.message as string
    }
    return {
      detail: {
        message: msg,
      },
      status: msg ? ActionStatus.ERROR : ActionStatus.SUCCESS,
    }
  }
}

export { DatasourceNode }
