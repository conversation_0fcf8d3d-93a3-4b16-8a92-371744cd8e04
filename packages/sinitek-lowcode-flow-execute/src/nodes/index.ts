import type { Engine } from '..'
import ActionNode from './action'
import AssignNode from './assign'
import ComponentNode from './component'
import ConditionNode from './condition'
import ConfirmNode from './confirm'
import DatasourceNode from './datasource'
import EndNode from './end'
import MessageNode from './message'
import ScriptNode from './script'
import { StartNode } from './start'

const nodes = [
  StartNode,
  EndNode,
  AssignNode,
  ScriptNode,
  ConditionNode,
  DatasourceNode,
  MessageNode,
  ActionNode,
  ComponentNode,
  ConfirmNode,
]

export * from './base'
export { StartNode }

export function registerNodes(engine: Engine): void {
  nodes.forEach((node) => {
    engine.register({
      type: node.nodeTypeName,
      model: node,
    })
  })
}
