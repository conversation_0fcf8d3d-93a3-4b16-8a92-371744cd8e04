import type { IBaseNodeProps } from './base'
import { log } from 'sinitek-lowcode-shared'
import { ActionStatus } from '..'
import BaseNode from './base'

interface Properties {
  name: string
  [key: string]: any
}

export default class ActionNode extends BaseNode {
  static nodeTypeName = 'action-node'

  fnName: string = ''
  params: any[] = []
  constructor({ nodeConfig, context }: IBaseNodeProps<Properties>) {
    super({ nodeConfig, context })
    if (nodeConfig.properties) {
      this.fnName = nodeConfig.properties?.name ?? ''
    }
  }

  public async action(): Promise<BaseNode.ActionResult | undefined> {
    let msg = ''
    log('执行action节点', '--参数fnName:', this.fnName)
    try {
      // 执行函数
      await this.context.doAction(this.fnName)
    }
    catch (e: any) {
      console.error(e)
      msg = e.message as string
    }
    return {
      detail: {
        message: msg,
      },
      status: msg ? ActionStatus.ERROR : ActionStatus.SUCCESS,
    }
  }
}

export { ActionNode }
