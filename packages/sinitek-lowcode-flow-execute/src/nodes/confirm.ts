import type { IBaseNodeProps } from './base'
import { log } from 'sinitek-lowcode-shared'
import { ActionStatus } from '..'
import { runEval } from '../utils/eval'
import BaseNode from './base'

type ConfirmType = 'operation' | 'save' | 'submit' | 'send' | 'delete' | ''

interface Properties {
  message: string
  type: ConfirmType
  [key: string]: any
}

export default class ConfirmNode extends BaseNode {
  static nodeTypeName = 'confirm-node'

  message: string = ''
  type: ConfirmType = ''
  constructor({ nodeConfig, context }: IBaseNodeProps<Properties>) {
    super({ nodeConfig, context })
    if (nodeConfig.properties) {
      this.message = nodeConfig.properties?.message
      this.type = nodeConfig.properties?.type
    }
  }

  public async action(): Promise<BaseNode.ActionResult | undefined> {
    let msg = ''
    log('执行确认节点', '--参数message:', this.message, '--参数type:', this.type)
    let result = false
    if (!this.context?.utils?.getConfirm) {
      throw new Error('找不到utils.getConfirm方法')
    }
    try {
      const confirm = this.context.utils.getConfirm()
      if (this.type) {
        result = await confirm[this.type]()
      }
      else {
        const msg = await runEval(this.context, this.message)

        result = await confirm(msg)
      }
    }
    catch (e: any) {
      msg = e.message as string
      result = false
    }
    return {
      detail: {
        message: msg,
        result,
        // 添加泛型类型说明
        selectNode: (list: BaseNode.OutgoingConfig<any>[]) => {
          return list.filter(e => result ? e?.properties?.type : !e?.properties?.type)
        },
      },
      status: msg ? ActionStatus.ERROR : ActionStatus.SUCCESS,
    }
  }
}

export { ConfirmNode }
