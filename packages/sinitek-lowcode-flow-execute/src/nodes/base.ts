import type { Engine } from '..'
import type { Context } from '../index'
import { ActionStatus } from '..'
import { runEval } from '../utils/eval'

export interface IBaseNodeProps<T> {
  nodeConfig: BaseNode.NodeConfig<T>
  context: Context
}

export class BaseNode implements BaseNode.Base {
  readonly baseType: string
  static nodeTypeName = 'BaseNode'

  /**
   * 节点的入边
   */
  incoming: BaseNode.IncomingConfig[]
  /**
   * 节点的出边
   */
  outgoing: BaseNode.OutgoingConfig[]
  /**
   * 节点的属性
   */
  properties?: Record<string, unknown>
  nodeId: Engine.Key
  type: string
  /**
   * 节点的上下文，是调用流程时传入的上下文
   */
  context: Context
  /**
   * 节点的全局数据，是调用流程时传入的全局数据
   * 在计算表达式时，即基于全局数据进行计算
   */

  constructor({ nodeConfig, context }: IBaseNodeProps<Record<string, unknown>>) {
    const { outgoing, incoming, id, type, properties } = nodeConfig
    this.baseType = 'logic'
    this.outgoing = outgoing
    this.incoming = incoming
    this.nodeId = id
    this.type = type
    this.properties = properties

    this.context = context
  }

  /**
   * 节点的执行逻辑
   * @overridable 可以自定义节点重写此方法
   * @param _param 流程对象
   * @param _param.executionId 流程执行记录 ID
   * @param _param.actionId 此节点执行记录 ID
   * @param _param.nodeId 节点 ID
   * @return 返回下一步的执行参数
   * 当不返回时，表示此节点执行成功，流程会继续执行下一步。
   * 当返回时，返回格式为
   */
  public async action(
    _param?: Engine.ActionParam,
  ): Promise<BaseNode.ActionResult | undefined> {
    return undefined
  }

  /**
   * 节点重新恢复执行的逻辑
   * @overridable 可以自定义节点重写此方法
   * @param _params 流程对象
   * @param _params.executionId 流程执行记录 ID
   * @param _params.actionId 此节点执行记录 ID
   * @param _params.nodeId 节点 ID
   */
  public async onResume(_params: Engine.ResumeParam): Promise<void> {
    return undefined
  }

  /**
   * 判断该节点是否满足条件
   */
  private async isPass(properties?: Record<string, unknown>): Promise<boolean> {
    if (!properties)
      return true

    const { conditionExpression } = properties
    if (!conditionExpression)
      return true
    try {
      // bug：uuid 创建的 NodeId 为 xxxx-xxxx-xxxx-zzzz 格式，eval 执行时会将 - 识别为数学减号，导致执行报错
      // 解决方案： 赋值变量直接命名为 isPassResult, 因为每次执行 getExpressionResult 时，都会重新射程一个 context
      return await runEval(this.context, conditionExpression as string)
    }
    catch (error) {
      console.error('isPass error --->>>', error)
      return false
    }
  }

  /**
   * 获取当前节点执行的下一个节点
   */
  private async getOutgoing(): Promise<BaseNode.OutgoingConfig[]> {
    const outgoing: BaseNode.OutgoingConfig[] = []
    const expressions: any = []
    for (const item of this.outgoing) {
      const { properties } = item
      expressions.push(this.isPass(properties))
    }

    const result = await Promise.all(expressions)
    result.forEach((item, index) => {
      const out = this.outgoing[index]
      out.result = item
      outgoing.push(out)
    })
    return outgoing
  }

  /**
   * 节点的每一次执行都会生成一个唯一的 actionId
   */
  public async execute(
    params: Engine.ExecParam,
  ): Promise<Engine.NextActionParam> {
    const { executionId, actionId } = params
    const res = await this.action({
      nodeId: this.nodeId,
      executionId,
      actionId,
    })
    const status = res ? res.status : 'success'

    if (status === ActionStatus.SUCCESS) {
      let outgoing = await this.getOutgoing()
      const detail = res ? res.detail : {}
      if (detail?.selectNode) {
        outgoing = detail.selectNode(outgoing)
      }
      params.next({
        status: ActionStatus.SUCCESS,
        detail,
        nodeId: this.nodeId,
        nodeType: this.type,
        properties: this.properties,
        executionId,
        actionId,
        outgoing,
      })
    }
    return {
      status,
      detail: res?.detail,
      executionId,
      actionId,
      nodeId: this.nodeId,
      nodeType: this.type,
      properties: this.properties,
      outgoing: [],
    }
  }

  public async resume(params: Engine.ExecResumeParam): Promise<undefined> {
    const outgoing = await this.getOutgoing()
    await this.onResume({
      executionId: params.executionId,
      actionId: params.actionId,
      nodeId: params.nodeId,
      data: params.data,
    })

    params.next({
      executionId: params.executionId,
      actionId: params.actionId,
      nodeId: this.nodeId,
      nodeType: this.type,
      properties: this.properties,
      outgoing,
      status: ActionStatus.SUCCESS,
    })
    return undefined
  }
}

export namespace BaseNode {
  export interface Base {
    incoming: IncomingConfig[]
    outgoing: OutgoingConfig[]
    properties?: Record<string, unknown>
    nodeId: Engine.Key
    type: string
    readonly baseType: string
    execute: (actionParam: Engine.ExecParam) => Promise<Engine.NextActionParam | undefined>
  }

  export interface IncomingConfig<T = Record<string, unknown>> {
    id: Engine.Key
    source: string
    properties?: T
  }

  export interface OutgoingConfig<T = Record<string, unknown>> {
    id: Engine.Key
    target: string
    properties?: T
    result?: string | boolean
  }

  export interface NodeConfig<T = Record<string, unknown>> {
    id: Engine.Key
    type: string
    properties?: T
    incoming: IncomingConfig<T>[]
    outgoing: OutgoingConfig<T>[]
  }

  export interface NodeConstructor<T = Record<string, unknown>> {
    new(config: {
      nodeConfig: NodeConfig<T>
      context: Record<string, any>
    }): BaseNode
  }

  export interface ActionResult {
    status: ActionStatus
    detail?: {
      // 过滤当前节点下面的子节点
      selectNode?: <T>(list: BaseNode.OutgoingConfig<T>[]) => BaseNode.OutgoingConfig<T>[]
    } & Record<string, unknown>
  }
}

export default BaseNode
