import type { IBaseNodeProps } from './base'
import { log } from 'sinitek-lowcode-shared'
import { ActionStatus } from '..'
import { runEval } from '../utils/eval'
import BaseNode from './base'

interface Properties {
  name: string
  params: any[]
  [key: string]: any
}

export default class ScriptNode extends BaseNode {
  static nodeTypeName = 'script-node'

  fnName: string = ''
  params: any[] = []
  constructor({ nodeConfig, context }: IBaseNodeProps<Properties>) {
    super({ nodeConfig, context })
    if (nodeConfig.properties) {
      this.fnName = nodeConfig.properties?.name ?? ''
      this.params = nodeConfig.properties?.params ?? []
    }
  }

  public async action(): Promise<BaseNode.ActionResult | undefined> {
    let msg = ''
    log('执行script节点', '--参数params:', this.params, '--参数fnName:', this.fnName)
    try {
      const newParams = []
      // 把参数计算一遍
      for (const item of this.params) {
        const value = await runEval(this.context, item)
        newParams.push(value)
      }
      // 执行函数
      await this.context.methods[this.fnName](...newParams)
    }
    catch (e: any) {
      console.error(e)
      msg = e.message as string
    }
    return {
      detail: {
        message: msg,
      },
      status: msg ? ActionStatus.ERROR : ActionStatus.SUCCESS,
    }
  }
}

export { ScriptNode }
