import type { IBaseNodeProps } from './base'
import { log } from 'sinitek-lowcode-shared'
import { ActionStatus } from '..'
import { runEval } from '../utils/eval'
import BaseNode from './base'

interface Properties {
  target: string
  method: string
  params: any[]
  [key: string]: any
}

export default class ComponentNode extends BaseNode {
  static nodeTypeName = 'component-node'

  target: string = ''
  method: string = ''
  params: any[] = []
  constructor({ nodeConfig, context }: IBaseNodeProps<Properties>) {
    super({ nodeConfig, context })
    if (nodeConfig.properties) {
      this.target = nodeConfig.properties?.target ?? ''
      this.method = nodeConfig.properties?.method ?? ''
      this.params = nodeConfig.properties?.params ?? []
    }
  }

  public async action(): Promise<BaseNode.ActionResult | undefined> {
    let msg = ''
    log('执行组件节点', '--参数target:', this.target, '--参数method:', this.method, '--参数params:', this.params)
    try {
      const newParams = []
      // 把参数计算一遍
      for (const item of this.params) {
        const value = await runEval(this.context, item)
        newParams.push(value)
      }
      // 执行函数
      await this.context.refs[this.target][this.method](...newParams)
    }
    catch (e: any) {
      console.error(e)
      msg = e.message as string
    }
    return {
      detail: {
        message: msg,
      },
      status: msg ? ActionStatus.ERROR : ActionStatus.SUCCESS,
    }
  }
}

export { ComponentNode }
