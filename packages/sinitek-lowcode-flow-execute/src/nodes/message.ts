import type { IBaseNodeProps } from './base'
import { log } from 'sinitek-lowcode-shared'
import { ActionStatus } from '..'
import { runEval } from '../utils/eval'
import BaseNode from './base'

interface Properties {
  message: string
  type: string
  [key: string]: any
}

export default class MessageNode extends BaseNode {
  static nodeTypeName = 'message-node'

  message: string = ''
  type: string = ''
  constructor({ nodeConfig, context }: IBaseNodeProps<Properties>) {
    super({ nodeConfig, context })
    if (nodeConfig.properties) {
      this.message = nodeConfig.properties?.message
      this.type = nodeConfig.properties?.type
    }
  }

  public async action(): Promise<BaseNode.ActionResult | undefined> {
    let msg = ''
    log('执行消息节点', '--参数message:', this.message, '--参数type:', this.type)
    try {
      const msg = await runEval(this.context, this.message)
      if (!this.context?.utils?.message) {
        throw new Error('找不到utils.message方法')
      }
      this.context.utils.message(msg, this.type)
    }
    catch (e: any) {
      console.error(e)
      msg = e.message as string
    }
    return {
      detail: {
        message: msg,
      },
      status: msg ? ActionStatus.ERROR : ActionStatus.SUCCESS,
    }
  }
}

export { MessageNode }
