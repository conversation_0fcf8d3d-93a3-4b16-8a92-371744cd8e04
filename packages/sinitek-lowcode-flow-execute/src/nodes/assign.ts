import type { IBaseNodeProps } from './base'
import { log } from 'sinitek-lowcode-shared'
import { ActionStatus } from '..'
import { runEval } from '../utils/eval'
import BaseNode from './base'

function setByPath(set: (target: object, key: string | number, value: any) => void, obj: any, path: string, value: any): void {
  if (!path || typeof path !== 'string')
    return

  const keys = path.split(/[.[\]]/).filter(Boolean)
  const lastKey = keys.pop()
  let target = obj
  for (const key of keys) {
    if (!Number.isNaN(Number(key))) {
      // 如果 key 是数字字符串，则将其转换为数字索引
      const index = Number(key)
      if (!Array.isArray(target)) {
        target = set(target, index, [])
      }
      else if (!(index in target)) {
        target[index] = {}
      }
    }
    else if (!(key in target)) {
      target = set(target, key, {})
    }
    else {
      target = target[key]
    }
  }
  if (lastKey) {
    set(target, lastKey, value)
  }
}

type Assignments = { target: string, value: any }[]
interface Properties {
  assignments: Assignments
  [key: string]: any
}

export default class AssignNode extends BaseNode {
  static nodeTypeName = 'assign-node'

  param: Assignments = []
  constructor({ nodeConfig, context }: IBaseNodeProps<Properties>) {
    super({ nodeConfig, context })
    if (nodeConfig.properties?.assignments) {
      this.param = nodeConfig.properties?.assignments
    }
  }

  public async action(): Promise<BaseNode.ActionResult | undefined> {
    let msg = ''
    log('执行赋值节点', '--参数assignments:', this.param)
    try {
      for (const item of this.param) {
        const value = await runEval(this.context, item.value)
        setByPath(this.context.vm.$set, this.context.state, item.target, value)
      }
      await Promise.resolve()
    }
    catch (e: any) {
      console.error(e)
      msg = e.message as string
    }
    return {
      detail: {
        message: msg,
      },
      status: msg ? ActionStatus.ERROR : ActionStatus.SUCCESS,
    }
  }
}

export { AssignNode }
