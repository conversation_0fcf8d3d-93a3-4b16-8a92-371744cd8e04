/**
 * 存储执行记录
 */
import type { Engine } from '../index'

export default {
  setItem(key: Engine.Key, value: unknown) {
    if (typeof value === 'object') {
      value = JSON.stringify(value)
    }

    window.sessionStorage.setItem(key, value as string)
  },

  getItem<T = unknown>(key: Engine.Key): T {
    const value = window.sessionStorage.getItem(key)
    try {
      return JSON.parse(value || '') as T
    }
    catch (e) {
      console.error(e)
      return value as T
    }
  },

  removeItem(key: Engine.Key) {
    window.sessionStorage.removeItem(key)
  },

  clear() {
    window.sessionStorage.clear()
  },
}
