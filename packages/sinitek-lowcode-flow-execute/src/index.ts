import type Vue from 'vue'
import { FlowModel } from './FlowModel'
// import { LogicFlow } from '@logicflow/core';
import { BaseNode, registerNodes, StartNode } from './nodes/index'
import { Recorder } from './recorder'
import { createEngineId } from './utils'

export class Engine {
  readonly instanceId: string
  graphData?: Engine.GraphConfigData

  flowModel?: FlowModel
  recorder?: Recorder
  context: Context
  nodeModelMap: Map<string, BaseNode.NodeConstructor<object>>

  constructor(options: Engine.Options) {
    this.nodeModelMap = new Map()
    this.instanceId = createEngineId()
    if (options?.debug) {
      this.recorder = new Recorder({
        instanceId: this.instanceId,
      })
    }
    // 默认注册节点 register default nodes
    registerNodes(this)
    this.context = options.context
  }

  /**
   * 注册节点
   * @param nodeConfig { type: 'custom-node', model: NodeClass }
   */
  register(nodeConfig: Engine.NodeConfig): void {
    this.nodeModelMap.set(nodeConfig.type, nodeConfig.model)
  }

  /**
   * 自定义执行记录的存储，默认浏览器使用 sessionStorage, nodejs 使用内存存储
   * 注意：由于执行记录不全会主动删除，所以需要自行清理。
   * nodejs 环境建议自定义为持久化存储。
   * engine.setCustomRecorder({{
   *   async addActionRecord(task) {}
   *   async getTask(actionId) {}
   *   async getExecutionTasks(executionId) {}
   *   clear(instanceId) {}
   * }}
   * @param recorder
   */
  setCustomRecorder(recorder: Recorder): void {
    this.recorder = recorder
  }

  /**
   * 加载流程图数据
   */
  load({
    graphData,
    startNodeType = 'StartNode',
  }: Engine.LoadGraphParam): FlowModel {
    this.graphData = graphData
    const flowModel = new FlowModel({
      nodeModelMap: this.nodeModelMap,
      recorder: this.recorder,
      context: this.context,
      startNodeType,
    })

    flowModel.load(graphData)
    this.flowModel = flowModel
    return flowModel
  }

  /**
   * 执行流程，允许多次调用
   */
  async execute(
    param?: Partial<Engine.ActionParam>,
  ): Promise<Engine.NextActionParam> {
    return new Promise((resolve, reject) => {
      let execParam = param
      if (!param) {
        execParam = {}
      }

      this.flowModel?.execute({
        ...execParam,
        callback: (result) => {
          resolve(result)
        },
        onError: (error) => {
          reject(error)
        },
      })
    })
  }

  /**
   * 中断流程恢复
   * @param resumeParam
   * @returns 下一次执行参数
   */
  async resume(
    resumeParam: Engine.ResumeParam,
  ): Promise<Engine.NextActionParam | undefined> {
    return new Promise((resolve, reject) => {
      this.flowModel?.resume({
        ...resumeParam,
        callback: (result) => {
          resolve(result)
        },
        onError: (error) => {
          reject(error)
        },
      })
    })
  }

  async getExecutionList(): Promise<Engine.Key[] | undefined> {
    return await this.recorder?.getExecutionList()
  }

  /**
   * 获取执行任务记录
   * @param executionId
   * @returns 异步返回所有执行信息
   */
  async getExecutionRecord(
    executionId: Engine.Key,
  ): Promise<Recorder.Info[] | null> {
    const actions = await this.recorder?.getExecutionActions(executionId)

    if (!actions) {
      return null
    }

    // DONE: 确认 records 的类型
    const records: Promise<Recorder.Info>[] = []
    for (let i = 0; i < actions?.length; i++) {
      const action = actions[i]
      if (this.recorder) {
        records.push(this.recorder?.getActionRecord(action))
      }
    }

    return Promise.all(records)
  }

  destroy(): void {
    this.recorder?.clear()
  }
}

export namespace Engine {
  export interface Point {
    id?: string
    x: number
    y: number
    [key: string]: unknown
  }

  export type TextConfig = {
    value: string
  } & Point

  export interface NodeData {
    id: string
    type: string
    x?: number
    y?: number
    text?: TextConfig | string
    zIndex?: number
    properties?: Record<string, unknown>
  }

  export interface EdgeData {
    id: string
    /**
     * 边的类型，不传默认为lf.setDefaultEdgeType(type)传入的类型。
     * LogicFlow内部默认为polyline
     */
    type?: string
    sourceNodeId: string
    sourceAnchorId?: string
    targetNodeId: string
    targetAnchorId?: string
    startPoint?: {
      x: number
      y: number
    }
    endPoint?: {
      x: number
      y: number
    }
    text?: string | {
      x: number
      y: number
      value: string
    }

    pointsList?: Point[]
    zIndex?: number
    properties?: Record<string, unknown>
  }

  export interface GraphConfigData {
    nodes: NodeData[]
    edges: EdgeData[]
  }

  export interface LoadGraphParam {
    graphData: GraphConfigData
    startNodeType?: string
  }

  export interface Options {
    context: Context
    debug?: boolean
  }
  export type Key = string
  export interface NodeConfig {
    type: string
    model: any // TODO: NodeModel 可能有多个，类型该如何定义呢？？？
  }

  export interface NodeParam {
    executionId: Key
    nodeId: Key
  }

  export type CommonActionInfo = {
    actionId: Key
    [key: string]: unknown
  } & NodeParam

  export type ActionParam = CommonActionInfo
  //           ^?

  export type ResumeParam = {
    data?: Record<string, unknown>
  } & CommonActionInfo

  export type ExecParam = {
    next: (data: NextActionParam) => void
  } & ActionParam

  export type ExecResumeParam = {
    next: (data: NextActionParam) => void
  } & ResumeParam

  export type ActionStatus = 'success' | 'error' | 'interrupted' | '' // ??? Question: '' 状态是什么状态

  export type NextActionParam = CommonActionInfo & {
    nodeType: string
    outgoing: BaseNode.OutgoingConfig[]
    properties?: Record<string, unknown>
    detail?: Record<string, unknown>
    status?: ActionStatus
  }

  export type ActionResult = NextActionParam

  export type NodeExecResult = {
    nodeType: string
    properties?: Record<string, unknown>
  } & CommonActionInfo & ActionResult
}

export * from './constant'
export { BaseNode, Recorder }

export interface Context {
  vm: Vue
  state: Record<string, unknown>
  methods: Record<string, (...args: any[]) => void>
  fetcher: {
    [key: string]: {
      [key: string]: (params: Record<string, unknown>) => void
    }
  }
  utils: {
    message: (text: string, type: string) => void
    getConfirm: () => {
      (message: string): Promise<boolean>
      operation: () => Promise<boolean>
      save: () => Promise<boolean>
      submit: () => Promise<boolean>
      send: () => Promise<boolean>
      delete: () => Promise<boolean>
    }
    [key: string]: (...args: any[]) => void
  }

  doAction: (actionName: string) => Promise<void>
  refs: Record<string, any>
}

export interface ExecOption {
  context: Context
  graphData: Engine.LoadGraphParam['graphData']
}

export async function exec({ context, graphData }: ExecOption): Promise<Engine.NextActionParam> {
  const engine = new Engine({ context })
  engine.load({ graphData, startNodeType: StartNode.nodeTypeName })
  return await engine.execute()
}
