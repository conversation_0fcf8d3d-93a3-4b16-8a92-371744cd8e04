/* eslint-disable antfu/no-top-level-await */
/* eslint-disable prefer-rest-params */
/* eslint-disable no-console */
import Vue from 'vue'
import { exec } from '../src/index'
import mockAssign from './mock/confirm.json'

const vm = new Vue()

const state = {
  state1: '',
  state2: 333,
  state3: 444,
  condition1: 2,
}
const methods = {
  fn1() {
    console.log('fn1')
  },
  fn2() {
    console.log('fn2', arguments)
  },
}
const fetcher = {
  model1: {
    send() {
      console.log('send')
    },
    single() {
      console.log('single', arguments)
    },
  },
  enum1: {
    send() {
      console.log('[ enum1 ] >')
    },
  },
}
const flowData = {
  graphData: mockAssign,
  context: {
    state,
    methods,
    vm,
    fetcher,
    utils: {
      message: (text, type) => {
        console.log('[ text ] >', text, type)
      },
      getConfirm() {
        // async function fn() {
        // console.log('confirm fn')
        // if (window.confirm('sd')) {
        //   return true
        // }
        //   return false
        // }
        // fn.save = async (text) => {
        // console.log('[ text ] >', text)
        // if (window.confirm('sd')) {
        //   return true
        // }
        // return Promise.reject(false)
        // }
        // return fn
      },
    },
    doAction: async (name) => {
      console.log(name)
    },
  },
}
await exec(flowData)
