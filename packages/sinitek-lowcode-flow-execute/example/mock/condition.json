{"nodes": [{"id": "node_id_1", "type": "start-node", "x": 0, "y": 50, "properties": {"validateMsgs": []}}, {"id": "node_id_2", "type": "end-node", "x": -114, "y": 414, "properties": {"validateMsgs": []}}, {"id": "2qfu", "type": "condition-node", "x": -63, "y": 165, "properties": {"color": "#60a5fa", "validateMsgs": [], "condition": "state.condition1===1"}, "text": {"x": -63, "y": 125, "value": "condition1===1"}}, {"id": "ij01", "type": "assign-node", "x": -101, "y": 281, "properties": {"color": "#60a5fa", "validateMsgs": [], "assignments": [{"target": "state1", "value": "123"}]}}, {"id": "zmrh", "type": "assign-node", "x": -7, "y": 269, "properties": {"color": "#60a5fa", "validateMsgs": [], "assignments": [{"target": "state1", "value": "2222"}]}}, {"id": "70t9", "type": "end-node", "x": -7, "y": 419, "properties": {"color": "#4ade80", "validateMsgs": []}}], "edges": [{"id": "57d7c2ea-0f28-4e79-a4f8-d5b8da71cd64", "type": "logic-line", "properties": {}, "sourceNodeId": "node_id_1", "targetNodeId": "2qfu", "startPoint": {"x": 0, "y": 81.5}, "endPoint": {"x": -63, "y": 133.5}}, {"id": "718bbfa5-6fc7-4ee1-bbfb-007b4d54b0f7", "type": "logic-line", "properties": {"type": 1, "conditionExpression": "state.condition1==1"}, "sourceNodeId": "2qfu", "targetNodeId": "ij01", "startPoint": {"x": -63, "y": 196.5}, "endPoint": {"x": -101, "y": 249.5}, "text": {"x": -82, "y": 223, "value": "真"}}, {"id": "52adf90b-c9fc-4887-ba60-4d551450b4e0", "type": "logic-line", "properties": {}, "sourceNodeId": "ij01", "targetNodeId": "node_id_2", "startPoint": {"x": -101, "y": 312.5}, "endPoint": {"x": -114, "y": 382.5}}, {"id": "08328300-3f81-436f-af91-32e5e4199bd6", "type": "logic-line", "properties": {"type": 0, "conditionExpression": "!(state.condition1==1)"}, "sourceNodeId": "2qfu", "targetNodeId": "zmrh", "startPoint": {"x": -63, "y": 196.5}, "endPoint": {"x": -7, "y": 237.5}, "text": {"x": -35, "y": 217, "value": "假"}}, {"id": "52261f9f-1b93-40b5-9c7e-9ebaced10bc8", "type": "logic-line", "properties": {}, "sourceNodeId": "zmrh", "targetNodeId": "70t9", "startPoint": {"x": -7, "y": 300.5}, "endPoint": {"x": -7, "y": 387.5}}]}