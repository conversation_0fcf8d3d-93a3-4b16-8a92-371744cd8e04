import config from '../../config/eslint-base.mjs'
import { FlatCompat } from '@eslint/eslintrc'

const compat = new FlatCompat()

const v1 = compat.config({
  extends: [
    '@vue/standard',
    // Other extends...
  ],
})
const rules = {
  'new-cap': 'off',
  'vue/require-default-prop': 'off',
  camelcase: 'off',
  'vue/require-prop-types': 'off',
  'prefer-promise-reject-errors': 'off',
  'no-prototype-builtins': 'off',
  'no-tabs': 'off',
  'vue/no-template-shadow': 'off',
  eqeqeq: 'off',
  'no-unused-expressions': 'off',
  'no-eval': 'off',
  'valid-typeof': 'off',
  'no-void': 'off',
  'handle-callback-err': 'off',
  'vue/no-use-v-if-with-v-for': 'warn',
  'no-undef': 'warn',
  'no-unused-vars': 'warn',
  'standard/no-callback-literal': 'off',
  'no-new-func': 'off',
  'n/no-callback-literal': 'off',
  'import/export': 'off',
}
v1[2].rules = {
  ...v1[2].rules,
  ...rules,
}

const c = config('strongly-recommended', {
  rules,
  add: v1,
})
export default c
