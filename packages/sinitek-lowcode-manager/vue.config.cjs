const BundleAnalyzerPlugin =
  require('webpack-bundle-analyzer').BundleAnalyzerPlugin
const CopyWebpackPlugin = require('copy-webpack-plugin')
const CopyWebpackPluginOption = require('./build/copy-webpack-plugin-option.cjs')
const HtmlWebpackPlugin = require('html-webpack-plugin')
// const NodePolyfillPlugin = require('node-polyfill-webpack-plugin')
const MonacoPlugin = require('monaco-editor-webpack-plugin')

const path = require('path')
const webpack = require('webpack')
const appVersion = require('./package.json').version
function resolve(dir) {
  return path.resolve(__dirname, dir)
}

module.exports = {
  assetsDir: 'static', // 静态资源目录名称
  productionSourceMap: process.env.NODE_ENV === 'development', // 去掉打包的时候生成的map文件
  devServer: {
    proxy: {
      '/frontend': {
        target: process.env.PROXY_URL,
        ws: true, // 开启websocket配置
        changeOrigin: true,
        pathRewrite: {
          '^/': '',
        },
      },
    },
    client: {
      progress: true,
      overlay: {
        warnings: false,
        runtimeErrors: (error) => {
          if (
            error.message ===
            'ResizeObserver loop completed with undelivered notifications.'
          ) {
            return false
          }
          return true
        },
      },
    },
  },
  // sass全局变量配置
  css: {
    extract:
      process.env.NODE_ENV === 'development'
        ? {
            filename: 'css/[name].css',
            chunkFilename: 'css/[name].css',
          }
        : false, // 样式强制内联
  },
  configureWebpack: (config) => {
    // webpack简单配置，比如增加一个plugin
    const plugins = []

    plugins.push(
      new webpack.DefinePlugin({
        'process.env': {
          ...process.env,
          VUE_APP_VERSION: JSON.stringify(appVersion),
        },
      })
    )

    plugins.push(
      new webpack.ProvidePlugin({
        $: 'jquery',
        jquery: 'jquery',
        jQuery: 'jquery',
        'windows.jQuery': 'jquery',
      })
    )
    if (process.env.NODE_ENV === 'development') {
      plugins.push(
        new CopyWebpackPlugin({
          patterns: CopyWebpackPluginOption,
        })
      )
      // plugins.push(new NodePolyfillPlugin())
      plugins.push(
        new MonacoPlugin({
          languages: ['json', 'javascript', 'typescript', 'css'],
          features: ['!gotoSymbol'],
        })
      )
      config.devtool = 'source-map'
    }
    config.plugins = [...config.plugins, ...plugins]
  },
  chainWebpack: (config) => {
    // 配置别名

    config.resolve.alias
      .set('src', resolve('./src'))
      .set('assets', resolve('./src/assets'))
      .set('example', resolve('./example'))

    config.module.rule('svg').exclude.add(resolve('src/assets/svg')).end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/svg'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .end()

    if (process.env.NODE_ENV === 'production') {
      /* 为生产环境修改配置... */
      config.optimization.minimizer('terser').tap((options) => {
        options[0].terserOptions.compress.warnings = false
        options[0].terserOptions.compress.drop_console = false
        options[0].terserOptions.compress.drop_debugger = true
        options[0].terserOptions.compress.pure_funcs = [
          'console.log',
          'console.info',
        ]
        return options
      })

      // 配置webpack-bundle-analyzer打包分析插件
      config.plugin('webpack-report').use(BundleAnalyzerPlugin, [
        {
          analyzerMode: 'static',
          // openAnalyzer: false
        },
      ])
      // 打包时排除
      config.externals({
        'element-ui': 'element-ui',
        jquery: 'jquery',
        'sinitek-css': 'sinitek-css',
        'sinitek-util': 'sinitek-util',
        'sinitek-ui': 'sinitek-ui',
        sirmapp: 'sirmapp',
        'sinitek-workflow': 'sinitek-workflow',
        'monaco-editor': 'monaco-editor',
        '@iconify/vue2': '@iconify/vue2',
      })
    } else {
      /* 为开发环境修改配置... */
      config.entryPoints
        .clear()
        .end()
        .entry('main')
        .add(path.resolve(__dirname, './example/src/main.js'))

      // 修改 webpack-html-plugin 配置
      config.plugin('html').tap((args) => {
        return [
          // 传递给 html-webpack-plugin 构造函数的新参数
          {
            template: path.join(__dirname, './example', 'index.html'),
            title: process.env.VUE_APP_DEFAULT_TITLE,
            excludeChunks: ['canvas'],
          },
        ]
      })
      config.entry('canvas').add('./example/src/canvas.js')
      config.plugin('html-canvas').use(HtmlWebpackPlugin, [
        {
          template: 'example/canvas.html',
          filename: 'canvas.html',
          excludeChunks: ['app', 'main'],
        },
      ])
    }
  },
}
