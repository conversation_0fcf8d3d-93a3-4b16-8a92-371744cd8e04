'use strict'
const path = require('path')

function resolve(dir) {
  return path.resolve(__dirname, dir)
}

// modules是包的名称，例：'sirmapp', 'sinitek-workflow', 'sinitek-message'
const devModules = [
  'sirmapp',
  'sinitek-message',
  'sinitek-lowcode-simulator',
  'sinitek-lowcode-advanced',
]
const prodModules = ['sinitek-lowcode-simulator', 'sinitek-lowcode-advanced']

module.exports = (isDev = true) => {
  const options = []

  const modules = isDev ? devModules : prodModules
  for (const module of modules) {
    const moduleLocaPath = '../node_modules/'
    const jsOptions = {
      from: resolve(moduleLocaPath + module + '/dist/'),
      to: resolve('../dist/'),
      globOptions: {
        ignore: ['*.umd.min.js', `*simulator.css`],
      },
    }
    options.push(jsOptions)
  }
  return options
}
