import GlobalConstant from 'src/views/GlobalConstant'

export const UrlProxyAPI = {
  URLPROXY_LIST: () => GlobalConstant.getServerUrl() + '/lowcode/proxy/list',
  URLPROXY_SAVE: () => GlobalConstant.getServerUrl() + '/lowcode/proxy/save',
  URLPROXY_DETAIL: () =>
    GlobalConstant.getServerUrl() + '/lowcode/proxy/detail',
  URLPROXY_DELETE: () =>
    GlobalConstant.getServerUrl() + '/lowcode/proxy/delete',
  URLPROXY_EXPORT: () =>
    GlobalConstant.getServerUrl() + '/lowcode/proxy/exportbatch',
  URLPROXY_LIST_FILE: () =>
    GlobalConstant.getServerUrl() + '/lowcode/proxy/list/file',
  URLPROXY_IMPORT: () =>
    GlobalConstant.getServerUrl() + '/lowcode/proxy/import',
}
