import GlobalConstant from 'src/views/GlobalConstant'

export const FormDesignApi = {
  GET_FORM_WITH_DESIGN_DATA_BY_ID_ON_DESIGN: () =>
    GlobalConstant.getServerUrl() +
    '/lowcode/form/design/get-design-data-by-id',
  SAVE_FORM_DESIGN_DATA_ON_DESIGN: () =>
    GlobalConstant.getServerUrl() + '/lowcode/form/design/save-design-data',
}

export const FormRuntimeApi = {
  GET_PUBLISHED_FORM_WITH_DESIGN_DATA_BY_CODE: () =>
    GlobalConstant.getServerUrl() +
    '/lowcode/form/runtime/get-published-design-data-by-code',
}

export const FormAPI = {
  FORM_DETAIL_LATEST_BY_CODE: () =>
    GlobalConstant.getServerUrl() + '/lowcode/form/detail-latest',
  FORM_SAVE: () =>
    GlobalConstant.getServerUrl() + '/lowcode/form/save-or-update',
  FORM_DELETE: () => GlobalConstant.getServerUrl() + '/lowcode/form/delete',

  FORM_IMPORT: () => GlobalConstant.getServerUrl() + '/lowcode/form/import',
  FORM_VALIDATE_IMPORT: () =>
    GlobalConstant.getServerUrl() + '/lowcode/form/import-validate',
  FORM_EXPORT_BATCH: () =>
    GlobalConstant.getServerUrl() + '/lowcode/form/export',
  FORM_EXPORT_JSON: () =>
    GlobalConstant.getServerUrl() + '/lowcode/form/exportjson',

  FORM_HISTORY_SEARCH: () =>
    GlobalConstant.getServerUrl() + '/lowcode/form/history/search',

  FORM_PUBLISH: () => GlobalConstant.getServerUrl() + '/lowcode/form/publish',
  FORM_UPDATE: () => GlobalConstant.getServerUrl() + '/lowcode/form/update',
  FORM_HISTORY_APPLY: () =>
    GlobalConstant.getServerUrl() + '/lowcode/form/apply-history',
  FORM_SEARCH: () => GlobalConstant.getServerUrl() + '/lowcode/form/search',
  FORM_LIST_BY_FILE: () =>
    GlobalConstant.getServerUrl() + '/lowcode/form/list/file',
  // 不分页
  FORM_LIST_BY_MODULE_IDS: () =>
    GlobalConstant.getServerUrl() + '/lowcode/form/list-by-module-id',
  FORM_SYNC: () => GlobalConstant.getServerUrl() + '/lowcode/form/sync',
  FORM_REF_DETAIL: () =>
    GlobalConstant.getServerUrl() + '/lowcode/form/ref-detail',
  FORM_PAGE_DATA_LIST_BY_IDS: () =>
    GlobalConstant.getServerUrl() + '/lowcode/form/page-data/list-by-ids',
  FORM_LIST_PAGE_DATA_BY_OBJIDS: () =>
    GlobalConstant.getServerUrl() + '/lowcode/form/list-page-data-by-objids',
}
