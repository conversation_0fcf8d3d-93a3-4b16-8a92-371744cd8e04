import GlobalConstant from 'src/views/GlobalConstant'

export const ModuleAPI = {
  MODULE_LIST_TREE: () =>
    GlobalConstant.getServerUrl() + '/lowcode/module/list-tree',
  MODULE_SEARCH: () => GlobalConstant.getServerUrl() + '/lowcode/module/search',
  MODULE_SAVE: () => GlobalConstant.getServerUrl() + '/lowcode/module/save',
  MODULE_DETAIL: () => GlobalConstant.getServerUrl() + '/lowcode/module/detail',
  MODULE_SYNC: () => GlobalConstant.getServerUrl() + '/lowcode/module/sync',
  MODULE_DELETE: () => GlobalConstant.getServerUrl() + '/lowcode/module/delete',
  MODEL_EXPORT: () => GlobalConstant.getServerUrl() + '/lowcode/module/export',
  MODEL_EXPORT_JSON: () =>
    GlobalConstant.getServerUrl() + '/lowcode/module/export-form-json',
  MODEL_IMPORT: () => GlobalConstant.getServerUrl() + '/lowcode/module/import',
  MODEL_IMPORT_VALIDATE: () =>
    GlobalConstant.getServerUrl() + '/lowcode/module/import-validate',

  MODULE_PUBLISH_MENU: () =>
    GlobalConstant.getServerUrl() + '/lowcode/module/menu/publish',
  MODULE_MENU_DETAIL: () =>
    GlobalConstant.getServerUrl() + '/lowcode/module/menu/detail',
  MENU_LIST: () => GlobalConstant.getServerUrl() + '/lowcode/module/menu/list',
  RECOMMAND_COMPONENT_PROPS: () =>
    GlobalConstant.getServerUrl() +
    '/lowcode/llm/form/recommend-component-props',
  GENERATE_PAGE_SCHEMA: () =>
    GlobalConstant.getServerUrl() + '/lowcode/llm/form/generate-page-schema',
}
