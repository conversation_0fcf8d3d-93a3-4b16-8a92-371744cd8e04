import GlobalConstant from 'src/views/GlobalConstant'
import { easypost, easyget } from 'sinitek-util'
import { log } from 'sinitek-lowcode-shared'

let SQL_FRAGMENTS_CACHE = null

let DATA_STRATEGY_SQL_FRAGMENTS_CACHE = null

export const DataSetAPI = {
  DATABASE_DATASET_SEARCH: () =>
    GlobalConstant.getServerUrl() + '/lowcode/data-set/data-base/search',
  APPLICATION_DATASET_SEARCH: () =>
    GlobalConstant.getServerUrl() + '/lowcode/data-set/application/search',
  DATASET_SAVE: () =>
    GlobalConstant.getServerUrl() + '/lowcode/data-set/save-or-update',
  DATASET_DELETE: () =>
    GlobalConstant.getServerUrl() + '/lowcode/data-set/delete',
  DATASET_VALIDATE_IMPORT: () =>
    GlobalConstant.getServerUrl() + '/lowcode/data-set/import-validate',
  DATASET_IMPORT: () =>
    GlobalConstant.getServerUrl() + '/lowcode/data-set/import',
  DATASET_EXPORT: () =>
    GlobalConstant.getServerUrl() + '/lowcode/data-set/export',
  DATASET_DETAIL: () =>
    GlobalConstant.getServerUrl() + '/lowcode/data-set/detail',
  DATASET_SYNC: () => GlobalConstant.getServerUrl() + '/lowcode/data-set/sync',
  SQL_ANALYSE: () =>
    `${GlobalConstant.getServerUrl()}/lowcode/data-set/sql/analyse`,
  FIELD_LIST: () =>
    `${GlobalConstant.getServerUrl()}/lowcode/data-set/field/list`,
  DATASET_LIST_BY_FILE: () =>
    GlobalConstant.getServerUrl() + '/lowcode/data-set/list-by-file',
  SQL_FRAGMENT_LIST: (appCode) => {
    if (appCode) {
      return `${GlobalConstant.getServerUrl()}/lowcode/sql-fragment/list?appCode=${appCode}`
    } else {
      return `${GlobalConstant.getServerUrl()}/lowcode/sql-fragment/list`
    }
  },
  DATA_STRATEGY_SQL_FRAGMENT_LIST: (appCode) => {
    if (appCode) {
      return `${GlobalConstant.getServerUrl()}/lowcode/sql-fragment/data-strategy/list?appCode=${appCode}`
    } else {
      return `${GlobalConstant.getServerUrl()}/lowcode/sql-fragment/data-strategy/list`
    }
  },
  DATASET_COPY: function () {
    return GlobalConstant.getServerUrl() + `/lowcode/data-set/copy`
  },
  DATASET_LOAD_DATA: () =>
    GlobalConstant.getServerUrl() + '/lowcode/data-set/load-data',

  copyDataSet: function (param, resolve, reject) {
    easypost(this.DATASET_COPY(), param)
      .then((res) => {
        resolve && resolve(res)
      })
      .catch((err) => {
        reject && reject(err)
      })
  },
  // 删除数据集
  deleteDataSets: function (ids) {
    easypost(this.DATASET_DELETE(), ids)
      .then(() => {
        log(`删除${ids}数据集`)
      })
      .catch((err) => {
        console.warn('删除数据集', err)
      })
  },

  // 获取SQL片段
  getSqlFragmentList: function (appCode, resolve, reject) {
    if (Array.isArray(SQL_FRAGMENTS_CACHE)) {
      resolve && resolve(SQL_FRAGMENTS_CACHE)
    } else {
      easyget(this.SQL_FRAGMENT_LIST(appCode))
        .then((res) => {
          SQL_FRAGMENTS_CACHE = res
          resolve && resolve(SQL_FRAGMENTS_CACHE)
        })
        .catch((err) => {
          console.error('获取SQL片段失败 err', err)
          reject && reject(err)
        })
    }
  },

  // 获取数据策略SQL片段
  getDataStrategySqlFragmentList: function (appCode, resolve, reject) {
    if (Array.isArray(DATA_STRATEGY_SQL_FRAGMENTS_CACHE)) {
      resolve && resolve(DATA_STRATEGY_SQL_FRAGMENTS_CACHE)
    } else {
      easyget(this.DATA_STRATEGY_SQL_FRAGMENT_LIST(appCode))
        .then((res) => {
          DATA_STRATEGY_SQL_FRAGMENTS_CACHE = res
          resolve && resolve(DATA_STRATEGY_SQL_FRAGMENTS_CACHE)
        })
        .catch((err) => {
          console.error('获取SQL片段失败 err', err)
          reject && reject(err)
        })
    }
  },
}
