import GlobalConstant from 'src/views/GlobalConstant'
import { isUseNewAPI } from 'src/config/globalConfig'

function adapter(url, params) {
  return (
    '/lowcode/model/dynamic/' +
    (isUseNewAPI()
      ? `${params.pageCode || '-'}/${params.componentCode || '-'}/${params.modelCode}/${url}`
      : url)
  )
}

export const DataModelAPI = {
  MODEL_SEARCH: () => GlobalConstant.getServerUrl() + '/lowcode/model/search',
  MODEL_AVAILABLELIST: () =>
    GlobalConstant.getServerUrl() + '/lowcode/model/list-available',
  MODEL_REFLIST: () =>
    GlobalConstant.getServerUrl() + '/lowcode/model/list-ref',
  MODEL_DETAIL: () => GlobalConstant.getServerUrl() + '/lowcode/model/detail',
  MODEL_SAVE: () => GlobalConstant.getServerUrl() + '/lowcode/model/save',
  MODEL_DELETE: () => GlobalConstant.getServerUrl() + '/lowcode/model/delete',
  MODEL_IMPORT: () => GlobalConstant.getServerUrl() + '/lowcode/model/import',
  MODEL_IMPORT_VALIDATE: () =>
    GlobalConstant.getServerUrl() + '/lowcode/model/import-validate',
  MODEL_EXPORT: () => GlobalConstant.getServerUrl() + '/lowcode/model/export',
  MODEL_LIST_BY_FILE: () =>
    GlobalConstant.getServerUrl() + '/lowcode/model/list-by-file',
  MODEL_RELATED_LIST: () =>
    GlobalConstant.getServerUrl() + '/lowcode/model/related/list',
  MODEL_SYNC: () => GlobalConstant.getServerUrl() + '/lowcode/model/sync',
  GENERATOR_DTO: () =>
    GlobalConstant.getServerUrl() + '/lowcode/model/generator-dto',
  GENERATOR_INTERCEPTOR: () =>
    GlobalConstant.getServerUrl() + '/lowcode/model/generator-interceptor',
  MODEL_LOAD: () => GlobalConstant.getServerUrl() + '/lowcode/model/load',
  MODEL_RUNTIME_LOAD: () =>
    GlobalConstant.getServerUrl() + '/lowcode/model/runtime/load',
  MODEL_DYNAMIC_READ: (params) =>
    GlobalConstant.getServerUrl() + adapter('search', params),
  MODEL_DYNAMIC_SAVE: (params) =>
    GlobalConstant.getServerUrl() + adapter('save', params),
  MODEL_DYNAMIC_DELETE_BY_KEY: (params) =>
    GlobalConstant.getServerUrl() + adapter('delete-by-key', params),
  MODEL_DYNAMIC_UPDATE_BY_KEY: (params) =>
    GlobalConstant.getServerUrl() + adapter('update-by-key', params),
  MODEL_DYNAMIC_LOAD_DETAIL_BY_KEY: (params) =>
    GlobalConstant.getServerUrl() + adapter('load-detail-by-key', params),

  // 获取模型拦截器全路径
  GET_MODEL_OP_INTERCEPTOR_CANONICAL_NAME: () =>
    GlobalConstant.getServerUrl() +
    '/lowcode/model/get-model-op-interceptor-canonical-name',
  // 获取自定义模型处理器全路径
  GET_CUSTOM_MODEL_HANDLER_CANONICAL_NAME: () =>
    GlobalConstant.getServerUrl() +
    '/lowcode/model/get-custom-model-handler-canonical-name',
  // 获取属性处理器全路径
  GET_CUSTOM_PROP_HANDLER_CANONICAL_NAME: () =>
    GlobalConstant.getServerUrl() +
    '/lowcode/model/prop/get-custom-prop-handler-canonical-name',
  GET_PUBLISHED_DESIGN_DATA_BY_CODE: () =>
    GlobalConstant.getServerUrl() +
    '/lowcode/form/runtime/get-published-design-data-by-code',
}
