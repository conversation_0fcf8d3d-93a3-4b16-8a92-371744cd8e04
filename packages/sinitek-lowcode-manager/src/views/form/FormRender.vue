<template>
  <Render :mode="DesignMode.PUBLISH" :schema="schema"></Render>
</template>

<script>
import Render from '../components/simulator/render.vue'
import { DataModelAPI } from 'src/api'
import { DesignMode } from 'sinitek-lowcode-shared'
export default {
  name: 'LcFormRender',
  components: {
    Render,
  },
  data() {
    return {
      schema: {},
      DesignMode,
    }
  },
  mounted() {
    this.loadModel()
  },
  methods: {
    loadModel() {
      this.$http
        .get(DataModelAPI.GET_PUBLISHED_DESIGN_DATA_BY_CODE(), {
          code: this.$route.params.code,
        })
        .then((res) => {
          if (!res?.data?.id) return
          if (!res?.data?.pageData) {
            this.$message.error('未获取到数据！将关闭当前页面')
            this.$closeTab()
            return
          }
          this.schema = res?.data?.pageData || {}
        })
    },
  },
}
</script>
