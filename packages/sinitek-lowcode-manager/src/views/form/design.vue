<template>
  <Simulator
    :id="$route.query.id"
    ref="simulator"
    :module-code="$route.query.moduleCode"
  ></Simulator>
</template>

<script>
export default {
  name: 'FormDesign',
  components: {
    Simulator: () => import('../components/simulator/simulator.vue'),
  },
  beforeRouteLeave(to, from, next) {
    this.checkChange(next)
  },
  beforeRouteUpdate(to, from, next) {
    // 设计tab切换时判断
    if (to.query?.id !== from.query?.id) {
      this.checkChange(next)
    } else {
      next()
    }
  },
  mounted() {
    // 缓存当前store的数据
    this.item = this.$store.getters.openRoutes.find(
      (e) => e.fullPath === this.$route.fullPath
    )
  },
  methods: {
    checkChange(done) {
      if (this.$refs?.simulator?.checkChange?.()) {
        this.$confirm('有改动未保存，是否放弃修改!')
          .then(() => {
            done()
          })
          .catch(() => {
            this.$nextTick(() => {
              // 将tab还原
              if (
                !this.$store.state.router.cachedRoutes.includes(this.item.name)
              ) {
                this.$store.state.router.openRoutes.push(this.item)
                this.$store.state.router.cachedRoutes.push(this.item.name)
              }
            })
          })
      } else {
        done()
      }
    },
  },
}
</script>
