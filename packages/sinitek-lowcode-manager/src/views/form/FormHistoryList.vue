<template>
  <div id="FormHistoryList" class="app-container">
    <xn-table :url="url" default-order="publish_version:desc" ref="table">
      <xn-col
        prop="code"
        label="编码"
        min-width="180px"
        align="left"
        sortable="true"
        show-overflow-tooltip
      />
      <xn-col
        prop="name"
        label="名称"
        min-width="180px"
        align="left"
        show-overflow-tooltip
      />
      <xn-col prop="type" label="类型" width="80px" align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.type == 1"> PC页面 </template>
          <template v-else-if="scope.row.type == 2"> PC弹框 </template>
          <template v-else-if="scope.row.type == 3"> APP页面 </template>
          <template v-else-if="scope.row.type == 4"> APP弹框 </template>
        </template>
      </xn-col>
      <xn-col
        prop="publishVersion"
        label="版本号"
        min-width="80px"
        align="right"
        sort-by="publish_version"
        sortable="true"
        show-overflow-tooltip
      />
      <xn-col
        prop="publishStatus"
        label="版本状态"
        min-width="100px"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ currentVersionStatusLabel(scope.row.publishStatus) }}
        </template>
      </xn-col>
      <xn-col
        prop="description"
        label="描述"
        min-width="200px"
        align="left"
        show-overflow-tooltip
      />
      <xn-col
        prop="publishDate"
        label="发布时间"
        width="180"
        align="center"
        sortable="true"
      />
      <xn-col
        prop="createTimeStamp"
        label="创建时间"
        width="180"
        align="center"
        sortable="true"
      />
      <xn-col
        prop="updateTimeStamp"
        label="更新时间"
        width="180"
        align="center"
        sortable="true"
      />
      <xn-col label="操作" min-width="140" align="center" fixed="right">
        <template slot-scope="scope">
          <xn-col-action-group :keys="scope.row">
            <xn-col-action
              @click="handleApply(scope.row)"
              v-if="
                scope.row.publishStatus === VERSION_STATUS.HISTORY_PUBLISHED ||
                scope.row.publishStatus === VERSION_STATUS.DEPRECATED
              "
            >
              应用
            </xn-col-action>
            <xn-col-action
              @click="handlePreview(scope.row)"
              v-if="scope.row.type === 1 || scope.row.type === 2"
            >
              预览
            </xn-col-action>
          </xn-col-action-group>
        </template>
      </xn-col>
    </xn-table>
  </div>
</template>

<script>
import { FormAPI } from 'src/api'
import GlobalConstant from 'src/views/GlobalConstant'
import { handleErrorMessage } from 'src/utils'
import { VERSION_STATUS_LABELS, VERSION_STATUS } from 'src/constant'
import { isMultiAppMode } from 'src/config'
export default {
  name: 'LcFormHistoryList',
  data: function () {
    return {
      url: `${FormAPI.FORM_HISTORY_SEARCH()}?id=${this.$route.params.formId}`,
      isMultiAppMode: isMultiAppMode(),
      appCode: '',
      appName: '',
      moduleName: this.$route.query.moduleName,
      VERSION_STATUS,
    }
  },
  created() {
    if (this.isMultiAppMode) {
      if (this.$route.query.appCode) {
        this.appCode = this.$route.query.appCode
      }
      this.appName = this.$route.query.title.split('-')[0] + '-'
    }
  },
  methods: {
    currentVersionStatusLabel(status) {
      return VERSION_STATUS_LABELS[status] || ''
    },
    // 表格刷新
    query() {
      this.$refs.table.query()
    },
    // 应用历史版本
    handleApply(row) {
      this.$http.get(FormAPI.FORM_HISTORY_APPLY(), { id: row.id }).then(
        (res) => {
          if (res !== null && res.resultcode !== '0') {
            this.$message.error(res.message || '应用该版本失败')
          } else {
            this.$message.success('应用成功')
            this.query()
          }
        },
        (err) => {
          handleErrorMessage(this, err)
        }
      )
    },
    // 表单设计
    handleDesign(row) {
      const query = {
        id: row.id,
        moduleCode: row.moduleCode,
        title: this.appName + row.name + '-立即生效',
      }
      if (this.isMultiAppMode) {
        query.appCode = this.appCode
      }
      this.$openTab(GlobalConstant.MAKINGPATH, { query })
    },
    // 预览
    handlePreview(row) {
      const query = {
        id: row.id,
        moduleCode: row.moduleCode,
        title: '预览',
      }
      if (this.isMultiAppMode) {
        query.appCode = this.appCode
      }
      this.$openTab(GlobalConstant.PREVIEWPATH, { query })
    },
  },
}
</script>

<style scoped></style>
