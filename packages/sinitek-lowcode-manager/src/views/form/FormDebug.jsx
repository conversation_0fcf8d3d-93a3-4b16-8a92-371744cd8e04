import { DesignMode } from 'sinitek-lowcode-shared'
import SinitekLowcodeRender from 'sinitek-lowcode-render'
import { decode } from 'js-base64'
import fetcherMixins from '../components/simulator/fetcherMixins'
import cloneDeep from 'lodash/cloneDeep'
export default {
  name: 'LcFormDebug',
  mixins: [fetcherMixins(DesignMode.DEBUG)],
  data() {
    return {
      DesignMode,
    }
  },
  methods: {
    init() {
      // 不实现任何逻辑，给mixins中的fetcherMixins使用，防止找不到方法报错
    },
  },
  mounted() {
    const material = JSON.parse(decode(this.$route.query.material))
    const renderConfig = JSON.parse(decode(this.$route.query.config))
    const componentConfig = material.snippets[0].schema
    const fetcher = this.config.fetcher
    const componentOptions = {
      name: componentConfig.componentName,
      props: material.props.map((e) => e.name),
      material,
      provide() {
        return {
          lowcodeProps: this.$props,
          lowcodeEmit: this.emit,
          lowcodeScope: {
            mode: DesignMode.DESIGN,
          },
        }
      },
      render() {
        return (
          <div>
            <SinitekLowcodeRender
              fetcher={fetcher}
              config={renderConfig}
              isLCComponent
              ref="render"
            />
          </div>
        )
      },
      mounted() {
        // 实现methods调用
        // 下划线开头和生命周期不会被调用
        const methods = this.$refs.render.getMethods()
        if (Object.keys(methods).length) {
          Object.entries(methods).forEach(([key, value]) => {
            this[key] = value
          })
        }
      },
      methods: {
        emit(...args) {
          this.$emit(...args)
        },
      },
    }
    this.LCName = componentOptions.name
    const config = {
      componentName: 'Page',
      datasource: [],
      state: {},
      methods: {},
      children: [componentConfig],
      id: Math.random().toString(36).slice(2),
    }
    window.__LCComponents__[componentOptions.name] = cloneDeep(componentOptions)
    window.__LCMaterials__[componentOptions.name] = material

    this.$refs.simulator.setSchema(config)
  },
  render() {
    return (
      <SinitekLowcodeSimulator
        class="app-container"
        ref="simulator"
        config={this.config}
        mode="design"
      />
    )
  },
  destroyed() {
    setTimeout(() => {
      this.LCName && delete window.__LCComponents__[this.LCName]
    })
  },
}
