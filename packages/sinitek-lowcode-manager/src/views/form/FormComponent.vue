<template>
  <SinitekLowcodeSimulator
    v-if="isActivated"
    ref="simulator"
    class="app-container"
    :config="config"
    :mode="DesignMode.LOWCODE"
    @hook:mounted="onMounted"
  />
</template>

<script>
import { DesignMode } from 'sinitek-lowcode-shared'
import fetcherMixins from '../components/simulator/fetcherMixins'
import { LcComponentAPI } from 'src/api'
import { encode } from 'js-base64'
export default {
  name: 'LcFormComponent',
  mixins: [fetcherMixins(DesignMode.LOWCODE)],
  data() {
    return {
      DesignMode,
      isActivated: true,
      loading: false,
      toolbarManager: null,
    }
  },
  mounted() {
    this.isActivated = true
    this.$nextTick(() => {
      this.init()
    })
  },
  activated() {
    this.isActivated = true
    this.$nextTick(() => {
      this.init()
    })
  },
  deactivated() {
    this.destroy()
  },
  beforeDestroy() {
    this.destroy()
  },
  methods: {
    destroy() {
      const query = JSON.parse(
        sessionStorage.getItem('LCComponentData') || '{}'
      )
      if (query.id) {
        const schema = this.$doc.getSchema(true)
        const material = schema.material || {}
        delete schema.material
        this.$http.post(
          LcComponentAPI.SAVE_DESIGN_DATA(),
          {
            id: query.id,
            designData: encode(JSON.stringify(schema)),
            materialData: encode(JSON.stringify(material)),
          },
          { loading: true }
        )
      }
      this.isActivated = false
    },
    init() {
      if (!this.isActivated) return
      this.load()
    },
    load() {
      const data = JSON.parse(sessionStorage.getItem('LCComponentData') || '{}')
      const id = data.id
      if (!id) return
      // 请求接口获取表单数据
      this.$http
        .get(LcComponentAPI.GET_FORM_WITH_DESIGN_DATA_BY_ID_ON_DESIGN(), {
          id,
        })
        .then((res) => {
          if (!this.isActivated) return
          const dv = {
            componentName: 'Page',
            datasource: [],
            state: {},
            methods: {},
            children: [],
            id: Math.random().toString(36).slice(2),
          }
          const v = res.data?.pageData ?? dv
          if (res.data?.materialData) {
            v.material = res.data.materialData
          }
          if (this.isLoaded) {
            this.$refs.simulator.setSchema(v)
            this.isSet = true
          } else {
            this.pageData = res.data?.pageData ?? dv
          }
        })
    },
    onMounted() {
      this.isLoaded = true
      if (!this.isSet && this.pageData) {
        this.$refs.simulator.setSchema(this.pageData)
        this.isSet = true
      }
      this.$nextTick(() => {
        this.$refs.simulator.register({
          area: 'topArea',
          id: 'ToolbarPageProp',
          name: 'pageProp',
          title: '页面属性',
          component: () =>
            import('../components/simulator/toolbar/pageProp.vue'),
          align: 'right',
          order: 5,
        })
        this.$refs.simulator.register({
          area: 'topArea',
          id: 'ToolbarExport',
          name: 'export',
          title: '导出',
          component: () => import('../components/simulator/toolbar/export.vue'),
          align: 'right',
          order: 5,
        })
        this.$refs.simulator.register({
          area: 'topArea',
          id: 'ToolbarComponentSave',
          name: 'componentSave',
          title: '保存',
          component: () =>
            import('../components/simulator/toolbar/lowcodePreviewAndSave.vue'),
          align: 'right',
          order: 5,
        })
      })
    },
  },
}
</script>
