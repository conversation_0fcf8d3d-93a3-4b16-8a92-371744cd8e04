<template>
  <xn-dialog
    :title="title"
    width="520px"
    :show.sync="display"
    :buttons="buttons"
    @save="handleSave"
    @close="handleClose"
  >
    <template slot="dialog-form">
      <el-form :model="model" :rules="rules" label-width="100px" ref="form">
        <el-form-item label="编码" prop="code">
          <span v-if="!!model.id">{{ model.code }}</span>
          <el-input
            v-else
            v-model.trim="model.code"
            class="dialog-input"
            :maxlength="50 - initCodeLen"
          >
            <span slot="prepend" v-if="isMultiAppMode">
              {{ getAppCode() }}-
            </span>
            <div
              slot="suffix"
              style="display: flex; align-items: center; height: 100%"
            >
              <span>{{ model.code.length + initCodeLen }}/50</span>
            </div>
          </el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="model.type">
            <el-radio
              v-for="item in dataType === ModuleConstant.TREE_TYPE_PC_FORM
                ? PC_FORM_TYPE_OPTIONS
                : APP_FORM_TYPE_OPTIONS"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input
            v-model.trim="model.name"
            class="dialog-input"
            show-word-limit
            :maxlength="50"
          />
        </el-form-item>
        <el-form-item label="所属模块" prop="moduleName">
          {{ model.moduleName }}
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model.trim="model.description"
            class="dialog-input"
            type="textarea"
            :rows="6"
            show-word-limit
            :maxlength="300"
          />
        </el-form-item>
      </el-form>
    </template>
  </xn-dialog>
</template>

<script>
import { FormAPI, FormDesignApi } from 'src/api'
import { validateFormCode } from 'src/utils'
import {
  ModuleConstant,
  FORM_TYPE,
  APP_FORM_TYPE_OPTIONS,
  PC_FORM_TYPE_OPTIONS,
} from 'src/constant'
import { isMultiAppMode } from 'src/config'
export default {
  name: 'MetaFormFormDialog',
  props: {
    title: {
      type: String,
      default: '新增',
    },
  },
  inject: ['getAppCode', 'getCurrentModuleCode'],
  data() {
    return {
      APP_FORM_TYPE_OPTIONS: Object.freeze(APP_FORM_TYPE_OPTIONS),
      PC_FORM_TYPE_OPTIONS: Object.freeze(PC_FORM_TYPE_OPTIONS),
      dataType: ModuleConstant.TREE_TYPE_PC_FORM,
      ModuleConstant,
      display: false,
      buttons: [
        { action: 'save', type: 'primary', event: 'save' },
        { action: 'cancel', type: 'info', event: 'close' },
      ],
      rules: {
        code: [
          { required: true, message: '请填写编码' },
          {
            validator: validateFormCode,
            message:
              '表单编码必须以字母开头，由英文字母、数字、连字符（-）组成',
          },
        ],
        type: [{ required: true, message: '请选择类型' }],
        name: [{ required: true, message: '请填写名称' }],
      },
      model: {
        appCode: '',
        moduleCode: '',
        id: '',
        code: '',
        type: '',
        name: '',
        path: '',
        description: '',
      },
      isMultiAppMode: isMultiAppMode(),
      initCodeLen: 0,
    }
  },
  created() {
    if (this.getAppCode()) {
      this.initCodeLen = this.getAppCode().length + 1
    }
  },
  methods: {
    show() {
      this.display = true
    },
    setDataType(type) {
      this.dataType = type
    },
    async resetFields(data) {
      if (data.id) {
        await this.$http
          .get(FormDesignApi.GET_FORM_WITH_DESIGN_DATA_BY_ID_ON_DESIGN(), {
            id: data.id,
          })
          .then((res) => {
            this.resetValue(res.data)
          })
      } else {
        this.resetValue(data)
      }
    },
    resetValue(data) {
      this.model.appCode = data.appCode || ''
      this.model.moduleCode =
        this.getCurrentModuleCode() || data.moduleCode || ''
      this.model.id = data.id || ''
      this.model.name = data.name || ''
      if (data.type) {
        this.model.type = data.type
      } else {
        if (this.dataType === ModuleConstant.TREE_TYPE_PC_FORM) {
          this.model.type = FORM_TYPE.PC_PAGE
        }
      }

      // 拆分code
      if (this.isMultiAppMode) {
        if (data?.code && data.code !== '') {
          if (data.code.startsWith(this.getAppCode())) {
            this.model.code = data.code.slice(this.getAppCode().length + 1)
          }
        } else {
          this.model.code = ''
        }
      } else {
        this.model.code = data.code || ''
      }
      this.model.moduleName = data.moduleName || ''
      this.model.description = data.description || ''
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
      })
    },
    handleSave(resolve, reject) {
      if (this.isMultiAppMode && (!this.model.appCode || !this.getAppCode())) {
        reject('缺少应用数据，无法保存')
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          const form = { ...this.model }
          // 组合code
          if (this.isMultiAppMode) {
            form.code = this.getAppCode() + '-' + this.model.code
          }
          this.$http
            .post(FormAPI.FORM_SAVE(), form)
            .then(() => {
              resolve()
              this.handleClose()
              this.$emit('refreshTableData')
            })
            .catch((error) => {
              // 禁用http请求报错的默认提示
              this.$message.closeAll()
              reject(error)
            })
        } else {
          reject()
        }
      })
    },
    handleClose() {
      this.display = false
      this.$refs.form.clearValidate()
    },
  },
}
</script>

<style scoped>
.dialog-input {
  width: 350px;
}
</style>
