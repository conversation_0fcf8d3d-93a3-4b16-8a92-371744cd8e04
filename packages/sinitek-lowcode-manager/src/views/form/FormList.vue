<template>
  <div id="FormList">
    <xn-table
      ref="table"
      :url="searchUrl"
      :querymodel="queryForm"
      :post-query="true"
      :toolbars="toolbars"
      default-order="code:asc"
      @add="handleAdd"
      @delete="handleDelete"
      @importtemplate="importTemplate"
      @exportbatch="exportBatch"
      @exportjson="exportJson"
      @generateFormCode="generateFormCode"
    >
      <div v-if="showSearch" slot="search">
        <el-form :model="queryForm" :inline="true" is-query-input>
          <el-form-item label="编码">
            <xn-input
              v-model.trim="queryForm.code"
              placeholder="请输入编码"
              clearable
            />
          </el-form-item>
          <el-form-item label="名称">
            <xn-input
              v-model.trim="queryForm.name"
              placeholder="请输入名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="发布状态">
            <xn-select
              v-model="queryForm.publishStatus"
              :options="VERSION_STATUS_ENUM"
              clearable
              multiple
              sorted
              filterable
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="handleBtnQueryClick"
            >
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <template #publish>
        <xn-button
          type="primary"
          svg-icon="check"
          plain
          :show-loading="publishBtnIsShowLoading"
          @click="handlePublish"
        >
          发布
        </xn-button>
      </template>
      <template slot="sync">
        <div>
          <sync-dialog
            ref="syncDialog"
            type="form"
            :ref-table="$refs.table"
            :app-info="appInfo"
          />
        </div>
      </template>
      <xn-col prop="id" align="center" type="selection" min-width="40px" />
      <xn-col
        prop="code"
        label="编码"
        min-width="180px"
        align="left"
        sortable="true"
        show-overflow-tooltip
      />
      <xn-col
        prop="name"
        label="名称"
        min-width="180px"
        align="left"
        show-overflow-tooltip
      />
      <xn-col
        prop="publishVersion"
        label="版本号"
        min-width="80px"
        align="right"
        show-overflow-tooltip
      />
      <xn-col
        prop="publishStatus"
        label="版本状态"
        min-width="100px"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ currentVersionStatusLabel(scope.row.publishStatus) }}
        </template>
      </xn-col>
      <xn-col
        prop="description"
        label="描述"
        min-width="200px"
        align="left"
        show-overflow-tooltip
      />
      <xn-col
        prop="createTimeStamp"
        label="创建时间"
        width="180"
        align="center"
        sortable="true"
      />
      <xn-col
        prop="updateTimeStamp"
        label="更新时间"
        width="180"
        align="center"
        sortable="true"
      />
      <xn-col label="操作" min-width="140" align="center" fixed="right">
        <template slot-scope="scope">
          <xn-col-action-group :keys="scope.row">
            <xn-col-action
              v-for="(colBtn, btnIndex) in dealOper(scope.row)"
              :key="btnIndex"
              @click="handleClickOpera(scope, colBtn)"
            >
              {{ colBtn.label }}
            </xn-col-action>
          </xn-col-action-group>
        </template>
      </xn-col>
    </xn-table>
    <sync-dialog
      ref="syncDialogSingle"
      type="form"
      :is-sync-all="false"
      :is-related="true"
      :app-info="appInfo"
    />
    <form-dialog
      ref="formDialog"
      :title="title"
      :forms="forms"
      @refreshTableData="queryAll"
    />
    <xn-dialog
      id="dlgFormListImport"
      ref="importRef"
      title="表单导入"
      :show.sync="importWindow"
      width-size="small"
      :buttons="importButtons"
      @save="showImportList"
      @cancel="handleDlgFormListImportCancel"
    >
      <xn-form
        id="formDlgFormListImport"
        slot="dialog-form"
        ref="formDlgFormListImportRef"
        :model="importData"
        size="small"
        :rules="formDataRules"
        label-width="150px"
      >
        <xn-form-item label="表单模板" prop="importTemplate">
          <xn-upload
            v-if="importWindow"
            ref="upload"
            v-model="importData.importFile"
            mode="upload"
            :read-only="false"
            accept=".json,.zip"
            :file-preview="{ isEnable: false }"
            @onError="importError"
          />
          <div style="color: red">
            提示：只允许上传从当前功能模块下导出的数据
          </div>
        </xn-form-item>
        <xn-form-item label="是否自动发布表单" prop="autoPublishFlag">
          <el-switch v-model="importData.autoPublishFlag"> </el-switch>
        </xn-form-item>
      </xn-form>
    </xn-dialog>

    <xn-dialog
      id="dlgDataSetTable"
      ref="dlgDataSetImport"
      title="导入数据列表"
      :show.sync="showTable"
      width-size="small"
      :buttons="buttons"
      @save="handleDlgFormListImportSave"
      @cancel="
        () => {
          showTable = false
        }
      "
    >
      <el-tabs v-model="tabName">
        <el-tab-pane label="覆盖数据" name="overrideData"></el-tab-pane>
        <el-tab-pane label="新导入数据" name="newImportData"></el-tab-pane>
      </el-tabs>
      <el-table :data="tableData[tabName]" border>
        <el-table-column prop="code" label="编码"> </el-table-column>
        <el-table-column prop="name" label="名称"> </el-table-column>
      </el-table>
    </xn-dialog>
  </div>
</template>

<script>
import { FormAPI, FormDesignApi, EnumDesignAPI } from 'src/api'
import { operation } from 'sinitek-util'
import { isMultiAppMode } from 'src/config'
import {
  ModuleConstant,
  VERSION_STATUS_LABELS,
  VERSION_STATUS,
} from 'src/constant'

import GlobalConstant from 'src/views/GlobalConstant'
export default {
  name: 'FormList',
  components: {
    FormDialog: () => import('./components/FormDialog'),
    syncDialog: () => import('../components/syncDialog.vue'),
  },
  inject: [
    'getAppCode',
    'getAppName',
    'getAppInfo',
    'getCurrentModuleCode',
    'getCurrentModuleName',
  ],
  props: {
    showSearch: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: ModuleConstant.TREE_TYPE_PC_FORM,
    },
  },
  data() {
    return {
      searchUrl: Object.freeze(FormAPI.FORM_SEARCH()),
      VERSION_STATUS_ENUM: [],
      ModuleConstant: Object.freeze(ModuleConstant),
      toolbars: [],
      title: '表单管理',
      forms: [],
      formDataRules: {
        importTemplate: [
          { validator: this.validateImportTemplate, required: true },
        ],
      },
      importData: {},
      importWindow: false,
      importButtons: [
        { label: '确定', action: 'import', type: 'primary', event: 'save' },
        { label: '取消', action: 'cancel', type: 'info', event: 'cancel' },
      ],
      // 查询参数
      queryForm: {
        name: '',
        code: '',
        queryType: this.type,
        moduleCode: this.getCurrentModuleCode(),
      },
      operations: [],
      publishStatusOperations: [],
      VERSION_STATUS,
      isMultiAppMode: isMultiAppMode(),
      publishBtnIsShowLoading: false,
      showTable: false,
      tabName: 'overrideData',
      tableData: {
        overrideData: [],
        newImportData: [],
      },
      buttons: [
        { label: '确定', action: 'save', type: 'primary', event: 'save' },
        { label: '取消', action: 'cancel', type: 'info', event: 'cancel' },
      ],
      importParam: {},
    }
  },
  computed: {
    appInfo() {
      return this.getAppInfo()
    },
    dealOper() {
      return function (row) {
        const tempOperations = [...this.operations]

        if (row.publishStatus === VERSION_STATUS.PUBLISHED) {
          return this.publishStatusOperations
        } else {
          if (row.publishStatus === VERSION_STATUS.UN_PUBLISHED) {
            for (let i = 0; i < tempOperations.length; i++) {
              if (tempOperations[i].label === '同步') {
                tempOperations.splice(i, 1)
                break
              }
            }
          }
          return tempOperations
        }
      }
    },
  },
  created() {
    this.queryForm.moduleCode = this.getCurrentModuleCode()
    this.queryForm.queryType = this.type
    this.initToolbarsAndOperations(this.type)
  },
  mounted() {
    this.initToolbarsAndOperations(this.type)
    this.loadVersionStatusEnum()
  },
  methods: {
    loadVersionStatusEnum() {
      this.$http
        .post(EnumDesignAPI.ENUM_LOAD(), {
          catalog: 'COMMON',
          type: 'version-status',
        })
        .then((res) => {
          const data = Array.isArray(res.data) ? res.data : []
          this.VERSION_STATUS_ENUM = data
        })
        .catch((err) => {
          console.warn('版本状态枚举加载出错,', err)
        })
    },
    getTableSelection() {
      const tableRef = this.$refs.table
      if (tableRef) {
        return tableRef.multipleSelection
      } else {
        return null
      }
    },
    currentVersionStatusLabel(status) {
      return VERSION_STATUS_LABELS[status] || ''
    },
    query() {
      this.$refs.table?.query()
    },
    // 表单新增
    handleAdd() {
      this.title = '新增'
      this.$refs.formDialog.setDataType(this.type)
      this.$refs.formDialog.show()
      this.$refs.formDialog.resetFields({
        moduleCode: this.getCurrentModuleCode(),
        moduleName: this.getCurrentModuleName(),
        appCode: this.getAppCode(),
      })
    },
    // 表单编辑
    handleEdit(row) {
      this.title = '编辑'
      this.$refs.formDialog.setDataType(this.type)
      this.$refs.formDialog.show()
      this.$refs.formDialog.resetFields(row)
    },
    // 表单删除
    handleDelete(row) {
      this.$confirm('请确认是否彻底删除该表单及其所有历史版本', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
      })
        .then(() => {
          this.$confirm('表单被删除后无法恢复，请确认', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
          })
            .then(() => {
              const objids = []
              if (Array.isArray(row)) {
                row.forEach((item) => objids.push(item.id))
              } else {
                objids.push(row.id)
              }
              this.$http
                .post(FormAPI.FORM_DELETE(), { ids: objids })
                .then(() => {
                  this.$message.deleteWithSuccess()
                  this.queryAll()
                })
            })
            .catch(() => {
              console.warn('用户取消是否彻底删除该表单及其所有历史版本')
            })
        })
        .catch(() => {
          console.warn('用户取消是否彻底删除该表单及其所有历史版本')
        })
    },
    // 预览
    handlePreview(row) {
      console.warn('暂不支持预览', row)
      this.$message.info('暂不支持预览')
    },
    // 表单设计
    handleDesign(row) {
      const query = {
        id: row.id,
        moduleCode: row.moduleCode,
        moduleId: row.moduleId,
        title: this.getAppName() + row.name + '-表单设计',
      }
      if (this.isMultiAppMode) {
        query.appCode = this.appInfo.code
      }
      this.$openTab(GlobalConstant.MAKINGPATH, { query })
    },
    setPublishBtnIsShowLoading(val) {
      this.publishBtnIsShowLoading = val
    },
    // 版本发布
    handlePublish(row) {
      row = this.getTableSelection()
      if (!row || row.length <= 0) {
        this.$message.info('请选择要发布的数据')
      } else {
        // 开发发布，发布按钮添加loading状态
        this.setPublishBtnIsShowLoading(true)
        this.$confirm
          .publish()
          .then(() => {
            const objids = []
            if (Array.isArray(row)) {
              row.forEach((item) => objids.push(item.id))
            } else {
              objids.push(row.id)
            }
            this.$http
              .post(FormAPI.FORM_PUBLISH(), { ids: objids })
              .then(() => {
                // 发布成功后发布按钮取消loading
                this.setPublishBtnIsShowLoading(false)
                this.$message.publishWithSuccess()
                this.query()
              })
              .catch(() => {
                // 发布成功后发布按钮取消loading
                this.setPublishBtnIsShowLoading(false)
              })
          })
          .catch(() => {
            // 发布成功后发布按钮取消loading
            this.setPublishBtnIsShowLoading(false)
          })
      }
    },
    // 版本更新
    handleUpdate(row) {
      this.$confirm.update().then(() => {
        this.$http
          .get(FormAPI.FORM_UPDATE(), { id: row.id })
          .then(() => {
            this.$message.success('更新成功')
            this.query()
          })
          .catch((e) => {
            this.$message.error(e.message || '更新失败')
          })
      })
    },
    handleShowHistory(row) {
      const vm = this
      const query = {
        title: this.getAppName() + '表单历史',
        moduleName: row.moduleName,
      }
      if (this.isMultiAppMode) {
        query.appCode = this.appInfo.code
      }
      const callback = () => {
        vm.query()
      }
      this.$openTab('/lowcode/form/history-list/' + row.id, { query }, callback)
    },
    getFormDetail(code) {
      return new Promise((resolve, reject) => {
        this.$http
          .post(FormAPI.FORM_DETAIL_LATEST_BY_CODE(), { code })
          .then((res) => {
            resolve(res.data)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    getFormDetailById(id) {
      return this.$http.get(FormAPI.FORM_DETAIL, { id })
    },
    /**
     * 生成表单
     */
    handleAddForm(model) {
      return new Promise((resolve, reject) => {
        this.$http
          .post(FormAPI.FORM_SAVE(), model)
          .then((res) => {
            resolve(res)
          })
          .catch((e) => {
            this.$message.closeAll()
            reject(e)
          })
      })
    },
    /**
     * 保存表单数据
     */
    setAppPageData(id, data) {
      return new Promise((resolve, reject) => {
        this.$http
          .post(FormDesignApi.SAVE_FORM_DESIGN_DATA_ON_DESIGN(), {
            id,
            designData: window.btoa(data),
          })
          .then((res) => {
            resolve(res)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    importError(error, file, fileList) {
      console.warn('导入失败', error, file, fileList)
      this.$refs.importRef.displayErrorMessage(error.message)
    },

    validateImportTemplate(rule, value, callback) {
      // 获取上传文件
      const template = this.$refs.upload.fileList
      if (template.length === 0) {
        callback(new Error('请上传表单模板文件'))
      } else {
        callback()
      }
    },
    importTemplate() {
      const vm = this
      vm.importWindow = true
    },
    showImportList(resolve, reject) {
      this.$refs.formDlgFormListImportRef.validate((valid) => {
        if (valid) {
          const param = {
            importFile: this.importData.importFile,
            autoPublishFlag: this.importData.autoPublishFlag,
            moduleCode: this.getCurrentModuleCode(),
          }
          this.importParam = param
          this.$easypost(FormAPI.FORM_VALIDATE_IMPORT(), param).then(
            (res) => {
              resolve('导入校验成功')
              this.handleDlgFormListImportCancel()
              this.showTable = true
              this.tableData = res
            },
            () => {
              reject()
            }
          )
          // this.$easypost(DataSetAPI.DATASET_LIST_BY_FILE(), param)
          //   .then((result) => {
          //     if (Array.isArray(result) && result.length > 0) {
          //       let dataSetName = ''
          //       const replaceDataSets = {}
          //       result.forEach((data) => {
          //         replaceDataSets[data.code] = data.id
          //         dataSetName += `【${data.name}(${data.code})】`
          //       })
          //       if (dataSetName !== '') {
          //         const h = this.$createElement
          //         this.$confirm('', '提示', {
          //           message: h(
          //             'div',
          //             {
          //               style: {
          //                 maxHeight: '400px',
          //                 overflowY: 'auto',
          //                 wordBreak: 'break-all',
          //               },
          //             },
          //             [
          //               h(
          //                 'div',
          //                 null,
          //                 '数据集' + dataSetName + '已存在，是否覆盖？'
          //               ),
          //             ]
          //           ),
          //         }).then(
          //           (res) => {
          //             this.saveDataSet(resolve, reject, replaceDataSets)
          //           },
          //           () => {
          //             reject()
          //           }
          //         )
          //       } else {
          //         this.saveDataSet(resolve, reject, null)
          //       }
          //     } else {
          //       this.saveDataSet(resolve, reject, null)
          //     }
          //   })
          //   .catch((error) => {
          //     // 禁用http请求报错的默认提示
          //     this.$message.closeAll()
          //     reject(error)
          //   })
        } else {
          reject()
        }
      })
    },
    // 导入列表弹框中，数据模型的保存按钮
    handleDlgFormListImportSave(resolve, reject) {
      this.$easypost(FormAPI.FORM_IMPORT(), this.importParam)
        .then(() => {
          resolve('导入成功')
          this.importWindow = false
          this.showTable = false
          this.queryAll()
        })
        .catch((error) => {
          // 禁用http请求报错的默认提示
          this.$message.closeAll()
          reject(error)
        })
    },
    // （导入时在验证code后）保存数据
    importForm(resolve, reject, repeatedData) {
      this.$refs.formDlgFormListImportRef.validate((valid) => {
        if (valid) {
          const param = {
            importFile: this.importData.importFile,
            moduleCode: this.getCurrentModuleCode(),
            repeatedData,
          }
          if (this.isMultiAppMode) {
            param.appCode = this.getAppCode()
          }
          this.$easypost(FormAPI.FORM_IMPORT(), param)
            .then((result) => {
              resolve(result)
              // 关闭弹出框
              this.handleDlgFormListImportCancel()
              this.query()
            })
            .catch((error) => {
              // 禁用http请求报错的默认提示
              this.$message.closeAll()
              reject(error)
            })
        } else {
          reject()
        }
      })
    },
    handleDlgFormListImportCancel() {
      this.importWindow = false
      this.importData.importFile = []
      this.$refs.upload.clear()
      // 清空验证信息
      this.$refs.formDlgFormListImportRef.clearValidate()
    },
    exportBatch(data) {
      if (data.length > 0) {
        let objids = []
        for (const i in data) {
          if (i === '0') {
            objids = [data[i].id]
          } else {
            objids.push(data[i].id)
          }
        }
        operation.download(FormAPI.FORM_EXPORT_BATCH(), {
          ids: objids,
          prettyPrinterFlag: true,
        })
      } else {
        this.$message.info('请选择要导出的表单')
      }
    },
    exportJson(data) {
      if (data.length > 0) {
        let objids
        for (const i in data) {
          if (i === '0') {
            objids = data[i].id
          } else {
            objids += ',' + data[i].id
          }
        }
        operation.download(FormAPI.FORM_EXPORT_JSON() + '?objids=' + objids)
      } else {
        this.$message.info('请选择要导出的表单')
      }
    },
    generateFormCode(data) {
      if (Array.isArray(data) && data.length > 0) {
        const ids = data.map((item) => item.id)
        this.$easypost(FormAPI.FORM_PAGE_DATA_LIST_BY_IDS(), { ids })
          .then((result) => {
            if (Array.isArray(result) && result.length > 0) {
              this.generateFormCodeFile(result)
            } else {
              this.$message.info('获取到的表单数据为空')
            }
          })
          .catch((error) => {
            console.error(error)
            this.$message.error('获取表单数据失败')
          })
      } else {
        this.$message.info('请选择要生成代码的表单')
      }
    },
    generateFormCodeFile(data) {
      console.warn('暂不支持生成代码功能', data)
      this.$message.info('暂不支持生成代码功能')
      // const that = this
      // if (data.length === 1) {
      //   if (!data[0].pageData) {
      //     this.$message.info(`${data[0].code}表单为空,无法生成代码`)
      //     return
      //   }
      //   // 生成单文件
      //   const { code, pageData } = data[0]
      //   const decodePageData = decodePageDataFunc(pageData)
      //   console.log('decodePageData', decodePageData)
      //   // const generatedAssist = generateCode(decodePageData, { formCode: code })
      //   const generatedAssist = ''
      //   const blob = new Blob([generatedAssist], {
      //     type: 'text/plain;charset=utf-8',
      //   })
      //   saveAs(blob, `${code}.vue`)
      // } else {
      //   // 生成zip
      //   const zip = new JSZip()
      //   data.forEach(({ code, pageData }) => {
      //     const decodePageData = decodePageDataFunc(pageData)
      //     console.log('decodePageData', decodePageData)
      //     // const generatedAssist = generateCode(decodePageData, { formCode: code })
      //     const generatedAssist = ''
      //     zip.file(`${code}.vue`, generatedAssist)
      //   })
      //   zip.generateAsync({ type: 'blob' }).then(function (content) {
      //     saveAs(content, `${that.getCurrentModuleCode()}.zip`)
      //   })
      // }
    },
    handleBtnQueryClick() {
      this.query()
    },
    queryAll() {
      this.query()
      this.$emit('afterSave')
    },
    handleClickSyncSingle(row) {
      this.$refs.syncDialogSingle.selectionsData = [row]
      this.$refs.syncDialogSingle.handleDataSetSync()
    },
    handleClickOpera(scope, item) {
      this[item.functionName](scope.row)
    },
    initToolbarsAndOperations(type) {
      this.toolbars = [
        { label: '新增', type: 'add', event: 'add' },
        {
          label: '删除',
          icon: 'icon-shanchu',
          type: 'delete',
          event: 'delete',
        },
        {
          type: 'slot',
          name: 'publish',
        },
        {
          label: '导入',
          type: 'import',
          event: 'importtemplate',
        },
        {
          label: '导出',
          icon: 'icon-daochu',
          type: 'button',
          event: 'exportbatch',
        },
      ]
      this.operations = [
        // {
        //   label: '表单设计',
        //   functionName: 'handleDesign',
        // },
        {
          label: '版本历史',
          functionName: 'handleShowHistory',
        },
        {
          label: '编辑',
          functionName: 'handleEdit',
        },
      ]
      this.publishStatusOperations = [
        {
          label: '立即生效',
          functionName: 'handleDesign',
        },
        {
          label: '版本历史',
          functionName: 'handleShowHistory',
        },
        {
          label: '更新',
          functionName: 'handleUpdate',
        },
      ]
      if (this.isMultiAppMode && ModuleConstant.TREE_TYPE_PC_FORM === type) {
        // this.toolbars.push({
        //   type: 'slot',
        //   name: 'sync',
        // })
        // this.publishStatusOperations.push({
        //   label: '同步',
        //   functionName: 'handleClickSyncSingle',
        // })
      }
      this.handlePcForm(type)
    },
    handlePcForm(type) {
      if (ModuleConstant.TREE_TYPE_PC_FORM === type) {
        // this.toolbars.push({
        //   label: '导出表单JSON',
        //   icon: 'icon-daochu',
        //   type: 'button',
        //   event: 'exportjson',
        // })
        // this.toolbars.push({
        //   label: '生成代码',
        //   icon: 'icon-daochu',
        //   type: 'button',
        //   event: 'generateFormCode',
        // })
        // this.operations.push({
        //   label: '预览',
        //   functionName: 'handlePreview',
        // })
        // this.operations.push({
        //   label: '转换',
        //   functionName: 'handleTransform',
        // })
        // this.publishStatusOperations.push({
        //   label: '预览',
        //   functionName: 'handlePreview',
        // })
        // this.publishStatusOperations.push({
        //   label: '转换',
        //   functionName: 'handleTransform',
        // })
      }
    },
  },
}
</script>
<style>
/**
  提示弹框高度
   */
.el-message-box__content {
  overflow: auto;
  max-height: 200px;
}
</style>
