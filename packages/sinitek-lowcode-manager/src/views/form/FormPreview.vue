<template>
  <Render
    :mode="DesignMode.PREVIEW"
    :id="$route.query.id"
    :module-code="$route.query.moduleCode"
  ></Render>
</template>

<script>
import Render from '../components/simulator/render.vue'
import { DesignMode } from 'sinitek-lowcode-shared'
export default {
  name: 'LcFormPreview',
  components: {
    Render,
  },
  data() {
    return {
      DesignMode,
    }
  },
}
</script>
