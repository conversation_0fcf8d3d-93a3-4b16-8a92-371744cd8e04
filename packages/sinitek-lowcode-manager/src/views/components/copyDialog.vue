<template>
  <div>
    <xn-dialog
      :title="dlgTitle"
      :show.sync="display"
      :buttons="buttons"
      @save="handleSave"
      @close="handleClose"
    >
      <xn-form
        id="copyDlgForm"
        :model="model"
        slot="dialog-form"
        class="copy-dlg-form"
      >
        <xn-form-item label="编码" prop="code">
          <xn-input
            v-model.trim="model.code"
            :maxlength="defaultCodeMaxLength - initCodeLen"
            placeholder="请输入编码"
          >
            <span slot="prepend" v-if="isMultiAppMode"> {{ appCode }}- </span>
            <div slot="suffix" class="code-input-suffix">
              <span
                >{{ model.code.length + initCodeLen }}/{{
                  defaultCodeMaxLength
                }}</span
              >
            </div>
          </xn-input>
        </xn-form-item>
        <xn-form-item label="名称" prop="name">
          <xn-input
            v-model.trim="model.name"
            placeholder="请输入名称"
            :maxlength="defaultNameMaxLength"
            show-word-limit
          />
        </xn-form-item>
      </xn-form>
    </xn-dialog>
  </div>
</template>
<script>
import { isMultiAppMode } from 'src/config'
export default {
  name: 'CopyDialog',
  inject: ['getAppCode'],
  props: {
    dlgTitle: {
      type: String,
      default: '',
    },
    handleCopy: {
      type: Function,
      default: () => {},
    },
    defaultCodeMaxLength: {
      type: Number,
      default: 50,
    },
    defaultNameMaxLength: {
      type: Number,
      default: 50,
    },
  },
  data() {
    return {
      display: false,
      buttons: Object.freeze([
        { label: '确定', action: 'save', type: 'primary', event: 'save' },
        { label: '取消', action: 'cancel', type: 'info', event: 'close' },
      ]),
      model: {
        id: '',
        code: '',
        name: '',
      },
      appCode: '',
      initCodeLen: 0,
      isMultiAppMode: isMultiAppMode(),
    }
  },
  created() {
    if (this.isMultiAppMode) {
      this.appCode = this.getAppCode()
      this.initCodeLen = this.appCode.length + 1
    }
  },
  methods: {
    show(param) {
      this.display = true
      this.model.id = param.id
      if (this.isMultiAppMode) {
        if (param?.code && this.appCode) {
          if (param.code.startsWith(this.appCode)) {
            this.model.code = param.code.slice(this.appCode.length + 1)
          } else {
            // 存在编码但是不以应用编码开头,直接重置为空
            this.model.code = ''
          }
        } else {
          this.model.code = ''
        }
      } else {
        this.model.code = param.code
      }

      this.model.name = param.name
    },
    handleSave(resolve, reject) {
      let code = this.model.code
      if (this.isMultiAppMode) {
        // 多应用此处拼接应用编码
        code = this.appCode + '-' + this.model.code
      }
      if (this.handleCopy) {
        this.handleCopy({
          ...this.model,
          code,
        })
          .then(() => {
            this.display = false
            this.resetData()
            this.$emit('copy-success')
            resolve()
          })
          .catch((error) => {
            console.error('复制出错', error)
            this.$emit('copy-failure')
            reject()
          })
      }
    },
    handleClose() {
      this.display = false
      this.resetData()
    },
    resetData() {
      this.model.id = ''
      this.model.code = ''
      this.model.name = ''
    },
  },
}
</script>
<style scoped>
.copy-dlg-form {
  padding-right: 50px;
}
.code-input-suffix {
  display: flex;
  align-items: center;
  height: 100%;
}
</style>
