<template>
  <el-button
    size="mini"
    class="lc-publish"
    float="导出对应的文件"
    @click="callExport"
  >
    导出
  </el-button>
</template>

<script>
// 低代码组件的导出
import { LcComponentAPI } from 'src/api'
import JSZip from 'jszip'
import FileSaver from 'file-saver'
export default {
  name: 'LCToolbarPublish',
  inject: ['$doc'],
  mounted() {
    this.query = JSON.parse(sessionStorage.getItem('LCComponentData') || '{}')
    if (!this.query.id) {
      this.$closeTab()
    }
  },
  methods: {
    async callExport() {
      const { getLowcode, getLCMaterial, getRenderConfig } = await import(
        'sinitek-lowcode-simulator'
      )
      // 修改默认值
      const schema = this.$doc.getSchema(true)
      if (schema?.material?.reflect) {
        schema.material.reflect
          .filter((e) => e.id && e.value != undefined)
          .forEach((item) => {
            item.defaultValue = this.$doc.node.getNode(item.id).props[
              item.value
            ]
          })
      }
      const vueComponent = await getLowcode(this.query.code, schema)
      schema.material = schema.material || {}
      schema.material.componentName = this.query.code
      let material = await getLCMaterial(schema.material, (id) => {
        return this.$doc.getMaterial(this.$doc.node.getNode(id).componentName)
      })
      if (this.query.name) {
        material = material.replace(/低代码组件/g, this.query.name)
      }
      const config = await getRenderConfig(schema)
      // 把数据保存到接口，方便下次修改
      this.$http
        .post(
          LcComponentAPI.COMPONENT_SAVE(),
          {
            code: this.query.code,
            name: this.query.name,
            id: this.query.id,
            pageData: this.$doc.getSchema(true),
          },
          { loading: true }
        )
        .then(() => {
          this.$message.success('导出成功!')
          // 下载文件
          const zip = new JSZip()
          zip.file(this.query.code + '.js', vueComponent)
          zip.file(this.query.code + '-meta.js', material)
          zip.file(this.query.code + '-config.js', config)
          zip.generateAsync({ type: 'blob' }).then((content) => {
            FileSaver(content, this.query.code + '.zip')
          })
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.lc-publish {
  cursor: pointer;
  margin-left: 10px;
  height: 30px;
  border: 1px solid var(--xn-color-primary);
  border-radius: 2px;
  width: 58px;
  color: var(--xn-color-primary);
  font-size: 14px;
}
</style>
