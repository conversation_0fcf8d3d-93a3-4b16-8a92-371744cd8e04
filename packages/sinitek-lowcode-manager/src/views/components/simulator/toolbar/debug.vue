<template>
  <div class="lc-debug" @click="debug" float="调试">调试</div>
</template>

<script>
// import { SimulatorAPI } from 'src/api/index'
import { encode } from 'js-base64'
export default {
  name: '<PERSON>CDebug',
  inject: ['$doc'],
  data() {
    return {
      loading: false,
    }
  },
  methods: {
    async debug() {
      const { getLCMaterial, getRenderConfig } = await import(
        'sinitek-lowcode-simulator'
      )
      // debug
      // eslint-disable-next-line no-unused-vars
      const componentName = 'LC' + Math.random().toString(36).substr(2)
      const schema = this.$doc.getSchema(true)
      if (schema?.material?.reflect) {
        schema.material.reflect.forEach((item) => {
          item.defaultValue = this.$doc.node.getNode(item.id).props[item.value]
        })
      }
      const material = getLCMaterial(schema.material, (id) => {
        return this.$doc.getMaterial(this.$doc.node.getNode(id).componentName)
      })
      const props = new Map(
        Object.entries(
          Object.groupBy(schema?.material?.reflect ?? [], ({ id }) => id)
        )
      )
      const config = getRenderConfig(schema, props)

      const result = eval(`(${material})`)
      this.$openTab('/lowcode/form/form-debug', {
        query: {
          material: encode(JSON.stringify(result)),
          config: encode(JSON.stringify(config)),
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.lc-debug {
  cursor: pointer;
  margin-left: 10px;
}
</style>
