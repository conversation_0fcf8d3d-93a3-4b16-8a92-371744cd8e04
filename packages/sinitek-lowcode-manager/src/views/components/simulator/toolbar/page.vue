<template>
  <div class="lc-gen-page">
    <el-button size="mini" class="gradient-text" @click="openDialog">
      <img src="~@/assets/ai.png" alt="ai" />
      生成页面</el-button
    >
    <!-- 使用element-ui添加弹框页面里面有表单，表单项是模型，字段，ai模板，自然语言补充。 -->
    <xn-dialog
      ref="dialog"
      width-size="small"
      :buttons="buttons"
      :title="dialogParam.title"
      :show.sync="dialogParam.show"
      @save="submitForm"
      @cancel="closeDialog"
    >
      <template #dialog-form>
        <xn-form ref="ruleForm" :rules="rules" :model="model" show-ellipsis>
          <!-- 省略... -->
          <xn-form-item label="类型" prop="modelCode">
            <xn-select
              v-model="model.formType"
              required
              size="mini"
              :options="formType"
              placeholder="请选择"
              @change="handleFormTypeChange"
            >
            </xn-select>
          </xn-form-item>
          <!-- 省略... -->
          <xn-form-item
            v-if="model.formType === 'model'"
            label="模型"
            prop="modelCode"
          >
            <xn-select
              v-model="model.modelCode"
              :options="modelCode"
              placeholder="请选择"
              size="mini"
              @change="handleChange"
            >
            </xn-select>
          </xn-form-item>
          <!-- 省略... -->
          <xn-form-item
            v-if="model.formType === 'model'"
            label="字段"
            prop="fields"
          >
            <xn-select
              v-model="model.fields"
              :options="fields"
              multiple
              size="mini"
              placeholder="请选择"
            >
            </xn-select>
          </xn-form-item>
          <xn-form-item
            v-if="model.formType === 'normal'"
            label="Controller"
            prop="controllers"
          >
            <xn-input
              v-model="model.controllers"
              type="textarea"
              size="mini"
              placeholder="com.sinitek.xxxx.AaaController 多个用逗号分隔"
            />
          </xn-form-item>
          <xn-form-item
            v-if="model.formType === 'model'"
            label="页面类型"
            prop="fields"
          >
            <xn-select
              v-model="model.pageType"
              required
              size="mini"
              :options="pageType"
              default-value="JS"
            >
            </xn-select>
          </xn-form-item>
          <xn-form-item required label="AI模版" prop="formContext">
            <div class="gp-cards">
              <div
                v-for="item of formContextList"
                :key="item.value"
                class="gp-card"
              >
                <div
                  class="right"
                  :class="{ active: model.formContext === item.value }"
                  @click="() => (model.formContext = item.value)"
                >
                  <el-image :src="item.img"> </el-image>
                  <div>
                    <div class="title">{{ item.title }}</div>
                    <div class="desc">{{ item.desc }}</div>
                  </div>
                </div>
              </div>
            </div>
          </xn-form-item>
          <xn-form-item label="自然语言补充" prop="prompt">
            <xn-input
              v-model="model.prompt"
              :maxlength="1000"
              size="mini"
              type="textarea"
              placeholder="暂不支持"
              show-word-limit
            />
          </xn-form-item>
        </xn-form>
      </template>
    </xn-dialog>
  </div>
</template>

<script>
import { DataModelAPI, ModuleAPI } from 'src/api'
import { COMPONENT_TYPE } from 'src/constant'
import submit from 'assets/submit.png'
import search from 'assets/search.png'
import curd from 'assets/curd.png'
export default {
  name: 'LCToolbarPage',
  inject: ['$doc', 'simulatorProps'],
  data() {
    return {
      buttons: [
        { type: 'primary', action: 'save', event: 'save', showLoading: false },
        { type: 'info', action: 'cancel', event: 'cancel' },
      ],
      dialogParam: {
        title: 'AI生成页面',
        show: false,
      },
      formContextList: [
        {
          title: '表单页',
          desc: '用于数据录入，如新建用户编辑资料、提交反馈等场景',
          img: submit,
          value: 'SUBMIT',
        },
        {
          title: '查询页',
          desc: '展示数据集合，通常包含搜索框、排序、分页、筛选功能。',
          img: search,
          value: 'SEARCH',
        },
        {
          title: '中后台增删改查',
          desc: '集成了数据列表展示、查询过滤、新增、详情与删除操作',
          img: curd,
          value: 'CURD',
        },
      ],
      model: {
        formContext: '',
        formType: 'model',
        modelCode: '',
        fields: [],
        controllers: '',
        prompt: '',
        pageType: 'JS',
      },
      cacheField: {},
      fields: [],
      pageType: [
        {
          label: 'JS',
          value: 'JS',
        },
        {
          label: '逻辑编排',
          value: 'LOGIC',
        },
      ],
      formType: [
        {
          label: '服务驱动',
          value: 'normal',
        },
        {
          label: '模型驱动',
          value: 'model',
        },
      ],
      modelCode: [],
      // 是否获取过数据
      isFetched: false,

      rules_model: {
        modelCode: [{ required: true, message: '请选择模型' }],
        formContext: [{ required: true, message: '请选择AI模版' }],
      },
      rules_normal: {
        controllers: [{ required: true, message: '请输入controller' }],
        formContext: [{ required: true, message: '请选择AI模版' }],
      },
    }
  },
  computed: {
    rules() {
      switch (this.model.formType) {
        case 'model':
          return this.rules_model
        case 'normal':
          return this.rules_normal
        default:
          return this.rules_model
      }
    },
  },
  mounted() {},
  methods: {
    async openDialog() {
      this.dialogParam.show = true
      this.$nextTick(() => {
        this.resetForm()
      })
      if (!this.isFetched) {
        this.modelCode = await this.getModelList()
        this.isFetched = true
      }
    },
    submitForm(resolve, reject) {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const loading = this.$loading({
            target: this.$refs.dialog.$el.querySelector('.el-dialog'),
            lock: true,
            text: '请耐心等待1~3分钟',
            background: 'rgba(255, 255, 255, 0.8)',
          })
          this.$http
            .get(
              ModuleAPI.GENERATE_PAGE_SCHEMA(),
              {
                platform: 'SINITEK', // 必填，目前只支持SINITEK
                formId: this.simulatorProps.id, // 必填，页面id，后台会捞取页面相关数据给大模型
                ...this.model,
              },
              {
                timeout: 1000 * 60 * 10,
              }
            )
            .then((res) => {
              loading.close()
              if (res.data) {
                try {
                  const data = JSON.parse(res.data || '{}')
                  this.$doc.schema.replace(data)
                  resolve('生成成功')
                  this.dialogParam.show = false
                } catch (e) {
                  reject('生成失败')
                }
              } else {
                reject('生成失败')
              }
            })

            .catch((e) => {
              loading.close()
              reject('生成失败')
            })
        } else {
          reject()
        }
      })
    },
    closeDialog() {
      this.dialogParam.show = false
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
    },
    async getModelList() {
      return this.$http
        .get(DataModelAPI.MODEL_AVAILABLELIST(), {
          moduleCode: this.simulatorProps.moduleCode,
        })
        .then((res) => {
          return res.data.map((e) => ({
            label: e.name,
            value: e.code,
          }))
        })
    },
    async getField(modelCode) {
      if (!this.cacheField[modelCode]) {
        this.cacheField[modelCode] = await this.$http
          .post(DataModelAPI.MODEL_RUNTIME_LOAD(), {
            modelCode,
          })
          .then((e) => e?.data)
      }
      return this.cacheField[modelCode]
    },
    async getModelFields(code) {
      const isGetChildren = (type) =>
        [COMPONENT_TYPE.FOREIGN_KEY, COMPONENT_TYPE.CHILDREN_FORM].includes(
          type
        )
      const getChildren = async (v, dep = 0) => {
        // 防止错误设置，无限递归
        if (dep > 2) return []
        const res = await this.getField(v)
        const promise = []
        const fields = [
          ...(res?.fieldProps ?? []),
          ...(res?.subModelProps ?? []),
          ...(res?.mvProps ?? []),
        ]
        fields?.forEach((e) => {
          if (isGetChildren(e.componentType)) {
            promise.push(getChildren(e.relaModelCode, dep + 1))
          }
        })
        const promiseResult = await Promise.all(promise)
        return fields?.map((e) => {
          const result = {
            label: e.comments || e.name,
            value: e.name,
            data: e,
          }
          if (isGetChildren(e.componentType)) {
            result.children = promiseResult.shift()
          }
          return result
        })
      }
      const load = await getChildren(code)
      if (load) return load
      return []
    },
    async handleChange(code) {
      this.fields = await this.getModelFields(code)
    },
    async handleFormTypeChange(formType) {
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate()
      })
      // 切换类型时重置相关字段
      if (formType === 'normal') {
        this.model.modelCode = ''
        this.model.fields = []
      } else {
        this.model.controllers = ''
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.lc-gen-page {
  cursor: pointer;
  margin-left: 10px;
  ::v-deep .el-button {
    height: 30px;
    border: 1px solid var(--xn-color-primary);
    border-radius: 2px;
  }
}
.gp-cards {
  display: flex;
  gap: 10px;
  width: 500px;
}
.gp-card {
  display: flex;
  font-size: 14px;
  line-height: 1.5;
  width: 33%;

  .el-radio {
    margin-right: 0;
  }
  .right {
    border: 1px solid transparent;
    background: var(--xn-color-primary);
    background: color-mix(in srgb, var(--xn-color-primary) 13%, white);
    border-radius: 6px;
    width: 100%;
    cursor: pointer;
    .el-image {
      margin: 10px 15px 5px;
    }
    &.active {
      border: 1px solid var(--xn-color-primary);
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
    }
  }
  img {
    width: 100%;
    height: 150px;
    background-color: #efefef;
    display: block;
  }
  .title {
    color: #333;
    font-size: 14px;
    color: #333333;
    padding: 0 10px 0;
  }
  .desc {
    padding: 0 10px 10px;
    font-size: 12px;
    color: #666666;
  }
}
.gradient-text {
  background-image: linear-gradient(90deg, #b024df 0%, #1754ff 100%);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 14px;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}
</style>
