<template>
  <div class="slm-preview-save">
    <div class="flex">
      <el-button size="mini" class="lc-preview" float="预览" @click="onPreview"
        >预览</el-button
      >

      <el-button
        v-loading.fullscreen.lock="loading"
        size="mini"
        type="primary"
        class="lc-save"
        float="保存"
        @click="save"
      >
        保存
      </el-button>
    </div>
    <xn-dialog show-fullscreen-icon title="预览" :show.sync="isShow">
      <LCPreviewRender v-if="isShow" :config="config" />
    </xn-dialog>
  </div>
</template>

<script>
import LCPreviewRender from './render.vue'
import { SimulatorAPI } from 'src/api/index'
import { encode } from 'js-base64'
export default {
  name: 'LCPreview',
  components: {
    LCPreviewRender,
  },
  inject: {
    simulatorProps: {
      default: () => ({}),
    },
    $doc: {
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      isShow: false,
      config: {},
    }
  },
  methods: {
    async onPreview() {
      await this.save()
      setTimeout(() => {
        this.isShow = true
        this.config = this.$doc.getSchema(true)
      }, 200)
    },
    async save() {
      // save
      const schema = JSON.stringify(this.$doc.getSaveSchema())
      this.loading = true
      await this.$http
        .post(SimulatorAPI.SAVE_DESIGN_DATA(), {
          designData: encode(schema),
          id: this.simulatorProps.id,
        })
        .then(() => {
          this.$doc.resetOriginal()
          this.$message.saveWithSuccess()
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>
<style scoped lang="scss">
.lc-preview,
.lc-save {
  cursor: pointer;
}
.slm-preview-save {
  margin-left: 10px;
  ::v-deep .el-button {
    height: 30px;
    border: 1px solid var(--xn-color-primary);
    border-radius: 2px;
    width: 58px;
    color: var(--xn-color-primary);
    font-size: 14px;
    &.el-button--primary {
      color: #fff;
      margin-right: 10px;
      border: none;
    }
  }
}
</style>
