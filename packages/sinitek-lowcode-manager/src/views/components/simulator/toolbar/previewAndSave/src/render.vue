<template>
  <div>
    <div
      class="canvas-box relative mx-a wh-full overflow-x-hidden overflow-y-auto"
      :style="{ width: $doc.container.device === 'phone' ? '375px' : '100%' }"
    >
      <!-- 主体容器 -->
      <div class="LCCanvas relative min-w-xs w-full bg-bg p-1px">
        <SinitekLowcodeRender
          :config="config"
          :mode="DesignMode.PREVENT"
          :fetcher="fetcher"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { DesignMode } from 'sinitek-lowcode-shared'
import 'sinitek-lowcode-render/dist/sinitek-lowcode-render.css'
import SinitekLowcodeRender from 'sinitek-lowcode-render'
export default {
  name: 'LCPreviewRender',
  inject: ['$doc', 'fetcher'],
  components: {
    SinitekLowcodeRender,
  },
  props: {
    config: Object,
  },
  data() {
    return {
      DesignMode,
    }
  },
}
</script>
