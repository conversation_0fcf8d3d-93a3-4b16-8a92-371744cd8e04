<template>
  <el-button
    class="lc-page-prop"
    float="便捷选中page"
    size="mini"
    @click="handleClick"
    >页面属性</el-button
  >
</template>

<script>
export default {
  name: 'PageProp',
  inject: ['$doc'],
  methods: {
    handleClick() {
      const id = this.$doc.getSchema().id
      if (id) {
        this.$doc.node.selectNode(id)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.lc-page-prop {
  height: 30px;
  border: 1px solid var(--xn-color-primary);
  border-radius: 2px;
  color: var(--xn-color-primary);
  font-size: 14px;
  margin-left: 10px;
}
</style>
