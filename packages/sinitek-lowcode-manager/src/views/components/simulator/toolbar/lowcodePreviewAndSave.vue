<template>
  <!-- 低代码组件的预览和保存 -->
  <div class="slm-preview-save flex">
    <el-button size="mini" class="lc-preview" float="预览" @click="onPreview"
      >预览</el-button
    >

    <el-button
      v-loading.fullscreen.lock="loading"
      size="mini"
      type="primary"
      class="lc-save"
      float="保存"
      @click="save"
    >
      保存
    </el-button>
    <xn-dialog show-fullscreen-icon title="预览" :show.sync="isShow">
      <LCPreviewRender v-if="isShow" :config="config" />
    </xn-dialog>
  </div>
</template>

<script>
// 低代码组件的预览和保存
import LCPreviewRender from './previewAndSave/src/render.vue'
import { LcComponentAPI } from 'src/api'
import { encode } from 'js-base64'
export default {
  name: 'LCToolbarComponentSave',
  components: {
    LCPreviewRender,
  },
  inject: ['$doc'],
  data() {
    return {
      isShow: false,
      config: {},
      loading: false,
    }
  },
  mounted() {
    this.query = JSON.parse(sessionStorage.getItem('LCComponentData') || '{}')
    if (!this.query.id) {
      this.$closeTab()
    }
  },
  methods: {
    async onPreview() {
      await this.save()
      setTimeout(() => {
        this.isShow = true
        this.config = this.$doc.getSchema(true)
      }, 200)
    },
    save() {
      this.loading = true
      // 把数据保存到接口，方便下次修改
      const schema = this.$doc.getSchema(true)
      const material = schema.material || {}
      delete schema.material
      this.$http
        .post(
          LcComponentAPI.SAVE_DESIGN_DATA(),
          {
            id: this.query.id,
            designData: encode(JSON.stringify(schema)),
            materialData: encode(JSON.stringify(material)),
          },
          { loading: true }
        )
        .then(() => {
          this.$message.success('保存成功!')
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.lc-preview,
.lc-save {
  cursor: pointer;
}
.slm-preview-save {
  margin-left: 10px;
  ::v-deep .el-button {
    height: 30px;
    border: 1px solid var(--xn-color-primary);
    border-radius: 2px;
    width: 58px;
    color: var(--xn-color-primary);
    font-size: 14px;
    &.el-button--primary {
      color: #fff;
      margin-right: 10px;
      border: none;
    }
  }
}
</style>
