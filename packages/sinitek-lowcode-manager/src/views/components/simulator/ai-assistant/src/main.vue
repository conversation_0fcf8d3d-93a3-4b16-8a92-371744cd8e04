<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="ai-assistant">
    <!-- 浮动按钮 -->
    <div v-if="!showChat" class="ai-float-button" @click="toggleChat">
      <AIIcon class="ai-icon" />
    </div>

    <!-- 聊天界面 -->
    <div
      v-if="showChat"
      class="ai-chat-container"
      :class="{
        'ai-chat-dragging': isDragging,
        'ai-chat-expanded': isExpanded,
      }"
      :style="chatContainerStyle"
    >
      <!-- 聊天头部 -->
      <div class="ai-chat-header" @mousedown="startDrag">
        <div class="ai-chat-title">
          <AIIcon class="ai-header-icon" />
          <span>{{ name }}</span>
        </div>
        <div class="ai-chat-actions">
          <div
            class="ai-chat-expand"
            :title="isExpanded ? '恢复宽度' : '扩展宽度'"
            @click="toggleExpanded"
          >
            <i
              :class="
                isExpanded
                  ? 'i-mingcute:contract-left-line'
                  : 'i-mingcute:expand-width-line'
              "
            ></i>
          </div>
          <div class="ai-chat-close" title="关闭" @click="toggleChat">
            <i class="el-icon-close"></i>
          </div>
        </div>
      </div>

      <!-- 聊天内容区域 -->
      <div ref="chatContent" class="ai-chat-content">
        <div class="ai-chat-messages">
          <!-- 欢迎消息 -->
          <div class="ai-message ai-message-bot">
            <div class="ai-message-content">
              <div class="ai-message-text user-select-text ai-example-text">
                <!-- AI搭建页面的欢迎消息 -->
                <template v-if="activeTab === 'generate'">
                  <div class="ai-welcome-text">
                    Hi，我可以帮你搭建表单，你只需要提供业务场景，比如你可以这么对我说：
                  </div>
                  <div class="ai-welcome-examples">
                    <div
                      class="ai-example-link"
                      @click="fillExample('我要在这个页面添加联系方式和邮箱')"
                    >
                      "我要在这个页面添加联系方式和邮箱"
                    </div>
                    <div
                      class="ai-example-link"
                      @click="fillExample('我要做一个简历管理页面')"
                    >
                      "我要做一个简历管理页面"
                    </div>
                  </div>
                </template>

                <!-- AI帮助的欢迎消息 -->
                <template v-else>
                  <div class="ai-welcome-text">
                    Hi，页面搭建有任何疑问可以咨询我。比如你可以这么对我说：
                  </div>
                  <div class="ai-welcome-examples">
                    <div
                      class="ai-example-link"
                      @click="fillExample('循环组件怎么使用')"
                    >
                      "循环组件怎么使用"
                    </div>
                    <div
                      class="ai-example-link"
                      @click="fillExample('如何配置枚举数据源')"
                    >
                      "如何配置枚举数据源"
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>

          <!-- 动态消息列表 -->
          <div
            v-for="message in messages"
            :key="message.id"
            class="ai-message"
            :class="[
              message.type === 'user' ? 'ai-message-user' : 'ai-message-bot',
              { 'ai-message-completion': message.isCompletion },
            ]"
          >
            <div v-if="message.type === 'bot'" class="ai-message-avatar">
              {{ name }}
            </div>
            <div class="ai-message-content">
              <div
                class="ai-message-text"
                :class="{
                  'user-select-text': true,
                  'ai-completion-text': message.isCompletion,
                }"
              >
                <!-- 如果是HTML内容，使用v-html渲染 -->
                <div v-if="message.isHtml" class="ai-html-content-wrapper">
                  <div class="ai-html-content" v-html="message.content"></div>
                  <!-- 全屏查看按钮 -->
                  <button
                    v-if="message.content && message.content.length > 500"
                    class="ai-fullscreen-view-btn"
                    title="全屏查看内容"
                    @click="showFullscreenContent(message.content)"
                  >
                    <i class="i-mingcute:fullscreen-line"></i>
                    全屏查看
                  </button>
                </div>
                <!-- 否则使用普通文本 -->
                <template v-else>{{ message.content }}</template>
              </div>
              <div class="ai-message-actions">
                <div v-if="message.timestamp" class="ai-message-time">
                  {{ formatTime(message.timestamp) }}
                </div>
                <button
                  v-if="message.hasDetail"
                  class="ai-detail-button"
                  title="查看详情"
                  @click="showDetail(message.detail, message.executionFailed)"
                >
                  <i class="i-mingcute:file-text-line"></i>
                  详情
                </button>
                <button
                  v-if="message.executionFailed && message.functionName"
                  class="ai-retry-button"
                  title="自动重试"
                  @click="
                    retryFailedFunction(
                      message.functionName,
                      message.errorMessage
                    )
                  "
                >
                  <i class="i-mingcute:refresh-1-line"></i>
                  重试
                </button>
              </div>
            </div>
          </div>

          <!-- 等待状态显示 -->
          <div
            v-if="sending || isWaitingForContinue"
            class="ai-message ai-message-bot"
          >
            <div class="ai-message-avatar">
              {{ name }}
              <!-- <div class="ai-loading-spinner"></div> -->
            </div>
            <div class="ai-message-content">
              <div class="ai-message-text ai-waiting-message">
                <template v-if="activeTab === 'generate'">
                  页面修改中...
                </template>
                <template v-else> AI正在思考中... </template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 聊天输入区域 -->
      <div class="ai-chat-input">
        <div class="ai-input-tabs">
          <div
            class="ai-input-tab"
            :class="{
              active: activeTab === 'generate',
              disabled: sending || isWaitingForContinue,
            }"
            @click="switchTab('generate')"
          >
            <AIIcon class="ai-icon" />
            <span>AI 搭建页面</span>
          </div>
          <div
            class="ai-input-tab"
            :class="{
              active: activeTab === 'reset',
              disabled: sending || isWaitingForContinue,
            }"
            @click="switchTab('reset')"
          >
            <svg-icon icon-class="dialogue" />
            <span>发起新对话</span>
          </div>
          <div
            class="ai-input-tab"
            :class="{
              active: activeTab === 'help',
              disabled: sending || isWaitingForContinue,
            }"
            @click="switchTab('help')"
          >
            <svg-icon icon-class="help" />
            <span>使用帮助</span>
          </div>
        </div>
        <div class="ai-input-container">
          <div class="ai-input-wrapper">
            <!-- 服务选择区域 - 仅在AI搭建页面显示 -->
            <div v-if="activeTab === 'generate'" class="ai-services-section">
              <div class="ai-services-header">
                <span class="ai-services-label">
                  选择服务（可选）
                  <span
                    v-if="selectedControllers.length > 0"
                    class="ai-services-count"
                  >
                    已选择 {{ selectedControllers.length }} 个
                  </span>
                </span>
                <div class="ai-services-actions">
                  <button
                    v-if="selectedControllers.length > 0"
                    class="ai-services-clear"
                    title="清空已选择的服务"
                    @click="clearSelectedServices"
                  >
                    <svg-icon icon-class="remove" />
                    清空
                  </button>
                  <button
                    v-if="!showServices"
                    class="ai-services-toggle"
                    @click="toggleServices"
                  >
                    <svg-icon icon-class="plus" />
                    添加服务
                  </button>
                  <button
                    v-else
                    class="ai-services-toggle"
                    @click="toggleServices"
                  >
                    <i class="el-icon-document-copy"></i>
                    收起
                  </button>
                </div>
              </div>

              <div v-if="showServices" class="ai-services-content">
                <div v-if="loadingControllers" class="ai-services-loading">
                  <i class="ai-loading i-mingcute:loading-line"></i>
                  <span>加载服务列表...</span>
                </div>
                <div
                  v-else-if="controllerList.length === 0"
                  class="ai-services-empty"
                >
                  暂无可用的服务
                </div>
                <div v-else class="ai-services-list">
                  <label
                    v-for="controller in controllerList"
                    :key="controller.classPath"
                    class="ai-service-item"
                  >
                    <input
                      v-model="selectedControllers"
                      type="checkbox"
                      :value="controller.classPath"
                      class="ai-service-checkbox"
                    />
                    <div class="ai-service-info">
                      <div class="ai-service-name">
                        {{
                          controller.description ||
                          controller.classPath.split('.').pop()
                        }}
                      </div>
                      <div class="ai-service-path">
                        {{ abbreviatePackageName(controller.classPath) }}
                      </div>
                    </div>
                  </label>
                </div>
              </div>
            </div>

            <!-- 枚举选择区域 - 仅在AI搭建页面显示 -->
            <div v-if="activeTab === 'generate'" class="ai-services-section">
              <div class="ai-services-header">
                <span class="ai-services-label">
                  选择枚举（可选）
                  <span
                    v-if="selectedEnums.length > 0"
                    class="ai-services-count"
                  >
                    已选择 {{ selectedEnums.length }} 个
                  </span>
                </span>
                <div class="ai-services-actions">
                  <button
                    v-if="selectedEnums.length > 0"
                    class="ai-services-clear"
                    title="清空已选择的枚举"
                    @click="clearSelectedEnums"
                  >
                    <svg-icon icon-class="remove" />
                    清空
                  </button>
                  <button
                    v-if="!showEnums"
                    class="ai-services-toggle"
                    @click="toggleEnums"
                  >
                    <svg-icon icon-class="plus" />
                    添加枚举
                  </button>
                  <button
                    v-else
                    class="ai-services-toggle"
                    @click="toggleEnums"
                  >
                    <i class="el-icon-document-copy"></i>
                    收起
                  </button>
                </div>
              </div>

              <div v-if="showEnums" class="ai-services-content">
                <div v-if="loadingEnums" class="ai-services-loading">
                  <i class="i-mingcute:loading-line ai-loading"></i>
                  <span>加载枚举列表...</span>
                </div>
                <div
                  v-else-if="enumTreeData.length === 0"
                  class="ai-services-empty"
                >
                  暂无可用的枚举
                </div>
                <div v-else class="ai-enum-tree-container">
                  <xn-tree
                    :data="enumTreeData"
                    :show-checkbox="true"
                    :default-expand-all="false"
                    :check-strictly="false"
                    height="200px"
                    node-key="id"
                    @check="onEnumCheck"
                  />
                </div>
              </div>
            </div>

            <!-- 图片上传区域 - 仅在AI搭建页面显示 -->
            <div v-if="activeTab === 'generate'" class="ai-services-section">
              <div class="ai-services-header">
                <span class="ai-services-label">
                  上传图片（可选）
                  <span
                    v-if="selectedImages.length > 0"
                    class="ai-services-count"
                  >
                    已选择 {{ selectedImages.length }} 个
                  </span>
                </span>
                <div class="ai-services-actions">
                  <button
                    v-if="selectedImages.length > 0"
                    class="ai-services-clear"
                    title="清空已选择的图片"
                    @click="clearSelectedImages"
                  >
                    <svg-icon icon-class="remove" />
                    清空
                  </button>
                  <button
                    v-if="!showImages"
                    class="ai-services-toggle"
                    @click="toggleImages"
                  >
                    <svg-icon icon-class="plus" />
                    添加图片
                  </button>
                  <button
                    v-else
                    class="ai-services-toggle"
                    @click="toggleImages"
                  >
                    <i class="el-icon-document-copy"></i>
                    收起
                  </button>
                </div>
              </div>

              <div v-if="showImages" class="ai-services-content">
                <div class="ai-image-upload-container">
                  <!-- 图片上传按钮 -->
                  <div
                    v-if="selectedImages.length < 6"
                    class="ai-image-upload-button"
                    @click="triggerImageUpload"
                  >
                    <svg-icon icon-class="plus" />
                    <span>选择图片</span>
                    <div class="ai-image-upload-hint">
                      最多6张，支持jpg/png/gif
                    </div>
                  </div>

                  <!-- 隐藏的文件输入框 -->
                  <input
                    ref="imageInput"
                    type="file"
                    accept="image/*"
                    multiple
                    style="display: none"
                    @change="handleImageSelect"
                  />

                  <!-- 已选择的图片预览 -->
                  <div
                    v-for="(image, index) in selectedImages"
                    :key="index"
                    class="ai-image-preview"
                  >
                    <img :src="image.preview" :alt="image.name" />
                    <div class="ai-image-overlay">
                      <div class="ai-image-name">{{ image.name }}</div>
                      <button
                        class="ai-image-remove"
                        title="删除图片"
                        @click="removeImage(index)"
                      >
                        <svg-icon icon-class="remove" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 多行文本输入框 -->
            <div class="ai-textarea-wrapper">
              <textarea
                ref="textareaInput"
                v-model="inputMessage"
                class="ai-textarea"
                placeholder="请输入你的需求，按Ctrl+Enter发送"
                rows="10"
                :maxlength="1000"
                @keydown="handleKeydown"
                @focus="handleInputFocus"
              ></textarea>

              <div
                class="ai-input-counter"
                :class="{
                  warning: inputMessage.length >= 800,
                  danger: inputMessage.length >= 950,
                }"
              >
                {{ inputMessage.length }}/1000
              </div>

              <button
                class="ai-send-button"
                :disabled="
                  !inputMessage.trim() || sending || isWaitingForContinue
                "
                @click="sendMessage"
              >
                <svg-icon
                  v-if="!sending && !isWaitingForContinue"
                  icon-class="send"
                />
                <i v-else class="ai-loading el-icon-loader"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情对话框 -->
    <div v-if="showDetailDialog" class="ai-detail-overlay" @click="closeDetail">
      <div class="ai-detail-dialog" @click.stop>
        <div class="ai-detail-header">
          <h3>函数详情</h3>
          <button class="ai-detail-close" @click="closeDetail">
            <svg-icon icon-class="remove" />
          </button>
        </div>
        <div class="ai-detail-content">
          <div ref="monacoContainer" class="ai-monaco-container"></div>
        </div>
        <div class="ai-detail-footer">
          <button
            class="ai-apply-button"
            title="应用修改后的代码"
            @click="applyModifiedScript"
          >
            <i class="i-mingcute:check-line"></i>
            手动执行
          </button>
          <button
            v-if="detailExecutionFailed"
            class="ai-execute-button"
            @click="executeScript"
          >
            <i class="i-mingcute:play-line"></i>
            执行脚本
          </button>
          <button class="ai-copy-button" @click="copyToClipboard">
            <i class="i-mingcute:copy-line"></i>
            复制代码
          </button>
        </div>
      </div>
    </div>

    <!-- 全屏内容查看对话框 -->
    <div
      v-if="showFullscreenDialog"
      class="ai-fullscreen-overlay"
      @click="closeFullscreenContent"
    >
      <div class="ai-fullscreen-dialog" @click.stop>
        <div class="ai-fullscreen-header">
          <h3>内容详情</h3>
          <button class="ai-fullscreen-close" @click="closeFullscreenContent">
            <svg-icon icon-class="remove" />
          </button>
        </div>
        <div class="ai-fullscreen-content">
          <div
            class="ai-fullscreen-html-content"
            v-html="fullscreenContent"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AIIcon from './ai-icon.vue'
import * as acorn from 'acorn'
import * as monaco from 'monaco-editor'
import { log } from 'sinitek-lowcode-shared'
export default {
  name: 'AIAssistant',
  components: {
    AIIcon,
  },
  inject: {
    $doc: {
      default: () => ({}),
    },
    fetcher: {
      default: () => ({}),
    },
    message: {
      default: () => ({}),
    },
  }, // 注入文档对象、fetcher和消息对象
  data() {
    return {
      name: '低代码AI助手',
      showChat: false,
      activeTab: 'generate',
      inputMessage: '',
      sending: false,
      generateMessages: [], // AI搭建页面的消息
      helpMessages: [], // AI帮助的消息
      messageIdCounter: 1,
      sessionId: null, // AI会话ID
      helpSessionId: null, // AI帮助的会话ID
      isWaitingForContinue: false, // 是否在等待继续调用
      showDetailDialog: false, // 显示详情对话框
      detailContent: '', // 详情内容
      detailExecutionFailed: false, // 当前详情脚本是否执行失败
      monacoEditor: null, // Monaco Editor 实例
      // 拖拽相关
      isDragging: false,
      dragStartX: 0,
      dragStartY: 0,
      chatPosition: {
        x: 0,
        y: 0,
      },
      // 服务选择相关
      showServices: false, // 是否显示服务选择区域
      controllerList: [], // 服务列表
      selectedControllers: [], // 选中的服务
      loadingControllers: false, // 是否正在加载服务列表
      // 枚举选择相关
      showEnums: false, // 是否显示枚举选择区域
      enumList: {}, // 枚举列表，格式为 {分类: [类型1, 类型2]}
      enumTreeData: [], // 枚举树形数据
      selectedEnums: [], // 选中的枚举，格式为 [{catalog: "分类1", type: "类型1"}]
      loadingEnums: false, // 是否正在加载枚举列表
      // 图片上传相关
      showImages: false, // 是否显示图片上传区域
      selectedImages: [], // 选中的图片文件，格式为 [{file: File, name: string, preview: string}]
      uploadingImages: false, // 是否正在上传图片
      // 扩展宽度相关
      isExpanded: false, // 是否扩展宽度模式
      showFullscreenDialog: false, // 是否显示全屏内容对话框
      fullscreenContent: '', // 全屏显示的内容
    }
  },
  computed: {
    chatContainerStyle() {
      return {
        transform: `translate(${this.chatPosition.x}px, ${this.chatPosition.y}px)`,
      }
    },
    // 根据当前标签页返回对应的消息
    messages() {
      return this.activeTab === 'generate'
        ? this.generateMessages
        : this.helpMessages
    },
    // 根据当前标签页返回对应的会话ID
    currentSessionId() {
      return this.activeTab === 'generate' ? this.sessionId : this.helpSessionId
    },
  },
  mounted() {
    // 设置初始位置为右下角
    this.setInitialPosition()
  },
  beforeDestroy() {
    // 清理拖拽事件监听器
    document.removeEventListener('mousemove', this.onDrag)
    document.removeEventListener('mouseup', this.stopDrag)
  },
  methods: {
    // 处理键盘事件
    handleKeydown(event) {
      // Ctrl+Enter 或 Shift+Enter 发送消息
      if (event.key === 'Enter' && (event.ctrlKey || event.shiftKey)) {
        event.preventDefault()
        this.sendMessage()
      }
      // 普通 Enter 键允许换行（不阻止默认行为）
    },

    // 处理输入框聚焦事件
    handleInputFocus() {
      // 聚焦输入框时收起服务、枚举和图片选择区域
      this.showServices = false
      this.showEnums = false
      this.showImages = false
    },

    // 判断是否是重试请求
    isRetryRequest(message) {
      const retryKeywords = [
        '函数执行失败',
        '执行失败',
        '重新生成',
        '重试',
        '再试一次',
        '重新执行',
        '报错',
        '错误',
        '失败了',
        '不行',
        '有问题',
      ]

      const lowerMessage = message.toLowerCase()
      return retryKeywords.some(
        (keyword) =>
          lowerMessage.includes(keyword) ||
          lowerMessage.includes(keyword.toLowerCase())
      )
    },

    // 切换服务选择区域显示/隐藏
    toggleServices() {
      this.showServices = !this.showServices
      // 展开服务时自动收起枚举和图片
      if (this.showServices) {
        this.showEnums = false
        this.showImages = false
        if (this.controllerList.length === 0) {
          this.loadControllers()
        }
      }
    },

    // 清空已选择的服务
    clearSelectedServices() {
      this.selectedControllers = []
    },

    // 切换枚举选择区域显示/隐藏
    toggleEnums() {
      this.showEnums = !this.showEnums
      // 展开枚举时自动收起服务和图片
      if (this.showEnums) {
        this.showServices = false
        this.showImages = false
        if (this.enumTreeData.length === 0) {
          this.loadEnums()
        }
      }
    },

    // 清空已选择的枚举
    clearSelectedEnums() {
      this.selectedEnums = []
    },

    // 切换图片上传区域显示/隐藏
    toggleImages() {
      this.showImages = !this.showImages
      // 展开图片时自动收起服务和枚举
      if (this.showImages) {
        this.showServices = false
        this.showEnums = false
      }
    },

    // 清空已选择的图片
    clearSelectedImages() {
      // 释放预览URL内存
      this.selectedImages.forEach((image) => {
        if (image.preview) {
          URL.revokeObjectURL(image.preview)
        }
      })
      this.selectedImages = []
    },

    // 触发图片选择
    triggerImageUpload() {
      if (this.$refs.imageInput) {
        this.$refs.imageInput.click()
      }
    },

    // 处理图片选择
    handleImageSelect(event) {
      const files = Array.from(event.target.files)
      if (!files.length) return

      // 检查总数量限制
      const remainingSlots = 6 - this.selectedImages.length
      if (remainingSlots <= 0) {
        this.message?.warning('最多只能选择6张图片')
        return
      }

      // 取前面可用的文件
      const filesToProcess = files.slice(0, remainingSlots)
      const validImages = []

      filesToProcess.forEach((file) => {
        // 验证文件类型
        if (!file.type.startsWith('image/')) {
          this.message?.warning(`文件 ${file.name} 不是有效的图片格式`)
          return
        }

        // 验证文件大小（限制为10MB）
        if (file.size > 10 * 1024 * 1024) {
          this.message?.warning(`图片 ${file.name} 大小超过10MB限制`)
          return
        }

        // 创建预览URL
        const preview = URL.createObjectURL(file)
        validImages.push({
          file,
          name: file.name,
          preview,
        })
      })

      // 添加到已选择的图片列表
      this.selectedImages.push(...validImages)

      // 清空input值，允许重复选择同一文件
      event.target.value = ''

      if (validImages.length > 0) {
        this.message?.success(`成功添加 ${validImages.length} 张图片`)
      }
    },

    // 删除指定图片
    removeImage(index) {
      const image = this.selectedImages[index]
      if (image && image.preview) {
        // 释放预览URL内存
        URL.revokeObjectURL(image.preview)
      }
      this.selectedImages.splice(index, 1)
    },

    // 处理枚举树选择事件
    onEnumCheck(data, checkedInfo) {
      const isChecked = checkedInfo.checkedKeys.includes(data.id)

      if (data.isCategory) {
        // 处理根节点（分类）的选择 - 全选/取消全选该分类下的所有枚举
        const categoryTypes = this.enumList[data.category] || []

        if (isChecked) {
          // 全选该分类下的所有枚举
          categoryTypes.forEach((type) => {
            const enumItem = { catalog: data.category, type }
            const exists = this.selectedEnums.some(
              (item) => item.catalog === data.category && item.type === type
            )
            if (!exists) {
              this.selectedEnums.push(enumItem)
            }
          })
        } else {
          // 取消选择该分类下的所有枚举
          this.selectedEnums = this.selectedEnums.filter(
            (item) => item.catalog !== data.category
          )
        }
      } else {
        // 处理叶子节点（枚举类型）的选择
        const enumItem = { catalog: data.category, type: data.type }

        if (isChecked) {
          // 添加选择（避免重复）
          const exists = this.selectedEnums.some(
            (item) => item.catalog === data.category && item.type === data.type
          )
          if (!exists) {
            this.selectedEnums.push(enumItem)
          }
        } else {
          // 移除选择
          const index = this.selectedEnums.findIndex(
            (item) => item.catalog === data.category && item.type === data.type
          )
          if (index > -1) {
            this.selectedEnums.splice(index, 1)
          }
        }
      }
    },

    // 加载Controller列表
    async loadControllers() {
      this.loadingControllers = true
      try {
        const response = await fetch(
          '/frontend/api/lowcode/llm/form/search-controller'
        )
        const result = await response.json()

        if (result.success && result.data) {
          this.controllerList = result.data
        } else {
          this.controllerList = []
          console.error('获取Controller列表失败:', result.message)
        }
      } catch (error) {
        console.error('加载Controller列表失败:', error)
        this.controllerList = []
      } finally {
        this.loadingControllers = false
      }
    },

    // 加载枚举列表
    async loadEnums() {
      this.loadingEnums = true
      try {
        const response = await fetch(
          '/frontend/api/lowcode/llm/form/search-enum'
        )
        const result = await response.json()

        if (result.success && result.data) {
          this.enumList = result.data
          this.enumTreeData = this.convertToTreeData(result.data)
        } else {
          this.enumList = {}
          this.enumTreeData = []
          console.error('获取枚举列表失败:', result.message)
        }
      } catch (error) {
        console.error('加载枚举列表失败:', error)
        this.enumList = {}
        this.enumTreeData = []
      } finally {
        this.loadingEnums = false
      }
    },

    // 将枚举数据转换为树形结构
    convertToTreeData(enumData) {
      const treeData = []
      let nodeId = 1

      Object.keys(enumData).forEach((category) => {
        const categoryNode = {
          id: `category_${nodeId++}`,
          label: category,
          category,
          isCategory: true,
          children: [],
        }

        enumData[category].forEach((type) => {
          categoryNode.children.push({
            id: `enum_${nodeId++}`,
            label: type,
            category,
            type,
            isCategory: false,
          })
        })

        treeData.push(categoryNode)
      })

      return treeData
    },

    // 缩写包名显示
    abbreviatePackageName(classPath) {
      if (!classPath) return ''

      const parts = classPath.split('.')
      if (parts.length <= 2) return classPath

      // 保留最后一个类名完整，其他部分只取首字母
      const className = parts[parts.length - 1]
      const packageParts = parts.slice(0, -1)
      const abbreviatedPackage = packageParts
        .map((part) => part.charAt(0))
        .join('.')

      return `${abbreviatedPackage}.${className}`
    },

    // 设置初始位置
    setInitialPosition() {
      // 设置为右下角位置，距离右边和上边各20px
      const containerWidth = this.isExpanded ? 760 : 380 // 根据扩展状态调整宽度
      this.chatPosition.x = window.innerWidth - containerWidth - 20
      this.chatPosition.y = 20
    },

    // 切换扩展宽度
    toggleExpanded() {
      this.isExpanded = !this.isExpanded
      // 重新计算位置，确保扩展后不会超出屏幕
      this.adjustPositionAfterExpand()
    },

    // 扩展后调整位置
    adjustPositionAfterExpand() {
      const containerWidth = this.isExpanded ? 760 : 380
      const minVisibleWidth = 100

      // 确保扩展后不会超出屏幕右边界
      const maxX = window.innerWidth - minVisibleWidth
      if (this.chatPosition.x + containerWidth > window.innerWidth) {
        this.chatPosition.x = Math.max(
          maxX - containerWidth,
          -containerWidth + minVisibleWidth
        )
      }
    },

    toggleChat() {
      this.showChat = !this.showChat
      if (this.showChat) {
        this.$nextTick(() => {
          this.scrollToBottom()
          // 聚焦到文本输入框
          const textareaInput = this.$refs.textareaInput
          if (textareaInput) {
            textareaInput.focus()
          }
        })
      } else {
        // 关闭聊天时重置会话
        this.resetSession()
      }
    },

    // 切换标签页
    switchTab(tab) {
      // 如果AI正在处理，不允许切换标签页
      if (this.sending || this.isWaitingForContinue) {
        this.message?.warning('AI正在处理中，请等待完成后再切换标签页')
        return
      }

      if (tab === 'reset') {
        this.resetConversation()
      }

      if (this.activeTab !== tab) {
        this.activeTab = tab
        // 切换标签页时重置会话
        this.resetSession()
      }
    },

    // 重置会话
    resetSession() {
      this.sessionId = null
      this.isWaitingForContinue = false
      this.sending = false
    },

    // 重置对话
    resetConversation() {
      // 如果AI正在处理，不允许重置对话
      if (this.sending || this.isWaitingForContinue) {
        this.message?.warning('AI正在处理中，请等待完成后再重置对话')
        return
      }

      // 清空对应标签页的聊天记录和会话ID
      if (this.activeTab === 'generate') {
        this.generateMessages = []
        this.sessionId = null // 重置AI搭建页面的会话ID
      } else {
        this.helpMessages = []
        this.helpSessionId = null // 重置AI帮助的会话ID
      }

      // 重置会话状态
      this.isWaitingForContinue = false
      this.sending = false

      // 清空输入框和选中的服务、枚举、图片
      this.inputMessage = ''
      this.selectedControllers = []
      this.selectedEnums = []
      this.clearSelectedImages()
      this.showServices = false
      this.showEnums = false
      this.showImages = false

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    async sendMessage() {
      if (!this.inputMessage.trim() || this.sending) return

      // 检查是否是重试请求（包含"函数执行失败"或"重新生成"等关键词）
      const isRetryRequest = this.isRetryRequest(this.inputMessage.trim())

      // 只有在AI搭建页面且不是重试请求时，才自动保存页面
      if (this.activeTab === 'generate' && !isRetryRequest) {
        try {
          await this.autoSavePage()
          // 保存成功后在对话中添加消息
          this.addBotMessage('📄 页面已保存，可从历史恢复')
        } catch (error) {
          console.error('自动保存失败:', error)
          this.addBotMessage('⚠️ 页面保存失败，但对话将继续')
        }
      }

      // 构建用户消息显示内容
      let displayContent = this.inputMessage.trim()
      if (this.selectedControllers.length > 0) {
        const serviceNames = this.selectedControllers.map((classPath) => {
          const controller = this.controllerList.find(
            (c) => c.classPath === classPath
          )
          return controller
            ? controller.description || controller.classPath.split('.').pop()
            : classPath
        })
        displayContent += `\n\n📋 选择的服务: ${serviceNames.join(', ')}`
      }
      if (this.selectedEnums.length > 0) {
        const enumNames = this.selectedEnums.map((enumItem) => {
          return `${enumItem.catalog}.${enumItem.type}`
        })
        displayContent += `\n\n🏷️ 选择的枚举: ${enumNames.join(', ')}`
      }
      if (this.selectedImages.length > 0) {
        const imageNames = this.selectedImages.map((image) => image.name)
        displayContent += `\n\n🖼️ 上传的图片: ${imageNames.join(', ')}`
      }

      const userMessage = {
        id: this.messageIdCounter++,
        type: 'user',
        content: displayContent,
        timestamp: new Date(),
      }

      // 根据当前标签页添加到对应的消息数组
      if (this.activeTab === 'generate') {
        this.generateMessages.push(userMessage)
      } else {
        this.helpMessages.push(userMessage)
      }
      const userInput = this.inputMessage.trim()
      const controllers = [...this.selectedControllers] // 复制选中的服务
      const enums = [...this.selectedEnums] // 复制选中的枚举
      const images = [...this.selectedImages] // 复制选中的图片

      // 清空输入框和选中的服务、枚举、图片
      this.inputMessage = ''
      this.selectedControllers = []
      this.selectedEnums = []
      this.clearSelectedImages() // 使用方法清空图片，释放内存
      this.sending = true

      // 立即滚动到底部显示等待状态
      this.$nextTick(() => {
        this.scrollToBottom()
      })

      try {
        if (this.activeTab === 'generate') {
          // AI 搭建页面 - 调用真实API
          await this.callAIGenerateAPI(userInput, controllers, enums, images)
        } else {
          // AI 帮助 - 使用模拟回复
          await this.generateMockResponse(userInput)
        }
      } catch (error) {
        console.error('发送消息失败:', error)
        this.addBotMessage('抱歉，服务暂时不可用，请稍后再试。')
      } finally {
        this.sending = false
      }
    },

    // 自动保存页面
    async autoSavePage() {
      try {
        // 检查$doc是否可用
        if (!this.$doc || !this.$doc.getSaveSchema) {
          console.warn('文档对象不可用，跳过自动保存')
          return
        }

        // 获取保存用的schema（用于验证数据完整性）
        const schema = this.$doc.getSaveSchema()

        // 模拟保存操作 - 在实际环境中，这里会调用真实的保存API
        // 这里我们只是标记页面已保存，实际保存由外部系统处理
        // 验证schema数据完整性
        if (!schema || typeof schema !== 'object') {
          throw new Error('页面数据格式错误')
        }

        // 标记页面已保存（重置原始状态）
        this.$doc.resetOriginal()
      } catch (error) {
        console.error('自动保存页面失败:', error)
        throw error
      }
    },

    // 调用AI生成API
    async callAIGenerateAPI(
      userInput,
      controllers = [],
      enums = [],
      images = []
    ) {
      try {
        // 获取当前页面的schema
        const currentSchema = this.getCurrentSchema()
        // 准备请求参数
        const formData = new FormData()
        formData.append('schema', JSON.stringify(currentSchema))

        const isFirstCall = !this.isWaitingForContinue

        if (this.isWaitingForContinue) {
          // 追问调用
          formData.append('prompt', '继续')
          formData.append('sessionId', this.sessionId)
        } else {
          // 首次调用
          formData.append('prompt', userInput)
          if (this.sessionId) {
            formData.append('sessionId', this.sessionId)
          }
          // 添加选中的服务（用逗号分隔的字符串）
          if (controllers.length > 0) {
            formData.append('controllers', controllers.join(','))
          }
          // 添加选中的枚举（JSON数组格式）
          if (enums.length > 0) {
            formData.append('enums', JSON.stringify(enums))
          }
          // 添加选中的图片文件（File数组）
          if (images.length > 0) {
            images.forEach((imageItem, index) => {
              formData.append('images', imageItem.file)
            })
          }
        }

        // 发送请求
        const response = await this.sendAIRequest(formData)

        if (response.success && response.data) {
          const { text, finishFlag, sessionId } = response.data

          // 保存sessionId
          if (sessionId) {
            this.sessionId = sessionId
          }

          // 执行返回的函数
          if (text) {
            const executionSuccess = await this.executeGeneratedFunction(
              text,
              currentSchema
            )

            // 如果函数执行报错，不继续追问
            if (!executionSuccess) {
              this.isWaitingForContinue = false
              return
            }
          } else {
            this.addBotMessage('⚠️ 服务返回了空的函数内容')
            // 如果是首次调用且没有返回函数，不继续追问
            if (isFirstCall) {
              this.isWaitingForContinue = false
              return
            }
          }

          // 检查是否需要继续调用
          if (finishFlag === 0) {
            this.isWaitingForContinue = true
            // 滚动到底部显示继续等待状态
            this.$nextTick(() => {
              this.scrollToBottom()
            })
            // 自动继续调用
            setTimeout(() => {
              this.callAIGenerateAPI('继续', [], [])
            }, 1000)
          } else {
            this.isWaitingForContinue = false
            this.addCompletionMessage(
              '🎉 页面生成完成！所有任务已执行完毕，您可以查看生成的页面结构。'
            )
          }
        } else {
          throw new Error(response.message || '服务调用失败')
        }
      } catch (error) {
        console.error('AI生成API调用失败:', error)
        this.addBotMessage('生成失败，请重试。错误信息：' + error.message)
        this.isWaitingForContinue = false
      }
    },

    // 发送AI请求
    async sendAIRequest(formData) {
      const response = await fetch(
        '/frontend/api/lowcode/llm/form/agent-generate-function',
        {
          method: 'POST',
          body: formData,
        }
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    },

    // 获取当前页面的schema
    getCurrentSchema() {
      try {
        if (this.$doc && this.$doc.getSimpleSchema) {
          return this.$doc.getSimpleSchema()
        }
        return {}
      } catch (error) {
        console.error('获取schema失败:', error)
        return {}
      }
    },

    // 执行生成的函数
    async executeGeneratedFunction(functionText, currentSchema) {
      try {
        // 获取当前schema
        const schema = this.$doc.getSimpleSchema()

        // 提取函数名
        const functionNameMatch = functionText.match(/function\s+(\w+)\s*\(/)
        if (!functionNameMatch) {
          throw new Error('无法解析函数名')
        }
        const functionName = functionNameMatch[1]

        // 如果函数名是 end，直接显示完成消息
        if (functionName === 'end') {
          return true
        }

        // 创建执行环境，定义函数并调用
        const executeCode = `
          ${functionText}
          return ${functionName}(arguments[0]);
        `

        // 执行函数
        const func = new Function(executeCode)
        const result = func(schema)

        // 更新schema
        if (result) {
          this.$doc.setSchema(result)
        }

        // 提取函数注释作为执行描述
        const functionDescription =
          this.extractFunctionDescription(functionText)
        const successMessage = functionDescription
          ? `✅ 已执行: ${functionDescription}`
          : '✅ 页面结构已更新！'

        // 成功执行时也显示详情
        this.addBotMessage(successMessage, true, functionText, false)

        // 添加历史记录
        if (this.$doc.history && this.$doc.history.addHistory) {
          this.$doc.history.addHistory()
        }

        return true // 返回成功状态
      } catch (error) {
        console.error('执行生成的函数失败:', error)

        // 提取函数名
        const functionNameMatch = functionText.match(/function\s+(\w+)\s*\(/)
        const functionName = functionNameMatch
          ? functionNameMatch[1]
          : '未知函数'

        this.addBotMessage(
          '❌ 函数执行失败：' + error.message,
          true,
          functionText,
          true, // 标记执行失败
          functionName, // 函数名
          error.message // 错误信息
        )
        return false // 返回失败状态
      }
    },

    // 添加机器人消息
    addBotMessage(
      content,
      hasDetail = false,
      detail = '',
      executionFailed = false,
      functionName = '',
      errorMessage = '',
      isHtml = false
    ) {
      const botMessage = {
        id: this.messageIdCounter++,
        type: 'bot',
        content,
        timestamp: new Date(),
        hasDetail,
        detail,
        executionFailed, // 标记脚本是否执行失败
        functionName, // 失败的函数名
        errorMessage, // 错误信息
        isHtml, // 标记内容是否为HTML
      }

      // 根据当前标签页添加到对应的消息数组
      if (this.activeTab === 'generate') {
        this.generateMessages.push(botMessage)
      } else {
        this.helpMessages.push(botMessage)
      }

      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    // 自动重试失败的函数
    retryFailedFunction(functionName, errorMessage) {
      if (this.sending || this.isWaitingForContinue) {
        return // 如果正在发送或等待，不允许重试
      }

      // 构建重试消息
      const retryMessage = `${functionName} 函数执行失败，请重试继续，错误信息：${errorMessage}`

      // 设置输入框内容并自动发送
      this.inputMessage = retryMessage
      this.sendMessage()
    },

    // 添加完成消息（带特殊样式）
    addCompletionMessage(content) {
      const completionMessage = {
        id: this.messageIdCounter++,
        type: 'bot',
        content,
        timestamp: new Date(),
        isCompletion: true, // 标记为完成消息
      }

      // 根据当前标签页添加到对应的消息数组
      if (this.activeTab === 'generate') {
        this.generateMessages.push(completionMessage)
      } else {
        this.helpMessages.push(completionMessage)
      }

      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    // 显示详情
    showDetail(detail, executionFailed = false) {
      this.detailContent = detail
      this.detailExecutionFailed = executionFailed
      this.showDetailDialog = true

      // 等待 DOM 更新后初始化 Monaco Editor
      this.$nextTick(() => {
        this.initMonacoEditor()
      })
    },

    // 关闭详情
    closeDetail() {
      this.showDetailDialog = false
      this.detailContent = ''
      this.detailExecutionFailed = false

      // 销毁 Monaco Editor 实例
      if (this.monacoEditor) {
        this.monacoEditor.dispose()
        this.monacoEditor = null
      }
    },

    // 初始化 Monaco Editor
    initMonacoEditor() {
      if (!this.$refs.monacoContainer || this.monacoEditor) return

      try {
        // 直接使用原始代码，不进行任何格式化处理
        const originalCode = this.detailContent

        // 创建 Monaco Editor 实例
        this.monacoEditor = monaco.editor.create(this.$refs.monacoContainer, {
          value: originalCode,
          language: 'javascript',
          theme: 'vs',
          readOnly: false, // 改为可编辑模式
          automaticLayout: true,
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          wordWrap: 'on',
          lineNumbers: 'on', // 启用行号显示，便于编辑
          glyphMargin: true, // 启用字形边距
          folding: true, // 启用代码折叠
          lineDecorationsWidth: 10,
          lineNumbersMinChars: 3,
          renderLineHighlight: 'line', // 启用行高亮
          contextmenu: true, // 启用右键菜单
          selectOnLineNumbers: true, // 允许点击行号选择
          // 编辑相关配置
          tabSize: 2,
          insertSpaces: true,
          formatOnPaste: true,
          formatOnType: true,
          suggestOnTriggerCharacters: true,
          acceptSuggestionOnEnter: 'on',
          quickSuggestions: true,
          // 格式化相关配置
          autoIndent: 'full',
          bracketPairColorization: { enabled: true },
          guides: {
            bracketPairs: true,
            indentation: true,
          },
          // 代码提示和验证
          semanticHighlighting: { enabled: true },
          occurrencesHighlight: true,
          selectionHighlight: true,
        })

        // 检测语法错误并标记
        this.markSyntaxErrors(originalCode)

        // 添加内容变化监听器，实时检查语法
        this.monacoEditor.onDidChangeModelContent(() => {
          const currentCode = this.monacoEditor.getValue()
          this.markSyntaxErrors(currentCode)
        })
      } catch (error) {
        console.error('初始化 Monaco Editor 失败:', error)
        // 降级到简单文本显示
        this.$refs.monacoContainer.innerHTML = `<pre class="fallback-code">${this.detailContent}</pre>`
      }
    },

    // 标记语法错误
    markSyntaxErrors(code) {
      if (!this.monacoEditor) return

      try {
        acorn.parse(code, { ecmaVersion: 2020 })
        // 如果解析成功，没有语法错误
      } catch (e) {
        if (e.name === 'SyntaxError' && e.pos !== undefined) {
          // 计算错误位置
          const model = this.monacoEditor.getModel()
          const position = model.getPositionAt(e.pos)

          // 添加错误标记
          monaco.editor.setModelMarkers(model, 'syntax', [
            {
              startLineNumber: position.lineNumber,
              startColumn: position.column,
              endLineNumber: position.lineNumber,
              endColumn: position.column + 10,
              message: e.message,
              severity: monaco.MarkerSeverity.Error,
            },
          ])
        }
      }
    },

    // 应用修改后的脚本
    async applyModifiedScript() {
      try {
        const modifiedCode = this.monacoEditor
          ? this.monacoEditor.getValue()
          : this.detailContent

        const executionSuccess =
          await this.executeGeneratedFunction(modifiedCode)

        if (executionSuccess) {
          this.addBotMessage('✅ 修改已应用成功！')
          // 关闭详情弹窗
          this.closeDetail()
        } else {
          this.addBotMessage('❌ 修改应用失败，请检查代码语法')
        }
      } catch (error) {
        console.error('应用修改失败:', error)
        this.addBotMessage('❌ 应用修改失败：' + error.message)
      }
    },

    // 手动执行脚本
    async executeScript() {
      try {
        const functionText = this.monacoEditor
          ? this.monacoEditor.getValue()
          : this.detailContent

        const executionSuccess =
          await this.executeGeneratedFunction(functionText)

        if (executionSuccess) {
          // 执行成功，隐藏执行按钮
          this.detailExecutionFailed = false
          this.addBotMessage('✅ 脚本执行成功！')
        } else {
          this.addBotMessage('❌ 脚本执行失败，请检查代码')
        }
      } catch (error) {
        console.error('手动执行脚本失败:', error)
        this.addBotMessage('❌ 脚本执行失败：' + error.message)
      }
    },

    // 复制到剪贴板
    async copyToClipboard() {
      try {
        // 优先从 Monaco Editor 获取格式化后的代码
        const codeToClipboard = this.monacoEditor
          ? this.monacoEditor.getValue()
          : this.detailContent

        await navigator.clipboard.writeText(codeToClipboard)
        this.addBotMessage('✅ 代码已复制到剪贴板')
      } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = this.monacoEditor
          ? this.monacoEditor.getValue()
          : this.detailContent
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.addBotMessage('✅ 代码已复制到剪贴板')
      }
    },

    // 填充示例到输入框
    fillExample(example) {
      this.inputMessage = example
      // 聚焦到文本输入框
      this.$nextTick(() => {
        const textareaInput = this.$refs.textareaInput
        if (textareaInput) {
          textareaInput.focus()
        }
      })
    },

    // 显示全屏内容
    showFullscreenContent(content) {
      this.fullscreenContent = content
      this.showFullscreenDialog = true
    },

    // 关闭全屏内容
    closeFullscreenContent() {
      this.showFullscreenDialog = false
      this.fullscreenContent = ''
    },

    // AI帮助功能 - 调用真实API
    async generateMockResponse(userInput) {
      try {
        // 调用AI帮助API
        const response = await this.callAIHelpAPI(userInput)

        log('AI帮助API响应:', response) // 调试日志

        if (response.success && response.data && response.data.text) {
          // 清理和处理HTML内容
          const htmlContent = this.processHtmlContent(response.data.text)
          log('处理后的HTML内容:', htmlContent) // 调试日志
          // 将HTML内容作为机器人回复
          this.addBotMessage(htmlContent, false, '', false, '', '', true)
        } else {
          this.addBotMessage('抱歉，暂时无法获取帮助信息，请稍后再试。')
        }
      } catch (error) {
        console.error('AI帮助API调用失败:', error)
        this.addBotMessage('抱歉，服务暂时不可用，请稍后再试。')
      }
    },

    // 处理HTML内容
    processHtmlContent(htmlText) {
      if (!htmlText) return ''

      // 确保HTML内容被正确处理
      let processedHtml = htmlText.trim()

      // 如果内容不包含HTML标签，直接返回
      if (!processedHtml.includes('<')) {
        return processedHtml
      }

      // 处理链接标签，添加样式和target属性
      processedHtml = processedHtml.replace(
        /<a\s+([^>]*?)>/gi,
        (match, attributes) => {
          // 强制添加样式
          const linkStyle =
            'color: #1e40af !important; text-decoration: underline !important;'

          // 检查是否已有style属性
          if (attributes.includes('style=')) {
            // 如果已有style，在现有样式后添加
            attributes = attributes.replace(
              /style\s*=\s*["']([^"']*?)["']/i,
              (styleMatch, existingStyle) => {
                return `style="${existingStyle}; ${linkStyle}"`
              }
            )
          } else {
            // 如果没有style属性，添加新的
            attributes += ` style="${linkStyle}"`
          }

          // 确保有target="_blank"
          if (!attributes.includes('target=')) {
            attributes += ' target="_blank"'
          }

          return `<a ${attributes}>`
        }
      )

      return processedHtml
    },

    // 调用AI帮助API
    async callAIHelpAPI(userInput) {
      try {
        // 准备请求参数
        const formData = new FormData()
        formData.append('prompt', userInput)

        // 如果有AI帮助的会话ID，添加到请求中
        if (this.helpSessionId) {
          formData.append('sessionId', this.helpSessionId)
        }

        // 发送请求
        const response = await fetch(
          '/frontend/api/lowcode/llm/form/agent-manual',
          {
            method: 'POST',
            body: formData,
          }
        )

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        // 保存AI帮助的sessionId（如果返回了）
        if (result.data && result.data.sessionId) {
          this.helpSessionId = result.data.sessionId
        }

        return result
      } catch (error) {
        console.error('调用AI帮助API失败:', error)
        throw error
      }
    },

    formatTime(timestamp) {
      return timestamp.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
      })
    },

    scrollToBottom() {
      if (this.$refs.chatContent) {
        this.$refs.chatContent.scrollTop = this.$refs.chatContent.scrollHeight
      }
    },

    // 提取函数描述注释
    extractFunctionDescription(functionText) {
      try {
        // 按行分割代码
        const lines = functionText.split('\n')

        // 查找函数定义行
        let functionLineIndex = -1
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim()
          if (line.startsWith('function ') && line.includes('(')) {
            functionLineIndex = i
            break
          }
        }

        // 如果找到函数定义，查找其上方的注释
        if (functionLineIndex > 0) {
          const previousLine = lines[functionLineIndex - 1].trim()

          // 检查上一行是否是注释
          if (previousLine.startsWith('//')) {
            // 提取注释内容，去掉 // 前缀
            return previousLine.replace(/^\/\/\s*/, '')
          }
        }

        return null
      } catch (error) {
        console.error('提取函数描述失败:', error)
        return null
      }
    },

    // 修复缺失的括号
    fixMissingBrackets(code) {
      try {
        // 尝试解析代码，如果成功则直接返回
        acorn.parse(code, { ecmaVersion: 2020 })
        return code
      } catch (error) {
        if (error.name === 'SyntaxError' && error.pos !== undefined) {
          // 简单的括号修复逻辑
          let fixedCode = code

          // 统计各种括号的数量
          const openBrackets = (fixedCode.match(/\{/g) || []).length
          const closeBrackets = (fixedCode.match(/\}/g) || []).length
          const openParens = (fixedCode.match(/\(/g) || []).length
          const closeParens = (fixedCode.match(/\)/g) || []).length
          const openSquare = (fixedCode.match(/\[/g) || []).length
          const closeSquare = (fixedCode.match(/\]/g) || []).length

          // 补充缺失的右括号
          if (openBrackets > closeBrackets) {
            fixedCode += '}'.repeat(openBrackets - closeBrackets)
          }
          if (openParens > closeParens) {
            fixedCode += ')'.repeat(openParens - closeParens)
          }
          if (openSquare > closeSquare) {
            fixedCode += ']'.repeat(openSquare - closeSquare)
          }

          // 再次尝试解析修复后的代码
          try {
            acorn.parse(fixedCode, { ecmaVersion: 2020 })
            return fixedCode
          } catch (e) {
            // 如果还是失败，返回原始代码
            return code
          }
        }
        return code
      }
    },

    // 拖拽相关方法
    startDrag(event) {
      // 防止在点击按钮时触发拖拽
      if (event.target.closest('.ai-chat-actions')) {
        return
      }

      this.isDragging = true
      this.dragStartX = event.clientX - this.chatPosition.x
      this.dragStartY = event.clientY - this.chatPosition.y

      // 添加全局事件监听
      document.addEventListener('mousemove', this.onDrag, { passive: false })
      document.addEventListener('mouseup', this.stopDrag)

      // 防止文本选择和默认行为
      event.preventDefault()
      event.stopPropagation()
    },

    onDrag(event) {
      if (!this.isDragging) return

      const newX = event.clientX - this.dragStartX
      const newY = event.clientY - this.dragStartY

      // 获取对话框的实际尺寸
      const chatContainer = document.querySelector('.ai-chat-container')
      const containerWidth = chatContainer ? chatContainer.offsetWidth : 380

      const minVisibleWidth = 100 // 至少保持100px可见
      const minVisibleHeight = 50 // 至少保持50px可见

      // 计算边界 - 允许更大的拖拽范围
      const minX = -containerWidth + minVisibleWidth // 允许大部分移出左边，但保持一部分可见
      const maxX = window.innerWidth - minVisibleWidth // 允许大部分移出右边，但保持一部分可见
      const minY = -minVisibleHeight // 允许部分移出顶部
      const maxY = window.innerHeight - minVisibleHeight // 允许部分移出底部

      this.chatPosition.x = Math.max(minX, Math.min(maxX, newX))
      this.chatPosition.y = Math.max(minY, Math.min(maxY, newY))
    },

    stopDrag() {
      this.isDragging = false

      // 移除全局事件监听
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },
  },
}
</script>

<style scoped lang="scss">
.ai-assistant {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
  font-size: 14px;
}

/* 浮动按钮样式 */
.ai-float-button {
  width: 40px;
  height: 40px;
  /* background: linear-gradient(135deg, var(--xn-color-primary) 0%, #764ba2 100%); */
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  background: #fff;
}

.ai-float-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

.ai-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 2px;
}

.ai-button-text {
  font-size: 10px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* 聊天容器样式 */
.ai-chat-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 300px;
  height: calc(100vh - 40px);
  min-height: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  transition:
    width 0.3s ease,
    opacity 0.2s ease;
  z-index: 9999;
}

/* 扩展宽度样式 */
.ai-chat-container.ai-chat-expanded {
  width: 760px;
}

/* 拖拽状态样式 */
.ai-chat-container.ai-chat-dragging {
  opacity: 0.7;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.25);
  z-index: 10001;
}

/* 聊天头部 */
.ai-chat-header {
  padding: 0 10px;
  height: 38px;
  background: #fff;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: move;
  user-select: none;
  border-bottom: 1px solid #e0e3e5;
}

.ai-chat-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 14px;
}

.ai-header-icon {
  width: 17px;
  height: 13px;
  margin-right: 5px;
}

.ai-chat-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-chat-expand,
.ai-chat-reset,
.ai-chat-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  color: #666;
}

.ai-chat-expand:hover,
.ai-chat-reset:hover,
.ai-chat-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.ai-chat-expand i,
.ai-chat-reset i,
.ai-chat-close i {
  font-size: 16px;
}

/* 聊天内容区域 */
.ai-chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #fff;
}

.ai-chat-messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 消息样式 */
.ai-message {
  display: flex;
  gap: 5px;
}

.ai-message-user {
  flex-direction: row-reverse;
}

.ai-message-avatar {
  flex-shrink: 0;
  font-size: 13px;
  color: #333333;
  letter-spacing: 0;
  font-weight: 600;
}

.ai-avatar-icon {
  width: 16px;
  height: 16px;
  color: white;
}

.ai-message-content {
  max-width: 260px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 扩展模式下的消息内容宽度 */
.ai-chat-expanded .ai-message-content {
  max-width: 660px;
}

.ai-message-text {
  padding: 10px;
  border-radius: 8px;
  font-size: 13px;
  line-height: 1.4;
  word-wrap: break-word;
}

.user-select-text {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}
.ai-message-bot {
  flex-direction: column;
}
.ai-message-bot .ai-message-text {
  background: white;
  color: #333333;
  background: #f5f6f8;
}
.ai-message-bot .ai-example-text {
  background: #f4f8ff;
  border-radius: 8px;
  border: 0;
  width: 100%;
}

.ai-message-bot .ai-waiting-message {
  background-image: linear-gradient(
    269deg,
    #3e82eb 0%,
    #56adff 100%
  ) !important;
  color: white !important;
  border: none !important;
}

/* 完成消息特殊样式 */
.ai-message-completion {
  animation: completionPulse 2s ease-in-out;
}

.ai-completion-text {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
  border: none !important;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  position: relative;
}

.ai-completion-text::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 20px;
  z-index: -1;
  opacity: 0.3;
  animation: completionGlow 2s ease-in-out infinite alternate;
}

.ai-message-user .ai-message-text {
  background-image: linear-gradient(269deg, #3e82eb 0%, #56adff 100%);
  color: white;
}

.ai-message-suggestion {
  padding: 8px 16px;
  background: #f3f4f6;
  border-radius: 12px;
  font-size: 13px;
  color: #6b7280;
  margin-top: 4px;
}

.ai-message-actions {
  display: flex;
  align-items: center;
  justify-content: right;
  gap: 8px;
  margin-top: 4px;
}

.ai-message-time {
  font-size: 11px;
  color: #9ca3af;
  padding: 0 4px;
}

.ai-detail-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  color: var(--xn-color-primary);
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
}

.ai-detail-button:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.3);
}

.ai-retry-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 12px;
  color: #ef4444;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: 4px;
}

.ai-retry-button:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
}

/* HTML内容样式 */
.ai-html-content-wrapper {
  position: relative;
}

.ai-html-content {
  line-height: 1.6;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-word;
}

.ai-html-content p {
  margin: 8px 0;
}

.ai-html-content ol,
.ai-html-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.ai-html-content li {
  margin: 4px 0;
}

.ai-html-content strong {
  font-weight: 600;
  color: #333333;
}

.ai-html-content a {
  color: #1e40af !important;
  text-decoration: underline !important;
  transition: all 0.2s;
}

.ai-html-content a:hover {
  color: #1d4ed8 !important;
  text-decoration: underline !important;
}

.ai-html-content div {
  margin: 4px 0;
}

.ai-html-content table {
  width: 100%;
  max-width: 100%;
  table-layout: fixed;
  word-wrap: break-word;
}

.ai-html-content img {
  max-width: 100%;
  height: auto;
}

.ai-html-content pre {
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.ai-html-content code {
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 全屏查看按钮 */
.ai-fullscreen-view-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(102, 126, 234, 0.9);
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
  z-index: 10;
}

.ai-fullscreen-view-btn:hover {
  background: rgba(102, 126, 234, 1);
  transform: translateY(-1px);
}

/* 欢迎消息样式 */
.ai-welcome-text {
  margin-bottom: 12px;
  line-height: 1.5;
}

.ai-welcome-examples {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.ai-example-link {
  font-size: 12px;
  color: var(--xn-color-primary);
  letter-spacing: 0;
  font-weight: 400;
}

.ai-example-link:hover {
  transform: translateY(-1px);
}

/* 输入区域 */
.ai-chat-input {
  background: white;
}

.ai-input-tabs {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  padding: 0 8px;
}

.ai-input-tab {
  padding: 0 4px;
  height: 23px;
  flex-shrink: 0;
  background: #ffffff;
  border: 1px solid rgba(216, 217, 220, 1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s;
  .ai-icon {
    width: 15px;
    height: 15px;
  }
  span {
    margin-left: 2px;
  }
}

.ai-input-tab.active {
  color: var(--xn-color-primary);
  border-color: var(--xn-color-primary);
  background: color-mix(in srgb, var(--xn-color-primary) 13%, white);
}

.ai-input-tab.disabled {
  color: #d1d5db !important;
  background: #f9fafb !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.ai-input-tab.disabled:hover {
  background: #f9fafb !important;
  color: #d1d5db !important;
}

.ai-input-container {
  padding: 8px;
}

.ai-input-wrapper {
  position: relative;
  width: 100%;
}

.ai-input-textarea {
  width: 100%;
  min-height: 40px;
  max-height: 120px;
  padding: 10px 44px 24px 12px;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  resize: none;
  font-size: 14px;
  line-height: 1.4;
  outline: none;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.ai-input-textarea:focus {
  border-color: var(--xn-color-primary);
}

.ai-input-textarea::placeholder {
  color: #9ca3af;
}

.ai-input-counter {
  position: absolute;
  bottom: 15px;
  right: 32px;
  font-size: 11px;
  color: #9ca3af;
  pointer-events: none;
  user-select: none;
  transition: color 0.2s;
}

.ai-input-counter.warning {
  color: #f59e0b;
}

.ai-input-counter.danger {
  color: #ef4444;
}

.ai-send-button {
  position: absolute;
  bottom: 10px;
  right: 5px;
  .svg-icon {
    width: 20px;
    height: 20px;
  }
  border: none;
  border-radius: 100%;
  color: var(--xn-color-primary);
}

.ai-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.ai-send-button i {
  font-size: 16px;
}

.ai-waiting-message {
  background-image: linear-gradient(269deg, #3e82eb 0%, #56adff 100%);
  color: white !important;
  border: none;
  padding: 12px 16px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
}

.ai-waiting-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.ai-waiting-dots {
  display: flex;
  gap: 4px;
}

.ai-waiting-dots span {
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.ai-waiting-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.ai-waiting-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.ai-waiting-text {
  font-size: 14px;
  font-weight: 500;
  color: white;
}

.ai-waiting-tip {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.ai-status-tip {
  padding: 8px 16px;
  background: #f0f9ff;
  border-top: 1px solid #e0f2fe;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #0369a1;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes completionPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes completionGlow {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.6;
  }
}

/* 详情对话框样式 */
.ai-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.ai-detail-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  max-height: 80vh;
  width: 90vw;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ai-detail-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ai-detail-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.ai-detail-close {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s;
}

.ai-detail-close:hover {
  background: #e5e7eb;
  color: #333333;
}

.ai-detail-content {
  flex: 1;
  padding: 20px 24px;
  overflow: hidden;
}

.ai-monaco-container {
  width: 100%;
  height: 400px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.fallback-code {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #334155;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  max-height: 400px;
  overflow-y: auto;
  user-select: text;
}

.ai-detail-footer {
  padding: 16px 24px 20px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.ai-execute-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.ai-execute-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.ai-copy-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, var(--xn-color-primary) 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.ai-copy-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.ai-apply-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.ai-apply-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

/* 滚动条样式 */
.ai-chat-content::-webkit-scrollbar {
  width: 4px;
}

.ai-chat-content::-webkit-scrollbar-track {
  background: transparent;
}

.ai-chat-content::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.ai-chat-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 服务选择区域 */
.ai-services-section {
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background: #f9fafb;
  margin-bottom: 8px;
}

.ai-services-header {
  display: flex;
  justify-content: space-between;
  padding: 4px 12px;
}

.ai-services-label {
  font-size: 13px;
  font-weight: 500;
  color: #333333;
  display: flex;
  flex-wrap: wrap;
}

.ai-services-count {
  padding: 2px 6px;
  background: var(--xn-color-primary);
  color: white;
  border-radius: 10px;
  font-size: 11px;
  font-weight: normal;
}

.ai-services-actions {
  display: flex;
  align-items: start;
  gap: 8px;
  flex-shrink: 0;
}

.ai-services-clear {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  background: transparent;
  border: 1px solid #ef4444;
  border-radius: 6px;
  color: #ef4444;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;
}

.ai-services-clear:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.ai-services-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  background: transparent;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: var(--xn-color-primary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.ai-services-toggle:hover {
  background: var(--xn-color-primary);
  color: white;
  border-color: var(--xn-color-primary);
}

.ai-services-content {
  padding: 12px;
  background: white;
  border-radius: 0 0 8px 8px;
}

.ai-services-loading,
.ai-services-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #6b7280;
  font-size: 13px;
}

.ai-services-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.ai-service-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.ai-service-item:hover {
  border-color: var(--xn-color-primary);
  background: #f8faff;
}

.ai-service-checkbox {
  margin-top: 2px;
  accent-color: var(--xn-color-primary);
}

.ai-service-info {
  flex: 1;
}

.ai-service-name {
  font-size: 13px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 2px;
}

.ai-service-path {
  font-size: 11px;
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 文本输入区域 */
.ai-textarea-wrapper {
  position: relative;
  width: 100%;
}

.ai-textarea {
  width: 100%;
  height: 120px;
  padding: 10px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  resize: vertical;
  font-size: 14px;
  line-height: 1.4;
  outline: none;
  transition: border-color 0.2s;
  box-sizing: border-box;
  font-family: inherit;
  background: white;
}

.ai-textarea:focus {
  border-color: var(--xn-color-primary);
}

.ai-textarea::placeholder {
  color: #9ca3af;
}

/* 枚举选择样式 */
.ai-enum-tree-container {
  padding: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  max-height: 200px;
  overflow-y: auto;
}

/* 枚举树形组件内部样式优化 */
.ai-enum-tree-container :deep(.xn-tree-node) {
  white-space: nowrap;
  overflow: visible;
}

.ai-enum-tree-container :deep(.xn-tree-node__content) {
  padding: 4px 8px;
  min-height: 28px;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.ai-enum-tree-container :deep(.xn-tree-node__label) {
  flex: 1;
  font-size: 13px !important;
  font-weight: 500;
  color: #333333;
  line-height: 1.4;
  word-break: break-all;
  white-space: normal;
  overflow: visible;
  text-overflow: unset;
  max-width: none;
}

.ai-enum-tree-container :deep(.xn-checkbox) {
  margin-right: 8px;
  flex-shrink: 0;
}

.ai-enum-tree-container :deep(.xn-tree-node__expand-icon) {
  margin-right: 4px;
  flex-shrink: 0;
}

/* 确保枚举树的所有文本都使用统一的字体大小 */
.ai-enum-tree-container :deep(.xn-tree-node) {
  font-size: 13px !important;
  font-weight: 500;
  color: #333333;
}

.ai-enum-tree-container :deep(.xn-tree-node__content) {
  font-size: 13px !important;
}

.ai-enum-tree-container :deep(.xn-tree) {
  font-size: 13px !important;
}

/* 全屏内容对话框样式 */
.ai-fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
}

.ai-fullscreen-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 90vw;
  max-height: 90vh;
  width: 80vw;
  height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ai-fullscreen-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.ai-fullscreen-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.ai-fullscreen-close {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s;
}

.ai-fullscreen-close:hover {
  background: #e5e7eb;
  color: #333333;
}

.ai-fullscreen-content {
  flex: 1;
  padding: 20px 24px;
  overflow: auto;
}

.ai-fullscreen-html-content {
  line-height: 1.6;
  word-wrap: break-word;
  word-break: break-word;
}

.ai-fullscreen-html-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.ai-fullscreen-html-content th,
.ai-fullscreen-html-content td {
  border: 1px solid #e5e7eb;
  padding: 8px 12px;
  text-align: left;
}

.ai-fullscreen-html-content th {
  background: #f9fafb;
  font-weight: 600;
}

.ai-fullscreen-html-content img {
  max-width: 100%;
  height: auto;
  margin: 16px 0;
}

.ai-fullscreen-html-content pre {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 16px 0;
}

.ai-fullscreen-html-content code {
  background: #f1f5f9;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.ai-fullscreen-html-content a {
  color: #1e40af !important;
  text-decoration: underline !important;
  transition: all 0.2s;
}

.ai-fullscreen-html-content a:hover {
  color: #1d4ed8 !important;
}

/* 图片上传容器样式 */
.ai-image-upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
  background: #fafafa;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

/* 图片上传按钮样式 */
.ai-image-upload-button {
  width: 80px;
  height: 80px;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: #ffffff;
  transition: all 0.2s ease;
  color: #6b7280;
  font-size: 12px;
}

.ai-image-upload-button:hover {
  border-color: var(--xn-color-primary);
  color: var(--xn-color-primary);
  background: #f8faff;
}

.ai-image-upload-button i {
  font-size: 20px;
  margin-bottom: 4px;
}

.ai-image-upload-hint {
  font-size: 10px;
  color: #9ca3af;
  text-align: center;
  line-height: 1.2;
  margin-top: 2px;
}

/* 图片预览样式 */
.ai-image-preview {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  background: #ffffff;
  border: 1px solid #e5e7eb;
}

.ai-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.ai-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.ai-image-preview:hover .ai-image-overlay {
  opacity: 1;
}

.ai-image-name {
  color: white;
  font-size: 10px;
  line-height: 1.2;
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.ai-image-remove {
  align-self: flex-end;
  width: 20px;
  height: 20px;
  border: none;
  background: rgba(239, 68, 68, 0.8);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: background 0.2s ease;
}

.ai-image-remove:hover {
  background: rgba(239, 68, 68, 1);
}
</style>
