import { registry, addCtxMaterials } from 'sinitek-lowcode-shared'
import SinitekLowcodeAdvanced from 'sinitek-lowcode-advanced'
import SinitekLowcodeMaterials from 'sinitek-lowcode-materials'

import { operation, auth } from 'sinitek-util'

// import SinitekLowcodeRender from 'sinitek-lowcode-render'

// import Vue from 'vue'
registry(SinitekLowcodeAdvanced)
addCtxMaterials(SinitekLowcodeMaterials)

registry({
  install(simulator) {
    simulator.addUtil('download', (url, params, options) => {
      let join = '?'
      if (url.indexOf('?') > -1) {
        join = '&'
      }
      operation.download(
        `${url}${join}accesstoken=${auth.getToken()}`,
        params,
        options
      )
    })
  },
})

// Vue.use(SinitekLowcodeRender)
