import {
  ModuleAPI,
  TemplateAPI,
  DataModelAPI,
  EnumDesignAPI,
  EnumRuntimeAPI,
} from 'src/api'
// import Save from './toolbar/save'
// // import Debug from './toolbar/debug'
// import ComponentSave from './toolbar/componentSave.vue'
// import Page from './toolbar/page.vue'
import { COMPONENT_TYPE } from 'src/constant'
import { DesignMode, isDesignMode } from 'sinitek-lowcode-shared'
import './registry.js'
import Sirmapp from 'sirmapp'
export default function (mode = DesignMode.DESIGN) {
  const EnumAPI = isDesignMode(mode) ? EnumDesignAPI : EnumRuntimeAPI

  return {
    components: {
      SinitekLowcodeSimulator: () => import('sinitek-lowcode-simulator'),
    },
    computed: {
      config() {
        return {
          // 获取图标 解耦sirmapp的getIcons
          getIcons: Sirmapp.getIcons,
          fetcher: {
            // datasouce获取数据函数
            fetcher: async (options, otherOptions = {}) => {
              return this.$http[options.method.toLowerCase()](
                options.url,
                options.data,
                { headers: options.headers, ...otherOptions }
              )
            },
            // 获取ai属性的接口
            getAIProps: async (params) => {
              const res = await this.$http.get(
                ModuleAPI.RECOMMAND_COMPONENT_PROPS(),
                {
                  ...params,
                  platform: 'SINITEK',
                  formId: this.$route.query.id,
                },
                {
                  showLoading: true,
                  loadingOptions: {
                    background: 'rgba(0,0,0,0.3)',
                  },
                }
              )
              return res.data || []
            },
            // 获取模板数据的接口
            getTemplate: async (params) => {
              const res = await this.$http.post(
                TemplateAPI.TEMPLATE_SEARCH(),
                params
              )
              if (res.resultcode === '0') {
                return { data: res.data.datalist, total: res.data.totalsize }
              } else {
                this.$message.error(res.message)
              }
              return null
            },
            // 设置模板数据的接口
            setSchema: async (templateId) => {
              let jsonSchema = {}
              const res = await this.$http.get(
                TemplateAPI.TEMPLATE_LIST_DETAIL(),
                {
                  id: templateId,
                }
              )
              if (res.resultcode === '0') {
                if (res.data.pcJson) {
                  jsonSchema = JSON.parse(res.data.pcJson)

                  return jsonSchema
                } else {
                  this.$message.error('获取失败')
                }
              } else {
                this.$message.error(res.message)
              }
              return null
            },
            // 获取模型列表的
            getModelList: async () => {
              return this.$http
                .get(DataModelAPI.MODEL_AVAILABLELIST(), {
                  moduleCode: this.moduleCode,
                })
                .then((res) => {
                  return res.data.map((e) => ({
                    label: e.name,
                    value: e.code,
                  }))
                })
            },
            getRefModelList: async (v) => {
              return this.$http
                .get(DataModelAPI.MODEL_REFLIST(), { modelCode: v })
                .then((res) => {
                  return res.data.map((e) => ({
                    label: e.name,
                    value: e.code,
                  }))
                })
            },
            // 获取模型字段用在条件筛选上
            getModelFields: async (code) => {
              const isGetChildren = (type) =>
                [
                  COMPONENT_TYPE.FOREIGN_KEY,
                  COMPONENT_TYPE.CHILDREN_FORM,
                ].includes(type)
              const getChildren = async (v, dep = 0) => {
                // 防止错误设置，无限递归
                if (dep > 2) return []
                const res = await this.getField(v)
                const promise = []
                const fields = [
                  ...(res?.fieldProps ?? []),
                  ...(res?.subModelProps ?? []),
                  ...(res?.mvProps ?? []),
                  ...(res?.attachmentProps ?? []),
                ]
                fields?.forEach((e) => {
                  if (isGetChildren(e.componentType)) {
                    promise.push(getChildren(e.relaModelCode, dep + 1))
                  }
                })
                const promiseResult = await Promise.all(promise)
                return fields?.map((e) => {
                  const result = {
                    label: e.comments || e.name,
                    value: e.name,
                    data: e,
                  }
                  if (isGetChildren(e.componentType)) {
                    result.children = promiseResult.shift()
                  }
                  return result
                })
              }
              const load = await getChildren(code)
              if (load) return load
              return []
            },
            // 模型的crud
            modelFetchers: {
              /**
               * params: {data: {}, modelCode, draftFlag: falsse}
               */
              create: (params, otherOptions) => {
                return this.$http.post(
                  DataModelAPI.MODEL_DYNAMIC_SAVE(params.data),
                  params.data,
                  otherOptions
                )
              },
              // this.fetcher.xxx.list({conditin:{}, page, pageSize, ...})
              list: (params, otherOptions) => {
                return this.$http.post(
                  DataModelAPI.MODEL_DYNAMIC_READ(params.data),
                  params.data,
                  otherOptions
                )
              },
              // {id}
              single: (params, otherOptions) => {
                return this.getDetailByID(
                  params.data.modelCode,
                  params.data.id,
                  otherOptions
                )
              },
              /**
               * params: {data: {}, modelCode, draftFlag: falsse}
               */
              update: (params, otherOptions) => {
                return this.$http.post(
                  DataModelAPI.MODEL_DYNAMIC_UPDATE_BY_KEY(params.data),
                  params.data,
                  otherOptions
                )
              },
              /**
               * params: {id, ids}
               */
              delete: (params, otherOptions) => {
                const newParams = {}
                if (params.data) {
                  newParams.modelCode = params.data.modelCode
                  newParams.param = {
                    id: params.data.id,
                    ids: params.data.ids,
                  }
                }
                return this.$http.post(
                  DataModelAPI.MODEL_DYNAMIC_DELETE_BY_KEY(newParams),
                  newParams,
                  otherOptions
                )
              },
              // 获取列表并带出关联表单数据
              lazyList: async (params, otherOptions) => {
                const list = await this.$http.post(
                  DataModelAPI.MODEL_DYNAMIC_READ(params.data),
                  params.data,
                  otherOptions
                )
                if (list?.data?.datalist?.length > 0) {
                  for (let i = 0; i < list.data.datalist.length; i++) {
                    const e = list.data.datalist[i]
                    this.getLazyRelaForm(params.data.modelCode, e)
                  }
                }
                return list
              },
              // 获取单个数据并带出关联表单和子表单数据
              // {id}
              lazySingle: async (params, otherOptions) => {
                const data = await this.getDetailByID(
                  params.data.modelCode,
                  params.data.id,
                  otherOptions
                )
                if (!data?.data) return data
                // 获取当前模型的关联表单字段数据
                this.getLazyRelaForm(params.data.modelCode, data.data)
                this.getLazyChildren(params.data.modelCode, data.data)
                return data
              },
            },
            // 获取枚举配置
            getEnumList: async (
              params = {
                catalog: '',
                name: '',
                type: '',
              }
            ) => {
              const res = await this.$http.post(EnumAPI.ENUM_LOAD_ALL(), [
                params,
              ])
              const result = []
              if (res.data) {
                Object.keys(res.data).forEach((key) => {
                  const value = res.data[key]
                  const option = {
                    label: key,
                    value: key,
                    children: Object.keys(value).map((e) => ({
                      label: e,
                      value: e,
                    })),
                  }
                  result.push(option)
                })
              }
              return result
            },
            getEnum: async (params) => {
              const res = await this.$http.post(
                EnumAPI.ENUM_LOAD(),
                params.data
              )
              return res.data || []
            },
          },
          // toolbar: [
          //   mode === DesignMode.DESIGN && {
          //     id: 'ToolbarSave',
          //     name: 'save',
          //     title: '保存',
          //     component: Save,
          //     align: 'right',
          //   },
          //   // mode === DesignMode.LOWCODE && {
          //   //   id: 'ToolbarDebug',
          //   //   name: 'debug',
          //   //   title: '调试',
          //   //   component: Debug,
          //   //   align: 'right',
          //   //   order: 5,
          //   // },
          //   mode === DesignMode.LOWCODE && {
          //     id: 'ToolbarExport',
          //     name: 'export',
          //     title: '导出',
          //     component: () => import('./toolbar/export.vue'),
          //     align: 'right',
          //     order: 5,
          //   },
          //   mode === DesignMode.LOWCODE && {
          //     id: 'ToolbarComponentSave',
          //     name: 'componentSave',
          //     title: '保存',
          //     component: ComponentSave,
          //     align: 'right',
          //     order: 5,
          //   },
          //   mode === DesignMode.DESIGN && {
          //     id: 'ToolbarPage',
          //     name: 'page',
          //     title: 'AI生成页面',
          //     component: Page,
          //     align: 'right',
          //     order: 6,
          //   },
          // ],
          message: {
            success: (msg) => {
              this.$message.success(msg)
            },
            error: (msg) => {
              this.$message.error(msg)
            },
            confirm: this.$confirm,
          },
        }
      },
    },
    data() {
      return {
        isActivated: true,
        // 刷新或者切换时清除
        cacheField: {},
        cacheDetailByID: {},
      }
    },
    mounted() {
      this.isActivated = true
      this.$nextTick(() => {
        this.init()
      })
    },
    activated() {
      this.isActivated = true
      this.$nextTick(() => {
        this.init()
      })
    },
    deactivated() {
      this.isActivated = false
    },
    methods: {
      async getField(modelCode) {
        if (!this.cacheField[modelCode]) {
          this.cacheField[modelCode] = await this.$http
            .post(DataModelAPI.MODEL_RUNTIME_LOAD(), {
              modelCode,
            })
            .then((e) => e?.data)
        }
        return this.cacheField[modelCode]
      },
      async getDetailByID(modelCode, id, otherOptions, isCache = false) {
        const key = `${modelCode}-${id}`
        if (!isCache || !this.cacheDetailByID[key]) {
          this.cacheDetailByID[key] = await this.$http.post(
            DataModelAPI.MODEL_DYNAMIC_LOAD_DETAIL_BY_KEY({ modelCode }),
            {
              modelCode,
              param: {
                id,
              },
            },
            otherOptions
          )
        }
        return this.cacheDetailByID[key]
      },
      getList(data) {
        return this.$http.post(DataModelAPI.MODEL_DYNAMIC_READ(data), data)
      },
      // 给item对应的属性赋值列表数据
      async getLazyChildren(modelCode, item) {
        const fileds = await this.getField(modelCode).then(
          (e) => e?.subModelProps
        )
        if (!fileds && !fileds.length) return
        const childrenFormFields = fileds.filter(
          (e) => e.componentType === COMPONENT_TYPE.CHILDREN_FORM
        )
        if (!childrenFormFields.length) return
        for (let i = 0; i < childrenFormFields.length; i++) {
          const e = childrenFormFields[i]
          item[e.name] = []
          const res = await this.getList({
            modelCode: e.relaModelCode,
            condition: {
              and: e.propRelas.map((p) => {
                return {
                  field: p.childName,
                  value: item[p.parentName],
                  op: 'eq',
                }
              }),
            },
          })
          const result = []
          if (res?.data?.datalist?.length > 0) {
            let itemResult = {}
            for (const _item of res.data.datalist) {
              itemResult = { ..._item }
              await this.getLazyRelaForm(e.relaModelCode, itemResult, [
                modelCode,
              ])
              result.push(itemResult)
            }
          }
          item[e.name] = result
        }
      },
      /**
       * 获取关联表单的数据
       * @param {string} modelCode 模型code
       * @param {object} item 数据，会改变item的值
       * @param {array} excluded 排除的modelCode, 针对子表单的外键不进行关联查询
       */
      async getLazyRelaForm(modelCode, item, excluded = []) {
        const fields = await this.getField(modelCode).then((res) => {
          return res?.fieldProps
        })
        const relaForm = fields.filter(
          (e) =>
            e.componentType === COMPONENT_TYPE.RELA_FORM &&
            !excluded.includes(e.relaModelCode)
        )
        if (relaForm.length) {
          for (const key of Object.keys(item)) {
            const field = relaForm.find((r) => r.name === key)
            if (field) {
              const ids = item[key]
              // 根据外键是对象还是数组来处理
              // 对象只查询一条，数组查询多条
              let result = {}
              if (!Array.isArray(ids)) {
                result = await this.getDetailByID(
                  field.relaModelCode,
                  ids?.id ?? ids,
                  {},
                  true
                ).then((res) => res.data)
              } else {
                result = []
                result = ids.map((_) =>
                  this.getDetailByID(
                    field.relaModelCode,
                    _?.id ?? _,
                    {},
                    true
                  ).then((res) => res.data)
                )
                result = await Promise.all(result)
              }
              item[key] = result
            }
          }
        }
      },
    },
  }
}
