<template>
  <div v-loading="loading" class="app-container">
    <SinitekLowcodeRender
      :fetcher="config.fetcher"
      :config="currentSchema"
      :mode="mode"
    ></SinitekLowcodeRender>
  </div>
</template>

<script>
import { SimulatorAPI } from 'src/api'
import fetcherMixins from './fetcherMixins'
import SinitekLowcodeRender from 'sinitek-lowcode-render'
import { DesignMode } from 'sinitek-lowcode-shared'
export default {
  name: 'LCRender',
  mixins: [fetcherMixins(DesignMode.PUBLISH)],
  components: {
    SinitekLowcodeRender,
  },
  props: {
    id: String,
    moduleCode: String,
    mode: String,
    schema: Object,
  },
  data() {
    return {
      pageData: {},
      loading: false,
    }
  },
  computed: {
    currentSchema() {
      return this.schema || this.pageData
    },
  },
  watch: {
    id() {
      this.init()
    },
  },
  mounted() {
    this.isActivated = true
    this.$nextTick(() => {
      this.init()
    })
  },
  activated() {
    this.isActivated = true
    this.$nextTick(() => {
      this.init()
    })
  },
  deactivated() {
    this.isActivated = false
  },
  methods: {
    init() {
      if (!this.id) return
      this.cacheField = {}
      this.loading = true
      // 请求接口获取表单数据
      this.$http
        .get(SimulatorAPI.GET_DESIGN_DATA_BY_ID(), {
          id: this.id,
        })
        .then((res) => {
          const dv = {
            componentName: 'Page',
            datasource: [],
            state: {},
            methods: {},
            children: [],
          }
          this.pageData = res.data?.pageData ?? dv
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>
