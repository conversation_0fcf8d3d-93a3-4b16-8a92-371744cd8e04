<template>
  <SinitekLowcodeSimulator
    class="app-container"
    :key="id"
    ref="simulator"
    v-if="isActivated"
    :config="config"
    v-loading="loading"
    mode="design"
    @hook:mounted="onMounted"
  />
</template>

<script>
import { SimulatorAPI } from 'src/api'
import fetcherMixins from './fetcherMixins'
import { DesignMode } from 'sinitek-lowcode-shared'
import aiAssistant from './ai-assistant'
export default {
  name: 'LCSimulator',
  mixins: [fetcherMixins(DesignMode.DESIGN)],
  props: {
    id: String,
    moduleCode: String,
  },
  provide() {
    return {
      // 保存使用获取id
      simulatorProps: this.simulatorProps,
    }
  },
  data() {
    return {
      simulatorProps: {
        id: this.id,
        moduleCode: this.moduleCode,
      },
      isActivated: true,
      loading: false,
    }
  },
  mounted() {
    this.isActivated = true
    this.$nextTick(() => {
      this.init()
    })
  },
  activated() {
    this.isActivated = true
    this.$nextTick(() => {
      this.init()
    })
  },
  deactivated() {
    this.isActivated = false
  },
  methods: {
    init() {
      if (!this.isActivated || !this.id) return
      this.cacheField = {}
      this.load()
    },
    checkChange() {
      return this.$refs.simulator.checkChange()
    },

    load() {
      if (!this.id) return
      this.loading = true
      // 请求接口获取表单数据
      this.$http
        .get(SimulatorAPI.GET_DESIGN_DATA_BY_ID(), {
          id: this.id,
        })
        .then((res) => {
          const dv = {
            componentName: 'Page',
            datasource: [],
            state: {},
            methods: {},
            children: [],
            className: 'blank-container',
            id: Math.random().toString(36).slice(2),
          }
          if (this.isLoaded) {
            this.$refs.simulator.setSchema(res.data?.pageData ?? dv)
            this.isSet = true
          } else {
            this.pageData = res.data?.pageData ?? dv
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    onMounted() {
      this.isLoaded = true
      if (!this.isSet && this.pageData) {
        this.$refs.simulator.setSchema(this.pageData)
        this.isSet = true
      }
      this.$nextTick(() => {
        this.$refs.simulator.register({
          area: 'topArea',
          id: 'ToolbarSave',
          name: 'save',
          title: '保存',
          component: () => import('./toolbar/previewAndSave/src/main.vue'),
          align: 'right',
          order: 7,
        })
        this.$refs.simulator.register({
          area: 'topArea',
          id: 'ToolbarPage',
          name: 'page',
          title: 'AI生成页面',
          component: () => import('./toolbar/page.vue'),
          align: 'right',
          order: 6,
        })
        this.$refs.simulator.register(aiAssistant)
      })
    },
  },
  watch: {
    id(v) {
      this.simulatorProps.id = v
      this.init()
    },
  },
}
</script>
