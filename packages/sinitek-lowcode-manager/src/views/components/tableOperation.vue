<template>
  <div style="display: flex; align-items: center; justify-content: center">
    <!-- 第一个按钮 -->
    <xn-button
      style="margin-right: 5px"
      type="primary"
      size="small"
      :show-loading="false"
      plain
      @click="handleClickFirstOpera"
    >
      {{ firstOpera.label }}
    </xn-button>
    <!-- 收起的按钮 -->
    <el-dropdown
      trigger="hover"
      style="margin-left: 5px"
      placement="bottom-end"
      @command="handleCommand"
    >
      <el-button type="primary" plain>
        <div style="display: flex; align-items: center">
          <svg-icon
            cursor="pointer"
            icon-color="invert active"
            icon-class="shezhi1"
            icon-size="14"
          />
          <i class="el-icon-arrow-down" style="margin-left: 5px" />
        </div>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          style="padding: 0"
          v-for="(item, index) in elDropdownItems"
          :key="index"
          :command="item"
        >
          <slot v-if="item.isCustom" name="custom" />
          <span style="padding: 0 15px" v-else>
            {{ item.label }}
          </span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  name: 'TableOperation',
  props: {
    operations: {
      type: Array,
      default: () => [],
    },
    row: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      firstOpera: null,
      elDropdownItems: [],
    }
  },
  watch: {
    operations: {
      handler(newVal, oldVal) {
        if (newVal.length > 0) {
          const tempOperations = [...newVal]
          for (let i = 0; i < tempOperations.length; i++) {
            if (tempOperations[i].isFirstOpera) {
              this.firstOpera = tempOperations[i]
              tempOperations.splice(i, 1)
              this.elDropdownItems = [...tempOperations]
              break
            }
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    handleClickFirstOpera() {
      this.$emit('click', { item: this.firstOpera, row: this.row })
    },
    handleCommand(command) {
      if (command.functionName) {
        this.$emit('click', { item: command, row: this.row })
      }
    },
  },
}
</script>

<style scoped>
::v-deep.el-dropdown-menu.el-popper {
  overflow-y: hidden;
}
</style>
