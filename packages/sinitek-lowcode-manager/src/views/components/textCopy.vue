<template>
  <div :class="{ 'text-copy': canCopy }">
    <span @click="handleCopyIconClick">{{ text }}</span>
    <i
      v-if="canCopy"
      class="el-icon-copy-document copy-icon"
      @click="handleCopyIconClick"
    />
    <slot />
  </div>
</template>

<script>
import { canBrowserCopy } from 'src/utils'
import { log } from 'sinitek-lowcode-shared'
export default {
  Text: 'TextCopy',
  components: {},
  props: {
    text: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
  computed: {
    canCopy() {
      return canBrowserCopy && !!this.text
    },
  },
  methods: {
    handleCopyIconClick() {
      if (this.canCopy) {
        navigator.clipboard
          .writeText(this.text)
          .then(() => {
            this.$message.success('复制成功')
          })
          .catch((error) => {
            log('复制失败', error)
            this.$message.info('复制失败')
          })
      }
    },
  },
}
</script>

<style scoped>
.text-copy {
  cursor: copy;
}
.text-copy span:hover {
  text-decoration: underline;
  color: red;
}
.copy-icon {
  padding-left: 5px;
  cursor: pointer;
}
</style>
