<template>
  <div
    ref="el"
    class="monaco-editor"
    :style="{ width: width, height: height }"
  />
</template>

<script>
import * as monaco from 'monaco-editor'

export default {
  name: 'LcEditor',
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    language: {
      type: String,
      default: 'json',
    },
    modelValue: String,
    options: Object,
  },
  data() {
    return {
      editor: null,
    }
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    this?.editor?.dispose()
    this?.js?.dispose()
  },
  methods: {
    init() {
      this.editor = monaco.editor.create(this.$refs.el, {
        value: this.modelValue,
        minimap: {
          enabled: false,
        },
        formatOnPaste: true,
        ...this.options,
      })
      this.editor.onDidChangeModelContent(() => {
        const val = this.editor.getValue()
        if (this.modelValue !== val) {
          this.$emit('update:modelValue', val)
          this.$emit('input', val)
        }
      })
    },
    refresh() {
      // 刷新monaco-editor大小
      this.$nextTick(() => {
        this.editor.layout()
      })
    },
    getValue() {
      return this.editor.getValue()
    },
  },
  watch: {
    language() {
      this.editor?.dispose()
      this.init()
    },
    modelValue(val) {
      if (val !== this.editor.getValue()) {
        this.editor.setValue(val)
      }
    },
  },
}
</script>
<style scoped>
.monaco-editor ::v-deep .margin-view-overlays {
  background-color: #ebebeb !important;
}
</style>
