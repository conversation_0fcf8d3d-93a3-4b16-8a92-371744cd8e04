<template>
  <div style="display: inline-block; width: 100%">
    <!-- 同步多行数据按钮 -->
    <el-button
      v-if="isSyncAll"
      type="primary"
      plain
      @click="handleAllDataSetSync"
    >
      同步
    </el-button>
    <!-- 同步单行数据按钮 -->
    <!-- <div
      style="width: 100%; padding: 0 15px"
      v-else
      @click="handleDataSetSync"
      type="text"
      size="small"
    >
      同步
    </div> -->
    <!-- 同步弹窗 -->
    <xn-dialog
      :title="dealTitle"
      width="510px"
      :show.sync="dialog.display"
      :buttons="dialog.buttons"
      @save="handleSave"
      @close="handleClose"
    >
      <template slot="dialog-form">
        <div v-if="dealIsinit" class="dialog-content">
          <div>{{ dealTip }}</div>
          <div
            class="scroll-box"
            v-if="isSyncAll || type === 'form' || type === 'model'"
          >
            <div
              :style="{
                height: templateSelections.length > 30 ? '300px' : 'auto',
              }"
              class="scroll"
            >
              <div class="text-box">
                <div v-for="(item, index) in templateSelections" :key="index">
                  <span v-if="!isSyncAll && type === 'form' && index === 0">
                    数据模型：
                  </span>
                  <span>【{{ item.name }}】</span>
                  <span v-if="index !== templateSelections.length - 1">、</span>
                </div>
              </div>
            </div>
          </div>
          <div class="scroll-box" v-if="!isSyncAll && type === 'form'">
            <div
              :style="{
                height: templateSelections.length > 30 ? '300px' : 'auto',
              }"
              class="scroll"
            >
              <div class="text-box">
                <div
                  v-for="(item, index) in templateSelections[0].dataSets"
                  :key="index"
                >
                  <!-- <el-tooltip
                    effect="dark"
                    :content="item.name"
                    placement="top"
                    v-if="item.name.length>14"
                  >
                    <div class="span">
                      <span>{{ item.name }}</span>
                    </div>
                  </el-tooltip>
                  <span v-else>{{ item.name }}</span> -->
                  <span v-if="!isSyncAll && type === 'form' && index === 0">
                    数据集：
                  </span>
                  <span>【{{ item.name }}】</span>
                  <span v-if="index !== templateSelections.length - 1">、</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </xn-dialog>
  </div>
</template>
<script>
import { ModuleAPI, FormAPI, DataModelAPI, DataSetAPI, MfAppAPI } from 'src/api'
import { VERSION_STATUS } from 'src/constant'
export default {
  name: 'SyncDialog',
  data() {
    return {
      dialog: {
        display: false,
        buttons: [
          { label: '确定', action: 'save', type: 'primary', event: 'save' },
          { label: '取消', action: 'cancel', type: 'info', event: 'close' },
        ],
      },
      ids: [],
      selectionsByIds: [],
      selectionsData: [],
    }
  },
  props: {
    // 同步的类型(数据集---dataset、数据模型---model、表单---form、模块---module)
    type: {
      type: String,
      default: '',
    },
    // 是否是同步多个按钮
    isSyncAll: {
      type: Boolean,
      default: true,
    },
    // 是否会查询关联数据
    isRelated: {
      type: Boolean,
      default: false,
    },
    appInfo: {
      type: Object,
      default: () => {},
    },
    refTable: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    // 之所以加这个是因为：
    // 1、同步多个表单时，需要筛选传进来的表单数据为已发布状态，所以需要对props中的selections进行过滤赋值
    // 2、由于不能对selections直接赋值，所以在data中定义了一个selectionsData数组，作为selections拷贝值
    // 3、只有在点击同步时才能实时获取到selections值，导致初始化同步组件时，传给自定义插槽的selectionsData初始值是空数组，使用scope[0].name时报错
    // 4、在插槽中直接v-for:seletionsData或selectionsByIds不会报错，v-for会判断是否应该渲染
    // 所以需要判断是否显示自定义插槽
    dealIsinit() {
      if (this.isRelated) {
        return this.selectionsByIds.length !== 0
      } else {
        return this.selectionsData.length !== 0
      }
    },
    dealTitle() {
      let title = ''
      switch (this.type) {
        case 'mfApp':
          title = '应用同步'
          break
        case 'module':
          title = this.appInfo.name
            ? `${this.appInfo.name}应用-模块同步`
            : '模块同步'
          break
        case 'model':
          title = this.appInfo.name
            ? `${this.appInfo.name}应用-数据模型同步`
            : '数据模型同步'
          break
        case 'form':
          title = this.appInfo.name
            ? `${this.appInfo.name}应用-表单同步`
            : '表单同步'
          break
        case 'dataset':
          title = this.appInfo.name
            ? `${this.appInfo.name}应用-数据集同步`
            : '数据集同步'
          break
      }
      return title
    },
    templateSelections() {
      return this.isRelated ? this.selectionsByIds : this.selectionsData
    },
    // 处理tip展示
    dealTip() {
      let tip = ''
      switch (this.type) {
        case 'module':
          tip = `确定将以下模块同步到【${this.appInfo.serviceName}】？`
          break
        case 'form':
          tip = `确定将以下表单同步到【${this.appInfo.serviceName}】？`
          break
        case 'model':
          tip = `确定将以下数据模型同步到【${this.appInfo.serviceName}】？`
          break
        case 'dataset':
          tip = `确定将以下数据集同步到【${this.appInfo.serviceName}】？`
          break
        case 'mfApp':
          tip = `确定将【${this.selectionsData[0].name}应用】下的数据同步到【${this.selectionsData[0].serviceName}】？`
          break
      }
      if (!this.isSyncAll) {
        switch (this.type) {
          case 'module':
            tip = `确定将【${this.templateSelections[0].name}模块】同步到【${this.appInfo.serviceName}】？`
            break
          case 'dataset':
            tip = `确定将【${this.templateSelections[0].name}数据集】同步到【${this.appInfo.serviceName}】？`
            break
        }
      }
      return tip
    },
  },
  methods: {
    // 确认同步
    handleSave(resolve, reject) {
      let url
      switch (this.type) {
        case 'module': {
          url = ModuleAPI.MODULE_SYNC()
          break
        }
        case 'model': {
          url = DataModelAPI.MODEL_SYNC()
          break
        }
        case 'dataset': {
          url = DataSetAPI.DATASET_SYNC()
          break
        }
        case 'form': {
          url = FormAPI.FORM_SYNC()
          break
        }
        case 'mfApp': {
          url = MfAppAPI.MFAPP_SYNC()
          break
        }
      }
      this.$http
        .post(url, { ids: this.ids })
        .then((res) => {
          if (res.success) {
            this.$message.success('同步成功')
            this.handleClose()
          }
        })
        .catch((e) => {
          this.$message.closeAll()
          reject(e.message || '同步失败')
        })
    },
    // 关闭弹窗
    handleClose() {
      this.dialog.display = false
    },
    // 打开弹窗
    show() {
      this.dialog.display = true
    },
    // 同步多行数据
    handleAllDataSetSync() {
      this.selectionsData = this.refTable.multipleSelection
      if (this.selectionsData.length === 0) {
        this.$message.info('请选择要同步的数据')
      } else {
        if (this.type === 'form') {
          const filterPublishForm = this.selectionsData.filter(
            (item) => item.publishStatus === VERSION_STATUS.PUBLISHED
          )
          if (filterPublishForm.length === 0) {
            this.$message.warning('当前所选数据不存在已经发布的表单，无法同步')
            return
          } else {
            this.selectionsData = [...filterPublishForm]
          }
        }
        this.ids = []
        this.selectionsData.forEach((item) => this.ids.push(item.id))
        if (this.isRelated) {
          this.handleRelated()
        } else {
          this.show()
        }
      }
    },
    // 同步单行数据
    handleDataSetSync() {
      this.ids = []
      this.ids.push(this.selectionsData[0].id)
      if (this.isRelated) {
        this.handleRelated()
      } else {
        this.show()
      }
    },
    // 处理关联数据
    handleRelated() {
      const loading = this.$loading({
        lock: true,
        text: '关联数据加载中，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
        customClass: 'loading',
      })
      let url
      switch (this.type) {
        case 'form': {
          url = FormAPI.FORM_REF_DETAIL()
          break
        }
        case 'model': {
          url = DataModelAPI.MODEL_RELATED_LIST()
          break
        }
      }
      this.$http
        .post(url, { ids: this.ids })
        .then((res) => {
          this.ids = []
          this.selectionsByIds = res.data
          this.selectionsData.forEach((item) => this.ids.push(item.id))
          loading.close()
          this.show()
        })
        .catch((e) => {
          this.$message.error(e.message || '获取关联数据失败')
        })
    },
  },
}
</script>
<style scoped lang="scss">
.loading {
  color: #fff;
}
.dialog-content {
  .scroll-box {
    overflow: hidden;
    margin-top: 5px;
    .scroll {
      display: flex;
      flex-direction: column;
      overflow-y: scroll;
      width: calc(100% + 22px);

      .text-box {
        // display: grid;
        // grid-template-columns: 1fr 1fr 1fr;
        // grid-column-gap: 10px;
        // grid-row-gap: 10px;
        display: flex;
        flex-wrap: wrap;
        width: 98%;
        margin-top: 3px;
        .text {
          border: 1px solid #dcdfe6;
          border-radius: 5px;
          overflow: hidden;
          text-align: center;
          padding: 10px;
          .span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
  .gap {
    border-left: 10px solid #3773d2;
    padding-left: 10px;
    margin-top: 15px;
  }
}
</style>
