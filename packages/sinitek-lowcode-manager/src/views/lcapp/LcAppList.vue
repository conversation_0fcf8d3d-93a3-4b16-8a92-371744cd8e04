<template>
  <div class="app-container">
    <xn-table
      :url="tableUrl"
      :querymodel="queryForm"
      :toolbars="toolbars"
      :columns="columns"
      :post-query="true"
      export-name="export.zip"
      default-order="createtimestamp:desc"
      @add="handleAddMfApp"
      @import="handleImportMfApp"
      @exportbatch="handleMfAppExportBatch"
      ref="MfAppTable"
    >
      <!-- 查询 -->
      <template slot="search">
        <el-form :model="queryForm" :inline="true">
          <el-form-item label="编码">
            <el-input
              v-model="queryForm.code"
              clearable
              placeholder="请输入完整的编码"
            />
          </el-form-item>
          <el-form-item label="名称">
            <el-input
              v-model="queryForm.name"
              clearable
              placeholder="请输入名称"
            />
          </el-form-item>
          <el-form-item label="服务名">
            <el-autocomplete
              style="width: 300px"
              v-model="queryForm.serviceName"
              :fetch-suggestions="handleServiceMatch"
              placeholder="请输入完整的服务名"
              :trigger-on-focus="false"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="query">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- 操作栏 -->
      <template #actions="scope">
        <xn-col-action-group :keys="scope.row">
          <xn-col-action
            v-for="(colBtn, btnIndex) in operations"
            :key="btnIndex"
            @click="handleClickOpera(scope, colBtn)"
          >
            {{ colBtn.label }}
          </xn-col-action>
        </xn-col-action-group>
      </template>
    </xn-table>
    <!-- 新增、编辑应用 -->
    <lc-app-edit
      ref="mfAppEditRef"
      :service-options="this.serviceList"
      :title="title"
      :type="isEdit"
      @refreshTable="query"
    />
    <!-- 应用导入弹出框 -->
    <import-dialog
      @refreshImportDla="query"
      ref="dlgImportRef"
      title="应用导入"
      type="mfApp"
    />
    <sync-dialog ref="syncDialogSingle" type="mfApp" :is-sync-all="false" />
  </div>
</template>
<script>
import { MfAppAPI } from 'src/api'
import { operation } from 'sinitek-util'
import sync from 'src/mixins/sync'
export default {
  components: {
    LcAppEdit: () => import('./components/LcAppEdit'),
    importDialog: () => import('./components/importDialog.vue'),
  },
  data() {
    return {
      tableUrl: MfAppAPI.MFAPP_LIST(),
      queryForm: {
        code: '',
        name: '',
        serviceName: '',
      },
      toolbars: [
        {
          type: 'add',
          label: '新增',
          event: 'add',
        },
        // {
        //   type: 'import',
        //   label: '导入',
        //   event: 'import',
        // },
        // {
        //   label: '导出',
        //   icon: 'icon-daochu',
        //   type: 'button',
        //   event: 'exportbatch',
        // },
      ],
      columns: [
        // 表格列中需要存在一列或多列的宽度是自适应的（设置了minWidth,一般把列内容会动态变化的，字数较多的列，宽度设为minWidth，保证当某列被表格配置功能隐藏后，自适应列会占据隐藏列的宽度，不至于出现空白）
        { type: 'selection', width: '40px' },
        { prop: 'code', label: '编码', width: '80px' },
        {
          prop: 'name',
          label: '名称',
          align: 'left',
          showOverflowTooltip: true,
          minWidth: '300px',
        },
        {
          prop: 'serviceName',
          label: '服务名',
          minWidth: '400px',
          showOverflowTooltip: true,
          align: 'left',
        },
        {
          prop: 'actions',
          label: '操作',
          fixed: 'right',
          align: 'center',
          width: '130px',
        },
      ],
      serviceList: [],
      title: '',
      isEdit: false,
      tableRef: 'MfAppTable',
      operations: [
        {
          label: '编辑',
          functionName: 'handleEditMfApp',
        },
        {
          label: '数据集管理',
          functionName: 'handleDataSetManage',
        },
        {
          label: '表单管理',
          functionName: 'handleFormManage',
        },
        {
          label: '删除',
          functionName: 'handleDelMfApp',
        },
        // {
        //   label: '导出',
        //   functionName: 'handleMfAppExportBatch',
        // },
      ],
    }
  },
  mixins: [sync],
  mounted() {
    this.getServiceList()
  },
  methods: {
    // 获取服务名字典
    getServiceList() {
      this.$http.get(MfAppAPI.MFAPP_SERVICE_LIST()).then((res) => {
        res.data.forEach((item) => {
          this.serviceList.push({
            value: item,
            label: item,
          })
        })
      })
    },
    // 新增应用
    handleAddMfApp() {
      this.isEdit = false
      this.title = '新增'
      this.$refs.mfAppEditRef.show()
    },
    // 编辑应用
    handleEditMfApp(row) {
      this.isEdit = true
      this.title = '编辑'
      this.$http.get(MfAppAPI.MFAPP_DETAIL(), { id: row.id }).then((res) => {
        this.$refs.mfAppEditRef.show(res.data)
      })
    },
    // 数据集管理
    handleDataSetManage(row) {
      this.$openTab(
        '/lowcode/dataset/list',
        {
          query: {
            appCode: row.code,
            title: row.name + '应用-数据集管理',
          },
        },
        () => {}
      )
    },
    // 表单管理
    handleFormManage(row) {
      this.$openTab(
        '/lowcode/module/list',
        {
          query: {
            appCode: row.code,
            title: row.name + '应用-表单管理',
          },
        },
        () => {}
      )
    },
    // 删除应用
    handleDelMfApp(row) {
      this.$confirm(
        `应用【${row.name}】下所有资源将全部被物理删除且无法恢复,是否继续?`
      )
        .then(() => {
          // 确认
          this.$http
            .post(MfAppAPI.MFAPP_DELETE(), { code: row.code })
            .then((res) => {
              this.query()
              res.success
                ? this.$message.success('删除成功')
                : this.$message.error(res.message)
            })
        })
        .catch(() => {
          // 取消
        })
    },
    // 应用导入
    handleImportMfApp() {
      this.$refs.dlgImportRef.show()
    },
    // 应用导出
    handleMfAppExportBatch(rows) {
      if (Array.isArray(rows)) {
        // 应用批量导出
        if (rows.length < 1) {
          this.$message.info('请选择要导出的数据')
          return
        }
        const objids = rows.map((item) => item.id).join(',')
        operation.download(MfAppAPI.MFAPP_EXPORT(), { objids })
      } else {
        // 单个应用导出
        if (!rows.id) {
          this.$message.info('请选择要导出的数据')
          return
        }
        operation.download(MfAppAPI.MFAPP_EXPORT(), { objids: [rows.id] })
      }
    },
    query() {
      this.$refs.MfAppTable?.query()
    },
    // 服务名匹配
    async handleServiceMatch(queryString, cb) {
      const results = queryString
        ? this.serviceList.filter(
            (item) => item.value.indexOf(queryString) != -1
          )
        : this.serviceList
      cb(results)
    },
  },
}
</script>
<style scoped lang="scss"></style>
