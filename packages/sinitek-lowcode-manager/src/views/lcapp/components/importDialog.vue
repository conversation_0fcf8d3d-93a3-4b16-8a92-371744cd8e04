<template>
  <xn-dialog
    id="dlgMfAppListImport"
    ref="dlgImportRef"
    :title="title"
    :show.sync="dlgModuleListImport.show"
    :width="dlgModuleListImport.width"
    :buttons="dlgModuleListImport.buttons"
    @save="handleDlgModuleListImportSave"
    @cancel="handleDlgModuleListImportCancel"
  >
    <el-form
      id="formDlgImport"
      ref="formDlgImportRef"
      slot="dialog-form"
      :model="formDlgImport.model"
      label-width="25%"
      size="small"
      :rules="formDlgImport.rules"
    >
      <el-form-item :label="fileType + '模板'" prop="dataModuleTemplate">
        <xn-upload
          v-if="dlgModuleListImport.show"
          id="uplFormDlgImportUploader"
          ref="uplFormDlgImportUploaderRef"
          v-model="formDlgImport.model.importFile"
          mode="upload"
          accept=".zip"
          :multi="false"
          @onError="handleUplFormDlgModuleListImportUploaderError"
        />
        <div style="color: red">提示：只允许上传从当前功能模块下导出的数据</div>
      </el-form-item>
    </el-form>
  </xn-dialog>
</template>

<script>
import { MfAppAPI } from 'src/api'
export default {
  name: 'ImportDialog',
  props: {
    title: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      dlgModuleListImport: {
        show: false,
        width: '435px',
        buttons: [
          { action: 'import', type: 'primary', event: 'save' },
          { action: 'cancel', type: 'info', event: 'cancel' },
        ],
      },
      // 导入弹框内表单
      formDlgImport: {
        model: {
          importFile: [],
        },
        rules: {
          dataModuleTemplate: [
            { validator: this.validateImport, required: true },
          ],
        },
      },
      importValidateUrl: '',
      importUrl: '',
      fileType: '',
    }
  },
  mounted() {
    switch (this.type) {
      case 'module':
        this.importValidateUrl = ''
        this.importUrl = ''
        this.fileType = '模块'
        break
      case 'form':
        this.importValidateUrl = ''
        this.importUrl = ''
        this.fileType = '表单'
        break
      case 'dataModel':
        this.importValidateUrl = ''
        this.importUrl = ''
        this.fileType = '数据模型'
        break
      case 'dataSet':
        this.importValidateUrl = ''
        this.importUrl = ''
        this.fileType = '数据集'
        break
      case 'mfApp':
        this.importValidateUrl = MfAppAPI.MFAPP_IMPORT_VALIDATE()
        this.importUrl = MfAppAPI.MFAPP_IMPORT()
        this.fileType = '表单应用'
        break
    }
  },
  methods: {
    // 打开弹窗
    show() {
      this.dlgModuleListImport.show = true
    },
    // 导入前验证是否上传了文件
    validateImport(rule, value, callback) {
      // 获取上传文件
      const template = this.$refs.uplFormDlgImportUploaderRef.fileList
      if (template.length === 0) {
        callback(new Error(`请上传${this.fileType}模板文件`))
      } else {
        callback()
      }
    },
    // 导入时的异常处理
    handleUplFormDlgModuleListImportUploaderError(error) {
      this.$refs.dlgImportRef.displayErrorMessage(error.message)
    },
    // 导入弹框中，模块的保存按钮
    handleDlgModuleListImportSave(resolve, reject) {
      // 判重
      this.$refs.formDlgImportRef.validate((valid) => {
        if (valid) {
          this.$easypost(this.importValidateUrl, this.formDlgImport.model)
            .then((result) => {
              if (Array.isArray(result) && result.length > 0) {
                let fileName = '表单应用'
                if (this.type === 'mfApp') {
                  result.forEach((data) => {
                    fileName += `【${data.name}(${data.code})】，`
                  })
                } else {
                  let moduleName = ''
                  let formName = ''
                  let dataModelName = ''
                  let dataSetName = ''
                  result.forEach((data) => {
                    switch (data.resultType) {
                      case 'module':
                        moduleName += `【${data.name}(${data.code})】`
                        break
                      case 'form':
                        formName += `【${data.name}(${data.code})】`
                        break
                      case 'dataModel':
                        dataModelName += `【${data.name}(${data.code})】`
                        break
                      case 'dataSet':
                        dataSetName += `【${data.name}(${data.code})】`
                        break
                    }
                  })
                  if (moduleName !== '') {
                    moduleName = '模块' + moduleName + '，'
                  }
                  if (formName !== '') {
                    formName = '表单' + formName + '，'
                  }
                  if (dataModelName !== '') {
                    dataModelName = '数据模型' + dataModelName + '，'
                  }
                  if (dataSetName !== '') {
                    dataSetName = '数据集' + dataSetName + '，'
                  }
                  fileName = moduleName + formName + dataModelName + dataSetName
                }
                fileName = fileName.substring(0, fileName.length - 1)
                const taht = this
                if (fileName !== '') {
                  this.$confirm(fileName + ' 已存在，是否覆盖？', '提示', {
                    customClass: 'lc-confirm-import-or-overwrite-dlg',
                  }).then(
                    () => {
                      taht.importFile(resolve, reject, result)
                    },
                    () => {
                      reject()
                    }
                  )
                } else {
                  this.importFile(resolve, reject, null)
                }
              } else {
                this.importFile(resolve, reject, null)
              }
            })
            .catch((error) => {
              // 禁用http请求报错的默认提示
              this.$message.closeAll()
              reject(error)
            })
        } else {
          reject()
        }
      })
    },
    importFile(resolve, reject) {
      this.$easypost(this.importUrl, {
        importFile: this.formDlgImport.model.importFile,
      })
        .then((result) => {
          resolve(result)
          // 关闭弹出框
          this.handleDlgModuleListImportCancel()
          // 刷新
          this.$emit('refreshImportDla')
        })
        .catch((error) => {
          // 禁用http请求报错的默认提示
          this.$message.closeAll()
          reject(error)
        })
    },
    // 导入弹框中的取消按钮
    handleDlgModuleListImportCancel() {
      this.$refs.uplFormDlgImportUploaderRef.clear()
      this.formDlgImport.model.importFile = []
      // 清空验证信息
      this.$refs.formDlgImportRef.clearValidate()
      this.dlgModuleListImport.show = false
    },
  },
}
</script>

<style scoped></style>
