<template>
  <xn-dialog
    :title="title + '应用'"
    width="750px"
    :show.sync="dialog.display"
    :buttons="dialog.buttons"
    @save="handleSave"
    @close="handleClose"
  >
    <template slot="dialog-form">
      <el-form ref="form" :model="form" :rules="formRules" label-width="150px">
        <el-form-item prop="code" label="编码">
          <span v-if="type">
            {{ form.code }}
          </span>
          <el-input
            v-else
            v-model.trim="form.code"
            :maxlength="5"
            :show-word-limit="true"
            class="formWidth"
          />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input
            v-model.trim="form.name"
            :maxlength="50"
            :show-word-limit="true"
            class="formWidth"
          />
        </el-form-item>
        <el-form-item label="服务名" prop="serviceName">
          <xn-select
            v-model.trim="form.serviceName"
            class="formWidth"
            :options="serviceOptions"
            sorted
          />
        </el-form-item>

        <el-form-item prop="contextPath" label="请求前缀">
          <span v-if="type">
            {{ form.contextPath }}
          </span>
          <el-input
            v-else
            v-model.trim="form.contextPath"
            :maxlength="50"
            :show-word-limit="true"
            class="formWidth"
          />
        </el-form-item>
      </el-form>
    </template>
  </xn-dialog>
</template>

<script>
import { validatorUtil } from 'sinitek-util'
import { MfAppAPI } from 'src/api'
export default {
  name: 'MfAppEdit',
  props: {
    serviceOptions: {
      type: Array,
      default: () => {
        return []
      },
    },
    title: {
      type: String,
      default: '',
    },
    // 操作类型，新增-false,编辑-true
    type: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialog: {
        display: false,
        buttons: [
          { label: '确定', action: 'save', type: 'primary', event: 'save' },
          { action: 'cancel', type: 'info', event: 'close' },
        ],
      },
      form: {
        name: '',
        code: '',
        serviceName: '',
        contextPath: '',
      },
      formRules: {
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: ['blur', 'change'],
          },
        ],
        code: [
          {
            required: true,
            message: '请输入编码',
            trigger: ['blur', 'change'],
          },
          {
            validator: validatorUtil.validateEncoder,
            trigger: ['blur', 'change'],
          },
        ],
        serviceName: [
          {
            required: true,
            message: '请输入服务名',
            trigger: ['blur', 'change'],
          },
        ],
      },
    }
  },
  computed: {},
  mounted() {},
  methods: {
    show(form) {
      this.dialog.display = true
      if (form) {
        this.form = form
      } else {
        if (this.$refs.form) {
          this.form = this.$options.data().form
          this.$refs.form.resetFields()
        }
      }
    },
    handleClose() {
      this.dialog.display = false
      // 重置数据
      this.$refs.form.clearValidate()
    },
    handleSave(resolve, reject) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$http
            .post(MfAppAPI.MFAPP_SAVE(), this.form)
            .then(() => {
              this.$message.success(this.title + '成功')
              this.dialog.display = false
              this.$emit('refreshTable')
            })
            .catch((error) => {
              // 禁用http请求报错的默认提示
              this.$message.closeAll()
              reject(error)
            })
        } else {
          reject()
        }
      })
    },
  },
}
</script>
<style scoped>
.formWidth {
  width: 430px !important;
}
.helpTooltip {
  color: #409eff;
}
</style>
