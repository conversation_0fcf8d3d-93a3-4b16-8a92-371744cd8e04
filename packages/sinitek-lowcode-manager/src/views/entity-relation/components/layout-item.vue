<template>
  <div class="layout-item" :class="{ 'active-layout': active }">
    <div class="layout-item-content" @click="$emit('click')">
      {{ item.name }}
    </div>
    <div>
      <el-button type="text" icon="el-icon-edit" @click.stop="editClick" />
      <el-button
        v-if="showDelete"
        type="text"
        icon="el-icon-delete"
        @click.stop="deleteItem"
      />
    </div>
  </div>
</template>

<script>
// import debounce from 'lodash/debounce'
export default {
  name: 'EntityRelationLayoutItem',
  props: {
    item: {
      type: Object,
      default: () => {},
    },
    active: {
      type: Boolean,
      default: false,
    },
    showDelete: {
      type: Boolean,
      default: true,
    },
  },
  // data() {
  //   return {
  //     // currentValue: this.item.name,
  //     // isEdit: false,
  //   }
  // },
  methods: {
    editClick() {
      this.$emit('editClick', this.item)
    },
    deleteItem() {
      this.$confirm
        .delete()
        .then(() => {
          // 确认
          this.$emit('deleteItem')
        })
        .catch(() => {
          // 取消
        })
    },
  },
  // watch: {
  //   currentValue: debounce(function (v) {
  //     this.change(v)
  //   }, 300),
  // },
}
</script>

<style lang="scss" scoped>
.layout-item {
  line-height: 36px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border-radius: 4px;
  &.active-layout {
    color: #fff;
    background-color: var(--xn-color-primary, #3773d2);
    .el-button {
      color: #fff;
    }
  }
  .el-button {
    flex-shrink: 0;
    height: 36px;
  }
  .layout-item-content {
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
