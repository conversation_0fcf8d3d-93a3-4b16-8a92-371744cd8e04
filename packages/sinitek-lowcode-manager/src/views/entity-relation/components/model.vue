<template>
  <div class="model-group">
    <div>模型</div>
    <div class="model-list">
      <!-- // 第一层 -->
      <div v-for="(first, index) of modules" :key="index">
        <el-checkbox
          :label="first.name"
          :value="currentValue.includes(first.code)"
          @change="changeModel('first', first)"
        />
        <!-- // 第二层 -->
        <div
          class="second-level"
          v-for="(second, index2) of first.children"
          :key="index2"
        >
          <el-checkbox
            :label="second.name"
            :value="currentValue.includes(second.code)"
            @change="changeModel('second', second)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EntityRelationModel',
  props: {
    // 根据布局选中的模型，来选中当前的模型数据
    value: Array,
    modules: Array,
  },
  data() {
    return {
      currentValue: this.value || [],
    }
  },
  methods: {
    changeModel(type, item) {
      const { code, moduleCode } = item
      const selectedCodes = new Set([...this.currentValue])
      const previousCodes = new Set([...this.currentValue])
      const isDelete = selectedCodes.has(code)
      const action = isDelete ? 'delete' : 'add'

      // 处理第一层选择
      if (type === 'first') {
        const module = this.modules.find((m) => m.code === code)
        const childCodes = module?.children ?? []
        selectedCodes[action](code)
        childCodes.forEach((child) => selectedCodes[action](child.code))
      }
      // 处理第二层选择
      else if (moduleCode) {
        selectedCodes[action](code)
        const parentModule = this.modules.find((m) => m.code === moduleCode)
        const allChildrenSelected = parentModule.children.every((child) =>
          isDelete
            ? !selectedCodes.has(child.code)
            : selectedCodes.has(child.code)
        )

        if (allChildrenSelected) {
          isDelete
            ? selectedCodes.delete(moduleCode)
            : selectedCodes.add(moduleCode)
        }
      }

      // 发送变更事件
      const changedCodes = isDelete
        ? [...previousCodes].filter((code) => !selectedCodes.has(code))
        : [...selectedCodes].filter((code) => !previousCodes.has(code))
      this.$emit(action, changedCodes)
      this.currentValue = [...selectedCodes]
    },
  },
  watch: {
    value(v) {
      this.currentValue = v
    },
  },
}
</script>

<style lang="scss" scoped>
.model-group {
  height: 100%;
  overflow: hidden;
  .model-list {
    height: 100%;
    overflow-y: auto;
    .second-level {
      margin-left: 20px;
    }
  }
}
</style>
