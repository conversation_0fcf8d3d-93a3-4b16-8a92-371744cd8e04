<template>
  <div>
    <div>
      <div class="layout-header">
        布局
        <el-button type="text" icon="el-icon-plus" @click="addItem" />
      </div>
      <LayoutItem
        v-for="item in list"
        :key="item.id"
        :item="item"
        @editClick="editClick"
        :active="activeLayout.id === item.id"
        @deleteItem="$emit('deleteLayout', item.id)"
        @click="handleClick(item)"
        :show-delete="list.length > 1"
      />
    </div>
    <xn-dialog
      :title="isAdd ? '新增布局' : '编辑布局'"
      :buttons="buttons"
      :show.sync="dialogVisible"
      @save="saveDialog"
      @cancel="closeDialog"
    >
      <xn-form :model="form" :rules="rules" ref="form">
        <xn-form-item label="布局名称" prop="name">
          <xn-input v-model="form.name" length-type="name" show-word-limit />
        </xn-form-item>
      </xn-form>
    </xn-dialog>
  </div>
</template>

<script>
import LayoutItem from './layout-item.vue'
export default {
  name: 'EntityRelationLayout',
  components: {
    LayoutItem,
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    activeLayout: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      layout: [],
      dialogVisible: false,
      isAdd: false,
      rules: {
        name: [{ required: true, message: '请输入布局名称', trigger: 'blur' }],
      },
      form: {
        name: '',
      },
      buttons: [
        { label: '保存', type: 'primary', action: 'save', event: 'save' },
        { label: '取消', type: 'info', action: 'cancel', event: 'cancel' },
      ],
    }
  },
  methods: {
    getLayout() {
      this.$emit('getLayout', this.layout)
    },
    addItem() {
      this.isAdd = true
      this.dialogVisible = true
      this.form = {
        name: '',
      }
    },
    handleClick(item) {
      this.$emit('changeLayout', item)
    },
    editClick(item) {
      this.isAdd = false
      this.dialogVisible = true
      this.form = {
        ...item,
      }
    },
    closeDialog() {
      this.dialogVisible = false
    },
    saveDialog(resolve, reject) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit(
            'saveLayout',
            this.form,
            (...args) => {
              this.dialogVisible = false
              resolve(...args)
            },
            reject
          )
        } else {
          reject()
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.layout-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}
.el-button {
  margin-left: 10px;
}
</style>
