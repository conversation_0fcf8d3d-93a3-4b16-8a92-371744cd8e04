<template>
  <div class="app-container">
    <div class="left">
      <layout
        v-if="layoutList.length > 0 && currentLayout"
        :list="layoutList"
        @changeLayout="changeLayout"
        @deleteLayout="deleteLayout"
        @saveLayout="updateLayout"
        :active-layout="currentLayout"
      />
      <Model
        :modules="modules"
        :value="modelCodes"
        @delete="deleteModel"
        @add="addModel"
        v-loading="loading"
      />
    </div>
    <div class="right" v-loading="loading">
      <div class="right-header">
        <div class="action">
          <el-button
            type="primary"
            @click="
              getSnapshot(
                currentLayout && currentLayout.name
                  ? currentLayout.name
                  : '图片',
                { type: 'png' }
              )
            "
          >
            导出图片
          </el-button>
          <el-button type="primary" @click="save">保存</el-button>
        </div>
        <div class="layout">
          布局：
          <xn-select v-model="zoomSize" :options="zoomOptions" @change="zoom" />
          <!-- <el-button @click="zoom(false)">缩小</el-button>
          <el-button @click="zoom(true)">放大</el-button>
          <el-button @click="resetZoom">恢复</el-button> -->
        </div>
      </div>
      <EntityRelationDiagram ref="er" @hook:mounted="erMounted" />
    </div>
  </div>
</template>

<script>
import Layout from './components/layout.vue'
import Model from './components/model.vue'
import { EntityRelationAPI } from '@/api'
import { COMPONENT_TYPE, PROP_TYPE_ER_NAME } from '@/constant'

export default {
  name: 'EntityRelation',
  components: {
    Model,
    Layout,
    EntityRelationDiagram: () =>
      import('sinitek-lowcode-flow').then((res) => res.EntityRelation),
  },
  data() {
    return {
      layoutList: [],
      currentGraphData: {},
      modules: [],
      relaInfo: [],
      modelCodes: [],
      currentLayout: null,
      loading: false,
      queue: [],
      zoomSize: 1,
      zoomOptions: [
        { label: '25%', value: 0.25 },
        { label: '50%', value: 0.5 },
        { label: '75%', value: 0.75 },
        { label: '100%', value: 1 },
        { label: '125%', value: 1.25 },
        { label: '150%', value: 1.5 },
        { label: '200%', value: 2 },
      ],
    }
  },
  methods: {
    async erMounted() {
      await this.getModules()
      await this.getCurrentLayout()
      if (this.currentLayout) {
        this.changeLayout(this.currentLayout)
      }
      // let timer = null
      this.$refs.er.lf.on('history:change', ({ data }) => {
        const graphData = data.undos[data.undos.length - 1]
        this.modelCodes = Object.values(graphData.nodes).map((e) => e.id)
        this.fixModelCodes()
        const schema = {
          nodes: graphData.nodes.map((e) => {
            return {
              id: e.id,
              x: e.x,
              y: e.y,
              isCollapse: e.properties.isCollapse,
              isShowMore: e.properties.isShowMore,
              maxHeight: e.properties.maxHeight,
            }
          }),
          edges: [],
        }
        // // 延迟2秒保存
        this.queue.push({
          ...this.currentLayout,
          schema: JSON.stringify(schema),
        })
        // timer && clearTimeout(timer)
        // timer = setTimeout(() => {
        //   this.runQueue()
        // }, 2000)
      })
    },
    async runQueue() {
      try {
        if (this.queue.length > 0) {
          const last = this.queue.pop()
          this.queue = []
          await this.saveLayout(last)
          this.$message.saveWithSuccess()
        }
      } catch (e) {
        this.$message.error(e.message)
      }
    },
    save() {
      this.runQueue()
    },
    async getCurrentLayout() {
      await this.getLayoutList()
      if (this.layoutList.length > 0) {
        this.currentLayout = this.layoutList[0]
      }
    },
    async getLayoutList() {
      const res = await this.$http(EntityRelationAPI.DIAGRAM_LIST())
      const list = res.data
      if (list.length > 0) {
        this.layoutList = list.sort((a, b) => b.sort - a.sort)
      }
    },
    async saveLayout(params) {
      return await this.$http.post(EntityRelationAPI.DIAGRAM_SAVE(), {
        ...params,
      })
    },
    async render(data = {}) {
      this.zoomSize = 1
      let graphData
      if (data.nodes && Object.keys(data.nodes).length > 0) {
        graphData = this.cleanData(data)
      } else {
        graphData = this.getAllModels()
      }
      this.$refs.er.render(this.getGraphData(graphData))
    },
    // 清洗数据，不存在的模型或者关系，修改对应的模型数据
    cleanData(graphData) {
      const allModels = {}
      this.modules.forEach((item) => {
        item.children.forEach((e) => {
          allModels[e.code] = e
        })
      })
      const models = []
      graphData.nodes.forEach((e) => {
        if (!allModels[e.id]) return
        models.push({ ...allModels[e.id], _gd_: { x: e.x, y: e.y } })
      })
      return models
    },
    getGraphData(models) {
      // 不存在的模型需要删除
      const nodes = []
      const map = {}
      const edges = {}
      models.forEach((item) => {
        if (!item.code || !item?.props?.length) return
        map[item.code] = true
        const node = this.nodes?.find((e) => e.id === item.code)
        nodes.push({
          id: item.code,
          type: 'entity-node',
          ...(item._gd_ || {}),
          properties: {
            title: item.name,
            isCollapse: node?.isCollapse,
            isShowMore: node?.isShowMore,
            maxHeight: node?.maxHeight,
            fields: item.props
              .filter((e) => e.name)
              .map((e) => {
                return {
                  key: e.name,
                  type: PROP_TYPE_ER_NAME[e.type] || '未定义',
                  // type: e.type,
                  primary: e.type === 'KEY',
                  foreign: e.type === 'FOREIGN',
                  required: e.required,
                  enum: !!e.enumType,
                  org: e.componentType === COMPONENT_TYPE.SIRMORG,
                }
              }),
          },
        })
      })
      // 2个节点都存在才添加edge
      this.relaInfo.forEach((e) => {
        if (map[e.modelCode] && map[e.relaModelCode]) {
          const [source = '', target = ''] = e.relaType.split('-')
          edges[`${e.modelCode}-${e.relaModelCode}`] = {
            id: `${e.modelCode}-${e.relaModelCode}`,
            sourceNodeId: e.modelCode,
            targetNodeId: e.relaModelCode,
            text: e.relaLineText,
            type: 'entity-line',
            properties: {
              source,
              target,
            },
          }
        }
      })
      return { nodes, edges: Object.values(edges) }
    },
    deleteModel(ids = []) {
      ids.forEach((id) => {
        this.$refs.er.deleteNodeById(id)
      })
    },
    deleteLayout(id) {
      this.layoutList = this.layoutList.filter((e) => e.id !== id)
      this.$http.post(EntityRelationAPI.DIAGRAM_DELETE(), [id]).then(() => {
        this.$message.deleteWithSuccess()
      })
      if (this.currentLayout.id === id) {
        this.currentLayout = this.layoutList[0]
        this.changeLayout(this.currentLayout)
      }
    },
    addModel(ids = []) {
      const models = this.getModelsByKeys(ids)
      const graphData = this.getGraphData(models)
      const edges = {}
      this.modelCodes.push(...ids)
      // 2个节点都存在才添加edge
      this.relaInfo.forEach((e) => {
        if (
          this.modelCodes.includes(e.modelCode) &&
          this.modelCodes.includes(e.relaModelCode)
        ) {
          const [source = '', target = ''] = e.relaType.split('-')
          edges[`${e.modelCode}-${e.relaModelCode}`] = {
            id: `${e.modelCode}-${e.relaModelCode}`,
            sourceNodeId: e.modelCode,
            targetNodeId: e.relaModelCode,
            text: e.relaLineText,
            type: 'entity-line',
            properties: {
              source,
              target,
            },
          }
        }
      })
      graphData.edges = Object.values(edges)
      this.$refs.er.addNode(graphData)
    },
    async changeLayout(params) {
      // this.runQueue()
      this.currentLayout = params
      this.loading = true
      const res = await this.$http.get(EntityRelationAPI.DIAGRAM_GET_SCHEMA(), {
        id: params.id,
      })
      this.loading = false
      const graphData = this.parseLayoutSchema(res.data)
      if (graphData.nodes) {
        this.nodes = graphData.nodes
        this.modelCodes = Object.values(graphData.nodes).map((e) => e.id)
      }
      this.fixModelCodes()
      this.render(graphData)
    },
    parseLayoutSchema(schema) {
      return JSON.parse(schema || '{"nodes":[], "edges":[]}')
    },
    async updateLayout(params, resolve, reject) {
      try {
        const res = await this.saveLayout(params)
        if (!params.id) {
          this.currentLayout = { ...params, id: res.data }
          this.layoutList.push(this.currentLayout)
          this.$message.saveWithSuccess()
          this.render()
        } else {
          this.$message.operationWithSuccess()
          this.layoutList = this.layoutList.map((e) => {
            if (e.id === params.id) {
              return { ...e, ...params }
            }
            return e
          })
        }
        resolve()
      } catch (e) {
        reject(e)
      }
    },
    async getModules() {
      const res = await this.$http(EntityRelationAPI.MODULE_LIST_MODEL_TREE())
      this.relaInfo = res?.data?.relaInfo ?? []
      this.modules = (res?.data?.models ?? []).filter((e) => e.children)
    },
    getAllModels() {
      const result = []
      const keys = []
      this.modules.forEach((item) => {
        result.push(...item.children)
        keys.push(item.code)
        item.children.forEach((e) => {
          keys.push(e.code)
        })
      })
      this.modelCodes = keys
      return result
    },
    getModelsByKeys(keys) {
      // 铺平
      const result = []
      const s = new Set([])
      this.modules.forEach((item) => {
        if (!s.has(item.code)) {
          s.add(item.code)
          result.push(item)
        }
        item.children.forEach((e) => {
          if (!keys.includes(e.code)) return
          result.push(e)
        })
      })
      return result
    },
    // 当模型全部选中时，需要把module也选中
    fixModelCodes() {
      this.modules.forEach((e) => {
        if (e.children.every((e) => this.modelCodes.includes(e.code))) {
          this.modelCodes.push(e.code)
        }
      })
    },
    getSnapshot(name, options) {
      this.$refs.er.getSnapshot(name, options)
    },
    resetZoom() {
      this.$refs.er.resetZoom()
    },
    zoom(v) {
      this.$refs.er.zoom(v)
    },
  },
}
</script>
<style lang="scss" scoped>
.app-container {
  display: flex;
  box-sizing: border-box;
  .left {
    padding: 10px;
    background: #fff;
    height: 100%;
    display: flex;
    flex-direction: column;
    width: 300px;
    flex-shrink: 0;
    box-sizing: border-box;
    overflow: hidden;
    border-right: 1px solid #e8e8e8;
  }
  .right {
    background: #fff;
    height: 100%;
    width: 100%;
    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
    }
  }
}
</style>
