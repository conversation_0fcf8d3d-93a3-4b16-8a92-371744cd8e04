<template>
  <div class="app-container">
    <div class="header-search user-container">
      <el-form :model="queryForm" :inline="true" size="small">
        <el-form-item label="编码">
          <el-input
            v-model.trim="queryForm.code"
            class="filter-item"
            placeholder="请输入编码"
            :maxlength="50"
          />
        </el-form-item>

        <el-form-item label="请求地址">
          <el-input
            v-model.trim="queryForm.url"
            class="filter-item"
            placeholder="请输入请求地址"
            :maxlength="200"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <xn-table
      id="tabUrlProxy"
      ref="tabUrlProxyRef"
      :url="queryUrl"
      :toolbars="toolbars"
      :querymodel="queryForm"
      post-query
      @handleAdd="handleAdd"
      @handleDelete="handleDelete"
      @exportbatch="exportBatch"
      @importtemplate="importTemplate"
    >
      <xn-col type="selection" prop="id" width="40" />
      <xn-col
        prop="code"
        label="编码"
        width="220"
        align="left"
        sortable="true"
        show-overflow-tooltip
      />
      <xn-col prop="url" label="请求地址" align="left" show-overflow-tooltip />
      <xn-col prop="requestClazz" label="请求实现类" show-overflow-tooltip />
      <xn-col
        prop="requestMethod"
        label="请求方式"
        width="160"
        align="center"
        show-overflow-tooltip
        :formatter="formatterRow"
      />
      <xn-col label="操作" align="center">
        <template slot-scope="scope">
          <xn-col-button
            type="text"
            size="small"
            @click="handleRowEdit(scope.row)"
          >
            编辑
          </xn-col-button>
          <xn-col-button
            type="text"
            size="small"
            @click="handleRowDelete(scope.row)"
          >
            删除
          </xn-col-button>
        </template>
      </xn-col>
    </xn-table>

    <xn-dialog
      id="dlgUrlProxyImport"
      ref="importRef"
      title="URL请求代理导入"
      :show.sync="importDlgVisable"
      width-size="small"
      :buttons="importButtons"
      @save="handleDlgUrlProxyImportSave"
      @cancel="handleDlgUrlProxyImportCancel"
    >
      <xn-form
        id="formDlgUrlProxyImport"
        slot="dialog-form"
        ref="formDlgUrlProxyImportRef"
        :model="importData"
        label-width="100px"
        size="small"
        :rules="importUrlProxyRules"
      >
        <xn-form-item label="URL请求代理模板" prop="importTemplate">
          <xn-upload
            v-if="importDlgVisable"
            ref="upload"
            v-model="importData.importFile"
            mode="upload"
            :read-only="false"
            accept=".xml,.zip"
            @onError="importError"
          />
          <div style="color: red">
            提示：只允许上传从当前功能模块下导出的数据
          </div>
        </xn-form-item>
      </xn-form>
    </xn-dialog>
  </div>
</template>

<script>
import { UrlProxyAPI } from 'src/api'
import { handleErrorMessage } from 'src/utils'
import { operation } from 'sinitek-util'
export default {
  name: 'LcUrlProxyList',
  data() {
    return {
      queryUrl: Object.freeze(UrlProxyAPI.URLPROXY_LIST()),
      queryForm: {
        code: '',
        url: '',
      },
      toolbars: [
        { label: '新增', type: 'add', event: 'handleAdd' },
        {
          label: '删除',
          icon: 'icon-shanchu',
          type: 'delete',
          event: 'handleDelete',
        },
        {
          label: '导入',
          type: 'import',
          event: 'importtemplate',
        },
        {
          label: '导出',
          icon: 'icon-daochu',
          type: 'button',
          event: 'exportbatch',
        },
      ],
      // 导入弹框显示与否
      importDlgVisable: false,
      // 导入弹框按钮
      importButtons: [
        { action: 'import', type: 'primary', event: 'save' },
        { action: 'cancel', type: 'info', event: 'cancel' },
      ],
      // 上传的文件
      importData: {
        importFile: {},
      },
      importUrlProxyRules: {
        importTemplate: [
          { validator: this.validateImportTemplate, required: true },
        ],
      },
    }
  },
  methods: {
    formatterRow(row, column, cellValue) {
      if (cellValue === 1) {
        return 'POST'
      } else if (cellValue === 2) {
        return 'GET'
      } else {
        return ''
      }
    },
    handleQuery() {
      this.tableQuery()
    },
    tableQuery() {
      this.$refs.tabUrlProxyRef.query()
    },
    handleAdd() {
      this.$openTab(
        '/lowcode/url-proxy/edit',
        { query: { title: this.$store.getters.appName + '新增URL请求代理' } },
        () => {
          this.tableQuery()
        }
      )
    },
    handleDelete(row) {
      this.$confirm
        .delete()
        .then(() => {
          if (Array.isArray(row) && row.length > 0) {
            this.deleteUrlProxy(row.map((item) => item.id))
          } else {
            this.$message.info('请选择要删除的数据')
          }
        })
        .catch((_) => {})
    },
    handleRowEdit(row) {
      this.$openTab(
        '/lowcode/url-proxy/edit',
        {
          query: {
            title: this.$store.getters.appName + '编辑URL请求代理',
            id: row.id,
          },
        },
        () => {
          this.tableQuery()
        }
      )
    },
    handleRowDelete(row) {
      this.deleteUrlProxy([row.id])
    },
    deleteUrlProxy(rowArray) {
      this.$confirm
        .delete()
        .then(() => {
          this.$http
            .post(UrlProxyAPI.URLPROXY_DELETE(), rowArray)
            .then(() => {
              this.$message.deleteWithSuccess()
              this.tableQuery()
            })
            .catch((error) => {
              handleErrorMessage(this, error)
            })
        })
        .catch((_) => {})
    },
    exportBatch(data) {
      if (data.length > 0) {
        let objids
        for (const i in data) {
          if (i === '0') {
            objids = data[i].id
          } else {
            objids += ',' + data[i].id
          }
        }
        operation.download(UrlProxyAPI.URLPROXY_EXPORT() + '?objids=' + objids)
      } else {
        this.$message.info('请选择要导出的数据')
      }
    },
    importTemplate() {
      this.importDlgVisable = true
    },
    importError(error) {
      this.$refs.importRef.displayErrorMessage(error.message)
    },
    validateImportTemplate(rule, value, callback) {
      // 获取上传文件
      const template = this.$refs.upload.fileList
      if (template.length === 0) {
        callback(new Error('请上传URL请求代理文件'))
      } else {
        // 缺了一个正在上传的判断
        callback()
      }
    },
    handleDlgUrlProxyImportSave(resolve, reject) {
      this.$refs.formDlgUrlProxyImportRef.validate((valid) => {
        if (valid) {
          this.$http
            .post(UrlProxyAPI.URLPROXY_LIST_FILE(), {
              importFile: this.importData.importFile,
            })
            .then((res) => {
              const result = res.data
              if (Array.isArray(result) && result.length > 0) {
                let urlProxyName = ''
                result.forEach((data) => {
                  urlProxyName += `【${data.code}】,`
                })
                if (urlProxyName !== '') {
                  urlProxyName = 'URL请求代理' + urlProxyName
                }
                urlProxyName = urlProxyName.substring(
                  0,
                  urlProxyName.length - 1
                )
                if (urlProxyName !== '') {
                  this.$confirm(urlProxyName + ' 已存在，是否覆盖？', '提示', {
                    customClass: 'lc-confirm-import-or-overwrite-dlg',
                  }).then(
                    () => {
                      this.importForm(resolve, reject)
                    },
                    () => {
                      reject()
                    }
                  )
                } else {
                  this.importForm(resolve, reject)
                }
              } else {
                this.importForm(resolve, reject)
              }
            })
            .catch((error) => {
              // 禁用http请求报错的默认提示
              this.$message.closeAll()
              reject(error)
            })
        } else {
          reject()
        }
      })
    },
    // （导入时在验证code后）保存数据
    importForm(resolve, reject) {
      this.$refs.formDlgUrlProxyImportRef.validate((valid) => {
        if (valid) {
          this.$easypost(UrlProxyAPI.URLPROXY_IMPORT(), {
            importFile: this.importData.importFile,
          })
            .then((result) => {
              resolve(result)
              // 关闭弹出框
              this.handleDlgUrlProxyImportCancel()
              this.tableQuery()
            })
            .catch((error) => {
              // 禁用http请求报错的默认提示
              this.$message.closeAll()
              reject(error)
            })
        } else {
          reject()
        }
      })
    },
    handleDlgUrlProxyImportCancel() {
      this.importDlgVisable = false
      this.importData.importFile = []
      this.$refs.upload.clear()
      // 清空验证信息
      this.$refs.formDlgUrlProxyImportRef.clearValidate()
    },
  },
}
</script>
