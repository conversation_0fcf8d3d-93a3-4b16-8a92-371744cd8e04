<template>
  <div class="app-container">
    <div class="blank-container">
      <el-form
        id="urlProxy"
        ref="urlProxyRef"
        :model="urlProxy.model"
        :rules="urlProxy.rules"
        label-width="130px"
      >
        <el-form-item label="编码" prop="code">
          <el-input
            v-model.trim="urlProxy.model.code"
            class="urlProxy_item"
            :maxlength="50"
            :show-word-limit="true"
          />
        </el-form-item>
        <el-form-item label="请求地址" prop="url">
          <el-input
            v-model.trim="urlProxy.model.url"
            class="urlProxy_item"
            :maxlength="1000"
            :show-word-limit="true"
          />
        </el-form-item>
        <el-form-item label="请求方式" prop="requestMethod">
          <el-radio-group v-model="urlProxy.model.requestMethod">
            <el-radio :label="1"> POST </el-radio>
            <el-radio :label="2"> GET </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="requestClazz">
          <span slot="label">
            请求实现类
            <el-tooltip
              class="icon_question"
              effect="light"
              content="须实现IUrlRequestHandler接口,配置了请求实现类须自行处理请求返回值"
              placement="top"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </span>
          <el-input
            v-model.trim="urlProxy.model.requestClazz"
            class="urlProxy_item"
            :maxlength="500"
            :show-word-limit="true"
          />
        </el-form-item>
        <el-form-item label="自定义请求头" prop="requestHeaders">
          <div>
            <ace-editor
              :id="'requestHeaders' + urlProxy.model.id"
              ref="requestHeaders"
              width="650px"
              height="200px"
              mode="ace/mode/json"
              :data.sync="requestHeaders"
              @blur="$refs.urlProxyRef.validateField('requestHeaders')"
            />
          </div>
        </el-form-item>
        <el-form-item prop="responseClazz">
          <span slot="label">
            返回值处理类
            <el-tooltip
              class="icon_question"
              effect="light"
              content="须实现IUrlResponseHandler接口"
              placement="top"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </span>
          <el-input
            v-model.trim="urlProxy.model.responseClazz"
            class="urlProxy_item"
            :maxlength="500"
            :show-word-limit="true"
          />
        </el-form-item>
        <el-form-item prop="responseMethod">
          <span slot="label">
            返回值处理函数
            <el-tooltip class="icon_question" effect="light" placement="top">
              <div slot="content">
                必须返回一个对象,且包含<em>resultcode</em><em>message</em
                ><em>data</em><br />
                例如:<br />
                <pre>
// response为请求返回值
// 可使用print(response)在后台的控制台中打印response结构
return {
  "resultcode":"0", // 0代表请求成功,其他值代表请求失败
  "message":response.msg, // 当resultcode为非0时,需要有异常信息
  "data":response.data
}
                </pre>
              </div>
              <i class="el-icon-question" />
            </el-tooltip>
          </span>
          (response) => {
          <div class="response-method">
            <ace-editor
              :id="'responseMethod' + urlProxy.model.id"
              ref="responseMethod"
              width="650px"
              height="200px"
              mode="ace/mode/javascript"
              :data.sync="urlProxy.model.responseMethod"
              @blur="$refs.urlProxyRef.validateField('responseMethod')"
            />
          </div>
          }
        </el-form-item>
        <el-form-item style="margin-bottom: 20px">
          <el-button type="primary" size="small" @click="handleSave">
            保存
          </el-button>
          <el-button size="small" @click="handleClose"> 取消 </el-button>
          <div class="urlproxy-edit-bottom-bar" />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { UrlProxyAPI } from 'src/api'
import { validateCode, handleErrorMessage } from 'src/utils'
import { log } from 'sinitek-lowcode-shared'

export default {
  name: 'LcUrlProxyEdit',
  data() {
    return {
      // 外部
      requestHeaders: {},
      urlProxy: {
        rules: {
          code: [
            { required: true, trigger: 'blur', message: '请填写编码' },
            {
              validator: validateCode,
              trigger: ['change', 'blur'],
              message:
                '编码必须以字母开头，由英文字母、数字、下划线（_）、连字符（-）组成',
            },
          ],
          url: [{ required: true, trigger: 'blur', message: '请填写请求地址' }],
          requestHeaders: [
            { validator: this.validateRequestHeaders, trigger: 'blur' },
          ],
          responseMethod: [
            { validator: this.validateResponseMethod, trigger: 'blur' },
          ],
        },
        model: {
          id: '',
          code: '',
          url: '',
          requestMethod: 1,
          requestClazz: '',
          requestHeaders: '{}',
          responseClazz: '',
          responseMethod: '',
        },
      },
    }
  },
  watch: {},
  created() {
    if (this.$route.query.id) {
      this.initData(this.$route.query.id)
    }
  },
  methods: {
    validateRequestHeaders(rule, value, callback) {
      try {
        this.urlProxy.model.requestHeaders = JSON.stringify(this.requestHeaders)
        if (this.urlProxy.model.requestHeaders.length > 3000) {
          callback(new Error('自定义请求头不能超过3000字符'))
        } else {
          callback()
        }
      } catch {
        callback(new Error('自定义请求头必须为JSON格式'))
      }
    },
    validateResponseMethod(rule, value, callback) {
      if (value && value.length > 3000) {
        callback(new Error('返回值处理函数不能超过3000字符'))
      } else {
        callback()
      }
    },
    initData(id) {
      if (id) {
        this.$http.get(UrlProxyAPI.URLPROXY_DETAIL(), { id }).then((res) => {
          const data = res.data
          this.urlProxy.model = { ...this.urlProxy.model, ...data }
          try {
            this.requestHeaders = JSON.parse(data.requestHeaders)
          } catch {
            log(data.requestHeaders, '序列化失败')
          }
          this.$nextTick(() => {
            this.$refs.urlProxyRef.clearValidate()
          })
        })
      } else {
        this.urlProxy.model = {
          id: '',
          code: '',
          url: '',
          requestMethod: 1,
          requestClazz: '',
          requestHeaders: '{}',
          responseClazz: '',
          responseMethod: '',
        }
        this.$nextTick(() => {
          this.$refs.urlProxyRef.clearValidate()
        })
      }
    },
    handleSave() {
      this.$refs.urlProxyRef.validate((valid) => {
        if (valid) {
          try {
            this.urlProxy.model.requestHeaders = JSON.stringify(
              this.requestHeaders
            )
          } catch {
            this.$message.info('自定义请求头必须为JSON格式')
            return
          }
          this.$http
            .post(UrlProxyAPI.URLPROXY_SAVE(), this.urlProxy.model)
            .then(() => {
              this.$message.success('保存成功')
              this.handleClose()
            })
            .catch((error) => {
              handleErrorMessage(this, error)
            })
        } else {
          console.error('数据集校验不通过')
        }
      })
    },
    handleClose() {
      // bug:288644 快速多次点击保存按钮--编辑页面和表单管理页面都关闭了
      // this.$closeTab(this.currentRoute)
      // this.$closeTab中不加this.currentRoute参数，加了会导致在兼容菜单返回不了
      this.$closeTab()
    },
  },
}
</script>

<style scoped>
.urlProxy_item {
  width: 560px !important;
}
.urlproxy-edit-bottom-bar {
  width: 100%;
  height: 60px;
}
</style>
