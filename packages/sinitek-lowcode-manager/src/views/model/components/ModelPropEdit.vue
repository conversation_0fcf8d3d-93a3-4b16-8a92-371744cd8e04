<template>
  <xn-dialog
    id="diagModelPropEdit"
    :title="title"
    :show.sync="diagModelPropEdit.display"
    :buttons="diagModelPropEdit.buttons"
    @save="handleSave"
    @close="handleClose"
  >
    <template slot="dialog-form">
      <el-form
        id="formModelProp"
        :model="formModelProp.model"
        :rules="formModelProp.rules"
        label-width="150px"
        ref="formModelPropRef"
        v-if="diagModelPropEdit.display"
      >
        <el-form-item prop="name">
          <span slot="label">
            属性名
            <el-tooltip
              v-if="formModelProp.model.propType === PROP_TYPE.FOREIGN_KEY"
              class="helpTooltip"
              effect="light"
              content="当属性类型为外键时,属性名必须以`Id`结尾"
              placement="top"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </span>
          <el-input
            v-model.trim="formModelProp.model.name"
            :maxlength="50"
            :show-word-limit="true"
            class="formWidth"
          />
        </el-form-item>
        <el-form-item label="物理字段名" prop="colName">
          <el-input
            v-model.trim="formModelProp.model.colName"
            disabled
            class="prop-col-name"
          />
          <xn-button @click="clearColName" class="prop-col-name-clear-btn">
            清空
          </xn-button>
          <xn-button
            @click="generatePropColName"
            class="prop-col-name-generate-btn"
          >
            生成
          </xn-button>
        </el-form-item>
        <el-form-item label="属性显示名" prop="comments">
          <el-input
            class="formWidth"
            v-model.trim="formModelProp.model.comments"
            maxlength="100"
            :show-word-limit="true"
          />
        </el-form-item>
        <el-form-item label="类型" prop="propType">
          <xn-select
            v-model="formModelProp.model.propType"
            :options="PROP_TYPE_OPTIONS"
            filterable
            @change="handleModelTypeChange"
          />
        </el-form-item>
        <el-form-item label="组件类型" prop="componentType">
          <xn-select
            filterable
            v-model="formModelProp.model.componentType"
            :options="formModelProp.componentTypeOptions"
            @change="handleComponentTypeChange"
          />
        </el-form-item>
        <el-form-item
          key="configJson.formatedName"
          label="格式化后显示名"
          prop="configJson.formatedName"
          v-if="showFormatedName"
        >
          <el-input
            v-model.trim="formModelProp.model.configJson.formatedName"
            maxlength="50"
            show-word-limit
            class="formWidth"
          />
          <el-tooltip
            class="helpTooltip"
            effect="light"
            content="数据格式化后值对应的key"
            placement="top"
          >
            <i class="el-icon-question" />
          </el-tooltip>
        </el-form-item>
        <!-- 自定义属性属性处理器 -->
        <template v-if="formModelProp.model.propType === PROP_TYPE.CUSTOM">
          <el-form-item label="属性处理器" prop="customHandler">
            <TextCopy :text="formModelProp.model.customHandler">
              <i
                class="el-icon-refresh refresh-icon"
                @click="handleRefreshCustomHandlerIconClick"
              />
            </TextCopy>
          </el-form-item>
          <el-form-item
            label="属性处理器参数"
            key="configJson.customConfig"
            prop="configJson.customConfig"
          >
            <div style="display: flex; width: 100%">
              <lc-editor
                language="json"
                :model-value.sync="formModelProp.model.configJson.customConfig"
                height="150px"
                class="formWidth"
              />
              <el-tooltip
                class="helpTooltip"
                effect="light"
                content="请输入JSON字符串"
                placement="top"
              >
                <i class="el-icon-question" />
              </el-tooltip>
            </div>
          </el-form-item>
        </template>
        <!-- 关联模型 -->
        <template
          v-if="
            formModelProp.model.propType === PROP_TYPE.MODEL ||
            formModelProp.model.propType === PROP_TYPE.FOREIGN_KEY ||
            formModelProp.model.propType === PROP_TYPE.FOREIGN_KEY_MV
          "
        >
          <el-form-item
            key="relaModelCode"
            label="关联模型"
            prop="relaModelCode"
          >
            <el-select
              v-model="formModelProp.model.relaModelCode"
              @change="handleRelaModelChange"
              :filterable="true"
              clearable
              :popper-append-to-body="true"
            >
              <el-option
                v-for="item in formModelProp.modelList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
                :disabled="!!item.disabled"
                style="max-width: 400px"
              >
                <span :title="item.name"> {{ item.name }} </span>
              </el-option>
            </el-select>
            <span class="__error" v-if="relaModelErrorMsg">
              {{ relaModelErrorMsg }}
            </span>
          </el-form-item>
          <template v-if="formModelProp.modelAssociationOptions.length > 0">
            <el-form-item
              key="configJson.association"
              label="关联关系"
              prop="configJson.association"
            >
              <el-select
                v-model="formModelProp.model.configJson.association"
                :popper-append-to-body="true"
              >
                <el-option
                  v-for="item in formModelProp.modelAssociationOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <el-form-item
            key="parentName"
            label="关联属性"
            label-position="top"
            prop="relas"
            v-if="
              formModelProp.model.propRelas &&
              formModelProp.model.propRelas.length > 0
            "
          >
            <ul>
              <li
                v-for="(relaModel, index) in formModelProp.model.propRelas"
                :key="relaModel.childName"
                class="relaModel"
              >
                <el-input
                  v-model="relaModel.childName"
                  disabled
                  class="modelName"
                />
                <!--关联属性操作选项-->
                <el-select
                  class="propType"
                  v-model="relaModel.type"
                  @change="handleModelTypeSelectChange(index)"
                  :popper-append-to-body="true"
                >
                  <el-option
                    v-for="relaTypeEnum in RELA_TYPE_OPTIONS"
                    :key="relaTypeEnum.value"
                    :label="relaTypeEnum.label"
                    :value="relaTypeEnum.value"
                  />
                </el-select>
                <!--当前关联属性下拉框-->
                <el-select
                  v-if="relaModel.type === RELA_TYPE.RELAKEY"
                  class="modelProp"
                  :key="relaModel.childName"
                  v-model="relaModel.parentName"
                  clearable
                  :popper-append-to-body="true"
                >
                  <template
                    v-if="
                      formModelProp.model.propType === PROP_TYPE.FOREIGN_KEY
                    "
                  >
                    <el-option
                      v-for="item in [{ value: COL_NAME.ID }]"
                      :key="item.value"
                      :label="item.value"
                      :value="item.value"
                    />
                  </template>
                  <template v-else>
                    <el-option
                      v-for="currentProp in propList.filter(
                        (currentProp) =>
                          !UN_SUPPORT_RELA_PROP_TYPE.includes(currentProp.type)
                      )"
                      :key="currentProp.name"
                      :label="currentProp.name"
                      :value="currentProp.name"
                    />
                  </template>
                </el-select>
                <!--当前关联属性输入框-->
                <tempalte v-if="relaModel.type === RELA_TYPE.VALUE">
                  <el-input
                    class="modelProp"
                    v-model.trim="relaModel.relaValue"
                    show-word-limit
                    :maxlength="50"
                  />
                  <el-select
                    class="modelProp rela-value-type"
                    v-model="relaModel.relaValueType"
                    :popper-append-to-body="true"
                    placeholder="请选择固定值类型"
                  >
                    <el-option
                      v-for="relaTypeEnum in RELA_VALUE_TYPE_OPTIONS"
                      :key="relaTypeEnum.value"
                      :label="relaTypeEnum.label"
                      :value="relaTypeEnum.value"
                    />
                  </el-select>
                </tempalte>
                <!--第一行出现帮助图标-->
                <el-tooltip
                  class="helpTooltip"
                  effect="light"
                  placement="top"
                  v-if="index === 0"
                >
                  <div slot="content">
                    第一列为关联模型的外键属性<br />第二列为关联类型<br />第三列为当前模型的属性
                  </div>
                  <i class="el-icon-question" />
                </el-tooltip>
                <el-button
                  v-if="
                    index !== 0 &&
                    subModelSelectablePropOptions.includes(relaModel.childName)
                  "
                  @click="handleRelaModelRemoveBtnClick(index)"
                  circle
                  plain
                  type="danger"
                  size="mini"
                  icon="el-icon-minus"
                  style="padding: 4px; margin-left: 5px"
                />
              </li>
              <li
                v-for="(relaModel, index) in addedPropRelas"
                :key="relaModel.childName + '' + index"
                class="relaModel"
              >
                <el-select
                  class="modelName"
                  v-model="relaModel.childName"
                  :popper-append-to-body="true"
                >
                  <el-option
                    v-for="prop in subModelSelectablePropOptions"
                    :disabled="isSubModelPropOptionsDisbaled(prop)"
                    :key="prop"
                    :label="prop"
                    :value="prop"
                  />
                </el-select>
                <!--关联属性操作选项-->
                <!-- 获取关联模型对应的关联属性信息
                这里一开始限定了关联类型只能为固定值(考虑到除了外键,父子数据模型不应该有其他字段关联)
                但是后来放开了,为了处理动态产品查询立项数据还是最新数据这种情形 -->
                <el-select
                  class="propType"
                  v-model="relaModel.type"
                  :popper-append-to-body="true"
                >
                  <el-option
                    v-for="relaTypeEnum in RELA_TYPE_OPTIONS"
                    :key="relaTypeEnum.value"
                    :label="relaTypeEnum.label"
                    :value="relaTypeEnum.value"
                  />
                </el-select>
                <!--当前关联属性下拉框-->
                <el-select
                  v-if="relaModel.type === RELA_TYPE.RELAKEY"
                  class="modelProp"
                  :key="relaModel.childName"
                  v-model="relaModel.parentName"
                  :popper-append-to-body="true"
                  clearable
                >
                  <el-option
                    v-for="currentProp in propList.filter(
                      (currentProp) =>
                        !UN_SUPPORT_RELA_PROP_TYPE.includes(currentProp.type)
                    )"
                    :key="currentProp.name"
                    :label="currentProp.name"
                    :value="currentProp.name"
                  />
                </el-select>
                <!--当前关联属性输入框-->
                <template v-if="relaModel.type === RELA_TYPE.VALUE">
                  <el-input
                    class="modelProp"
                    v-model.trim="relaModel.relaValue"
                    show-word-limit
                    :maxlength="50"
                  />
                  <el-select
                    class="modelProp rela-value-type"
                    v-model="relaModel.relaValueType"
                    :popper-append-to-body="true"
                    placeholder="请选择固定值类型"
                  >
                    <el-option
                      v-for="relaTypeEnum in RELA_VALUE_TYPE_OPTIONS"
                      :key="relaTypeEnum.value"
                      :label="relaTypeEnum.label"
                      :value="relaTypeEnum.value"
                    />
                  </el-select>
                </template>
                <el-button
                  @click="handleAddedPropOptionsRemoveBtnClick(index)"
                  circle
                  plain
                  type="danger"
                  size="mini"
                  icon="el-icon-minus"
                  style="padding: 4px; margin-left: 5px"
                />
              </li>
            </ul>
            <el-button
              type="text"
              @click="handleAddPropBtnClick"
              v-if="
                subModelSelectablePropOptions.length > 0 &&
                formModelProp.model.propType != PROP_TYPE.FOREIGN_KEY
              "
            >
              添加
            </el-button>
          </el-form-item>
        </template>
        <!-- 枚举 -->
        <template v-if="isEnumType(formModelProp.model.propType)">
          <el-form-item key="enumCatalog" label="模块名称" prop="enumCatalog">
            <!-- 长度限制66数据来源系统管理 -> 系统配置 -> 枚举配置-> 新增枚举类型 -->
            <el-input
              v-model.trim="formModelProp.model.enumCatalog"
              :maxlength="66"
              :show-word-limit="true"
              class="formWidth"
            />
          </el-form-item>
          <el-form-item key="enumType" label="枚举类型" prop="enumType">
            <!-- 长度限制66数据来源系统管理 -> 系统配置 -> 枚举配置-> 新增枚举类型 -->
            <el-input
              v-model.trim="formModelProp.model.enumType"
              :maxlength="66"
              :show-word-limit="true"
              class="formWidth"
            />
          </el-form-item>
        </template>
        <!-- 附件属性 -->
        <template v-if="formModelProp.model.propType === PROP_TYPE.ATTACHMENT">
          <el-form-item
            key="configJson.attachmentSourceName"
            label="来源实体"
            prop="configJson.attachmentSourceName"
          >
            <el-input
              v-model.trim="formModelProp.model.configJson.attachmentSourceName"
              maxlength="50"
              show-word-limit
              class="formWidth"
            />
          </el-form-item>
          <el-form-item
            key="configJson.attachmentSourceIdPropName"
            label="来源ID字段"
            prop="configJson.attachmentSourceIdPropName"
          >
            <el-select
              v-model="
                formModelProp.model.configJson.attachmentSourceIdPropName
              "
              :popper-append-to-body="true"
              class="formWidth"
            >
              <el-option
                v-for="currentProp in propList.filter(
                  (currentProp) =>
                    !UN_SUPPORT_RELA_PROP_TYPE.includes(currentProp.type) &&
                    currentProp.colName
                )"
                :key="currentProp.name"
                :label="currentProp.name"
                :value="currentProp.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            key="configJson.attachmentType"
            label="附件类型"
            prop="configJson.attachmentType"
          >
            <el-input
              v-model.number="formModelProp.model.configJson.attachmentType"
              maxlength="4"
              show-word-limit
              class="formWidth"
            />
          </el-form-item>
        </template>
        <!-- 时间 -->
        <el-form-item
          label="时间格式"
          prop="format"
          key="time"
          v-if="formModelProp.model.propType === PROP_TYPE.DATE"
        >
          <el-input
            v-model="formModelProp.model.format"
            :maxlength="50"
            class="formWidth"
            show-word-limit
            @blur="handlePropFormatBlur"
          />
        </el-form-item>
        <!-- 多值 -->
        <template v-if="showMvStoreType">
          <el-form-item label="存储类型" prop="configJson.mvStoreType">
            <xn-select
              v-model="formModelProp.model.configJson.mvStoreType"
              :options="MV_STORE_TYPE_OPTIONS"
            />
          </el-form-item>
          <template
            v-if="
              formModelProp.model.configJson.mvStoreType ===
              MV_STORE_TYPE.MV_TABLE
            "
          >
            <el-form-item
              key="configJson.mvTableName"
              label="多值表名"
              prop="configJson.mvTableName"
            >
              <el-input
                v-model.trim="formModelProp.model.configJson.mvTableName"
                maxlength="50"
                show-word-limit
                class="formWidth"
              />
              <el-tooltip
                class="helpTooltip"
                effect="light"
                content="数据存储的表名,一般为对应表+'_mv'"
                placement="top"
              >
                <i class="el-icon-question" />
              </el-tooltip>
            </el-form-item>
            <el-form-item
              key="configJson.mvTableValueColName"
              label="当前值对应物理字段名"
              prop="configJson.mvTableValueColName"
            >
              <el-input
                v-model.trim="
                  formModelProp.model.configJson.mvTableValueColName
                "
                maxlength="50"
                show-word-limit
                class="formWidth"
              />
              <el-tooltip
                class="helpTooltip"
                effect="light"
                content="数据存储的字段名"
                placement="top"
              >
                <i class="el-icon-question" />
              </el-tooltip>
            </el-form-item>
            <el-form-item
              key="configJson.mvTableForeignKeyName"
              label="外键对应物理字段名"
              prop="configJson.mvTableForeignKeyName"
            >
              <el-input
                v-model.trim="
                  formModelProp.model.configJson.mvTableForeignKeyName
                "
                maxlength="50"
                show-word-limit
                class="formWidth"
              />
              <el-tooltip
                class="helpTooltip"
                effect="light"
                content="数据对应的外键名"
                placement="top"
              >
                <i class="el-icon-question" />
              </el-tooltip>
            </el-form-item>
          </template>
        </template>
        <!-- 校验 -->
        <!-- 必填校验 -->
        <el-form-item label="必填校验" v-if="needRequiredValidate">
          <el-switch v-model="formModelProp.model.validateJson.required" />
        </el-form-item>
        <!-- 必填校验提示信息 -->
        <el-form-item
          label="必填校验提示信息"
          v-if="
            needRequiredValidate && formModelProp.model.validateJson.required
          "
        >
          <el-input
            class="formWidth"
            maxlength="50"
            :show-word-limit="true"
            v-model.trim="formModelProp.model.validateJson.requiredValidateMsg"
          />
        </el-form-item>

        <!-- 长度校验 -->
        <el-form-item label="长度校验" v-if="needRegrexAndStrLengthValidate">
          <el-switch
            v-model="formModelProp.model.validateJson.validateStrLength"
          />
        </el-form-item>
        <!-- 最大长度 -->
        <el-form-item
          v-if="
            formModelProp.model.validateJson.validateStrLength &&
            needRegrexAndStrLengthValidate
          "
          label="最大长度"
          prop="validateJson.strMaxLength"
        >
          <el-input
            class="formWidth"
            v-model.number="formModelProp.model.validateJson.strMaxLength"
          />
        </el-form-item>
        <!-- 长度校验提示信息 -->
        <el-form-item
          label="长度校验提示信息"
          v-if="
            formModelProp.model.validateJson.validateStrLength &&
            needRegrexAndStrLengthValidate
          "
        >
          <el-input
            class="formWidth"
            maxlength="50"
            :show-word-limit="true"
            v-model.trim="formModelProp.model.validateJson.strLengthValidateMsg"
          />
          <el-tooltip
            class="helpTooltip"
            effect="light"
            content="支持动态变量`$strMaxLength$`"
            placement="top"
          >
            <i class="el-icon-question" />
          </el-tooltip>
        </el-form-item>
        <!-- 正则校验 -->
        <el-form-item label="正则校验" v-if="needRegrexAndStrLengthValidate">
          <el-switch
            v-model="formModelProp.model.validateJson.validateRegexp"
          />
        </el-form-item>
        <!-- 正则校验正则表达式 -->
        <el-form-item
          v-if="
            formModelProp.model.validateJson.validateRegexp &&
            needRegrexAndStrLengthValidate
          "
          label="正则表达式"
          prop="validateJson.regexp"
        >
          <el-input
            class="formWidth"
            maxlength="100"
            :show-word-limit="true"
            v-model="formModelProp.model.validateJson.regexp"
          />
        </el-form-item>
        <!-- 正则校验提示信息 -->
        <el-form-item
          label="正则校验提示信息"
          v-if="
            formModelProp.model.validateJson.validateRegexp &&
            needRegrexAndStrLengthValidate
          "
        >
          <el-input
            class="formWidth"
            maxlength="50"
            :show-word-limit="true"
            v-model.trim="formModelProp.model.validateJson.regexpValidateMsg"
          />
          <el-tooltip
            class="helpTooltip"
            effect="light"
            content="支持动态变量`$regexp$`"
            placement="top"
          >
            <i class="el-icon-question" />
          </el-tooltip>
        </el-form-item>
        <!-- 最大值校验 -->
        <el-form-item label="最大值校验" v-if="needMaxNumAndMinNumValidate">
          <el-switch
            v-model="formModelProp.model.validateJson.validateMaxNum"
          />
        </el-form-item>
        <!-- 最大值校验最大值 -->
        <el-form-item
          label="最大值"
          prop="validateJson.maxNum"
          v-if="
            formModelProp.model.validateJson.validateMaxNum &&
            needMaxNumAndMinNumValidate
          "
        >
          <el-input
            class="formWidth"
            v-model.trim="formModelProp.model.validateJson.maxNum"
          />
        </el-form-item>
        <!-- 最大值校验提示信息 -->
        <el-form-item
          label="最大值校验提示信息"
          v-if="
            formModelProp.model.validateJson.validateMaxNum &&
            needMaxNumAndMinNumValidate
          "
        >
          <el-input
            class="formWidth"
            maxlength="50"
            :show-word-limit="true"
            v-model.trim="formModelProp.model.validateJson.maxNumValidateMsg"
          />
          <el-tooltip
            class="helpTooltip"
            effect="light"
            content="支持动态变量`$maxNum$`"
            placement="top"
          >
            <i class="el-icon-question" />
          </el-tooltip>
        </el-form-item>
        <!-- 最小值校验 -->
        <el-form-item label="最小值校验" v-if="needMaxNumAndMinNumValidate">
          <el-switch
            v-model="formModelProp.model.validateJson.validateMinNum"
          />
        </el-form-item>
        <!-- 最小值校验最小值 -->
        <el-form-item
          label="最小值"
          prop="validateJson.minNum"
          v-if="
            formModelProp.model.validateJson.validateMinNum &&
            needMaxNumAndMinNumValidate
          "
        >
          <el-input
            class="formWidth"
            v-model.trim="formModelProp.model.validateJson.minNum"
          />
        </el-form-item>
        <!-- 最小值校验提示信息 -->
        <el-form-item
          label="最小值校验提示信息"
          v-if="
            formModelProp.model.validateJson.validateMinNum &&
            needMaxNumAndMinNumValidate
          "
        >
          <el-input
            class="formWidth"
            maxlength="50"
            :show-word-limit="true"
            v-model.trim="formModelProp.model.validateJson.minNumValidateMsg"
          />
          <el-tooltip
            class="helpTooltip"
            effect="light"
            content="支持动态变量`$minNum$`"
            placement="top"
          >
            <i class="el-icon-question" />
          </el-tooltip>
        </el-form-item>
      </el-form>
    </template>
  </xn-dialog>
</template>

<script>
import { DataModelAPI } from 'src/api'
import {
  PROP_TYPE,
  COMPONENT_TYPE,
  RELA_TYPE,
  DEFAULT_DATE_FORMAT,
  DATA_MODEL_ASSOCIATION_OPTIONS,
  PROP_TYPE_OPTIONS,
  RELA_TYPE_OPTIONS,
  COL_NAME,
  RELA_VALUE_TYPE,
  RELA_VALUE_TYPE_OPTIONS,
  MV_STORE_TYPE,
  MV_STORE_TYPE_OPTIONS,
} from 'src/constant'
import {
  validateIntNum,
  findModelPropComponentTypeOptionsByPropType,
  findModelAssociationOptions,
  makePropName2ColName,
  isFrameworkPropType,
  isMvValue,
  isEnumType,
} from 'src/utils'

export default {
  name: 'ModelPropEdit',
  props: {
    moduleCode: {
      type: String,
      default: '',
    },
    modelId: {
      type: String,
      default: '',
    },
    modelCode: {
      type: String,
      default: '',
    },
    propList: {
      type: Array,
      default: () => {
        return []
      },
    },
    title: {
      type: String,
      default: '',
    },
    propName: {
      // 属性名称，用于标识新增/编辑的属性, 新增时：name为空
      type: String,
      default: '',
    },
    filterPropList: {
      type: Function,
      default: undefined,
    },
  },
  components: {
    TextCopy: () => import('src/views/components/textCopy'),
  },
  watch: {
    'diagModelPropEdit.display'(val) {
      if (val) {
        this.loadDataModel()
      }
    },
    'formModelProp.model.validateJson.maxNum'() {
      this.$refs.formModelPropRef.validate()
    },
    'formModelProp.model.validateJson.minNum'() {
      this.$refs.formModelPropRef.validate()
    },
  },
  mounted() {
    this.formModelProp.componentTypeOptions =
      findModelPropComponentTypeOptionsByPropType(PROP_TYPE.STRING)
  },
  data() {
    return {
      isEnumType,
      MV_STORE_TYPE: Object.freeze(MV_STORE_TYPE),
      MV_STORE_TYPE_OPTIONS: Object.freeze(MV_STORE_TYPE_OPTIONS),
      RELA_VALUE_TYPE: Object.freeze(RELA_VALUE_TYPE),
      RELA_VALUE_TYPE_OPTIONS: Object.freeze(RELA_VALUE_TYPE_OPTIONS),
      COL_NAME: Object.freeze(COL_NAME),
      PROP_TYPE_OPTIONS: Object.freeze(PROP_TYPE_OPTIONS),
      DATA_MODEL_ASSOCIATION_OPTIONS: Object.freeze(
        DATA_MODEL_ASSOCIATION_OPTIONS
      ),
      PROP_TYPE: Object.freeze(PROP_TYPE),
      RELA_TYPE: Object.freeze(RELA_TYPE),
      RELA_TYPE_OPTIONS: Object.freeze(RELA_TYPE_OPTIONS),
      UN_SUPPORT_RELA_PROP_TYPE: [
        PROP_TYPE.ATTACHMENT,
        PROP_TYPE.MODEL,
        PROP_TYPE.CREATETIMESTAMP,
        PROP_TYPE.UPDATETIMESTAMP,
        PROP_TYPE.VERSION,
      ],
      diagModelPropEdit: {
        display: false,
        buttons: [
          { label: '确定', action: 'save', type: 'primary', event: 'save' },
          { action: 'cancel', type: 'info', event: 'close' },
        ],
      },
      formModelProp: {
        componentTypeOptions: [],
        modelAssociationOptions: [],
        tableName: '',
        model: {
          // 当前属性配置
          id: '',
          name: '',
          colName: '',
          tableName: '',
          propType: PROP_TYPE.STRING,
          componentType: COMPONENT_TYPE.INPUT,
          // 自定义属性处理器
          customHandler: '',
          relaModelCode: '',
          enumCatalog: '',
          enumType: '',
          format: '',
          propRelas: [], // type=model时，与子模型的关联关系
          // 校验的描述
          validateJson: {
            // 该对象会打包成JSON串
            // 是否必填
            required: false,
            requiredValidateMsg: '',

            // 字符串长度校验
            validateStrLength: false,
            // 最大长度
            strMaxLength: 50,
            strLengthValidateMsg: '',

            // 是否正则校验
            validateRegexp: false,
            // 正则表达式
            regexp: '',
            regexpValidateMsg: '',

            // 是否开启最大值校验
            validateMaxNum: false,
            // 最大值
            maxNum: 0,
            maxNumValidateMsg: '',

            // 是否开启最小值校验
            validateMinNum: false,
            // 最小值
            minNum: 0,
            minNumValidateMsg: '',
          },
          configJson: {
            // 关联关系
            association: '',
            // 当属性为自定义类型时,项目自定义的配置
            customConfig: '{}',
            // 附件来源实体
            attachmentSourceName: '',
            // 附件来源ID字段
            attachmentSourceIdPropName: '',
            // 附件附件类型
            attachmentType: 0,
            // 格式化后显示名
            formatedName: '',
            mvStoreType: undefined,
            mvTableName: '',
            mvTableValueColName: '',
            mvTableForeignKeyName: '',
          },
          comments: '',
        },
        modelList: [], // 数据模型列表数据
        rules: {
          name: [
            {
              required: true,
              trigger: ['blur', 'change'],
              message: '请填写属性名',
            },
            { validator: this.validateName },
            {
              validator: this.validateMultiName,
              trigger: 'blur',
              message: '属性名称已存在，不能重复！',
            },
          ],
          colName: [
            { validator: this.validateColName },
            {
              validator: this.validateMultiColName,
              trigger: 'blur',
              message: '物理字段名已存在，不能重复！',
            },
          ],
          propType: [
            {
              validator: this.validateOnlyKey,
              message: '主键唯一，已存在其他属性为主键！',
            },
            {
              required: true,
              trigger: ['blur'],
              message: '属性的类型不能为空',
            },
          ],
          componentType: [
            {
              required: true,
              trigger: ['blur'],
              message: '属性的组件类型不能为空',
            },
          ],
          relas: [
            {
              required: true,
              validator: this.validateCurrentModelProp,
              message: '请选择关联属性',
            },
          ],
          enumCatalog: [
            {
              required: true,
              trigger: ['blur', 'change'],
              message: '请填写模块名称',
            },
          ],
          enumType: [
            {
              required: true,
              trigger: ['blur', 'change'],
              message: '请填写枚举类型',
            },
          ],
          format: [
            {
              required: true,
              trigger: ['blur', 'change'],
              message: '请填写时间格式',
            },
          ],
          'validateJson.regexp': [
            {
              required: true,
              trigger: ['blur', 'change'],
              message: '请输入正则校验所需的正则表达式',
            },
          ],
          'validateJson.maxNum': [
            { required: true, trigger: ['blur'], message: '请输入最大值' },
            { validator: this.validateMaxNum, trigger: ['blur'] },
          ],
          'validateJson.minNum': [
            { required: true, trigger: ['blur'], message: '请输入最小值' },
            { validator: this.validateMinNum, trigger: ['blur'] },
          ],
          'validateJson.strMaxLength': [
            { required: true, trigger: ['blur'], message: '请输入最大长度' },
            {
              validator: validateIntNum,
              trigger: ['blur'],
              message: '请输入正整数',
            },
          ],
          'configJson.formatedName': [
            {
              required: true,
              trigger: ['blur', 'change'],
              message: '请输入格式化后显示名',
            },
          ],
          'configJson.customConfig': [
            { validator: this.validateCustomConfig, trigger: ['blur'] },
          ],
          'configJson.attachmentSourceName': [
            {
              required: true,
              trigger: ['blur', 'change'],
              message: '请输入来源实体',
            },
          ],
          'configJson.attachmentSourceIdPropName': [
            { required: true, trigger: ['blur'], message: '请选择来源ID字段' },
          ],
          'configJson.attachmentType': [
            {
              required: true,
              trigger: ['blur', 'change'],
              message: '请输入附件类型',
            },
          ],
        },
      },
      // 添加时子模型可选属性:抛去框架字段与所有外键
      subModelSelectablePropOptions: [],
      // type=model时，添加的与子模型的关联关系
      addedPropRelas: [],
      relaModelErrorMsg: '',
    }
  },
  computed: {
    showMvStoreType() {
      return isMvValue(this.formModelProp.model.propType)
    },
    showFormatedName() {
      return (
        this.formModelProp.model.propType === PROP_TYPE.SIRMORG ||
        this.formModelProp.model.propType === PROP_TYPE.STRING_SIRMENUM ||
        this.formModelProp.model.propType === PROP_TYPE.NUMBER_SIRMENUM ||
        this.formModelProp.model.propType === PROP_TYPE.SIRMORG_MV ||
        this.formModelProp.model.propType === PROP_TYPE.STRING_SIRMENUM_MV ||
        this.formModelProp.model.propType === PROP_TYPE.NUMBER_SIRMENUM_MV
      )
    },
    needRequiredValidate() {
      return (
        this.formModelProp.model.propType !== PROP_TYPE.ATTACHMENT &&
        this.formModelProp.model.propType !== PROP_TYPE.MODEL &&
        !isFrameworkPropType(this.formModelProp.model.propType)
      )
    },
    needRegrexAndStrLengthValidate() {
      return this.formModelProp.model.propType === PROP_TYPE.STRING
    },
    needMaxNumAndMinNumValidate() {
      return (
        this.formModelProp.model.propType === PROP_TYPE.INTEGER ||
        this.formModelProp.model.propType === PROP_TYPE.DECIMAL ||
        this.formModelProp.model.propType === PROP_TYPE.LONG
      )
    },
    dataAggregationSubModelSelectablePropOptions() {
      return [
        'ALL',
        ...this.subModelSelectablePropOptions.filter(
          (item) => !this.isSubModelPropOptionsDisbaled(item)
        ),
      ]
    },
  },
  methods: {
    handlePropFormatBlur() {
      if (this.formModelProp.model.format) {
        this.formModelProp.model.format = this.formModelProp.model.format.trim()
      }
    },
    handleRefreshCustomHandlerIconClick() {
      // 模型名 this.modelCode
      // 属性名 formModelProp.model.name
      if (this.formModelProp.model.name) {
        this.$http
          .get(DataModelAPI.GET_CUSTOM_PROP_HANDLER_CANONICAL_NAME(), {
            modelCode: this.modelCode,
            propName: this.formModelProp.model.name,
          })
          .then((res) => {
            if (res.data) {
              this.formModelProp.model.customHandler = res.data
              this.$message.success('刷新成功')
            } else {
              this.$message.info('当前属性不存在自定义属性处理器')
            }
          })
          .catch((error) => {
            console.error('获取属性处理器全路径报错', error)
            this.$message.info('获取属性处理器全路径报错')
          })
      } else {
        this.$message.info('属性名不能为空')
      }
    },
    handleModelTypeSelectChange(index) {
      this.formModelProp.model.propRelas[index].parentName = null
      this.formModelProp.model.propRelas[index].relaValue = null
      this.formModelProp.model.propRelas[index].relaValueType = null
    },
    loadDataModel() {
      const para = {
        moduleCode: this.moduleCode,
      }
      // 获取关联模型信息
      this.$http
        .get(DataModelAPI.MODEL_AVAILABLELIST(), para)
        .then((result) => {
          if (result.resultcode === '0') {
            const tempModelList = []
            result.data.forEach((item) => {
              if (item.id !== this.modelId) {
                tempModelList.push(item)
              }
            })
            this.formModelProp.modelList = [...tempModelList]
          }
        })
    },
    show(data, tableName) {
      this.formModelProp.tableName = tableName
      // eslint-disable-next-line no-console
      console.log('show data', data)
      this.diagModelPropEdit.display = true
      let validate = this.$options.data().formModelProp.model.validateJson
      let configJson = this.$options.data().formModelProp.model.configJson
      // 编辑
      if (data) {
        if (typeof data.validateJson === 'string' && data.validateJson !== '') {
          validate = { ...validate, ...JSON.parse(data.validateJson) }
        }
        if (typeof data.configJson === 'string' && data.configJson !== '') {
          configJson = { ...configJson, ...JSON.parse(data.configJson) }
        }
        this.formModelProp.componentTypeOptions =
          findModelPropComponentTypeOptionsByPropType(data.type)
        this.formModelProp.modelAssociationOptions =
          findModelAssociationOptions(data.type, data.componentType)
        this.formModelProp.model = {
          id: data.id,
          name: data.name,
          colName: data.colName,
          propType: data.type,
          componentType: data.componentType,
          customHandler: data.customHandler,
          relaModelCode: data.relaModelCode,
          propRelas: data.propRelas ? [...data.propRelas] : null,
          enumCatalog: data.enumCatalog,
          enumType: data.enumType,
          format: data.format,
          validateJson: { ...validate },
          configJson: { ...configJson },
          comments: data.comments,
        }
        // 如果类型为数据模型则需要加载subModelSelectablePropOptions
        if (this.formModelProp.model.propType === PROP_TYPE.MODEL) {
          this.$nextTick(() => {
            this.loadSubModelPropsData(
              this.formModelProp.model.relaModelCode,
              1
            )
          })
        }
      }
    },
    // 加载关联模型关联属性
    // modelId应该为子数据模型的id
    // type有两种:
    // 1:表示是父组件例如ModelEdit调用ref.show方法打开ModelPropEdit子组件,加载未配置的关联属性
    // 2:表示是关联的属性类型修改导致子模型关联属性全量刷新
    loadSubModelPropsData(relaModelCode, type) {
      this.relaModelErrorMsg = ''
      this.subModelSelectablePropOptions = []
      this.addedPropRelas = []
      if (relaModelCode) {
        const para = {
          code: relaModelCode,
        }
        // 获取关联模型对应的关联属性信息
        this.$http.get(DataModelAPI.MODEL_DETAIL(), para).then((result) => {
          // eslint-disable-next-line no-console
          console.log('result', result)
          if (result.resultcode === '0') {
            const res = result.data
            if (res && res.props) {
              // 关联属性筛选：type === 'FOREIGN_KEY'
              const subModelProps = res.props
              const normalProps = []
              const relaModels = []
              subModelProps.forEach((item) => {
                if (
                  item.type !== PROP_TYPE.FOREIGN_KEY &&
                  item.type !== PROP_TYPE.CREATETIMESTAMP &&
                  item.type !== PROP_TYPE.UPDATETIMESTAMP &&
                  item.type !== PROP_TYPE.VERSION &&
                  item.type !== PROP_TYPE.MODEL
                ) {
                  normalProps.push(item)
                } else if (item.type === PROP_TYPE.FOREIGN_KEY) {
                  relaModels.push(item)
                }
              })
              normalProps.forEach((item) => {
                this.subModelSelectablePropOptions.push(item.name)
              })
              if (type === 1) {
                relaModels.forEach((item) => {
                  // 这里应该补充子数据的模型
                  if (!this.isPropRelasAlreadyExist(item.name)) {
                    this.formModelProp.model.propRelas.push({
                      childName: item.name,
                      parentName: '',
                      relaValue: '',
                      relaValueType: RELA_VALUE_TYPE.STRING,
                      type: RELA_TYPE.RELAKEY,
                    })
                  }
                })
              } else if (type === 2) {
                if (relaModels.length === 0) {
                  this.relaModelErrorMsg = '该模型没有外键，请先补充'
                }
                relaModels.forEach((item) => {
                  this.formModelProp.model.propRelas.push({
                    childName: item.name,
                    parentName: '',
                    relaValue: '',
                    relaValueType: RELA_VALUE_TYPE.STRING,
                    type: RELA_TYPE.RELAKEY,
                  })
                })
              }
            }
          }
        })
      } else {
        console.warn('加载关联模型关联属性时,传入模型编码为空')
      }
    },
    isPropRelasAlreadyExist(name) {
      for (
        let index = 0;
        index < this.formModelProp.model.propRelas.length;
        index++
      ) {
        const prop = this.formModelProp.model.propRelas[index]
        if (prop.childName === name) {
          return true
        }
      }
      return false
    },
    isSubModelPropOptionsDisbaled(propChildName) {
      for (
        let index = 0;
        index < this.formModelProp.model.propRelas.length;
        index++
      ) {
        const prop = this.formModelProp.model.propRelas[index]
        if (prop.childName === propChildName) {
          return true
        }
      }
      for (let index = 0; index < this.addedPropRelas.length; index++) {
        const prop = this.addedPropRelas[index]
        if (prop.childName === propChildName) {
          return true
        }
      }
      return false
    },
    handleComponentTypeChange(val) {
      this.formModelProp.modelAssociationOptions = []
      if (
        this.formModelProp.model.propType === PROP_TYPE.FOREIGN_KEY ||
        this.formModelProp.model.propType === PROP_TYPE.MODEL
      ) {
        this.formModelProp.modelAssociationOptions =
          findModelAssociationOptions(this.formModelProp.model.propType, val)
        this.formModelProp.model.configJson.association =
          this.formModelProp.modelAssociationOptions[0].value
      }
    },
    handleModelTypeChange(val) {
      // 重置数据
      this.formModelProp.model = {
        ...this.formModelProp.model,
        relaModelCode: '',
        enumCatalog: '',
        enumType: '',
        format: val === PROP_TYPE.DATE ? DEFAULT_DATE_FORMAT : '',
        propRelas: [],
        componentType: undefined,
        validateJson: this.$options.data().formModelProp.model.validateJson,
        configJson: this.$options.data().formModelProp.model.configJson,
      }
      this.formModelProp.componentTypeOptions =
        findModelPropComponentTypeOptionsByPropType(val)
      if (val === PROP_TYPE.ATTACHMENT) {
        // 来源实体默认为当前数据模型的code
        if (!this.formModelProp.model.configJson?.attachmentSourceName) {
          if (!this.modelCode) {
            // eslint-disable-next-line no-console
            console.log('为附件属性赋来源实体值时传入模型编码为空')
          }
          this.$set(
            this.formModelProp.model.configJson,
            'attachmentSourceName',
            this.modelCode
          )
        }
        // 来源ID字段默认为主键
        if (!this.formModelProp.model.configJson?.attachmentSourceIdPropName) {
          const modelKeyProps = this.propList.filter(
            (item) => item.type === PROP_TYPE.KEY
          )
          if (Array.isArray(modelKeyProps) && modelKeyProps.length === 1) {
            this.$set(
              this.formModelProp.model.configJson,
              'attachmentSourceIdPropName',
              modelKeyProps[0].name
            )
          } else {
            // eslint-disable-next-line no-console
            console.log(
              '为附件属性赋来源ID字段值时找不到主键属性',
              modelKeyProps
            )
          }
        }
        // 附件类型默认为0
        if (
          typeof this.formModelProp.model.configJson?.attachmentType !==
          'number'
        ) {
          this.$set(this.formModelProp.model.configJson, 'attachmentType', 0)
        }
      }
      this.$refs.formModelPropRef.clearValidate()
    },
    camelToSnake(str) {
      return str.replace(/[A-Z]/g, function (match) {
        return '_' + match.toLowerCase()
      })
    },
    // 选择关联模型
    handleRelaModelChange(val) {
      if (this.formModelProp.model.propType === PROP_TYPE.FOREIGN_KEY_MV) {
        if (val) {
          const relaTableName = this.camelToSnake(
            this.formModelProp.model.relaModelCode.replace('DTO', '')
          )
          if (this.formModelProp.model.propType === PROP_TYPE.FOREIGN_KEY_MV) {
            if (!this.formModelProp.model.configJson.mvTableName) {
              this.formModelProp.model.configJson.mvTableName =
                this.formModelProp.tableName + '_' + relaTableName + '_mv'
            }
            if (!this.formModelProp.model.configJson.mvTableForeignKeyName) {
              this.formModelProp.model.configJson.mvTableForeignKeyName =
                this.formModelProp.tableName + '_id'
            }
            if (!this.formModelProp.model.configJson.mvTableValueColName) {
              this.formModelProp.model.configJson.mvTableValueColName =
                relaTableName + '_id'
            }
          }
        }
      }
      if (this.formModelProp.model.propType === PROP_TYPE.MODEL) {
        // 重置数据
        this.formModelProp.model = {
          ...this.formModelProp.model,
          propRelas: [],
        }
        if (val) {
          this.loadSubModelPropsData(val, 2)
        }
      }
    },
    filterPropRelas() {
      // 把额外添加的关联属性添加到propRelas中
      if (this.addedPropRelas && this.addedPropRelas.length > 0) {
        this.formModelProp.model.propRelas =
          this.formModelProp.model.propRelas.concat(this.addedPropRelas)
      }
      // 如果没有配置parentName就抛弃
      const tempPropRelas = []
      for (
        let index = 0;
        index < this.formModelProp.model.propRelas.length;
        index++
      ) {
        const prop = this.formModelProp.model.propRelas[index]
        if (prop.parentName || prop.relaValue) {
          tempPropRelas.push(prop)
        }
      }
      this.formModelProp.model.propRelas = tempPropRelas
    },
    // 保存模型属性信息
    handleSave(resolve, reject) {
      this.$refs.formModelPropRef.validate((valid) => {
        if (valid) {
          if (
            this.addedPropRelas &&
            this.formModelProp.model.propRelas &&
            this.formModelProp.model.propRelas.length > 0
          ) {
            this.filterPropRelas()
          }
          // 外键多值只能是多值表模式
          if (this.formModelProp.model.propType === PROP_TYPE.FOREIGN_KEY_MV) {
            this.formModelProp.model.configJson.mvStoreType =
              MV_STORE_TYPE.MV_TABLE
          }
          const data = {
            id: this.formModelProp.model.id,
            name: this.formModelProp.model.name,
            colName: this.formModelProp.model.colName,
            type: this.formModelProp.model.propType,
            componentType: this.formModelProp.model.componentType,
            relaModelCode: this.formModelProp.model.relaModelCode,
            relaModelName: '',
            enumCatalog: this.formModelProp.model.enumCatalog,
            enumType: this.formModelProp.model.enumType,
            format: this.formModelProp.model.format,
            propRelas: this.formModelProp.model.propRelas,
            validateJson: JSON.stringify(this.formModelProp.model.validateJson),
            configJson: JSON.stringify(this.formModelProp.model.configJson),
            comments: this.formModelProp.model.comments,
          }
          // 获取模型名称和编码
          const relaModel = this.formModelProp.modelList.find(
            (item) => item.code === this.formModelProp.model.relaModelCode
          )
          if (relaModel) {
            data.relaModelName = relaModel.name
          }
          this.$emit('afterSave', data)
          this.handleClose()
          this.addedPropRelas = []
          this.subModelSelectablePropOptions = []
        } else {
          reject()
        }
      })
    },
    // 关闭弹框并清空重置数据
    handleClose() {
      this.diagModelPropEdit.display = false
      this.$refs.formModelPropRef.clearValidate()
      // 重置数据
      this.formModelProp.model = this.$options.data().formModelProp.model
    },
    validateCustomConfig(rule, value, callback) {
      if (value) {
        if (value.length > 500) {
          callback(new Error('属性处理器参数不能超过500个字符'))
        } else {
          try {
            JSON.parse(value)
            callback()
          } catch (error) {
            console.warn(
              '属性处理器参数格式应该为JSON格式 JSON.parse 异常',
              error
            )
            callback(new Error('属性处理器参数格式应该为JSON格式'))
          }
        }
      }
    },
    validateMultiName(rule, value, callback) {
      if (value) {
        const index = this.propList.findIndex(
          (item) => item.name === this.propName
        )
        this.propList.forEach((item, key) => {
          if (item.name === value) {
            const isRepeat = this.propName && key !== index
            if (isRepeat || !this.propName) {
              // 新增时验证名称重复
              callback(new Error(rule.message))
            }
          }
        })
      }
      callback()
    },
    // 验证物理字段名不重复
    validateMultiColName(rule, value, callback) {
      if (value) {
        const index = this.propList.findIndex(
          (item) => item.name === this.propName
        )
        this.propList.forEach((item, key) => {
          if (item.colName === value) {
            const isRepeat = this.propName && key !== index
            if (isRepeat || !this.propName) {
              // 编辑时验证名称重复
              callback(new Error(rule.message))
            }
          }
        })
      }
      callback()
    },
    validateName(rule, value, callback) {
      const reg = /^[a-z][a-zA-Z0-9]*$/g
      if (value && !reg.test(value)) {
        callback(new Error('以小写字母开头,由字母、数字组成'))
      }
      callback()
    },
    validateColName(rule, value, callback) {
      const reg = /^[a-zA-Z]\w*[a-zA-Z0-9]$/
      if (value && !reg.test(value)) {
        callback(
          new Error('以字母开头,字母或数字结尾，由字母、数字、下划线组成')
        )
      }
      callback()
    },
    validateOnlyKey(rule, value, callback) {
      const key = PROP_TYPE.KEY
      if (value === key) {
        const index = this.propList.findIndex(
          (item) => item.name === this.propName
        )
        const modelPropIndex = this.propList.findIndex(
          (item) => item.type === key
        )
        if (modelPropIndex !== -1) {
          const isRepeat = this.propName && index !== modelPropIndex
          if (isRepeat || !this.propName) {
            // 编辑时验证重复
            callback(new Error(rule.message))
          }
        }
      }
      callback()
    },
    validateCurrentModelProp(rule, value, callback) {
      const relaModelProps = this.formModelProp.model.propRelas
      // 一个子数据模型中的外键不一定都是父数据模型的
      let indexRela = 0
      let indexValue = 0
      relaModelProps.forEach((item) => {
        // 类型为关键键且未配置对应属性
        if (!item.parentName && item.type === RELA_TYPE.RELAKEY) {
          indexRela++
        }
        // 类型为固定值且未配置对应属性
        if (!item.relaValue && item.type === RELA_TYPE.VALUE) {
          indexValue++
        }
      })
      if (indexRela > 0) {
        rule.message = '请选择关联属性'
        callback(new Error(rule.message))
      }
      if (indexValue > 0) {
        rule.message = '请输入固定值'
        callback(new Error(rule.message))
      }
      // 如果前一个增加的属性还未选择childName就不能继续添加
      if (this.addedPropRelas.length > 0) {
        for (
          let addedIndex = 0;
          addedIndex < this.addedPropRelas.length;
          addedIndex++
        ) {
          const prop = this.addedPropRelas[addedIndex]
          if (!prop.childName) {
            this.$message.info('请先给已添加的关联属性选择子数据模型字段')
            callback(new Error())
          }
          if (!prop.parentName && !prop.relaValue) {
            if (prop.type === RELA_TYPE.RELAKEY) {
              rule.message = '请选择关联属性'
            } else if (prop.type === RELA_TYPE.VALUE) {
              rule.message = '请输入固定值'
            }
            callback(new Error(rule.message))
          }
        }
      }
      // 已添加属性允许为空
      callback()
    },
    validateMaxNum(rule, value, callback) {
      if (/^[-]?([0-9]\d*)(\.\d+)?$/.test(value)) {
        const maxNum = parseFloat(value)
        if (
          this.formModelProp.model.validateJson.validateMinNum &&
          maxNum <= this.formModelProp.model.validateJson.minNum
        ) {
          callback(new Error('最大值必须大于最小值'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入数字'))
      }
    },
    validateMinNum(rule, value, callback) {
      if (/^[-]?([0-9]\d*)(\.\d+)?$/.test(value)) {
        const minNum = parseFloat(value)
        if (
          this.formModelProp.model.validateJson.validateMaxNum &&
          minNum >= this.formModelProp.model.validateJson.maxNum
        ) {
          callback(new Error('最小值必须小于最大值'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入数字'))
      }
    },
    handleAddPropBtnClick() {
      // 先校验是否配置好外键的关联属性
      this.$refs.formModelPropRef.validate((valid) => {
        if (valid) {
          if (this.subModelSelectablePropOptions.length < 1) {
            this.$message.info('请重新选择关联模型')
            return
          }
          /**
           * 获取关联模型对应的关联属性信息
           * 这里一开始限定了关联类型只能为固定值(考虑到除了外键,父子数据模型不应该有其他字段关联)
           * 但是后来放开了,为了处理动态产品查询立项数据还是最新数据这种情形
           */
          this.addedPropRelas.push({
            type: RELA_TYPE.VALUE,
            parentName: '',
            relaValue: '',
            relaValueType: RELA_VALUE_TYPE.STRING,
          })
        }
      })
    },
    handleAddedPropOptionsRemoveBtnClick(index) {
      this.addedPropRelas.splice(index, 1)
    },
    handleRelaModelRemoveBtnClick(index) {
      this.formModelProp.model.propRelas.splice(index, 1)
    },
    clearColName(resolve) {
      this.formModelProp.model.colName = ''
      resolve()
    },
    generatePropColName(resolve, reject) {
      this.$refs.formModelPropRef.validateField('name', (errorMessage) => {
        if (errorMessage) {
          console.warn('校验属性名失败', errorMessage)
          reject()
        } else {
          const propName = this.formModelProp.model.name
          const result = makePropName2ColName(propName)
          if (result) {
            this.formModelProp.model.colName = result
            resolve()
          } else {
            this.$message.info(`根据属性名${propName}生成的物理字段名为空`)
            reject()
          }
        }
      })
    },
  },
}
</script>
<style scoped>
.relaModel {
  margin-bottom: 5px;
}
.modelName {
  width: 180px;
}
.propType {
  width: 110px;
  margin-left: 2px;
}
.modelProp {
  width: 185px;
  margin-left: 2px;
}
.prop-col-name {
  width: calc(100% - 195px) !important;
  padding-right: 10px;
}
.formWidth {
  width: calc(100% - 85px) !important;
}
.helpTooltip {
  margin-left: 5px;
  color: #409eff;
}
.refresh-icon {
  cursor: pointer;
  padding-left: 5px;
}
</style>
