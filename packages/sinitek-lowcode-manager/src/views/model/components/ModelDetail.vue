<template>
  <div class="header-search">
    <el-form
      id="formDataModelDetail"
      :model="formDataModelDetail.model"
      label-width="100px"
      label-suffix="："
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="编码">
            {{ formDataModelDetail.model.code }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="名称"
            style="
              width: 400px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            "
          >
            <span :title="formDataModelDetail.model.name">{{
              formDataModelDetail.model.name
            }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="物理表名">
            {{ formDataModelDetail.model.tableName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="使用范围">
            {{ formDataModelDetail.model.useRange === 1 ? '私有' : '公开' }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="类型">
            {{ formDataModelDetail.model.modelTypeLabelName
            }}<span v-if="embeddedModelName"
              >(启用{{ embeddedModelName }})</span
            >
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="删除方式">
            {{
              formDataModelDetail.model.deleteMode === DELETE_MODE.LOGICAL
                ? '逻辑删除'
                : '物理删除'
            }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="配置">
            <ConfigJsonDlg
              v-if="formDataModelDetail.model.configJson"
              :edit-data="formDataModelDetail.model.configJson"
              save-btn-name="确定"
            >
              <template #default="{ data }">
                <xn-form
                  :model="data"
                  ref="configJsonForm"
                  show-ellipsis
                  class="config-json-form"
                  label-width="150px"
                >
                  <xn-form-item label="主键策略">
                    <span>{{ getIdTypeName(data.idType) }}</span>
                  </xn-form-item>
                  <el-form-item label="启用数据审计">
                    <el-switch v-model="data.enableDataAudit" disabled />
                  </el-form-item>
                  <!-- 唯一校验 -->
                  <el-form-item label="唯一校验">
                    <el-switch v-model="data.unique" disabled />
                  </el-form-item>
                  <!-- 唯一校验关联字段 -->
                  <el-form-item
                    label="关联字段"
                    prop="uniquePropNames"
                    v-if="data.unique"
                  >
                    <span>{{ showUniquePropNames(data.uniquePropNames) }}</span>
                  </el-form-item>
                  <!-- 唯一校验提示信息 -->
                  <el-form-item label="唯一校验提示信息" v-if="data.unique">
                    <span>{{ data.uniqueValidateMsg }}</span>
                  </el-form-item>
                  <xn-form-item
                    label="最大深度"
                    v-if="
                      formDataModelDetail.model.modelType === MODEL_TYPE.TREE
                    "
                  >
                    <span>{{ data.maximumDepth }}</span>
                    <el-tooltip
                      class="helpTooltip"
                      effect="light"
                      content="超过该深度,树无法保存或移动;0代表不生效"
                      placement="top"
                    >
                      <i class="el-icon-question" />
                    </el-tooltip>
                  </xn-form-item>
                </xn-form>
              </template>
            </ConfigJsonDlg>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="其他配置">
            <ConfigJsonDlg
              :edit-data="formDataModelDetail.model"
              save-btn-name="确定"
            >
              <template #default="{ data }">
                <xn-form show-ellipsis label-width="150px" class="other-config">
                  <xn-form-item label="操作拦截器">
                    <TextCopy :text="data.opInterceptor"> </TextCopy>
                  </xn-form-item>
                  <xn-form-item
                    label="自定义属性处理器"
                    v-if="data.modelType === MODEL_TYPE.CUSTOM"
                  >
                    <TextCopy :text="data.customHandler"> </TextCopy>
                  </xn-form-item>
                </xn-form>
              </template>
            </ConfigJsonDlg>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="模型属性" style="width: 95%">
        <xn-table
          id="tblModelProp"
          :data="tblModelProp.propList"
          :toolbars="tblModelProp.buttons"
          :allow-init="false"
          :show-pagination="false"
          @generatorCode="handleGeneratorCode"
        >
          <xn-col
            prop="name"
            label="属性名"
            align="left"
            show-overflow-tooltip
          />
          <xn-col
            prop="colName"
            label="物理字段名"
            align="left"
            show-overflow-tooltip
          />
          <xn-col
            prop="comments"
            label="属性显示名"
            align="left"
            show-overflow-tooltip
          />
          <xn-col
            prop="modelTypeLabel"
            label="类型"
            align="center"
            show-overflow-tooltip
          />
          <xn-col
            label="组件类型"
            prop="componentTypeLabel"
            align="center"
            show-overflow-tooltip
          />
        </xn-table>
      </el-form-item>
    </el-form>
    <model-generator-code
      :model-code="realDataModelCode"
      :model-id="formDataModelDetail.model.id"
      ref="modelGeneratorCodeRef"
    />
  </div>
</template>

<script>
import {
  ID_TYPE_OPTIONS,
  MODEL_TYPE,
  DELETE_MODE,
  MODEL_TYPE_AND_OPTION_MAP,
} from 'src/constant'
import { DataModelAPI } from 'src/api'
import { formatDataModel } from 'src/utils'

export default {
  name: 'ModelDetail',
  props: {
    id: {
      type: [String, Number],
      default: 0,
    },
  },
  components: {
    ModelGeneratorCode: () => import('./ModelGeneratorCode'),
    ConfigJsonDlg: () => import('./ConfigJsonDlg'),
    TextCopy: () => import('src/views/components/textCopy'),
  },
  watch: {
    id() {
      this.loadDataModel()
    },
  },
  data() {
    return {
      DELETE_MODE: Object.freeze(DELETE_MODE),
      MODEL_TYPE: Object.freeze(MODEL_TYPE),
      formDataModelDetail: {
        model: {
          name: '',
          code: '',
          dataSource: '', // 数据源编码
          useRange: 1,
          modelType: MODEL_TYPE.COMMON, // 类型
          modelTypeLabelName: '',
          datasource: '',
          tableName: '',
          deleteMode: 0,
          embeddedModel: '',
          configJson: {
            // 树模型支持的最大深度,0代表不生效
            maximumDepth: 0,
          },
          props: [],
        },
      },
      tblModelProp: {
        propList: [],
        buttons: [
          {
            label: '模型驱动代码生成',
            type: 'generatorCode',
            event: 'generatorCode',
          },
        ],
      },
    }
  },
  methods: {
    showUniquePropNames(array) {
      if (Array.isArray(array)) {
        return this.tblModelProp.propList
          .map((item) => {
            if (array.includes(item.name)) {
              return item.comments
                ? `${item.name}(${item.comments})`
                : item.name
            } else {
              return null
            }
          })
          .filter((item) => !!item)
          .join(',')
      } else {
        return ''
      }
    },
    loadDataModel() {
      const para = {
        id: this.id,
      }
      this.$http.get(DataModelAPI.MODEL_DETAIL(), para).then((result) => {
        this.formDataModelDetail.model = result.data
        if (this.formDataModelDetail.model.configJson) {
          this.formDataModelDetail.model.configJson = JSON.parse(
            this.formDataModelDetail.model.configJson
          )
        } else {
          this.formDataModelDetail.model.configJson =
            this.$options.data().formDataModelDetail.model.configJson
        }
        // 显示类型 label值
        const modelTypeData =
          MODEL_TYPE_AND_OPTION_MAP[this.formDataModelDetail.model.modelType]
        this.formDataModelDetail.model.modelTypeLabelName = modelTypeData.label

        // 设置列表展示的字段
        this.tblModelProp.propList = []
        result.data.props.forEach((item) => {
          const data = formatDataModel(item)
          item = {
            ...item,
            ...data,
          }
          this.tblModelProp.propList.push(item)
        })
      })
    },
    handleGeneratorCode() {
      if (
        !this.formDataModelDetail.model ||
        !this.formDataModelDetail.model.id
      ) {
        return
      }
      const data = {
        modelId: this.formDataModelDetail.model.id,
      }
      this.$refs.modelGeneratorCodeRef.show(data, false)
    },
  },
  mounted() {
    this.loadDataModel()
  },
  computed: {
    embeddedModelName() {
      if (this.formDataModelDetail.model.embeddedModel) {
        switch (this.formDataModelDetail.model.embeddedModel) {
          case MODEL_TYPE.DATATRACE:
            return '留痕'
          case MODEL_TYPE.VERSION:
            return '版本'
        }
      }
      return false
    },
    getIdTypeName() {
      return function (value) {
        const idTypeEunm = ID_TYPE_OPTIONS.find((item) => item.value === value)
        // 没有找到对应枚举值，就返回空字符串
        return (idTypeEunm && idTypeEunm.label) || ''
      }
    },
    realDataModelCode() {
      if (this.isMultiAppMode) {
        return this.appCode + '-' + this.formDataModelDetail.model.code
      } else {
        return this.formDataModelDetail.model.code
      }
    },
  },
}
</script>
<style scoped lang="scss">
.header-search {
  padding: 10px 0px;
}
.config-json-form {
  width: 90%;
}
.other-config {
  width: 90%;
}
</style>
