<template>
  <div>
    <xn-button
      type="primary"
      plain
      circle
      size="mini"
      :show-loading="false"
      icon="el-icon-setting"
      @click="openDlg"
    />
    <xn-dialog
      id="dlg"
      :title="dlgTitle"
      :show.sync="isShow"
      :buttons="buttons"
      @cancel="closeDlg"
      @save="handleDlgSaveData"
    >
      <template slot="dialog-form">
        <slot v-if="data !== null" :data="data" />
      </template>
    </xn-dialog>
  </div>
</template>

<script>
import { deepCopy } from 'src/utils'
export default {
  name: 'ConfigJsonDlg',
  props: {
    dlgTitle: {
      type: String,
      default: '配置',
    },
    saveBtnName: {
      type: String,
      default: '保存',
    },
    editData: {
      type: [Array, Object],
    },
    validate: {
      type: Function,
      default: undefined,
    },
  },
  data() {
    return {
      isShow: false,
      buttons: [
        {
          label: this.saveBtnName,
          type: 'primary',
          action: 'save',
          event: 'save',
        },
        {
          label: '取消',
          type: 'info',
          action: 'cancel',
          event: 'cancel',
          showLoading: false,
        },
      ],
      // 初始值来源于props.data
      data: null,
    }
  },
  methods: {
    resetData() {
      this.data = deepCopy(this.editData)
    },
    openDlg() {
      this.data = deepCopy(this.editData)
      this.isShow = true
    },
    closeDlg() {
      this.data = null
      this.isShow = false
    },
    handleDlgSaveData(resolve, reject) {
      if (typeof this.validate === 'function') {
        this.validate()
          .then(() => {
            this.$emit('updateEditData', this.data)
            this.closeDlg()
            resolve()
          })
          .catch((error) => {
            console.warn('校验不通过', error)
            reject()
          })
      } else {
        this.$emit('updateEditData', this.data)
        this.closeDlg()
        resolve()
      }
    },
  },
}
</script>

<style></style>
