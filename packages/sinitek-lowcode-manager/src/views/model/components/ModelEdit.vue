<template>
  <div class="app-container">
    <el-form
      id="formDataModel"
      ref="formDataModelRef"
      class="blank-container"
      :model="formDataModel.model"
      :rules="formDataModel.rules"
      label-width="120px"
      label-suffix="："
    >
      <el-row>
        <el-col :span="leftSpan">
          <el-form-item label="编码" prop="code">
            <span v-if="!!formDataModel.model.id">{{
              formDataModel.model.code
            }}</span>
            <el-input
              v-else
              v-model="formDataModel.model.code"
              class="inputWidth"
              :maxlength="30 - initCodeLen"
            >
              <span v-if="isMultiAppMode" slot="prepend"> {{ appCode }}- </span>
              <div
                slot="suffix"
                style="display: flex; align-items: center; height: 100%"
              >
                <span
                  >{{ formDataModel.model.code.length + initCodeLen }}/30</span
                >
              </div>
            </el-input>
            <el-tooltip
              class="helpTooltip"
              effect="light"
              content="用于导入导出以及关联的唯一标识"
              placement="top"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="rightSpan">
          <el-form-item label="名称" prop="name">
            <el-input
              v-model.trim="formDataModel.model.name"
              :maxlength="100"
              :show-word-limit="true"
              class="inputWidth"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="leftSpan">
          <el-form-item label="物理表名" prop="tableName">
            <el-input
              v-model.trim="formDataModel.model.tableName"
              :maxlength="50"
              :show-word-limit="true"
              class="inputWidth"
            />
            <el-button
              type="primary"
              size="small"
              style="margin-left: 10px"
              @click="autoAnalyse"
            >
              解析
            </el-button>
          </el-form-item>
        </el-col>
        <el-col :span="rightSpan">
          <el-form-item label="使用范围" prop="useRange">
            <el-radio-group v-model="formDataModel.model.useRange">
              <el-radio
                v-for="item in USE_RANGE_OPTIONS"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="leftSpan">
          <el-form-item label="类型" prop="modelType">
            <el-select
              v-model="formDataModel.model.modelType"
              style="margin-right: 15px; width: 208px"
              :popper-append-to-body="false"
              @change="handleModelTypeChange"
            >
              <xn-option
                v-for="item in MODEL_TYPE_OPTIONS"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              />
            </el-select>
            <template
              v-if="formDataModel.model.modelType === MODEL_TYPE.WORKFLOW"
            >
              <template>
                <span style="margin-right: 5px">启用留痕:</span>
                <el-switch
                  v-model="enableDataTrace"
                  :disabled="enableVersion"
                  style="margin-right: 15px"
                  @change="handleEmbeddedModelSwitchChange"
                />
              </template>
              <template>
                <span style="margin-right: 5px">启用版本:</span>
                <el-switch
                  v-model="enableVersion"
                  :disabled="enableDataTrace"
                  @change="handleEmbeddedModelSwitchChange"
                />
              </template>
            </template>
          </el-form-item>
        </el-col>
        <el-col
          v-if="formDataModel.model.modelType !== MODEL_TYPE.CUSTOM"
          :span="rightSpan"
        >
          <el-form-item label="删除方式" prop="deleteMode">
            <el-radio-group
              v-model="formDataModel.model.deleteMode"
              @change="handleChangeDeleteMode"
            >
              <el-radio
                v-for="item in DELETE_MODE_OPTIONS"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="leftSpan">
          <el-form-item label="配置">
            <ConfigJsonDlg
              v-if="formDataModel.model.configJson"
              :edit-data="formDataModel.model.configJson"
              :validate="validateModelConfig"
              @updateEditData="handleUpdateConfigJson"
            >
              <template #default="{ data }">
                <xn-form
                  ref="configJsonForm"
                  :model="data"
                  show-ellipsis
                  class="config-json-form"
                  label-width="150px"
                  :rules="modelConfigRules"
                >
                  <xn-form-item label="主键策略">
                    <xn-select
                      v-model="data.idType"
                      :options="ID_TYPE_OPTIONS"
                      clearable
                      sorted
                    />
                  </xn-form-item>
                  <el-form-item label="启用数据审计">
                    <el-switch v-model="data.enableDataAudit" />
                  </el-form-item>
                  <!-- 唯一校验 -->
                  <el-form-item label="唯一校验">
                    <el-switch v-model="data.unique" />
                  </el-form-item>
                  <!-- 唯一校验关联字段 -->
                  <el-form-item
                    v-if="data.unique"
                    label="关联字段"
                    prop="uniquePropNames"
                  >
                    <el-select
                      v-model="data.uniquePropNames"
                      placeholder="请选择"
                      clearable
                      multiple
                      collapse-tags
                      :popper-append-to-body="true"
                      @change="handleUniquePropNamesChange"
                    >
                      <el-option
                        v-for="item in uniqueOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                  <!-- 唯一校验提示信息 -->
                  <el-form-item v-if="data.unique" label="唯一校验提示信息">
                    <el-input
                      v-model.trim="data.uniqueValidateMsg"
                      class="formWidth"
                      maxlength="50"
                      :show-word-limit="true"
                    />
                  </el-form-item>
                  <xn-form-item
                    v-if="formDataModel.model.modelType === MODEL_TYPE.TREE"
                    label="最大深度"
                  >
                    <el-input-number
                      v-model="data.maximumDepth"
                      :min="0"
                      :max="100"
                      :step="1"
                    />
                    <el-tooltip
                      class="helpTooltip"
                      effect="light"
                      content="超过该深度,树无法保存或移动;0代表不生效"
                      placement="top"
                    >
                      <i class="el-icon-question" />
                    </el-tooltip>
                  </xn-form-item>
                </xn-form>
              </template>
            </ConfigJsonDlg>
          </el-form-item>
        </el-col>
        <el-col :span="leftSpan">
          <el-form-item label="其他配置">
            <ConfigJsonDlg
              ref="optherConfig"
              :edit-data="formDataModel.model"
              save-btn-name="确定"
            >
              <template #default="{ data }">
                <xn-form show-ellipsis label-width="150px" class="other-config">
                  <xn-form-item label="操作拦截器">
                    <TextCopy :text="data.opInterceptor">
                      <i
                        class="el-icon-refresh refresh-icon"
                        @click="handleRefreshOpInterceptorIconClick"
                      />
                    </TextCopy>
                  </xn-form-item>
                  <xn-form-item
                    v-if="data.modelType === MODEL_TYPE.CUSTOM"
                    label="自定义属性处理器"
                  >
                    <TextCopy :text="data.customHandler">
                      <i
                        class="el-icon-refresh refresh-icon"
                        @click="handleRefreshCustomHandlerIconClick"
                      />
                    </TextCopy>
                  </xn-form-item>
                </xn-form>
              </template>
            </ConfigJsonDlg>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="模型属性" style="width: 95%">
        <xn-table
          id="tblModelProp"
          ref="tblModelPropRef"
          :data="tblModelProp.propList"
          :toolbars="tblModelProp.buttons"
          max-height="400px"
          :allow-init="false"
          class="modeltable"
          :show-pagination="false"
          @add="handlePropsAdd"
          @delete="handlePropsDelete"
          @generatorCode="handleGeneratorCode"
        >
          <xn-col
            type="selection"
            :selectable="canEditProp"
            prop="id"
            width="40"
          />
          <xn-col
            label="顺序"
            prop="id"
            type="draggable"
            width="50"
            align="center"
          />
          <xn-col
            prop="name"
            label="属性名"
            align="left"
            show-overflow-tooltip
          />
          <xn-col
            prop="colName"
            label="物理字段名"
            align="left"
            show-overflow-tooltip
          />
          <xn-col
            prop="comments"
            label="属性显示名"
            align="left"
            show-overflow-tooltip
          />
          <xn-col
            prop="modelTypeLabel"
            label="类型"
            align="center"
            show-overflow-tooltip
          />
          <xn-col
            label="组件类型"
            prop="componentTypeLabel"
            width="120"
            align="center"
            show-overflow-tooltip
          />
          <xn-col label="操作" align="center" fixed="right">
            <template slot-scope="scope">
              <xn-col-action-group
                v-if="canEditProp(scope.row)"
                :keys="scope.row"
              >
                <xn-col-action @click="handlePropsEdit(scope.row)">
                  编辑
                </xn-col-action>
                <xn-col-action @click="handlePropsDelete(scope.row)">
                  删除
                </xn-col-action>
              </xn-col-action-group>
            </template>
          </xn-col>
        </xn-table>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          size="small"
          :loading="btnLoading"
          @click="handleSave"
        >
          保存
        </el-button>
        <el-button size="small" @click="handleClose"> 取消 </el-button>
      </el-form-item>
    </el-form>
    <model-prop-edit
      ref="modelPropEditRef"
      :model-code="realDataModelCode"
      :module-code="formDataModel.model.moduleCode"
      :model-id="formDataModel.model.id"
      :title="tblModelProp.title"
      :prop-list.sync="tblModelProp.propList"
      :prop-name="tblModelProp.name"
      :filter-prop-list="canEditProp"
      @afterSave="handleModelPropFormSave"
    />
    <model-generator-code
      ref="modelGeneratorCodeRef"
      :model-code="realDataModelCode"
      :model-id="formDataModel.model.id"
    />
  </div>
</template>

<script>
import { DataModelAPI, DataSetAPI } from 'src/api'
import {
  MODEL_TYPE,
  PROP_TYPE,
  DEFAULT_DATE_FORMAT,
  LATEST_FLAG_COL,
  VERSION_NO_LATEST_FLAG,
  ID_TYPE_OPTIONS,
  COMPONENT_TYPE,
  DELETE_MODE_OPTIONS,
  DELETE_MODE,
  COMMON_BOOLEAN,
  COMMON_BOOLEAN_NAME,
  USE_RANGE_OPTIONS,
  USE_RANGE,
  PROP_TYPE_MAP,
  REMOVE_FLAG_PROP,
  LOGIC_DELETE_PROP,
  MODEL_TYPE_AND_PROP_MAP,
  COMPONENT_TYPE_AND_OPTION_MAP,
  MODEL_TYPE_OPTIONS,
  PROP_TYPE_AND_COMPONENT_OPTIONS_MAP,
} from 'src/constant'
import {
  validateCode,
  handleErrorMessage,
  isFramworkBaseColName,
  getPropTypeByDBColType,
  makeColName2PropName,
  formatDataModel,
  isFrameworkPropType,
} from 'src/utils'
import { isMultiAppMode } from 'src/config'

export default {
  name: 'LcModelEdit',
  components: {
    ModelGeneratorCode: () => import('./ModelGeneratorCode'),
    ModelPropEdit: () => import('./ModelPropEdit'),
    ConfigJsonDlg: () => import('./ConfigJsonDlg'),
    TextCopy: () => import('src/views/components/textCopy'),
  },
  data: function () {
    return {
      MODEL_TYPE_OPTIONS: Object.freeze(MODEL_TYPE_OPTIONS),
      USE_RANGE_OPTIONS: Object.freeze(USE_RANGE_OPTIONS),
      DELETE_MODE_OPTIONS: Object.freeze(DELETE_MODE_OPTIONS),
      MODEL_TYPE: Object.freeze(MODEL_TYPE),
      PROP_TYPE: Object.freeze(PROP_TYPE),
      COMMON_BOOLEAN: Object.freeze(COMMON_BOOLEAN),
      COMMON_BOOLEAN_NAME: Object.freeze(COMMON_BOOLEAN_NAME),
      tblModelProp: {
        propList: [],
        buttons: [
          { label: '新增', type: 'add', event: 'add' },
          { label: '删除', type: 'delete', event: 'delete' },
          {
            label: '模型代码生成',
            type: 'generatorCode',
            event: 'generatorCode',
          },
        ],
        title: '新增属性',
        name: '', // 属性名称，用于标识新增/编辑的属性, 新增时：name为空
      },
      formDataModel: {
        model: {
          moduleCode: '',
          appCode: '',
          id: '',
          code: '',
          name: '',
          type: '',
          dataSource: '', // 数据源编码
          useRange: USE_RANGE.PUBLIC,
          modelType: '', // 类型
          datasource: '',
          tableName: '',
          deleteMode: DELETE_MODE.PHYSICAL,
          embeddedModel: '',
          configJson: {
            // 树模型支持的最大深度,0代表不生效
            maximumDepth: 0,
          },
          // 操作拦截器
          opInterceptor: '',
          // 自定义模型处理器
          customHandler: '',
          props: [],
        },
        rules: {
          code: [
            { required: true, trigger: 'blur', message: '请填写模型编码' },
            {
              validator: validateCode,
              trigger: ['change', 'blur'],
              message:
                '模型编码必须以字母开头，由英文字母、数字、下划线（_）、连字符（-）组成',
            },
          ],
          name: [
            { required: true, trigger: 'blur', message: '请填写模型名称' },
          ],
          modelType: [
            {
              required: true,
              trigger: ['change', 'blur'],
              message: '请选择类型',
            },
          ],
          tableName: [
            { required: true, trigger: 'blur', message: '请填写物理表名' },
          ],
        },
      },
      // 模型配置校验规则
      modelConfigRules: {
        uniquePropNames: [
          { required: true, trigger: 'blur', message: '请选择关联字段' },
        ],
      },
      enableDataTrace: false,
      enableVersion: false,
      btnLoading: false,
      appCode: '',
      isMultiAppMode: isMultiAppMode(),
      initCodeLen: 0,
      ID_TYPE_OPTIONS: Object.freeze(ID_TYPE_OPTIONS),
    }
  },
  computed: {
    // 返回可以作为唯一关联的字段(非框架字段,非同字段)
    uniqueOptions() {
      return this.tblModelProp.propList
        .filter((item) => item.colName && !isFrameworkPropType(item.type))
        .map((item) => {
          return {
            label: item.comments ? `${item.name}(${item.comments})` : item.name,
            value: item.name,
          }
        })
    },
    leftSpan() {
      return this.formDataModel.model.modelType === MODEL_TYPE.WORKFLOW ? 12 : 8
    },
    rightSpan() {
      return this.formDataModel.model.modelType === MODEL_TYPE.WORKFLOW
        ? 12
        : 16
    },
    realDataModelCode() {
      if (this.isMultiAppMode) {
        return this.appCode + '-' + this.formDataModel.model.code
      } else {
        return this.formDataModel.model.code
      }
    },
  },
  watch: {
    'formDataModel.model.embeddedModel': {
      immediate: true,
      handler() {
        if (this.formDataModel.model.embeddedModel === MODEL_TYPE.DATATRACE) {
          this.enableDataTrace = true
        }
        if (this.formDataModel.model.embeddedModel === MODEL_TYPE.VERSION) {
          this.enableVersion = true
        }
      },
    },
  },
  created() {
    if (this.$route.query) {
      if (this.$route.query.id) {
        this.$http
          .get(DataModelAPI.MODEL_DETAIL(), { id: this.$route.query.id })
          .then((result) => {
            this.formDataModel.model = result.data
            if (this.formDataModel.model.configJson) {
              this.formDataModel.model.configJson = JSON.parse(
                this.formDataModel.model.configJson
              )
            } else {
              this.formDataModel.model.configJson =
                this.$options.data().formDataModel.model.configJson
            }
            this.tblModelProp.propList = []
            // 格式化表格中的属性信息
            result.data.props.forEach((item) => {
              const data = formatDataModel(item)
              item = {
                ...item,
                ...data,
              }
              this.tblModelProp.propList.push(item)
            })
            // 拆分code
            if (this.isMultiAppMode) {
              if (this.formDataModel.model?.code && this.appCode) {
                if (this.formDataModel.model.code.startsWith(this.appCode)) {
                  this.formDataModel.model.code =
                    this.formDataModel.model.code.slice(this.appCode.length + 1)
                }
              } else {
                this.formDataModel.model.code = ''
              }
            }
          })
      } else if (this.$route.query.moduleCode) {
        this.formDataModel.model.moduleCode = this.$route.query.moduleCode
      } else {
        this.$message.info('缺少模块编码,无法保存')
      }
      if (this.$route.query.appCode) {
        this.appCode = this.$route.query.appCode
        this.initCodeLen = this.appCode.length + 1
        this.formDataModel.model.appCode = this.$route.query.appCode
      }
    }
  },
  methods: {
    handleUniquePropNamesChange() {
      this.$refs.configJsonForm.validateField('uniquePropNames')
    },
    validateModelConfig() {
      const that = this
      return new Promise(function (resolve, reject) {
        that.$refs.configJsonForm.validate((valid) => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    // 获取模型拦截器全路径
    handleRefreshOpInterceptorIconClick() {
      if (this.formDataModel.model.code) {
        this.$http
          .get(DataModelAPI.GET_MODEL_OP_INTERCEPTOR_CANONICAL_NAME(), {
            modelCode: this.formDataModel.model.code,
          })
          .then((res) => {
            if (res.data) {
              this.formDataModel.model.opInterceptor = res.data
              this.$refs.optherConfig.resetData(this.formDataModel.model)
              this.$message.success('刷新成功')
            } else {
              this.$message.info('当前模型不存在模型拦截器')
            }
          })
          .catch((error) => {
            console.error('获取模型拦截器全路径报错', error)
            this.$message.info('获取模型拦截器全路径报错')
          })
      } else {
        this.$message.info('编码不能为空')
      }
    },
    // 获取自定义模型处理器全路径
    handleRefreshCustomHandlerIconClick() {
      if (this.formDataModel.model.code) {
        this.$http
          .get(DataModelAPI.GET_CUSTOM_MODEL_HANDLER_CANONICAL_NAME(), {
            modelCode: this.formDataModel.model.code,
          })
          .then((res) => {
            if (res.data) {
              this.formDataModel.model.customHandler = res.data
              this.$refs.optherConfig.resetData(this.formDataModel.model)
              this.$message.success('刷新成功')
            } else {
              this.$message.info('当前模型不存在自定义模型处理器')
            }
          })
          .catch((error) => {
            console.error('获取自定义模型处理器全路径报错', error)
            this.$message.info('获取自定义模型处理器全路径报错')
          })
      } else {
        this.$message.info('编码不能为空')
      }
    },
    // 根据当前模型所属的类型以及删除方式获取其所能拥有的数据模型属性
    // {colName:{prop}}
    getCurrentModelPropMap() {
      let modelPropData =
        MODEL_TYPE_AND_PROP_MAP[this.formDataModel.model.modelType]
      if (this.formDataModel.model.modelType === MODEL_TYPE.VERSION) {
        modelPropData = modelPropData.concat(
          MODEL_TYPE_AND_PROP_MAP[MODEL_TYPE.DATATRACE]
        )
      } else if (this.formDataModel.model.modelType === MODEL_TYPE.WORKFLOW) {
        if (
          this.formDataModel.model.embeddedModel === MODEL_TYPE.VERSION ||
          this.formDataModel.model.embeddedModel === MODEL_TYPE.DATATRACE
        ) {
          modelPropData = modelPropData.concat([
            ...MODEL_TYPE_AND_PROP_MAP[MODEL_TYPE.DATATRACE],
            LATEST_FLAG_COL,
          ])
        }
        // 如果是文档模型还要看版本模型的字段
        if (this.formDataModel.model.embeddedModel === MODEL_TYPE.VERSION) {
          modelPropData = modelPropData.concat(VERSION_NO_LATEST_FLAG)
        }
      }
      if (this.formDataModel.model.deleteMode === DELETE_MODE.LOGICAL) {
        modelPropData = modelPropData.concat(LOGIC_DELETE_PROP)
      }
      const propColNameMap = {}
      if (Array.isArray(modelPropData)) {
        modelPropData.forEach((item) => {
          propColNameMap[item.colName] = item
        })
      }
      return propColNameMap
    },
    autoAnalyse() {
      if (this.formDataModel.model.tableName) {
        this.$http
          .post(DataSetAPI.FIELD_LIST(), {
            tableName: this.formDataModel.model.tableName,
            appCode: this.appCode,
          })
          .then((result) => {
            if (Array.isArray(result.data) && result.data.length > 0) {
              const propMap = this.makePropList2Map(this.tblModelProp.propList)
              const colFieldMap = propMap.colFieldMap || {}
              const addableFields = propMap.addableFields || {}
              const tempPropArray = []
              const modelPropMap = this.getCurrentModelPropMap()
              const pushedProp = {}
              for (let i = 0; i < result.data.length; i++) {
                const item = result.data[i]
                let colField = {
                  name: '',
                  colName: '',
                  type: '',
                  modelTypeLabel: '',
                  componentTypeLabel: '',
                  comments: '',
                }
                if (colFieldMap[item.name]) {
                  // 表格中已经有了,看有没有comments,然后添加到tempPropArray中
                  colField = colFieldMap[item.name]
                  if (!colField.comments) {
                    colField.comments = item.comments
                  }
                } else {
                  // 表格中还没有,根据item.dataType添加到tempPropArray中
                  // item结构
                  // {alias: "end_date",code: "end_date",comments: "到期日",dataType: "date",name: "end_date",tableAlias: null,tableName: "flcm_product"}
                  // prop结构如下
                  // { name: 'createtimestamp', colName: 'createtimestamp', type: PROP_TYPE.CREATETIMESTAMP, modelTypeLabel: '新增时间', comments: '新增时间'},
                  // 如果此时该模型拥有某些系统字段,且数据库中colName又对应上即为该字段
                  if (modelPropMap[item.name]) {
                    colField = modelPropMap[item.name]
                  } else {
                    // 构建prop结构
                    const currentPropType = getPropTypeByDBColType(
                      item.dataType
                    )

                    const name = makeColName2PropName(item.name)
                    const modelTypeItem = PROP_TYPE_MAP[currentPropType]
                    let currentComponentType
                    if (isFramworkBaseColName(item.name)) {
                      currentComponentType = COMPONENT_TYPE.TEXT
                    } else {
                      const componentTypes =
                        PROP_TYPE_AND_COMPONENT_OPTIONS_MAP[currentPropType]
                      if (Array.isArray(componentTypes)) {
                        currentComponentType = componentTypes[0].value
                      } else {
                        console.info(
                          `数据模型属性类型${currentPropType}没有对应的组件类型,默认为TEXT组件`
                        )
                        currentComponentType = COMPONENT_TYPE.TEXT
                      }
                    }
                    const componentTypeOption =
                      COMPONENT_TYPE_AND_OPTION_MAP[currentComponentType]
                    colField = {
                      name,
                      colName: item.name,
                      type: currentPropType,
                      componentType: currentComponentType,
                      modelTypeLabel: modelTypeItem ? modelTypeItem.label : '',
                      componentTypeLabel: componentTypeOption
                        ? componentTypeOption.label
                        : '',
                      comments: item.comments,
                    }
                    if (currentPropType === PROP_TYPE.DATE) {
                      colField.format = DEFAULT_DATE_FORMAT
                    }
                  }
                }
                pushedProp[colField.colName] = true
                tempPropArray.push(colField)
              }
              Object.keys(modelPropMap).forEach((propKey) => {
                if (!pushedProp[propKey]) {
                  tempPropArray.push(modelPropMap[propKey])
                }
              })

              // 把tempPropArray转为以name为key,value为true的map
              const nameMap = this.makePropList2NameMap(tempPropArray)
              // 把addableFields中的数据添加到tempPropArray中
              Object.keys(addableFields).forEach((name) => {
                if (!nameMap[name]) {
                  tempPropArray.push(addableFields[name])
                }
              })
              this.tblModelProp.propList = tempPropArray
              this.$message.success('解析成功,请根据实际需要自行修改')
            } else {
              this.$message.info(
                `无法解析数据表${this.formDataModel.model.tableName}`
              )
            }
          })
      }
    },
    makePropList2NameMap(propList) {
      const nameMap = {}
      propList.forEach((prop) => {
        nameMap[prop.name] = true
      })
      return nameMap
    },
    // 把PropList转为map结构
    makePropList2Map(propList) {
      const propMap = {
        // 没有物理表只有name
        addableFields: {},
        // 存放有物理表名的属性
        // {
        //   'name':{}
        // }
        colFieldMap: {},
      }
      Array.isArray(propList) &&
        propList.forEach((item) => {
          if (item.colName) {
            propMap.colFieldMap[item.colName] = item
          } else {
            propMap.addableFields[item.name] = item
          }
        })
      return propMap
    },
    // 判断prop是否是模型特殊字段(不能修改和删除)
    canEditProp(prop) {
      // 自定义模型都可以编辑
      if (this.formDataModel.model.modelType === MODEL_TYPE.CUSTOM) {
        return true
      }
      // 通用模型一直都可以编辑
      if (this.formDataModel.model.modelType) {
        let propDatas =
          MODEL_TYPE_AND_PROP_MAP[this.formDataModel.model.modelType]
        // 如果当前是审批模型,考虑嵌入模型的情况
        if (
          this.formDataModel.model.modelType === MODEL_TYPE.WORKFLOW &&
          this.formDataModel.model.embeddedModel
        ) {
          propDatas = propDatas.concat([
            ...MODEL_TYPE_AND_PROP_MAP[MODEL_TYPE.DATATRACE],
            LATEST_FLAG_COL,
          ])
          // 如果是文档模型还要看版本模型的字段
          if (this.formDataModel.model.embeddedModel === MODEL_TYPE.VERSION) {
            propDatas = propDatas.concat(VERSION_NO_LATEST_FLAG)
          }
        }
        // 如果当前是版本模型,考虑留痕模型
        if (this.formDataModel.model.modelType === MODEL_TYPE.VERSION) {
          propDatas = propDatas.concat(
            MODEL_TYPE_AND_PROP_MAP[MODEL_TYPE.DATATRACE]
          )
        }
        if (Array.isArray(propDatas)) {
          for (const propData of propDatas) {
            if (this.isPropASameAsPropB(propData, prop)) {
              return false
            }
          }
        }
      }
      // 如果是逻辑删除,判断一下逻辑删除字段类型
      if (
        this.formDataModel.model.deleteMode === DELETE_MODE.LOGICAL &&
        prop.colName === REMOVE_FLAG_PROP.colName
      ) {
        const logicDeletePropDatas = LOGIC_DELETE_PROP
        if (Array.isArray(logicDeletePropDatas)) {
          for (const propData of logicDeletePropDatas) {
            if (this.isPropASameAsPropB(propData, prop)) {
              return false
            }
          }
        }
      }
      return true
    },
    isPropASameAsPropB(propA, propB) {
      return (
        propA.colName === propB.colName &&
        propA.name === propB.name &&
        propA.type === propB.type &&
        propA.modelTypeLabel === propB.modelTypeLabel
      )
    },
    handleModelTypeChange(value) {
      if (value === MODEL_TYPE.VERSION || value === MODEL_TYPE.DATATRACE) {
        this.dealDataTraceOrVersionModel(value)
      } else {
        const propDatas = MODEL_TYPE_AND_PROP_MAP[value]
        this.pushPropDataIfNotExists(propDatas)
      }
      this.enableDataTrace = false
      this.enableVersion = false
      this.whenEmbeddedModelEnableStatusChange()
    },
    handleChangeDeleteMode(value) {
      const propDatas = LOGIC_DELETE_PROP
      if (value === 1) {
        this.pushPropDataIfNotExists(propDatas)
      } else {
        let logicDeleteKeyIndex = -1
        for (let i = 0; i < this.tblModelProp.propList.length; i++) {
          const prop = this.tblModelProp.propList[i]
          if (this.isPropASameAsPropB(REMOVE_FLAG_PROP, prop)) {
            logicDeleteKeyIndex = i
            break
          }
        }
        logicDeleteKeyIndex !== -1 &&
          this.$confirm(
            `是否确认同步删除类型为${REMOVE_FLAG_PROP.modelTypeLabel},物理字段名为${REMOVE_FLAG_PROP.colName}的属性？`
          )
            .then(() => {
              this.tblModelProp.propList.splice(logicDeleteKeyIndex, 1)
            })
            .catch()
      }
    },
    handleEmbeddedModelSwitchChange() {
      this.whenEmbeddedModelEnableStatusChange()
    },
    whenEmbeddedModelEnableStatusChange() {
      if (this.enableVersion) {
        this.formDataModel.model.embeddedModel = MODEL_TYPE.VERSION
      } else if (this.enableDataTrace) {
        this.formDataModel.model.embeddedModel = MODEL_TYPE.DATATRACE
      } else {
        this.formDataModel.model.embeddedModel = ''
      }
      this.dealDataTraceOrVersionModel(this.formDataModel.model.embeddedModel)
    },
    /**
     * 由于版本模型依赖数据留痕模型，因此摘出来单独处理
     */
    dealDataTraceOrVersionModel(value) {
      if (value === MODEL_TYPE.VERSION || value === MODEL_TYPE.DATATRACE) {
        let propDatas = [
          ...MODEL_TYPE_AND_PROP_MAP[MODEL_TYPE.DATATRACE],
          LATEST_FLAG_COL,
        ]
        if (value === MODEL_TYPE.VERSION) {
          propDatas = propDatas.concat(
            MODEL_TYPE_AND_PROP_MAP[MODEL_TYPE.VERSION]
          )
        }
        this.pushPropDataIfNotExists(propDatas)
      }
    },
    /**
     * 如果propDatas中数据在this.tblModelProp.propList不存在，那么就push进去
     * @param {Array} propDatas
     */
    pushPropDataIfNotExists(propDatas) {
      if (propDatas) {
        for (const propData of propDatas) {
          let flag = true
          for (let i = 0; i < this.tblModelProp.propList.length; i++) {
            const prop = this.tblModelProp.propList[i]
            if (
              propData.colName === prop.colName ||
              propData.name === prop.name
            ) {
              if (this.isPropASameAsPropB(propData, prop)) {
                flag = false
              } else {
                this.tblModelProp.propList.splice(i, 1)
              }
              break
            }
          }
          flag && this.tblModelProp.propList.push(propData)
        }
      }
    },
    validateModel(resolve) {
      if (this.formDataModel.model.moduleCode === '') {
        this.$message.info('缺少模块编码,无法保存')
      } else if (this.isMultiAppMode && !this.appCode) {
        this.$message.error('缺少应用数据，无法保存')
      } else {
        this.$refs.formDataModelRef.validate((valid) => {
          if (valid) {
            if (this.tblModelProp.propList.length === 0) {
              this.$message.info('模型属性不能为空！')
            } else {
              if (!this.validateHasKeyProp()) {
                this.$message.info('当前数据模型不存在类型为主键的模型属性')
                return false
              }
              resolve()
            }
          }
        })
      }
    },
    handleSave() {
      this.validateModel(() => {
        this.btnLoading = true
        const modelData = {
          appCode: this.formDataModel.model.appCode,
          id: this.formDataModel.model.id,
          name: this.formDataModel.model.name,
          moduleCode: this.formDataModel.model.moduleCode,
          useRange: this.formDataModel.model.useRange,
          modelType: this.formDataModel.model.modelType,
          datasource: this.formDataModel.model.datasource,
          tableName: this.formDataModel.model.tableName,
          deleteMode: this.formDataModel.model.deleteMode,
          embeddedModel: this.formDataModel.model.embeddedModel,
          props: this.tblModelProp.propList,
          code: this.formDataModel.model.code,
          configJson: JSON.stringify(this.formDataModel.model.configJson),
        }
        // 组合code
        if (this.isMultiAppMode) {
          modelData.code = this.appCode + '-' + this.formDataModel.model.code
        }
        this.$http
          .post(DataModelAPI.MODEL_SAVE(), modelData)
          .then(() => {
            this.btnLoading = false
            this.$message.success('保存成功')
            this.$nextTick(() => {
              this.handleClose()
            })
          })
          .catch((error) => {
            this.btnLoading = false
            handleErrorMessage(this, error)
          })
      })
    },
    validateHasKeyProp() {
      return (
        Array.isArray(this.tblModelProp.propList) &&
        this.tblModelProp.propList.some(
          (item) => item.type === this.PROP_TYPE.KEY
        )
      )
    },
    handleClose() {
      // bug:288644 快速多次点击保存按钮--编辑页面和表单管理页面都关闭了
      // this.$closeTab(this.currentRoute)
      // this.$closeTab中不加this.currentRoute参数，加了会导致在兼容菜单返回不了
      this.$closeTab()
    },
    handlePropsAdd() {
      this.tblModelProp.title = '新增属性'
      this.tblModelProp.name = ''
      const tableName = this.formDataModel.model.tableName
      this.$refs.modelPropEditRef.show(undefined, tableName)
    },
    handlePropsEdit(data) {
      this.tblModelProp.title = '编辑属性'
      this.tblModelProp.name = data.name
      data.modelCode = this.formDataModel.model.code
      const tableName = this.formDataModel.model.tableName
      this.$refs.modelPropEditRef.show(data, tableName)
    },
    handlePropsDelete(row) {
      this.$confirm
        .delete()
        .then(() => {
          if (Array.isArray(row)) {
            row.forEach((prop) => {
              const index = this.tblModelProp.propList.findIndex(
                (item) => item.name === prop.name
              )
              this.tblModelProp.propList.splice(index, 1)
            })
          } else {
            const index = this.tblModelProp.propList.findIndex(
              (item) => item.name === row.name
            )
            this.tblModelProp.propList.splice(index, 1)
          }
          this.$message.deleteWithSuccess()
        })
        .catch()
    },
    handleGeneratorCode() {
      if (!this.formDataModel.model || !this.formDataModel.model.id) {
        this.$message.info('请先保存模型信息')
        return
      }
      this.validateModel(() => {
        const data = {
          modelId: this.formDataModel.model.id,
        }
        this.$refs.modelGeneratorCodeRef.show(data)
      })
    },
    handleModelPropFormSave(data) {
      // 设置属性列表展示的字段
      const propData = {
        ...data,
        ...formatDataModel(data),
      }
      if (this.tblModelProp.name) {
        // 编辑
        const index = this.tblModelProp.propList.findIndex(
          (item) => item.name === this.tblModelProp.name
        )
        this.tblModelProp.propList.splice(index, 1, propData)
      } else {
        // 新增
        this.tblModelProp.propList.push(propData)
      }
    },
    handleUpdateConfigJson(newData) {
      this.$set(this.formDataModel.model, 'configJson', newData)
    },
  },
}
</script>

<style scoped>
.inputWidth {
  width: 208px !important;
}
.clazzWidth {
  width: 350px !important;
}
.helpTooltip {
  margin-left: 5px !important;
  color: #409eff;
}
::v-deep .table-index .table-header .el-button {
  margin: 0px 10px 10px 0px !important;
}
.config-json-form {
  width: 90%;
}
.other-config {
  width: 90%;
}
.refresh-icon {
  cursor: pointer;
  padding-left: 5px;
}
</style>
