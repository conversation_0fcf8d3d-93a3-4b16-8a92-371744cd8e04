<template>
  <xn-dialog
    id="diagModelGeneratorCode"
    title="模型代码生成"
    width="750px"
    height="600px"
    :show.sync="diagModelGeneratorCode.display"
  >
    <el-switch
      id="switchFull"
      v-model="out.full"
      active-text="完整版（嵌套DTO，未来支持自动填充）"
      inactive-text="简单版(该版本段推荐)"
      @change="refresh(out.full)"
    ></el-switch>
    <hr />
    <el-tabs
      id="generatorTabs"
      type="border-card"
      value="tabDTO"
      style="height: auto"
    >
      <el-tab-pane label="DTO" name="tabDTO" class="codeLine">
        <pre>{{ out.code.dto }}</pre>
      </el-tab-pane>
      <el-tab-pane label="拦截器" name="tabInterceptor" class="codeLine">
        <pre>{{ out.code.interceptor }}</pre>
      </el-tab-pane>
    </el-tabs>
  </xn-dialog>
</template>
<script>
import { DataModelAPI } from 'src/api'
export default {
  name: 'ModelGeneratorCode',
  props: {
    modelId: {
      type: String,
      default: '',
    },
    modelCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      diagModelGeneratorCode: {
        display: false,
      },
      out: {
        code: {
          dto: '',
          interceptor: '',
        },
        isFull: false,
      },
    }
  },
  mounted() {},
  methods: {
    show(data) {
      this.out.modelId = data.modelId
      this.$http
        .get(DataModelAPI.GENERATOR_DTO(), {
          id: data.modelId,
          isFull: false,
        })
        .then((res) => {
          if (res.data) {
            this.out.code.dto = res.data
            this.diagModelGeneratorCode.display = true
          } else {
            this.$message.info('生成DTO失败')
          }
        })
      this.$http
        .get(DataModelAPI.GENERATOR_INTERCEPTOR(), {
          id: data.modelId,
        })
        .then((res) => {
          if (res.data) {
            this.out.code.interceptor = res.data
          } else {
            this.$message.info('生成拦截器失败')
          }
        })
    },
    refresh(isFull) {
      this.$http
        .get(DataModelAPI.GENERATOR_DTO(), {
          id: this.out.modelId,
          isFull,
        })
        .then((res) => {
          if (res.data) {
            this.out.code.dto = res.data
          }
        })
    },
  },
}
</script>
<style scoped>
.codeLine {
  padding: 0 20px 0 20px;
}
.info {
  color: red;
}
</style>
