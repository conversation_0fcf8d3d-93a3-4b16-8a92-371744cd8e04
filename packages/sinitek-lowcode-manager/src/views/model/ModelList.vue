<template>
  <div id="ModelList">
    <xn-table
      id="tblModelList"
      ref="tblModelListRef"
      :url="url"
      :querymodel="queryForm"
      :toolbars="tblModelList.toolbars"
      @add="handleAdd"
      @delete="handleDelete"
      @import="handleTblModelListImport"
      @export="handleTblModelListExport"
    >
      <div v-if="showSearch" slot="search">
        <el-form :model="queryForm" :inline="true" is-query-input>
          <el-form-item label="名称">
            <xn-input
              v-model.trim="queryForm.name"
              placeholder="请输入名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="编码">
            <xn-input
              v-model.trim="queryForm.code"
              placeholder="请输入编码"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="handleBtnQueryClick"
            >
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <template slot="sync">
        <div>
          <sync-dialog
            ref="syncDialog"
            type="model"
            :ref-table="$refs.tblModelListRef"
            :is-related="true"
            :app-info="appInfo"
          />
        </div>
      </template>
      <xn-col type="selection" width="40" />
      <xn-col
        prop="code"
        label="模型编码"
        min-width="180"
        align="left"
        sortable="true"
        show-overflow-tooltip
      />
      <xn-col
        prop="name"
        label="模型名称"
        min-width="180"
        align="left"
        sortable="true"
        show-overflow-tooltip
      />
      <xn-col
        prop="modelType"
        label="模型类型"
        width="150"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ translateModelType(scope.row.modelType) }}
        </template>
      </xn-col>
      <xn-col
        prop="tableName"
        label="物理表名"
        min-width="150"
        align="left"
        show-overflow-tooltip
      />
      <xn-col
        prop="createTimeStamp"
        label="创建时间"
        width="180"
        align="center"
        sortable="true"
      />
      <xn-col
        prop="updateTimeStamp"
        label="更新时间"
        width="180"
        align="center"
        sortable="true"
      />
      <xn-col label="操作" min-width="140" fixed="right" align="center">
        <template slot-scope="scope">
          <xn-col-action-group :keys="scope.row">
            <xn-col-action
              v-for="(colBtn, btnIndex) in operations"
              :key="btnIndex"
              @click="handleClickOpera(scope, colBtn)"
            >
              {{ colBtn.label }}
            </xn-col-action>
          </xn-col-action-group>
        </template>
      </xn-col>
    </xn-table>
    <!-- 导入弹出框 -->
    <xn-dialog
      id="dlgModelListImport"
      ref="dlgModelListImportRef"
      :title="dlgModelListImport.title"
      :show.sync="dlgModelListImport.show"
      width-size="small"
      :buttons="dlgModelListImport.buttons"
      @save="showImportList"
      @cancel="handleDlgModelListImportCancel"
    >
      <xn-form
        id="formDlgModelListImport"
        ref="formDlgModelListImportRef"
        slot="dialog-form"
        :model="formDlgModelListImport.model"
        size="small"
        :rules="formDlgModelListImport.rules"
        default-label-width="120px"
      >
        <xn-form-item label="数据模型模板" prop="dataModleTemplate">
          <xn-upload
            v-if="dlgModelListImport.show"
            id="uplFormDlgModelListImportUploader"
            ref="uplFormDlgModelListImportUploaderRef"
            v-model="formDlgModelListImport.model.importFile"
            mode="upload"
            accept=".json,.zip"
            :file-preview="{ isEnable: false }"
            @onError="handleUplFormDlgModelListImportUploaderError"
          />
          <div style="color: red">
            提示：只允许上传从当前功能模块下导出的数据
          </div>
        </xn-form-item>
      </xn-form>
    </xn-dialog>

    <!-- 模块导入数据列表框 -->
    <xn-dialog
      id="dlgModelTable"
      ref="dlgModelListImportTable"
      title="导入数据列表"
      :show.sync="showTable"
      width-size="small"
      :buttons="importTableButtons"
      @save="handleDlgModelListImportSave"
      @cancel="
        () => {
          showTable = false
        }
      "
    >
      <el-tabs v-model="tabName">
        <el-tab-pane label="覆盖数据" name="overrideData"></el-tab-pane>
        <el-tab-pane label="新导入数据" name="newImportData"></el-tab-pane>
      </el-tabs>
      <el-table :data="tableData[tabName]" border>
        <el-table-column prop="code" label="编码"> </el-table-column>
        <el-table-column prop="name" label="名称"> </el-table-column>
      </el-table>
    </xn-dialog>

    <sync-dialog
      ref="syncDialogSingle"
      type="model"
      :is-sync-all="false"
      :is-related="true"
      :app-info="appInfo"
    />
  </div>
</template>

<script>
import { DataModelAPI } from 'src/api'
import { operation } from 'sinitek-util'
import { handleErrorMessage } from 'src/utils'
import { MODEL_TYPE_AND_OPTION_MAP } from 'src/constant'
import sync from 'src/mixins/sync'

export default {
  name: 'ModelList',
  mixins: [sync],
  inject: ['getAppCode', 'getAppName', 'getCurrentModuleCode'],
  props: {
    showSearch: {
      type: Boolean,
      default: false,
    },
  },
  data: function () {
    return {
      url: Object.freeze(DataModelAPI.MODEL_SEARCH()),
      tblModelList: {
        toolbars: [
          { label: '新增', type: 'add', event: 'add' },
          { label: '删除', type: 'delete', event: 'delete' },
          { label: '导入', type: 'import', event: 'import' },
          { label: '导出', type: 'export', event: 'export' },
        ],
      },
      // 导入弹框
      dlgModelListImport: {
        title: '数据模型导入',
        show: false,
        width: '435px',
        buttons: [
          { action: 'import', type: 'primary', event: 'save' },
          { action: 'cancel', type: 'info', event: 'cancel' },
        ],
      },
      // 导入表单
      formDlgModelListImport: {
        model: {
          importFile: [],
        },
        rules: {
          dataModleTemplate: [
            { validator: this.validateImport, required: true },
          ],
        },
      },
      // 导入列表
      showTable: false,
      tabName: 'overrideData',
      tableData: {
        overrideData: [],
        newImportData: [],
      },
      importTableButtons: [
        { label: '确定', action: 'save', type: 'primary', event: 'save' },
        { label: '取消', action: 'cancel', type: 'info', event: 'cancel' },
      ],
      importParam: {},
      // 查询参数
      queryForm: {
        name: '',
        code: '',
        moduleCode: null,
      },
      tableRef: 'tblModelListRef',
      operations: [
        {
          label: '编辑',
          functionName: 'handleEdit',
        },
        {
          label: '删除',
          functionName: 'handleDelete',
        },
      ],
    }
  },
  created() {
    this.queryForm.moduleCode = this.getCurrentModuleCode()
  },
  methods: {
    handleBtnQueryClick() {
      this.query()
    },
    query() {
      this.$refs.tblModelListRef?.query()
    },
    handleAdd() {
      this.$openTab(
        '/lowcode/model/edit',
        {
          query: {
            title: this.getAppName() + '数据模型新增',
            moduleCode: this.getCurrentModuleCode(),
            appCode: this.getAppCode(),
          },
        },
        () => {
          this.query()
          this.$emit('afterSave')
        }
      )
    },
    handleEdit(row) {
      this.$openTab(
        '/lowcode/model/edit',
        {
          query: {
            title: this.getAppName() + '数据模型编辑',
            id: row.id,
            appCode: this.getAppCode(),
          },
        },
        () => {
          this.query()
          this.$emit('afterSave')
        }
      )
    },
    handleDelete(row) {
      this.$confirm
        .delete()
        .then(() => {
          const objids = []
          if (Array.isArray(row)) {
            row.forEach((item) => objids.push(item.id))
          } else {
            objids.push(row.id)
          }
          this.$http
            .post(DataModelAPI.MODEL_DELETE(), objids)
            .then(() => {
              this.$message.deleteWithSuccess()
              this.query()
              this.$emit('afterSave')
            })
            .catch((err) => {
              handleErrorMessage(this, err)
            })
        })
        .catch(() => {
          console.warn('用户取消删除')
        })
    },
    // 数据模型导入按钮
    handleTblModelListImport() {
      this.dlgModelListImport.show = true
    },
    // 导入弹框中，模块的保存按钮
    showImportList(resolve, reject) {
      // 判重
      this.$refs.formDlgModelListImportRef.validate((valid) => {
        if (valid) {
          const param = {
            importFile: this.formDlgModelListImport.model.importFile,
            appCode: this.getAppCode(),
            moduleCode: this.getCurrentModuleCode(),
          }
          this.importParam = param
          this.$easypost(DataModelAPI.MODEL_IMPORT_VALIDATE(), param).then(
            (result) => {
              resolve('导入校验成功')
              this.handleDlgModelListImportCancel()
              this.showTable = true
              this.tableData = result
            },
            () => {
              reject()
            }
          )
        } else {
          reject()
        }
      })
    },
    // 导入弹框中，数据模型的保存按钮
    handleDlgModelListImportSave(resolve, reject) {
      this.$easypost(DataModelAPI.MODEL_IMPORT(), this.importParam)
        .then(() => {
          resolve('导入成功')
          this.dlgModelListImport.show = false
          this.showTable = false
          this.query()
          this.$emit('afterSave')
        })
        .catch((error) => {
          // 禁用http请求报错的默认提示
          this.$message.closeAll()
          reject(error)
        })
    },
    // 导入弹框中的取消按钮
    handleDlgModelListImportCancel() {
      this.dlgModelListImport.show = false
      this.formDlgModelListImport.model.importFile = []
      this.$refs.uplFormDlgModelListImportUploaderRef.clear()
      this.$refs.formDlgModelListImportRef.clearValidate()
    },
    // 导入时的异常处理
    handleUplFormDlgModelListImportUploaderError(error, file, fileList) {
      console.warn('导入时异常', error, file, fileList)
      this.$refs.dlgModelListImportRef.displayErrorMessage(error.message)
    },
    // 数据模型导出按钮
    handleTblModelListExport(data) {
      if (data.length > 0) {
        let objids = []
        for (const i in data) {
          if (i === '0') {
            objids = [data[i].id]
          } else {
            objids.push(data[i].id)
          }
        }
        operation.download(DataModelAPI.MODEL_EXPORT(), {
          ids: objids,
          prettyPrinterFlag: true,
        })
      } else {
        this.$message.info('请选择要导出的数据模型')
      }
    },
    // 导入前验证是否上传了文件
    validateImport(rule, value, callback) {
      // 获取上传文件
      const template = this.$refs.uplFormDlgModelListImportUploaderRef.fileList
      if (template.length === 0) {
        callback(new Error('请上传数据模型模板文件'))
      } else {
        callback()
      }
    },
    // 模型类型名称翻译
    translateModelType(modelType) {
      return MODEL_TYPE_AND_OPTION_MAP[modelType]?.label
    },
  },
}
</script>

<style></style>
