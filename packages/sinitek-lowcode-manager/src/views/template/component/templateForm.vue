<template>
  <div class="template_form">
    <xn-form ref="ruleForm1" :model="ruleForm" :rules="rules">
      <xn-form-item label="模板名称" prop="name">
        <xn-input
          v-if="dialogType !== 'detail'"
          v-model="ruleForm.name"
          :maxlength="50"
          show-word-limit
        />
        <xn-form-item-content v-else>{{ ruleForm.name }}</xn-form-item-content>
      </xn-form-item>
      <xn-form-item label="描述" prop="description">
        <xn-input
          v-if="dialogType !== 'detail'"
          v-model="ruleForm.description"
          type="textarea"
          :maxlength="100"
          :rows="3"
          show-word-limit
          class="des"
        />
        <xn-form-item-content v-else>{{
          ruleForm.description
        }}</xn-form-item-content>
      </xn-form-item>
      <xn-form-item label="预览图" prop="thumbnail">
        <xn-upload
          v-if="dialogType !== 'detail'"
          v-model="ruleForm.thumbnail"
          mode="upload"
          :limit="1"
          accept=".jpg,.png"
          :source-id="templateId"
          source-entity="LC_TEMPLATE"
          type="0"
          @onSuccess="handleSuccess"
          @onError="importError"
        />
        <el-image
          v-else-if="ruleForm.thumbnailBase64"
          style="width: 100px; height: 100px"
          :src="ruleForm.thumbnailBase64"
          :preview-src-list="[ruleForm.thumbnailBase64]"
          :z-index="3000"
        >
        </el-image>
        <el-image
          v-else
          style="width: 100px; height: 100px"
          :src="require('./demo.png')"
          :preview-src-list="[require('./demo.png')]"
          :z-index="3000"
        >
        </el-image>
      </xn-form-item>
      <xn-form-item label="PC端JSON" prop="pcJson">
        <xn-input
          v-model="ruleForm.pcJson"
          type="textarea"
          :disabled="dialogType === 'detail'"
          :rows="10"
          :maxlength="50000"
        />
      </xn-form-item>
      <xn-form-item label="移动端JSON" prop="mobileJson">
        <xn-input
          v-model="ruleForm.mobileJson"
          :disabled="dialogType === 'detail'"
          type="textarea"
          :rows="10"
          :maxlength="50000"
        />
      </xn-form-item>
      <template v-if="dialogType === 'detail'">
        <xn-form-item label="来源">
          <xn-form-item-content>{{
            ruleForm.sourceType ? sourceTypes.TRIPARTITE : sourceTypes.OFFICIAL
          }}</xn-form-item-content>
        </xn-form-item>
        <xn-form-item label="提交人">
          <xn-form-item-content>{{
            ruleForm.createdName
          }}</xn-form-item-content>
        </xn-form-item>
        <xn-form-item label="创建日期">
          <xn-form-item-content>{{
            timestampToTime(ruleForm.createTimeStamp)
          }}</xn-form-item-content>
        </xn-form-item>
        <xn-form-item label="更新日期">
          <xn-form-item-content>{{
            timestampToTime(ruleForm.updateTimeStamp)
          }}</xn-form-item-content>
        </xn-form-item>
      </template>
    </xn-form>
  </div>
</template>

<script>
import { deepCopy } from 'src/utils'
import { TemplateAPI } from 'src/api'
import { dateUtil } from 'sinitek-util'
import { SOURCE_TYPES } from 'src/constant'
import { log } from 'sinitek-lowcode-shared'
export default {
  name: 'TemplateForm',
  props: {
    dialogType: {
      type: String,
      default: '',
    },
    dialogVisible: {
      type: Boolean,
      default: null,
    },
    sourceType: {
      type: Number,
      default: 0,
    },
    templateId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      thumbnail: {
        uploadFileList: [],
        type: '0',
        mode: 'avatar',
      },
      ruleForm: {
        name: '',
        description: '',
        thumbnail: {},
        pcJson: '',
        mobileJson: '',
      },
      mb: false,
      pc: false,
      importData: {
        importFile: {},
      },
      sourceTypes: SOURCE_TYPES,
    }
  },
  computed: {
    rules() {
      const rulesCopy = {
        name: [
          {
            required: true,
            message: '请输入模板名称',
            trigger: ['blur'],
          },
        ],
        description: [
          {
            required: true,
            trigger: ['blur'],
            validator: this.checkDescription,
          },
        ],
        pcJson: [
          {
            trigger: ['blur'],
            validator: this.checkPcJson,
          },
        ],
        mobileJson: [
          {
            trigger: ['blur'],
            validator: this.checkMobileJson,
          },
        ],
      }
      for (const k in rulesCopy) {
        if (k === 'pcJson' || k === 'mobileJson') {
          rulesCopy[k][0].required = false
        } else {
          rulesCopy[k][0].required = this.dialogType !== 'detail'
        }
      }
      return rulesCopy
    },
  },
  methods: {
    importError(error) {
      this.$message.error(error.message.message)
    },
    handleSave(resolve, reject) {
      this.$refs.ruleForm1.validate((valid) => {
        if (valid) {
          const formData = deepCopy(this.ruleForm)
          formData.pcJson = formData.pcJson
            ? this.jsonToBase64(this.ruleForm.pcJson)
            : undefined
          formData.mobileJson = formData.mobileJson
            ? this.jsonToBase64(this.ruleForm.mobileJson)
            : undefined
          const params = {
            ...formData,
            ...{ sourceType: this.sourceType },
          }
          if (this.dialogType === 'edit') {
            params.id = this.templateId
          }
          this.$http
            .post(TemplateAPI.TEMPLATE_LIST_UPDATE(), {
              ...params,
            })
            .then(
              (res) => {
                if (res.resultcode === '0') {
                  resolve()
                  this.$emit('onDialogClose')
                  this.$emit('getTemplateList')
                } else {
                  this.$message.error(res.message)
                  reject()
                }
              },
              (err) => {
                log(err)
                reject()
              }
            )
        } else {
          reject()
        }
      })
    },
    handleClose() {
      this.$refs.ruleForm1.resetFields()
      this.ruleForm = {}
    },
    handleEdit() {
      this.$http
        .get(TemplateAPI.TEMPLATE_LIST_DETAIL(), { id: this.templateId })
        .then(
          (res) => {
            if (res.resultcode === '0') {
              const rowData = res.data
              for (const k in this.ruleForm) {
                if (k !== 'thumbnail') {
                  this.ruleForm[k] = rowData[k]
                }
              }
            }
          },
          (err) => {
            log(err)
          }
        )
    },
    handleDetail() {
      this.$http
        .get(TemplateAPI.TEMPLATE_LIST_DETAIL(), { id: this.templateId })
        .then(
          (res) => {
            if (res.resultcode === '0') {
              this.ruleForm = res.data
            }
          },
          (err) => {
            log(err)
          }
        )
    },

    timestampToTime(timestamp) {
      return dateUtil.formatDate(new Date(timestamp), 'yyyy-MM-dd HH:mm:ss')
    },
    setRules() {
      for (const k in this.rules) {
        if (k === 'pcJson' || k === 'mobileJson') {
          this.rules[k][0].required = false
        } else {
          this.rules[k][0].required = this.dialogType !== 'detail'
        }
      }
    },
    handleSuccess() {
      log('handleSuccess', this.ruleForm.thumbnail)
    },
    checkPcJson(rule, value, callback) {
      this.pc = true
      if (!value && !this.ruleForm.mobileJson) {
        callback(new Error('请至少填写一项json'))
      } else {
        callback()
      }
      if (this.pc && this.mb) {
        this.pc = this.mb = false
        return
      }
      this.$refs.ruleForm1.validateField('mobileJson')
    },
    checkMobileJson(rule, value, callback) {
      this.mb = true
      if (!value && !this.ruleForm.pcJson) {
        callback(new Error('请至少填写一项json'))
      } else {
        callback()
      }
      if (this.pc && this.mb) {
        this.pc = this.mb = false
        return
      }
      this.$refs.ruleForm1.validateField('pcJson')
    },
    checkDescription(rule, value, callback) {
      if (value === '') {
        callback(new Error('请输入描述！'))
      } else if (value.length <= 10) {
        callback(new Error('描述不能少于十个字!'))
      } else {
        callback()
      }
    },
    jsonToBase64(jsonString) {
      const utf8Bytes = new TextEncoder().encode(jsonString)
      return btoa(String.fromCharCode(...utf8Bytes))
    },
  },
}
</script>

<style scoped>
::v-deep .des > .el-textarea > span {
  right: 5px;
}
</style>
