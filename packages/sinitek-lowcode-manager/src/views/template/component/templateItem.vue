<template>
  <div class="template_card">
    <div class="template_item_content">
      <el-image
        :style="`height:200px;width:${itemWidth - 8}px`"
        :src="
          itemData.thumbnailBase64
            ? itemData.thumbnailBase64
            : require('./demo.png')
        "
        :preview-src-list="
          itemData.thumbnailBase64
            ? [itemData.thumbnailBase64]
            : [require('./demo.png')]
        "
        fit="contain"
      ></el-image>
      <div class="message">
        <div class="title_name">
          <div class="tag_item">
            <el-tag :type="itemData.sourceType ? 'success' : ''">{{
              itemData.sourceType
                ? sourceTypes.TRIPARTITE
                : sourceTypes.OFFICIAL
            }}</el-tag>
            <div class="name template_name" :title="itemData.name">
              {{ itemData.name }}
            </div>
          </div>

          <div class="tag_item">
            <el-tag
              class="name"
              v-show="itemData.mobileFlag"
              type="info"
              effect="dark"
              ><i class="el-icon-mobile-phone"></i
            ></el-tag>
            <el-tag
              class="name"
              v-show="itemData.pcFlag"
              type="info"
              effect="dark"
              ><i class="el-icon-s-platform"></i
            ></el-tag>
          </div>
        </div>
        <div class="title_des" :title="itemData.description">
          {{ itemData.description }}
        </div>
        <div class="title_up" v-show="itemData.createdName">
          提交人：{{ itemData.createdName }}
        </div>
      </div>
    </div>
    <div class="template_item_footer">
      <div class="action_button">
        <el-button
          type="primary"
          icon="el-icon-eleme"
          circle
          @click="show('detail')"
          plain
        ></el-button>
      </div>
      <div class="action_button" v-show="this.type === 1">
        <el-button
          type="warning"
          icon="el-icon-edit"
          circle
          @click="show('edit')"
          plain
        ></el-button>
      </div>
      <div class="action_button" v-show="this.type === 1">
        <el-button
          type="danger"
          icon="el-icon-delete"
          circle
          @click="onDelete"
          plain
        ></el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { SOURCE_TYPES } from 'src/constant'
export default {
  name: 'TemplateItem',
  props: {
    type: {
      type: Number,
      default: null,
    },
    onlyme: {
      type: Boolean,
      default: false,
    },
    itemHeight: {
      type: Number,
      default: 0,
    },
    itemWidth: {
      type: Number,
      default: 0,
    },
    itemData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return { sourceTypes: SOURCE_TYPES }
  },
  methods: {
    show(action) {
      if (action === 'edit') {
        this.onEdit()
      } else {
        this.onDetail()
      }
    },
    onEdit() {
      this.$emit('onEdit')
    },
    onDetail() {
      this.$emit('onDetail')
    },
    onDelete() {
      this.$emit('onDelete')
    },
  },
}
</script>

<style lang="scss">
.template_card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.template_item_content {
  width: 100%;
  flex-grow: 1;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
.template_item_footer {
  width: 100%;
  flex: 0 0 50px;
  height: 50px;
  padding: 0 10px;
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
}
.action_button {
  display: flex;
  align-items: center;
}
.message {
  display: flex;
  flex-direction: column;
  padding: 0 5px;
}
.title_name {
  display: inline-flex;
  justify-content: space-between;
  margin-top: 5px;
  .tag_item {
    display: inline-flex;
    .name {
      margin-left: 10px;
    }
    .template_name {
      width: 120px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.title_des {
  font-size: 12px;
  color: gray;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 3em;
  margin-top: 5px;
}
.title_up {
  font-size: 15px;
  text-align: right;
  color: #3773d2;
  margin-top: 5px;
}
</style>
