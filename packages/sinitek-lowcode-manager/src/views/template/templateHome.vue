<template>
  <div class="template_container">
    <el-container style="height: 100%">
      <el-header>
        <div class="action_bar">
          <div class="action_item">
            <el-radio-group v-model="templateType" @input="changeTab">
              <el-radio-button
                v-for="item in radioGroupList"
                :key="item.value"
                :label="item.label"
              ></el-radio-button>
            </el-radio-group>
          </div>
          <div class="action_item">
            <el-checkbox v-model="onlyMe" @change="onlyMeChanged"
              >只看我的</el-checkbox
            >
          </div>
          <div class="action_item">
            <el-button
              v-show="queryParams.sourceType === 1"
              type="primary"
              @click="show('add', '新增模板')"
              >新增模板</el-button
            >
          </div>
          <div class="action_item">
            <xn-input
              v-model.trim="queryParams.name"
              placeholder="请输入模板名称"
              clearable
            >
              <el-button
                slot="append"
                icon="el-icon-search"
                @click="search"
              ></el-button>
            </xn-input>
          </div>
        </div>
      </el-header>
      <el-main v-loading="loading" style="padding-bottom: 0; padding-top: 0">
        <div
          v-show="total"
          ref="content_box"
          :style="{ overflow: loading ? 'hidden' : 'auto' }"
          class="template_content"
        >
          <div
            v-for="item in templateList"
            id="card_template"
            :key="item.id"
            class="temlate_image"
            :style="`height:${itemHeight}px;width:${itemWidth}px`"
          >
            <template-item
              :type="item.sourceType"
              :only-me="onlyMe"
              :item-height="itemHeight"
              :item-width="itemWidth"
              :item-data="item"
              @onEdit="onEdit(item)"
              @onDetail="onDetail(item)"
              @onDelete="onDelete(item)"
            ></template-item>
          </div>
          <div
            v-for="n in placeholders"
            :key="`placeholder_${n}`"
            :style="`height:${itemHeight}px;width:${itemWidth}px`"
            class="temlate_image placeholder"
          ></div>
        </div>
        <el-empty v-show="!total"></el-empty>
      </el-main>
      <el-footer>
        <div class="template_footer">
          <el-pagination
            v-show="total"
            background
            :total="total"
            :current-page="queryParams.pageIndex"
            :page-sizes="[12, 16, 30, 50]"
            :page-size="queryParams.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </el-footer>
    </el-container>
    <xn-dialog
      :title="dealTitle"
      width="600px"
      :show.sync="dialog.display"
      :buttons="dialog.buttons"
      :show-footer="showFooter"
      @save="handleSave"
      @close="handleClose"
    >
      <template slot="dialog-form">
        <template-form
          v-if="dialog.display"
          ref="templateSettingFrom"
          :dialog-type="dialogType"
          :dialog-visible="dialog.display"
          :source-type="queryParams.sourceType"
          :template-id="templateId"
          @onDialogClose="handleClose"
          @getTemplateList="getTemplateList"
        ></template-form>
      </template>
    </xn-dialog>
  </div>
</template>

<script>
import TemplateItem from './component/templateItem'
import TemplateForm from './component/templateForm.vue'
import { TemplateAPI } from 'src/api'
import { TABLIST } from 'src/constant'
import { error } from 'sinitek-lowcode-shared'
export default {
  components: {
    TemplateItem,
    TemplateForm,
  },
  data() {
    return {
      itemHeight: 0,
      itemWidth: 0,
      total: 0,
      loading: false,
      dialogType: '',
      showFooter: true,
      templateId: '',
      dialog: {
        display: false,
        buttons: [
          { label: '确定', action: 'save', type: 'primary', event: 'save' },
          { label: '取消', action: 'cancel', type: 'info', event: 'close' },
        ],
      },
      dealTitle: '表单标题',
      templateType: '全部',
      radioGroupList: TABLIST,
      onlyMe: false,
      templateList: [],
      queryParams: {
        name: '',
        sourceType: undefined,
        pageSize: 12,
        pageIndex: 1,
        createBy: undefined,
      },
      placeholders: 0,
      pageTotal: 0,
    }
  },
  created() {
    this.getTemplateList()
  },
  mounted() {
    window.addEventListener('resize', this.getItemSize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getItemSize)
  },
  methods: {
    getTemplateList() {
      this.loading = true
      this.$http.post(TemplateAPI.TEMPLATE_SEARCH(), this.queryParams).then(
        (res) => {
          if (res.resultcode !== '0') {
            this.$message.error(res.message)
          }
          this.total = res.data.totalsize
          this.pageTotal = res.data.datalist.length
          this.templateList = res.data.datalist
          if (this.total > 0) {
            this.$nextTick(() => {
              this.getItemSize()
            })
          }
          this.loading = false
        },
        (err) => {
          error(err)
          this.loading = false
        }
      )
    },
    changeTab(e) {
      const tabParam = this.radioGroupList.filter((item) => {
        return item.label === e
      })[0].value
      this.queryParams.sourceType = tabParam
      this.queryParams.pageIndex = 1
      this.getTemplateList()
    },
    onlyMeChanged(e) {
      this.queryParams.createBy = e ? this.$store.getters.orgId : undefined
      this.getTemplateList()
    },
    handleSave(resolve, reject) {
      this.$refs.templateSettingFrom.handleSave(resolve, reject)
    },
    handleClose() {
      this.dialog.display = false
      this.$refs.templateSettingFrom.handleClose()
    },
    show(type, text) {
      this.dealTitle = text
      this.dialogType = type
      this.showFooter = type !== 'detail'
      this.dialog.display = true
      if (type === 'add') {
        this.templateId = undefined
      }
    },
    onEdit(item) {
      this.show('edit', '编辑模板')
      this.templateId = item.id
      this.$nextTick(() => {
        this.$refs.templateSettingFrom.handleEdit(item)
      })
    },
    onDetail(item) {
      this.show('detail', '模板详情')
      this.templateId = item.id
      this.$nextTick(() => {
        this.$refs.templateSettingFrom.handleDetail()
      })
    },
    onDelete(item) {
      this.$confirm('确认要删除吗？', {
        confirmButtonText: '确定？',
        cancelButtonText: '取消！',
      })
        .then(() => {
          this.$http
            .post(TemplateAPI.TEMPLATE_LIST_DELETE(), { id: item.id })
            .then(
              (res) => {
                if (res.resultcode === '0') {
                  this.$message.success('删除成功')
                  this.getTemplateList()
                }
              },
              (err) => {
                this.$message.error(err.message)
              }
            )
        })
        .catch((err) => {
          error(err)
        })
    },
    getItemSize() {
      const total =
        this.queryParams.pageSize === this.pageTotal
          ? this.queryParams.pageSize
          : this.pageTotal
      const container = this.$refs.content_box
      this.itemHeight = container.offsetHeight / 2
      this.itemWidth = (this.itemHeight / 4) * 3
      const rowCounts = Math.floor(container.clientWidth / this.itemWidth)
      const missingItems = rowCounts - (total % rowCounts)
      this.placeholders = missingItems === rowCounts ? 0 : missingItems
    },
    handleSizeChange(size) {
      this.queryParams.pageSize = size
      this.getTemplateList()
    },
    handleCurrentChange(index) {
      this.queryParams.pageIndex = index
      this.getTemplateList()
    },
    search() {
      this.getTemplateList()
    },
  },
}
</script>

<style lang="scss" scoped>
.template_container {
  height: calc(100% - 46px);
}
::v-deep .el-footer,
.el-main {
  padding-top: 0;
}
::v-deep .el-loading-mask {
  height: 100%;
}
.action_bar {
  display: inline-flex;
  align-items: center;
  height: 100%;
}
.action_item {
  margin-right: 16px;
}
.template_content {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  justify-content: space-between;
  .temlate_image {
    background-color: #f2f2f2;
    justify-content: center;
    padding: 4px;
    aspect-ratio: 3/4;
    margin: 0;
  }
  .placeholder {
    visibility: hidden;
    background: none;
  }
}
.template_footer {
  height: 100%;
  display: flex;
  align-items: center;
}
</style>
