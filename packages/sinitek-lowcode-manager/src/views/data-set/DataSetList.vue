<template>
  <div id="DataSetList" class="app-container filter-container">
    <el-tabs
      id="tabDataSet"
      type="border-card"
      @tab-click="handleTabClick"
      :value="tabDataSet.currentTab"
    >
      <!--搜索栏-->
      <div class="header-search">
        <el-form
          id="dataSetQuery"
          :inline="true"
          :model="dataSetQuery.model"
          class="search-params-bar"
        >
          <el-form-item label="编码">
            <el-input
              clearable
              placeholder="请输入编码"
              v-model="dataSetQuery.model.code"
            />
          </el-form-item>
          <el-form-item label="名称">
            <el-input
              clearable
              placeholder="请输入名称"
              v-model="dataSetQuery.model.name"
            />
          </el-form-item>
          <el-form-item>
            <xn-button
              id="btnFormQueryQuery"
              class="filter-item"
              type="primary"
              icon="el-icon-search"
              :show-loading="false"
              @click="handleQueryDataSet"
            >
              查询
            </xn-button>
          </el-form-item>
        </el-form>
      </div>
      <el-tab-pane label="数据库" name="dataBaseList">
        <data-base-list :param="dataSetQuery.model" ref="dataBaseListRef" />
      </el-tab-pane>
      <el-tab-pane label="应用系统" name="applicationList">
        <application-list
          :param="dataSetQuery.model"
          ref="applicationListRef"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { isMultiAppMode } from 'src/config'
import { MfAppAPI } from 'src/api'
export default {
  name: 'LcDataSetList',
  components: {
    ApplicationList: () => import('./components/ApplicationList'),
    DataBaseList: () => import('./components/DataBaseList'),
  },
  data: function () {
    return {
      tabDataSet: {
        currentTab: 'dataBaseList',
      },
      dataSetQuery: {
        model: {
          name: '',
          code: '',
          appCode: '',
        },
      },
      appCode: '',
      appInfo: {
        serviceName: '',
        name: '',
        contextPath: '',
      },
      appName: '',
    }
  },
  created() {
    if (isMultiAppMode()) {
      if (this.$route.query.appCode) {
        this.appCode = this.$route.query.appCode
        this.dataSetQuery.model.appCode = this.appCode
        this.getMfAppInfo(this.appCode)
      }
      this.appName =
        decodeURIComponent(this.$route.query.title.split('-')[0]) + '-'
    }
  },
  provide() {
    return {
      getAppCode: () => this.appCode,
      getAppInfo: () => {
        return this.appInfo
      },
      getAppName: () => this.appName,
    }
  },
  methods: {
    handleTabClick(tab) {
      this.tabDataSet.currentTab = tab.name
      this.$refs[tab.name + 'Ref'].query()
    },
    handleQueryDataSet() {
      this.$refs[this.tabDataSet.currentTab + 'Ref'].query(
        this.dataSetQuery.model
      )
    },
    getMfAppInfo(appCode) {
      this.$http.get(MfAppAPI.MFAPP_DETAIL(), { code: appCode }).then((res) => {
        if (res.data) {
          this.appInfo = res.data
        } else {
          this.$message.info(`根据应用编码【${appCode}】无法获取应用信息`)
        }
      })
    },
  },
}
</script>

<style scoped></style>
