<template>
  <xn-dialog
    :title="title"
    width="600px"
    :show.sync="visible"
    :before-close="handleClose"
    close-on-click-modal
    top="0"
    :buttons="buttons"
    class="dlgSqlFragment"
    @save="handleSave"
    @close="handleClose"
  >
    <template slot="dialog-form">
      <el-form label-width="50px" ref="SqlFragmentListFormRef">
        <el-form-item label="名称">
          <el-input
            @input="handleNameInputChange"
            v-model.trim="name"
            style="width: 500px"
            :maxlength="200"
            placeholder="请输入名称"
            :close-on-press-escape="true"
          />
        </el-form-item>
        <xn-table :data="tableData" class="tblSqlFragment">
          <xn-col prop="refid" label="选择" width="50" align="center">
            <template slot-scope="scope">
              <el-radio
                v-model="selectRefid"
                :label="scope.row.refid"
                @change="changeRedio($event, scope.row)"
              >
                {{ '' }}
              </el-radio>
            </template>
          </xn-col>
          <xn-col prop="name" label="名称" align="left" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tooltip
                class="item"
                effect="dark"
                v-model="scope.row.showTooltip"
                placement="top"
              >
                <div slot="content">
                  {{ scope.row.namespace }}
                </div>
                <div>{{ scope.row.name }}</div>
              </el-tooltip>
            </template>
          </xn-col>
        </xn-table>
      </el-form>
    </template>
  </xn-dialog>
</template>

<script>
import { DataSetAPI } from 'src/api'
export default {
  name: 'MetaFormFormDialog',
  props: {
    title: {
      type: String,
      default: '可选数据策略',
    },
    show: {
      type: Boolean,
      default: false,
    },
    appCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      selectRefid: null,
      select: null,
      name: '',
      tableData: [],
      visible: false,
      sqlFragments: [],
      buttons: [
        { label: '确定', action: 'save', type: 'primary', event: 'save' },
        { action: 'cancel', type: 'info', event: 'close' },
      ],
    }
  },
  mounted() {
    DataSetAPI.getDataStrategySqlFragmentList(
      this.appCode,
      (result) => {
        this.sqlFragments = result
      },
      () => {
        console.warn('获取数据策略失败')
        this.sqlFragments = []
      }
    )
  },
  methods: {
    changeRedio(e, row) {
      this.select = row
    },
    handleClose() {
      this.$emit('close')
      this.visible = false
    },
    handleSave() {
      if (this.select != null) {
        this.$emit('select', this.select)
      }
      this.$emit('close')
      this.visible = false
    },
    handleNameInputChange() {
      if (this.name) {
        const tempData = []
        this.sqlFragments.forEach((item) => {
          if (
            item.refid.toLowerCase().indexOf(this.name.toLowerCase()) !== -1
          ) {
            item.showTooltip = false
            tempData.push(item)
          }
        })
        this.tableData = tempData
      } else {
        this.tableData = this.sqlFragments
      }
    },
  },
  watch: {
    show: {
      immediate: true,
      handler(val) {
        this.visible = val
      },
    },
    sqlFragments: {
      immediate: true,
      handler(val) {
        this.tableData = val
      },
    },
  },
}
</script>

<style scoped>
.tblSqlFragment ::v-deep .el-table--border {
  max-height: 300px;
}
.dlgSqlFragment ::v-deep .el-dialog {
  margin-top: 8vh !important;
}
</style>
