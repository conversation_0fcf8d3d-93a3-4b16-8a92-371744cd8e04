<template>
  <div>
    <xn-dialog
      id="dlgDataSetImport"
      ref="dlgDataSetImport"
      :title="title"
      :show.sync="dlgDataSetImport.show"
      width-size="small"
      :buttons="dlgDataSetImport.buttons"
      @save="showImportList"
      @cancel="handleDlgDataSetImportCancel"
    >
      <xn-form
        id="formDlgDataSetImport"
        ref="formDlgDataSetImportRef"
        slot="dialog-form"
        :model="formDlgDataSetImport.model"
        size="small"
        :rules="formDlgDataSetImport.rules"
      >
        <xn-form-item label="数据集模板" prop="dataSetTemplate">
          <xn-upload
            v-if="dlgDataSetImport.show"
            id="uplFormDlgDataSetImportUploader"
            ref="uplFormDlgDataSetImportUploaderRef"
            v-model="formDlgDataSetImport.model.importFile"
            mode="upload"
            accept=".zip,.json"
            @onError="importError"
          />
          <div style="color: red">
            提示：只允许上传从当前功能模块下导出的数据
          </div>
        </xn-form-item>
      </xn-form>
    </xn-dialog>

    <xn-dialog
      id="dlgDataSetTable"
      ref="dlgDataSetImport"
      title="导入数据列表"
      :show.sync="showTable"
      width-size="small"
      :buttons="buttons"
      @save="handleDlgDataSetImportSave"
      @cancel="
        () => {
          showTable = false
        }
      "
    >
      <el-tabs v-model="tabName">
        <el-tab-pane label="覆盖数据" name="overrideData"></el-tab-pane>
        <el-tab-pane label="新导入数据" name="newImportData"></el-tab-pane>
      </el-tabs>
      <el-table :data="tableData[tabName]" border>
        <el-table-column prop="code" label="编码"> </el-table-column>
        <el-table-column prop="name" label="名称"> </el-table-column>
      </el-table>
    </xn-dialog>
  </div>
</template>

<script>
import { DataSetAPI } from 'src/api'
import { isMultiAppMode } from 'src/config'
export default {
  name: 'DataSetImport',
  inject: ['getAppCode'],
  props: {
    title: {
      type: String,
      default: '导入数据集',
    },
    sourceType: {
      type: Number,
      default: 1,
    },
  },
  data: function () {
    return {
      dlgDataSetImport: {
        show: false,
        width: '435px',
        buttons: [
          { label: '确定', action: 'save', type: 'primary', event: 'save' },
          { label: '取消', action: 'cancel', type: 'info', event: 'cancel' },
        ],
      },
      formDlgDataSetImport: {
        model: {
          importFile: [],
        },
        rules: {
          dataSetTemplate: [{ validator: this.validateImport, required: true }],
        },
      },
      showTable: false,
      tabName: 'overrideData',
      tableData: {
        overrideData: [],
        newImportData: [],
      },
      buttons: [
        { label: '确定', action: 'save', type: 'primary', event: 'save' },
        { label: '取消', action: 'cancel', type: 'info', event: 'cancel' },
      ],
      importParam: {},
    }
  },
  methods: {
    importError(error) {
      this.$refs.dlgDataSetImport.displayErrorMessage(error.message)
    },
    // 显示弹框
    show() {
      this.dlgDataSetImport.show = true
    },
    // 导入弹框中，数据集的保存按钮
    showImportList(resolve, reject) {
      this.$refs.formDlgDataSetImportRef.validate((valid) => {
        if (valid) {
          const param = {
            importFile: this.formDlgDataSetImport.model.importFile,
          }
          if (isMultiAppMode()) {
            param.appCode = this.getAppCode()
          }
          this.importParam = param
          this.$easypost(DataSetAPI.DATASET_VALIDATE_IMPORT(), param).then(
            (res) => {
              resolve('导入校验成功')
              this.handleDlgDataSetImportCancel()
              this.showTable = true
              this.tableData = res
            },
            () => {
              reject()
            }
          )
          // this.$easypost(DataSetAPI.DATASET_LIST_BY_FILE(), param)
          //   .then((result) => {
          //     if (Array.isArray(result) && result.length > 0) {
          //       let dataSetName = ''
          //       const replaceDataSets = {}
          //       result.forEach((data) => {
          //         replaceDataSets[data.code] = data.id
          //         dataSetName += `【${data.name}(${data.code})】`
          //       })
          //       if (dataSetName !== '') {
          //         const h = this.$createElement
          //         this.$confirm('', '提示', {
          //           message: h(
          //             'div',
          //             {
          //               style: {
          //                 maxHeight: '400px',
          //                 overflowY: 'auto',
          //                 wordBreak: 'break-all',
          //               },
          //             },
          //             [
          //               h(
          //                 'div',
          //                 null,
          //                 '数据集' + dataSetName + '已存在，是否覆盖？'
          //               ),
          //             ]
          //           ),
          //         }).then(
          //           (res) => {
          //             this.saveDataSet(resolve, reject, replaceDataSets)
          //           },
          //           () => {
          //             reject()
          //           }
          //         )
          //       } else {
          //         this.saveDataSet(resolve, reject, null)
          //       }
          //     } else {
          //       this.saveDataSet(resolve, reject, null)
          //     }
          //   })
          //   .catch((error) => {
          //     // 禁用http请求报错的默认提示
          //     this.$message.closeAll()
          //     reject(error)
          //   })
        } else {
          reject()
        }
      })
    },
    handleDlgDataSetImportSave(resolve, reject) {
      this.$easypost(DataSetAPI.DATASET_IMPORT(), this.importParam)
        .then(() => {
          resolve('导入成功')
          this.dlgDataSetImport.show = false
          this.showTable = false
        })
        .catch((error) => {
          // 禁用http请求报错的默认提示
          this.$message.closeAll()
          reject(error)
        })
    },
    // （导入时在验证code后）保存数据
    saveDataSet(resolve, reject, replaceDataSets) {
      this.$refs.formDlgDataSetImportRef.validate((valid) => {
        if (valid) {
          const param = {
            importFile: this.formDlgDataSetImport.model.importFile,
            replaceDataSets,
          }
          if (isMultiAppMode()) {
            param.appCode = this.getAppCode()
          }
          this.$easypost(DataSetAPI.DATASET_IMPORT(), param)
            .then((result) => {
              resolve(result)
              this.handleDlgDataSetImportCancel()
              this.$emit('afterSave')
            })
            .catch((error) => {
              // 禁用http请求报错的默认提示
              this.$message.closeAll()
              reject(error)
            })
        } else {
          reject()
        }
      })
    },
    // 导入弹框中的取消按钮
    handleDlgDataSetImportCancel() {
      this.dlgDataSetImport.show = false
      this.formDlgDataSetImport.model.importFile = []
      this.$refs.uplFormDlgDataSetImportUploaderRef.clear()
      this.$refs.formDlgDataSetImportRef.clearValidate()
    },
    // 导入时的异常处理
    handleUplFormDlgDataSetImportUploaderError(error) {
      this.$refs.dlgDataSetImport.displayErrorMessage(error.message)
    },
    // 导入前验证是否上传了文件
    validateImport(rule, value, callback) {
      // 获取上传文件
      const template = this.$refs.uplFormDlgDataSetImportUploaderRef.fileList
      if (template.length === 0) {
        callback(new Error('请上传数据集模板文件'))
      } else {
        callback()
      }
    },
  },
}
</script>
