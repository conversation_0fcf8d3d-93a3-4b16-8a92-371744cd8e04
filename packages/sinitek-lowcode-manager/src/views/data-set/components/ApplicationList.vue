<template>
  <div>
    <xn-table
      id="tblApplication"
      ref="tblApplicationRef"
      :querymodel="param"
      :url="tblApplication.applicationListUrl"
      :toolbars="tblApplication.buttons"
      default-order="name:asc"
      @add="handleAdd"
      @delete="handleDelete"
      @import="handleTblApplicationImport"
      @export="handleTblApplicationExport"
    >
      <template slot="sync">
        <div>
          <sync-dialog
            ref="syncDialog"
            type="dataset"
            :app-info="appInfo"
            :ref-table="$refs.tblApplicationRef"
          />
        </div>
      </template>
      <xn-col type="selection" prop="数据集id" width="40" />
      <xn-col
        prop="code"
        label="编码"
        min-width="180"
        align="left"
        sortable="true"
        show-overflow-tooltip
      />
      <xn-col
        prop="name"
        label="名称"
        min-width="180"
        align="left"
        sortable="true"
        show-overflow-tooltip
      />
      <xn-col
        prop="requestTypeName"
        label="请求方式"
        width="160"
        align="center"
        show-overflow-tooltip
      />
      <xn-col
        prop="url"
        label="请求地址"
        min-width="400"
        align="left"
        show-overflow-tooltip
      />
      <xn-col
        prop="createTimeStamp"
        label="创建时间"
        width="180"
        align="center"
        sortable="true"
      />
      <xn-col
        prop="updateTimeStamp"
        label="更新时间"
        width="180"
        align="center"
        sortable="true"
      />
      <xn-col label="操作" min-width="140" fixed="right" align="center">
        <template slot-scope="scope">
          <xn-col-action-group :keys="scope.row">
            <xn-col-action
              v-for="(colBtn, btnIndex) in operations"
              :key="btnIndex"
              @click="handleClickOpera(scope, colBtn)"
            >
              {{ colBtn.label }}
            </xn-col-action>
          </xn-col-action-group>
        </template>
      </xn-col>
    </xn-table>
    <!-- 导入弹出框 -->
    <data-set-import
      id="dlgDataSetImport"
      :title="dlgDataSetImport.title"
      ref="dlgDataSetImportRef"
      :source-type="dlgDataSetImport.sourceType"
      @afterSave="query"
    />
    <sync-dialog
      ref="syncDialogSingle"
      type="dataset"
      :is-sync-all="false"
      :app-info="appInfo"
    />
    <copy-dialog
      ref="copyDlg"
      dlg-title="复制数据集"
      :handle-copy="handleCopySqlDataSet"
      @copy-success="handleCopySuccess"
    />
  </div>
</template>

<script>
import { DataSetAPI } from 'src/api'
import { operation } from 'sinitek-util'
import { isMultiAppMode } from 'src/config'
import sync from 'src/mixins/sync'

export default {
  name: 'ApplicationList',
  components: {
    DataSetImport: () => import('./DataSetImport'),
    CopyDialog: () => import('src/views/components/copyDialog'),
  },
  props: {
    param: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  mixins: [sync],
  inject: ['getAppCode', 'getAppName'],
  data: function () {
    return {
      tblApplication: {
        applicationListUrl: DataSetAPI.APPLICATION_DATASET_SEARCH(),
        buttons: [
          { label: '新增', type: 'add', event: 'add' },
          { label: '删除', type: 'delete', event: 'delete' },
          { label: '导入', type: 'import', event: 'import' },
          { label: '导出', type: 'export', event: 'export' },
        ],
      },
      dataSetForm: {
        title: '新增',
        sourceType: 2,
      },
      dlgDataSetImport: {
        title: '导入数据集',
        sourceType: 2,
      },
      tableRef: 'tblApplicationRef',
      operations: [
        {
          label: '编辑',
          functionName: 'handleEdit',
        },
        {
          label: '复制',
          functionName: 'copy',
        },
        {
          label: '删除',
          functionName: 'handleDelete',
        },
      ],
    }
  },
  methods: {
    copy(row) {
      this.$refs.copyDlg.show(row)
    },
    handleCopySqlDataSet({ id, code: newCode, name: newName }) {
      return new Promise(function (resolve, reject) {
        DataSetAPI.copyDataSet(
          {
            id,
            newCode,
            newName,
          },
          (res) => {
            if (res) {
              resolve()
            } else {
              reject()
            }
          },
          (error) => {
            console.warn('复制数据集失败', error)
            reject()
          }
        )
      })
    },
    handleCopySuccess() {
      this.query()
    },
    query() {
      this.$refs.tblApplicationRef.query()
    },
    handleAdd() {
      this.openDataSetEditTab('新增应用系统数据集', this.dataSetForm.sourceType)
    },
    handleEdit(row) {
      this.openDataSetEditTab(
        `${row.name}-编辑`,
        this.dataSetForm.sourceType,
        row
      )
    },
    openDataSetEditTab(title, sourceType, data) {
      const query = {
        title: this.getAppName() + title,
        sourceType,
        dataSetId: data ? data.id : undefined,
      }
      if (isMultiAppMode()) {
        query.appCode = this.getAppCode()
        query.contextPath = this.appInfo.contextPath
      }
      this.$openTab('/lowcode/dataset/edit', { query }, () => {
        this.query()
      })
    },
    // 数据集导入按钮
    handleTblApplicationImport() {
      this.dlgDataSetImport.title = '导入数据集'
      this.$refs.dlgDataSetImportRef.show()
    },
    // 数据集导出按钮
    handleTblApplicationExport(data) {
      if (data.length > 0) {
        let objids = []
        for (const i in data) {
          if (i === '0') {
            objids = [data[i].id]
          } else {
            objids.push(data[i].id)
          }
        }
        operation.download(DataSetAPI.DATASET_EXPORT(), {
          ids: objids,
          prettyPrinterFlag: true,
        })
      } else {
        this.$message.info('请选择要导出的数据集')
      }
    },
    handleDelete(row) {
      this.$confirm
        .delete()
        .then(() => {
          const ids = []
          if (Array.isArray(row)) {
            row.forEach((item) => ids.push(item.id))
          } else {
            ids.push(row.id)
          }
          const that = this
          this.$http
            .post(DataSetAPI.DATASET_DELETE(), ids)
            .then(() => {
              that.$message.deleteWithSuccess()
              that.query()
            })
            .catch((err) => {
              if (typeof err === 'object' && err && err.message) {
                that.$message.error(err.message)
              } else if (typeof err === 'string' && err) {
                that.$message.error(err)
              } else {
                that.$message.error('服务器错误，请联系管理员')
              }
            })
        })
        .catch((_) => {})
    },
  },
}
</script>

<style scoped></style>
