<template>
  <div class="app-container">
    <div class="blank-container">
      <el-form
        id="formDataSet"
        ref="formDataSetRef"
        :model="formDataSet.model"
        :rules="formDataSet.rules"
        label-width="100px"
      >
        <el-form-item label="编码" prop="code">
          <span v-if="!!formDataSet.model.id">{{
            formDataSet.model.code
          }}</span>
          <el-input
            v-else
            v-model.trim="formDataSet.model.code"
            :class="
              sourceType === DATA_SET_SOURCE_TYPE.APPLICATION
                ? 'formDataSet_item'
                : 'formDataSet_item_other_width'
            "
            :maxlength="50 - initCodeLen"
          >
            <span v-if="isMultiAppMode" slot="prepend"> {{ appCode }}- </span>
            <div
              slot="suffix"
              style="display: flex; align-items: center; height: 100%"
            >
              <span>{{ formDataSet.model.code.length + initCodeLen }}/50</span>
            </div>
          </el-input>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input
            v-model.trim="formDataSet.model.name"
            :class="
              sourceType === DATA_SET_SOURCE_TYPE.APPLICATION
                ? 'formDataSet_item'
                : 'formDataSet_item_other_width'
            "
            show-word-limit
            :maxlength="50"
          />
        </el-form-item>
        <!-- 数据源类型为‘数据库’ -->
        <template v-if="sourceType === DATA_SET_SOURCE_TYPE.DATABASE">
          <el-form-item label="SQL" prop="sql">
            <lc-editor
              language="sql"
              :model-value.sync="formDataSet.model.sql"
              width="1000px"
              height="200px"
            />
          </el-form-item>
          <el-form-item prop="orderRule">
            <span slot="label">
              排序SQL
              <el-tooltip
                class="helpTooltip"
                effect="light"
                content="例如:createtimestamp desc,version asc"
                placement="top"
              >
                <i class="el-icon-question" />
              </el-tooltip>
            </span>
            <div>
              <lc-editor
                language="sql"
                :model-value.sync="formDataSet.model.orderRule"
                width="1000px"
                height="100px"
              />
            </div>
          </el-form-item>
          <el-form-item label="查询条件" prop="conditions">
            <li
              v-for="(item, index) in formDataSet.model.conditions"
              :key="index"
              style="list-style: none"
            >
              <el-button
                plain
                circle
                type="danger"
                size="mini"
                icon="el-icon-minus"
                class="condition_btn"
                @click="handleRemoveCondition(index)"
              />
              <el-select
                v-model="item.type"
                class="condition_type"
                :popper-append-to-body="false"
              >
                <el-option
                  v-for="typeItem in formDataSet.joinTypes"
                  :key="typeItem.value"
                  :label="typeItem.label"
                  :value="typeItem.value"
                />
              </el-select>
              <lc-editor
                language="sql"
                :model-value.sync="item.sql"
                width="972px"
                height="100px"
                style="margin-top: 10px; margin-left: 30px"
              />
            </li>
            <div>
              <el-button type="text" @click="handleAddCondition()">
                <i class="el-icon-plus" />添加
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="格式化字段" prop="formats">
            <li
              v-for="(item, index) in formDataSet.model.formats"
              :key="index"
              style="
                list-style: none;
                margin-bottom: 5px;
                display: flex;
                width: 1000px;
              "
            >
              <el-button
                circle
                plain
                type="danger"
                size="mini"
                icon="el-icon-minus"
                class="condition_btn"
                @click="handleRemoveFormat(index)"
              />
              <el-input
                v-model.trim="item.field"
                placeholder="格式化字段"
                :maxlength="50"
                class="format_field"
                show-word-limit
              />
              <el-input
                v-model.trim="item.fieldFormatted"
                placeholder="展示字段"
                :maxlength="50"
                class="format_field"
                show-word-limit
              />
              <el-select
                v-model="item.type"
                style="margin-right: 5px; width: 100px; flex-shrink: 0"
                :popper-append-to-body="false"
              >
                <el-option
                  v-for="typeItem in formDataSet.formatTypes"
                  :key="typeItem.value"
                  :label="typeItem.label"
                  :value="typeItem.value"
                />
              </el-select>
              <template v-if="item.type === DATA_SET_FORMAT_TYPE.ENUM">
                <el-input
                  v-model.trim="item.catalog"
                  placeholder="模块名称"
                  :maxlength="50"
                  class="format_field"
                  show-word-limit
                />
                <el-input
                  v-model.trim="item.name"
                  :maxlength="50"
                  placeholder="枚举类型"
                  class="format_field"
                  show-word-limit
                />
              </template>
              <template v-if="item.type === DATA_SET_FORMAT_TYPE.DATE">
                <el-input
                  v-model.trim="item.format"
                  :maxlength="30"
                  placeholder="日期格式"
                  show-word-limit
                  class="format_field"
                />
                <el-input style="visibility: hidden" class="format_field" />
              </template>
              <template v-if="item.type === DATA_SET_FORMAT_TYPE.EMPLOYEE">
                <el-input style="visibility: hidden" class="format_field" />
                <el-input style="visibility: hidden" class="format_field" />
              </template>
            </li>
            <div>
              <el-button type="text" @click="handleAddFormat()">
                <i class="el-icon-plus" />添加
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="解析结果" prop="parseResult">
            <el-button type="text" @click="handleParseResult"> 解析 </el-button>
            <li
              v-for="(item, index) in formDataSet.model.parseResult"
              :key="index"
              style="
                list-style: none;
                margin-bottom: 5px;
                display: flex;
                width: 1000px;
              "
            >
              <el-button
                circle
                plain
                type="danger"
                size="mini"
                icon="el-icon-minus"
                class="condition_btn"
                @click="handleRemoveParseResult(index)"
              />
              <el-form-item
                :prop="`parseResult.${index}.name`"
                :rules="{
                  required: true,
                  trigger: ['change', 'blur'],
                  message: '请输入字段名',
                }"
                class="ml5"
              >
                <el-input
                  v-model.trim="item.name"
                  placeholder="字段名"
                  :maxlength="50"
                  class="format_dataType"
                  show-word-limit
                  @blur="handleNameblur(item)"
                />
              </el-form-item>
              <el-form-item
                :prop="`parseResult.${index}.dataType`"
                :rules="{
                  required: true,
                  trigger: ['change', 'blur'],
                  message: '请输入数据类型',
                }"
                class="ml5"
              >
                <el-input
                  v-model.trim="item.dataType"
                  placeholder="数据类型"
                  :maxlength="50"
                  class="format_dataType"
                  show-word-limit
                />
              </el-form-item>
              <el-form-item
                :prop="`parseResult.${index}.comments`"
                :rules="{
                  required: true,
                  trigger: ['change', 'blur'],
                  message: '请输入字段注释',
                }"
                class="ml5"
              >
                <el-input
                  v-model.trim="item.comments"
                  placeholder="字段注释"
                  :maxlength="100"
                  class="format_dataType"
                  show-word-limit
                />
              </el-form-item>
              <el-form-item class="ml5">
                <el-input
                  v-model.trim="item.group"
                  placeholder="字段分组"
                  :maxlength="50"
                  class="format_dataType"
                  show-word-limit
                />
              </el-form-item>
            </li>
            <div>
              <el-button type="text" @click="handleAddParseResult()">
                <i class="el-icon-plus" />添加
              </el-button>
            </div>
          </el-form-item>
        </template>

        <!-- 数据源类型为‘应用系统’ -->
        <template v-if="sourceType === 2">
          <el-form-item label="URL" prop="url">
            <el-input
              v-model.trim="formDataSet.model.url"
              class="dialog-input formDataSet_item"
              :maxlength="300"
              :show-word-limit="true"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="请求方式" prop="requestType">
            <el-radio-group v-model="formDataSet.model.requestType">
              <el-radio :label="1"> POST </el-radio>
              <el-radio :label="2"> GET </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="param">
            <span slot="label">
              请求参数
              <el-tooltip class="icon_question" effect="light" placement="top">
                <div slot="content">
                  必须为JSON对象格式<br />
                  使用`#{key}`可以引用动态值,其中`key`可以为请求数据集接口({{
                    DATASET_LOAD_DATA
                  }})参数中`formDatas`里面的所有`key`。<br />
                  使用`#{_root}`可以引用请求数据集时的参数,例如`#{_root.accessToken}`代表请求数据集接口参数中的token数据。
                </div>
                <i class="el-icon-question" />
              </el-tooltip>
            </span>
            <lc-editor
              language="javascript"
              :model-value.sync="formDataSet.model.param"
              height="200px"
              width="560px"
            />
          </el-form-item>
        </template>
        <el-form-item style="margin-bottom: 20px">
          <el-button type="primary" size="small" @click="handleSave">
            保存
          </el-button>
          <el-button size="small" @click="handleClose"> 取消 </el-button>
          <div class="dataset-edit-bottom-bar" />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { DataSetAPI } from 'src/api'
import {
  validateSql,
  handleErrorMessage,
  sqlValidate,
  validateCode,
} from 'src/utils'
import {
  MAX_CONDITION_SQL_STRING_LENGTH,
  MAX_CONDITIONS_SQL_STRING_TOTAL_LENGTH,
  MAX_RELATED_QUERY_SQL_STRING_LENGTH,
  MAX_RELATED_QUERY_STRING_TOTAL_LENGTH,
  DATA_SET_FORMAT_TYPE,
  DATA_SET_FORMAT_OPTIONS,
  DATA_SET_SOURCE_TYPE,
} from 'src/constant'
import { isMultiAppMode } from 'src/config'
import { log } from 'sinitek-lowcode-shared'

export default {
  name: 'LcDataSetEdit',
  data() {
    return {
      DATA_SET_FORMAT_TYPE: Object.freeze(DATA_SET_FORMAT_TYPE),
      DATASET_LOAD_DATA: Object.freeze(DataSetAPI.DATASET_LOAD_DATA()),
      DATA_SET_SOURCE_TYPE: Object.freeze(DATA_SET_SOURCE_TYPE),
      sourceType: 1,
      formDataSet: {
        rules: {
          name: [{ required: true, message: '请填写名称' }],
          code: [
            { required: true, trigger: 'blur', message: '请填写编码' },
            {
              validator: validateCode,
              trigger: ['change', 'blur'],
              message:
                '数据集编码必须以字母开头，由英文字母、数字、下划线（_）、连字符（-）组成',
            },
          ],
          // sourceId: [{required: true, message: '请选择数据源'}],
          sql: [
            { required: true, message: '请填写sql语句' },
            {
              validator: validateSql,
              trigger: 'blur',
              message: '仅支持输入select语句',
            },
          ],
          url: [{ required: true, message: '请填写URL' }],
          conditions: [{ validator: this.validateConditions }],
          orderRule: [{ validator: this.validateOrderRule }],
          formats: [{ validator: this.validateFormats, trigger: 'blur' }],
          'config.relatedQuery': [{ validator: this.validateRelatedQuery }],
          param: [{ validator: this.validateParams }],
        },
        model: {
          appCode: '',
          id: '',
          code: '',
          name: '',
          // sourceId: '',
          sql: '',
          url: '',
          requestType: 1,
          param: '{}',
          conditions: [],
          formats: [],
          parseResult: [],
          orderRule: '',
          config: {
            relatedQuery: [],
            parseResult: [],
          },
        },
        joinTypes: [
          { label: '默认拼接', value: 1 },
          { label: '不为空时拼接', value: 2 },
        ],
        formatTypes: Object.freeze(DATA_SET_FORMAT_OPTIONS),
        relatedQueryTypes: Object.freeze([
          { label: '无', value: 'NONE' },
          ...DATA_SET_FORMAT_OPTIONS,
        ]),
        completions: [],
        // sourceList: []
      },
      sqlFragmentList: null,
      appCode: '',
      isMultiAppMode: isMultiAppMode(),
      btnDisabled: false,
      contextPath: '',
      initCodeLen: 0,
    }
  },
  watch: {
    'formDataSet.model.sql': function (val) {
      if (val) {
        this.handleAnalyseSql(val)
      }
    },
  },
  created() {
    const param = this.$route.query
    if (param.sourceType) {
      this.sourceType = parseInt(param.sourceType)
    }
    if (param.appCode) {
      this.appCode = param.appCode
      this.initCodeLen = this.appCode.length + 1
      this.formDataSet.model.appCode = param.appCode
    }
    if (this.sourceType === DATA_SET_SOURCE_TYPE.DATABASE) {
      this.getSqlFragmentList()
    }
    if (param.contextPath) {
      this.contextPath = decodeURIComponent(param.contextPath)
    }
    this.initData(param.dataSetId)
  },
  methods: {
    getSqlFragmentList(resolve, reject) {
      DataSetAPI.getSqlFragmentList(
        this.appCode,
        (result) => {
          this.sqlFragmentList = result
          resolve && resolve(this.sqlFragmentList)
        },
        () => {
          this.sqlFragmentList = []
          reject && reject()
        }
      )
    },
    validateConditions(rule, value, callback) {
      let errmsg = ''
      for (let i = 0; i < value.length; i++) {
        const item = value[i]
        if (!item.sql) {
          errmsg = `第${i + 1}个查询条件的SQL不能为空`
          break
        } else {
          if (item.sql.length > MAX_CONDITION_SQL_STRING_LENGTH) {
            errmsg = `第${i + 1}个查询条件的SQL不能超过${MAX_CONDITION_SQL_STRING_LENGTH}个字符`
          }
        }
      }
      if (errmsg.length === 0) {
        if (
          JSON.stringify(value).length > MAX_CONDITIONS_SQL_STRING_TOTAL_LENGTH
        ) {
          errmsg = `查询SQL总长度不能超过${MAX_CONDITIONS_SQL_STRING_TOTAL_LENGTH}个字符`
        }
      }
      if (errmsg.length > 0) {
        callback(new Error(errmsg))
      } else {
        callback()
      }
    },
    checkRelatedQueryItemConfig(config, index) {
      const configItemIndex = index + 1
      let errmsg = ''
      if (Array.isArray(config)) {
        for (let i = 0; i < config.length; i++) {
          const configItem = config[i]
          const itemIndex = i + 1
          if (!configItem.field) {
            errmsg = `第${configItemIndex}个子查询的第${itemIndex}个格式化字段不能为空`
            break
          }
          if (!configItem.fieldFormatted) {
            errmsg = `第${configItemIndex}个子查询的第${itemIndex}个展示字段不能为空`
            break
          }
          if (configItem.type === DATA_SET_FORMAT_TYPE.ENUM) {
            if (!configItem.enumCatalog) {
              errmsg = `第${configItemIndex}个子查询的第${itemIndex}个模块名称不能为空`
              break
            }
            if (!configItem.enumType) {
              errmsg = `第${configItemIndex}个子查询的第${itemIndex}个枚举类型不能为空`
              break
            }
          }
          if (configItem.type === DATA_SET_FORMAT_TYPE.DATE) {
            if (!configItem.format) {
              errmsg = `第${configItemIndex}个子查询的第${itemIndex}个日期格式化不能为空`
              break
            }
          }
        }
      } else {
        log('子查询配置', config, '不是一个数组')
        errmsg = '子查询配置不正确'
      }
      return errmsg
    },
    validateRelatedQuery(rule, value, callback) {
      const valueIsValid =
        typeof value !== 'undefined' && typeof value.length !== 'undefined'
      let errmsg = ''
      if (valueIsValid) {
        for (let i = 0; i < value.length; i++) {
          const item = value[i]
          if (!item.sql) {
            errmsg = `第${i + 1}个子查询的SQL不能为空`
            break
          } else {
            if (item.sql.length > MAX_RELATED_QUERY_SQL_STRING_LENGTH) {
              errmsg = `第${i + 1}个子查询的SQL不能超过${MAX_RELATED_QUERY_SQL_STRING_LENGTH}个字符`
              break
            }
          }
          // 校验配置
          const config = item.config
          errmsg = this.checkRelatedQueryItemConfig(config, i)
          if (errmsg.length > 0) {
            break
          }
        }
      }
      if (errmsg.length === 0) {
        if (
          valueIsValid &&
          JSON.stringify(value).length > MAX_RELATED_QUERY_STRING_TOTAL_LENGTH
        ) {
          errmsg = `查询SQL总长度不能超过${MAX_RELATED_QUERY_STRING_TOTAL_LENGTH}个字符`
        }
      }
      if (errmsg.length > 0) {
        callback(new Error(errmsg))
      } else {
        callback()
      }
    },
    validateOrderRule(rule, value, callback) {
      let errmsg = ''
      if (value) {
        if (value.length > 100) {
          errmsg = '排序规则不能超过100个字符'
        }
        if (errmsg.length > 0) {
          callback(new Error(errmsg))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    validateParams(rule, value, callback) {
      try {
        JSON.parse(value)
        callback()
      } catch {
        callback(new Error('请求参数格式不正确'))
      }
    },
    validateFormats(rule, value, callback) {
      let index = -1
      let label = ''
      for (let i = 0; i < value.length; i++) {
        const item = value[i]
        if (!item.field) {
          index = i
          label = '格式化字段'
          break
        }
        if (!item.fieldFormatted) {
          index = i
          label = '展示字段'
          break
        }
        if (item.type === DATA_SET_FORMAT_TYPE.ENUM) {
          if (!item.catalog) {
            index = i
            label = '模块名称'
            break
          }
          if (!item.name) {
            index = i
            label = '枚举类型'
            break
          }
        }
        if (item.type === DATA_SET_FORMAT_TYPE.DATE) {
          if (!item.format) {
            index = i
            label = '日期格式化'
            break
          }
        }
      }
      if (index !== -1) {
        callback(new Error(`第${index + 1}个格式化字段的${label}不能为空`))
      } else {
        callback()
      }
    },
    initData(dataSetId) {
      if (dataSetId) {
        this.$http
          .get(DataSetAPI.DATASET_DETAIL(), { id: dataSetId })
          .then((res) => {
            const data = res.data
            this.formDataSet.model = { ...this.formDataSet.model, ...data }
            // 这一代码块主要是为了：将app应用前缀从formDataSet.model.code中切分出来（之所以这样设计是因为编码的应用前缀不能修改，只能修改剩余部分）
            // code应用前缀的格式：app应用编码-（其余可编辑部分）
            // 要是this.appCode为空，则报错（不能保存页面）
            // 要是code中不包含应用前缀，则代表该条数据是脏数据（处理方法是编辑时自动为它加上appCode）
            // 拆分code
            if (this.isMultiAppMode) {
              if (this.formDataSet.model?.code && this.appCode) {
                // code存在且不为空
                if (this.formDataSet.model.code.startsWith(this.appCode)) {
                  this.formDataSet.model.code =
                    this.formDataSet.model.code.slice(this.appCode.length + 1)
                }
              } else {
                // 这里是为了防止从接口中获得的data.model中的code不存在或者为undefined的情况
                this.formDataSet.model.code = ''
              }
            }
            if (data.param) {
              this.formDataSet.model.param = data.param
            }
            // orderRule 为 null 导致 代码编辑器 报错
            if (!data.orderRule) {
              this.formDataSet.model.orderRule = ''
            }
            if (data.conditions) {
              try {
                this.formDataSet.model.conditions = JSON.parse(data.conditions)
              } catch {
                console.warn('数据集条件格式异常', data.conditions)
                this.formDataSet.model.conditions = []
              }
            } else {
              this.formDataSet.model.conditions = []
            }
            if (data.formats) {
              try {
                this.formDataSet.model.formats = JSON.parse(data.formats)
              } catch {
                console.warn('数据集格式化配置格式异常', data.conditions)
                this.formDataSet.model.formats = []
              }
            } else {
              this.formDataSet.model.formats = []
            }
            if (data.config) {
              try {
                this.formDataSet.model.config = JSON.parse(data.config)
                if (this.formDataSet.model.config.parseResult) {
                  this.formDataSet.model.config.parseResult = JSON.parse(
                    this.formDataSet.model.config.parseResult
                  )
                }
                if (
                  !Array.isArray(this.formDataSet.model.config.relatedQuery)
                ) {
                  this.$set(this.formDataSet.model.config, 'relatedQuery', [])
                }
                if (!Array.isArray(this.formDataSet.model.config.parseResult)) {
                  this.formDataSet.model.parseResult = []
                } else {
                  this.formDataSet.model.parseResult =
                    this.formDataSet.model.config.parseResult
                }
              } catch {
                console.warn('数据集配置格式异常', data.config)
                this.$set(this.formDataSet.model.config, 'relatedQuery', [])
                this.formDataSet.model.parseResult = []
              }
            } else {
              this.$set(this.formDataSet.model, 'config', {})
              this.formDataSet.model.parseResult = []
            }

            this.$nextTick(() => {
              this.$refs.formDataSetRef.clearValidate()
            })
          })
      } else {
        this.formDataSet.model = {
          appCode: this.appCode,
          id: '',
          code: '',
          name: '',
          sql: '',
          url: this.contextPath,
          requestType: 1,
          param: '{}',
          conditions: [],
          formats: [],
          orderRule: '',
          parseResult: [],
          config: {
            relatedQuery: [],
            parseResult: [],
          },
        }
        this.$nextTick(() => {
          this.$refs.formDataSetRef.clearValidate()
        })
      }
    },
    handleAnalyseSql: function (sql) {
      sql = sql || this.formDataSet.model.sql
      if (sqlValidate(sql)) {
        this.$http
          .post(DataSetAPI.SQL_ANALYSE(), { sql, appCode: this.appCode })
          .then((result) => {
            this.formDataSet.completions = []
            result.data.forEach((item) => {
              const temp = {
                caption: '', // 显示的名称，‘奖金’
                value: '', // 插入的值，‘100’
                score: 1000, // 分数，越大的排在越上面
                meta: '', // 描述，‘我的常量’
              }
              temp.caption = item.name + '(' + item.comments + ')'
              temp.value = item.name
              temp.meta = item.tableName
              this.formDataSet.completions.push(temp)
            })
          })
      }
    },
    handleSave() {
      if (this.btnDisabled) {
        return
      }
      if (this.isMultiAppMode && !this.appCode) {
        this.$message.error('缺少应用数据，无法保存')
        return
      }
      this.$refs.formDataSetRef.validate((valid) => {
        if (valid) {
          this.btnDisabled = true
          this.formDataSet.model.sourceType = this.sourceType
          this.formDataSet.model.config.parseResult = JSON.stringify(
            this.formDataSet.model.parseResult
          )
          const form = { ...this.formDataSet.model }
          // 组合code
          if (this.isMultiAppMode) {
            form.code = this.appCode + '-' + this.formDataSet.model.code
          }

          // 数据库数据集解析提供POST方法
          this.$http
            .post(DataSetAPI.DATASET_SAVE(), form)
            .then(() => {
              this.$message.success('保存成功')
              this.handleClose()
            })
            .catch((error) => {
              this.btnDisabled = false
              handleErrorMessage(this, error)
            })
        } else {
          console.error('数据集校验不通过')
        }
      })
    },
    handleClose() {
      // bug:288644 快速多次点击保存按钮--编辑页面和表单管理页面都关闭了
      // this.$closeTab(this.currentRoute)
      // this.$closeTab中不加this.currentRoute参数，加了会导致在兼容菜单返回不了
      this.$closeTab()
    },
    handleRemoveCondition(index) {
      if (this.formDataSet.model.conditions.length >= 1) {
        this.formDataSet.model.conditions.splice(index, 1)
      }
      this.$refs.formDataSetRef.clearValidate()
    },
    handleAddCondition() {
      this.formDataSet.model.conditions.push({ sql: '', type: 2 })
      const index =
        this.formDataSet.model.conditions.length - 1 > -1
          ? this.formDataSet.model.conditions.length - 1
          : 0
      // 给条件SQL输入框添加数据表字段自动提示
      this.$nextTick(() => {
        const arr = this.$refs.dataSetConditionSql
        if (Array.isArray(arr) && arr.length > 0) {
          const vmItem = arr[index]
          if (vmItem && vmItem.setCompletions) {
            vmItem.setCompletions(this.formDataSet.completions)
          }
        } else {
          if (arr && arr.setCompletions) {
            arr.setCompletions(this.formDataSet.completions)
          }
        }
      })
    },
    handleRemoveFormat(index) {
      if (this.formDataSet.model.formats.length >= 1) {
        this.formDataSet.model.formats.splice(index, 1)
      }
      this.$refs.formDataSetRef.clearValidate()
    },
    handleRemoveRelatedQuery(index) {
      this.formDataSet.model.config.relatedQuery.splice(index, 1)
    },
    handleRemoveInnerConfigInRelatedQuery(index, innerIndex) {
      this.formDataSet.model.config.relatedQuery[index].config.splice(
        innerIndex,
        1
      )
    },
    handleAddFormat() {
      this.formDataSet.model.formats.push({
        // 数据字段
        field: '',
        // 格式化后字段
        fieldFormatted: '',
        // 格式化类型
        type: DATA_SET_FORMAT_TYPE.ENUM,
        // 枚举catalog
        catalog: '',
        // 因为type被视为格式化类型,所以使用name代表枚举type
        name: '',
        // 日期格式
        format: 'yyyy-MM-dd',
      })
    },
    // 解析
    handleParseResult() {
      if (
        this.formDataSet.model.parseResult &&
        this.formDataSet.model.parseResult.length > 0
      ) {
        this.$confirm('再次解析会覆盖现有解析结果，确定要解析吗？')
          .then(() => {
            this.getParseResult()
          })
          .catch(() => {})
      } else {
        this.getParseResult()
      }
    },
    getParseResult() {
      const sql = this.formDataSet.model.sql
      this.$http
        .post(DataSetAPI.SQL_ANALYSE(), { sql, appCode: this.appCode })
        .then((result) => {
          if (result.data && result.data.length > 0) {
            this.formDataSet.model.parseResult = result.data
          } else {
            this.formDataSet.model.parseResult = []
          }
        })
    },
    // 删除解析结果
    handleRemoveParseResult(index) {
      if (this.formDataSet.model.parseResult.length >= 1) {
        this.formDataSet.model.parseResult.splice(index, 1)
      }
      this.$refs.formDataSetRef.clearValidate()
    },
    handleNameblur(item) {
      // 手动添加时,code字段与name一致
      if (item.isAdd) {
        item.code = item.name
      }
    },
    // 新增解析结果
    handleAddParseResult() {
      this.formDataSet.model.parseResult.push({
        name: '', // 字段名
        isAdd: true, // 是否是手动新增
        code: '', // 字段名(手动添加时,该字段与名称一致)
        alias: '', // 查询时列别名(手动添加时,允许该字段不存在)
        dataType: '', // 数据类型
        comments: '', // 字段注释
        group: '', // 字段分组(解析数据的分组为空)
        tableName: '', // 表名(手动添加时,允许该字段不存在)
        tableAlias: '', // 查询时别名(手动添加时,允许该字段不存在)
      })
    },
    handleAddRelatedQuery() {
      const relatedQuery = this.formDataSet.model.config.relatedQuery
      Array.isArray(relatedQuery) &&
        relatedQuery.push({
          // 子查询sql
          sql: '',
          // 格式化配置
          config: [],
        })
    },
    handleAddInnerConfigInRelatedQuery(index) {
      this.formDataSet.model.config.relatedQuery[index].config.push({
        // 数据字段
        field: '',
        // 格式化后字段
        fieldFormatted: '',
        // 格式化类型
        type: DATA_SET_FORMAT_TYPE.ENUM,
        // 枚举catalog
        enumCatalog: '',
        // 枚举type
        enumType: '',
        // 日期格式
        format: 'yyyy-MM-dd',
      })
    },
  },
}
</script>

<style scoped>
.contextmenu {
  margin: 0;
  background: #fff;
  z-index: 3000;
  position: fixed;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
}
.contextmenu li {
  margin: 0;
  padding: 7px 16px;
  cursor: pointer;
}
.contextmenu li:hover {
  background-color: rgb(3, 125, 243);
  color: white;
}
.formDataSet_item {
  width: 560px !important;
}
.condition_sql {
  width: 250px;
  margin-right: 5px;
}
.condition_type {
  width: 150px !important;
}
.condition_btn {
  padding: 3px;
  margin-right: 10px;
  height: 28px;
  width: 28px;
  flex-shrink: 0;
}
.format_field {
  /* width: 150px !important; */
  margin-right: 5px;
}
.li-style {
  list-style: none;
  margin-bottom: 5px;
  display: flex;
}
.data-set-sql-span {
  color: gray;
  font-weight: bold;
}
.helpTooltip {
  color: #409eff;
}
.dataset-edit-bottom-bar {
  width: 100%;
  height: 60px;
}
.related-query-item {
  font-size: small;
  flex: 1;
}
.related-query-item-sql {
  margin-top: 2px;
  margin-left: 2px;
  margin-bottom: 5px;
}
.related-query-li-style {
  list-style: none;
  margin-bottom: 5px;
  display: flex;
}

.related-query-item-li-style {
  list-style: none;
  margin-bottom: 5px;
  display: flex;
  width: 972px;
}
.formDataSet_item_other_width {
  width: 1000px !important;
}
.cursor {
  cursor: pointer;
}
.parse-result-wrap {
  display: flex;
}
.parse-result-content {
  list-style: none;
  margin-bottom: 5px;
  margin-left: 5px;
}
.condition_btn_delete {
  margin-left: 5px;
}
.parse-result-input {
  min-width: 120px;
}
::v-deep .format_dataType {
  width: 200px !important;
}
.ml5 {
  margin-left: 5px;
}
</style>
