<template>
  <xn-dialog
    title="生成菜单"
    :show.sync="menuWindow"
    width="800px"
    :buttons="menuButtons"
    @saveMenu="saveMenu"
    @closeMenu="closeMenu"
  >
    <el-tabs
      slot="dialog-form"
      v-model="menuData.type"
      :before-leave="handleMenuTypeChange"
      type="card"
    >
      <el-tab-pane label="PC" name="1">
        <el-button
          style="margin-bottom: 10px"
          type="text"
          class="el-icon-plus"
          @click="handleAddMenu"
        >
          新增
        </el-button>
        <div style="display: flex; flex-wrap: wrap">
          <template v-for="(menuRela, index) in tempMenuRelaList">
            <el-card v-if="menuRela.type === 1" :key="index" class="box-card">
              <div slot="header">
                <span>{{
                  menuRela.home && menuRela.homeName
                    ? menuRela.homeName
                    : menuData.data.name
                }}</span>
                <el-button
                  style="float: right; padding: 3px 0"
                  type="text"
                  class="el-icon-delete"
                  @click="handleDeleteMenu(index)"
                >
                  删除
                </el-button>
              </div>
              <el-form
                ref="pcMenuRef"
                :key="'pcForm_' + index"
                :model="menuRela"
                :rules="{
                  home: [
                    {
                      required: true,
                      trigger: ['blur', 'change'],
                      message: '请选择首页',
                    },
                    {
                      trigger: ['blur', 'change'],
                      validator: validateHome,
                      message: '请选择首页',
                    },
                    {
                      trigger: ['blur', 'change'],
                      validator: validateHomeRepeat,
                      message: '首页已存在',
                    },
                  ],
                  parentIds: [
                    {
                      required: true,
                      trigger: ['blur', 'change'],
                      message: '请选择父级菜单',
                    },
                  ],
                }"
                label-width="30%"
                size="small"
              >
                <el-form-item label="首页" prop="home">
                  <el-select
                    v-if="menuData.data.children"
                    v-model="menuRela.home"
                    value-key="code"
                    clearable
                    :popper-append-to-body="true"
                    @change="
                      (item) => {
                        handleHomeChange(item, menuRela)
                      }
                    "
                  >
                    <el-option
                      v-for="item in pcFormMenuOptions"
                      :key="item.id"
                      :disabled="item.disabled"
                      :value="item"
                      :label="item.name"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="父级菜单" prop="parentIds">
                  <el-cascader
                    v-model="menuRela.parentIds"
                    expand-trigger="click"
                    :options="menuList"
                    :show-all-levels="false"
                    clearable
                    change-on-select
                    @change="
                      (value) => {
                        handleParentChange(value, menuRela)
                      }
                    "
                  />
                </el-form-item>
              </el-form>
            </el-card>
          </template>
        </div>
      </el-tab-pane>
      <el-tab-pane label="APP" name="2">
        <el-button
          style="margin-bottom: 10px"
          type="text"
          class="el-icon-plus"
          @click="handleAddMenu"
        >
          新增
        </el-button>
        <div style="display: flex; flex-wrap: wrap">
          <template v-for="(menuRela, index) in tempMenuRelaList">
            <el-card v-if="menuRela.type === 2" :key="index" class="box-card">
              <div
                slot="header"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                "
              >
                <span>{{
                  menuRela.home && menuRela.home.name
                    ? menuRela.home.name
                    : menuData.data.name
                }}</span>
                <el-button
                  style="float: right; padding: 3px 0"
                  type="text"
                  class="el-icon-delete"
                  @click="handleDeleteMenu(index)"
                >
                  删除
                </el-button>
              </div>
              <el-form
                ref="appMenuRef"
                :key="'appForm_' + index"
                :model="menuRela"
                :rules="{
                  home: [
                    {
                      required: true,
                      trigger: ['blur', 'change'],
                      message: '请选择首页',
                    },
                    {
                      trigger: ['blur', 'change'],
                      validator: validateHome,
                      message: '请选择首页',
                    },
                    {
                      trigger: ['blur', 'change'],
                      validator: validateHomeRepeat,
                      message: '首页已存在',
                    },
                  ],
                  importFile: [
                    {
                      required: true,
                      trigger: ['blur', 'change'],
                      message: '请上传图标',
                    },
                    {
                      validator: validateFile,
                      trigger: ['blur', 'change'],
                      message: '请上传图标',
                    },
                  ],
                }"
                label-width="
                25%"
                size="small"
              >
                <el-form-item label="首页" prop="home">
                  <el-select
                    v-if="menuData.data.children"
                    v-model="menuRela.home"
                    value-key="code"
                    clearable
                    :popper-append-to-body="true"
                    @change="
                      (item) => {
                        handleHomeChange(item, menuRela)
                      }
                    "
                  >
                    <el-option
                      v-for="item in appFormMenuOptions"
                      :key="item.id"
                      :disabled="item.disabled"
                      :value="item"
                      :label="item.name"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="图标类型" prop="iconType">
                  <el-select
                    v-model="menuRela.iconType"
                    placeholder="请选择图标类型"
                    :popper-append-to-body="false"
                  >
                    <el-option label="banner" value="banner" />
                    <el-option label="menu" value="menu" />
                  </el-select>
                </el-form-item>
                <el-form-item label="图标" prop="importFile">
                  <xn-upload
                    v-if="menuWindow"
                    id="upldMbIcon"
                    ref="upldMbIconRef"
                    v-model="menuRela.importFile"
                    mode="upload"
                    :limit="1"
                    :multi="false"
                    accept=".png,.jpg"
                    :source-id="menuRela.upldMbIcon.sourceId"
                    show-warning
                    :source-entity="menuRela.upldMbIcon.sourceEntity"
                    @onError="handleUpldMbIconError"
                    @onSuccess="handleUpldMbIconSuccess($event, index)"
                    @onRemove="handleUpldMbIconRemove($event, index)"
                  />
                </el-form-item>
              </el-form>
            </el-card>
          </template>
        </div>
      </el-tab-pane>
    </el-tabs>
  </xn-dialog>
</template>

<script>
import { ModuleConstant } from 'src/constant'
import { ModuleAPI } from 'src/api'
export default {
  name: 'MenuSaveDialog',
  props: {
    appCode: {
      type: String,
      default: '',
    },
  },
  data: function () {
    return {
      menuWindow: false,
      menuDataRules: {
        home: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: '请选择首页',
          },
          {
            trigger: ['blur', 'change'],
            validator: this.validateHome,
            message: '请选择首页',
          },
          {
            trigger: ['blur', 'change'],
            validator: this.validateHomeRepeat,
            message: '首页已存在',
          },
        ],
        parentIds: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: '请选择父级菜单',
          },
        ],
      },
      appMenuDataRules: {
        home: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: '请选择首页',
          },
          {
            trigger: ['blur', 'change'],
            validator: this.validateHome,
            message: '请选择首页',
          },
          {
            trigger: ['blur', 'change'],
            validator: this.validateHomeRepeat,
            message: '首页已存在',
          },
        ],
        importFile: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: '请上传图标',
          },
          {
            validator: this.validateFile,
            trigger: ['blur', 'change'],
            message: '请上传图标',
          },
        ],
      },
      menuData: {
        data: {},
        type: '1',
      },
      pcMenuData: {
        home: {
          code: '',
          name: '',
        },
        homeCode: '',
        parentId: '',
        menuId: '',
        type: 1,
      },
      appMenuData: {
        home: {
          code: '',
          name: '',
        },
        menuId: '',
        type: 2,
        iconType: 'menu',
        importFile: {},
        upldMbIcon: {
          sourceId: '',
          sourceEntity: 'SIRM_MOBILE_ICON',
        },
      },
      menuButtons: [
        { action: 'save', type: 'primary', event: 'saveMenu' },
        { action: 'cancel', type: 'info', event: 'closeMenu' },
      ],
      menuList: [],
      menuRelaList: [],
      tempMenuRelaList: [],
      deleteMenuList: [],
    }
  },
  computed: {
    appMenuList: function () {
      return this.tempMenuRelaList.filter((item) => {
        return item.type === 2
      })
    },
    pcMenuList: function () {
      return this.tempMenuRelaList.filter((item) => {
        return item.type === 1
      })
    },
    pcFormMenuOptions() {
      const moduleDatas = this.menuData.data.children
      let pcMenuData = []
      for (let i = 0; i < moduleDatas.length; i++) {
        pcMenuData = moduleDatas[i]
        if (pcMenuData.dataType === ModuleConstant.TREE_TYPE_PC_FORM) {
          break
        }
      }
      return pcMenuData.children
    },
    appFormMenuOptions() {
      const moduleDatas = this.menuData.data.children
      let pcMenuData = []
      for (let i = 0; i < moduleDatas.length; i++) {
        pcMenuData = moduleDatas[i]
        if (pcMenuData.dataType === ModuleConstant.TREE_TYPE_MOBILE_FORM) {
          break
        }
      }
      return pcMenuData.children
    },
  },
  methods: {
    validateHome(rule, value, callback) {
      if (!value || value.code.length <= 0) {
        callback(new Error(rule.message))
      }
      callback()
    },
    // 不可选择重复首页
    validateHomeRepeat(rule, value, callback) {
      let index = 0
      this.tempMenuRelaList.forEach((item) => {
        if (item.homeCode === value.code) {
          index += 1
        }
      })
      index > 1 ? callback(new Error(rule.message)) : callback()
    },
    validateFile(rule, value, callback) {
      if (!value || value.uploadFileList?.length <= 0) {
        callback(new Error(rule.message))
      }
      callback()
    },
    show() {
      this.menuWindow = true
      this.tempMenuRelaList = []
      // 请求模块菜单数据
      this.$http
        .get(ModuleAPI.MODULE_MENU_DETAIL(), {
          moduleCode: this.menuData.data.code,
        })
        .then((res) => {
          this.menuRelaList = res.data
          this.setTempMenuList()
        })
    },
    // 设置临时菜单列表数据
    setTempMenuList() {
      if (this.menuRelaList.length > 0) {
        this.tempMenuRelaList = [...this.menuRelaList]
        this.tempMenuRelaList.forEach((item) => {
          if (item.type === 1) {
            if (item.parentIds && item.parentIds.length > 0) {
              item.parentIds.reverse()
              item.parentId = item.parentIds[item.parentIds.length - 1]
            }
          } else if (item.type === 2) {
            this.$set(item, 'upldMbIcon', { ...this.appMenuData.upldMbIcon })
            item.upldMbIcon.sourceId = item.menuId
          }
          this.$set(item, 'home', { code: item.homeCode, name: item.homeName })
        })
      }
    },
    handleMenuTypeChange(active, oldActive) {
      const that = this
      return new Promise(function (resolve, reject) {
        // 切换前当前数据会丢失
        that
          .$confirm('离开将不保存本页修改', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
          })
          .then(() => {
            // 确认
            that.deleteMenuList = []
            that.tempMenuRelaList = that.tempMenuRelaList.filter((item) => {
              return item.type + '' === active
            })
            that.tempMenuRelaList = that.tempMenuRelaList.concat(
              that.menuRelaList.filter((item) => {
                return item.type + '' === oldActive
              })
            )
            resolve()
          })
          .catch((err) => {
            // 取消或出错
            reject(err)
          })
      })
    },
    clearMenuValidate() {
      const refs = this.getCurrentMenuRefs()
      refs.forEach((item) => {
        item.clearValidate()
      })
    },
    getCurrentMenuRefs() {
      let refs = []
      switch (this.menuData.type) {
        case '1': {
          if (this.$refs.pcMenuRef) {
            refs = refs.concat(this.$refs.pcMenuRef)
          }
          break
        }
        case '2': {
          if (this.$refs.appMenuRef) {
            refs = refs.concat(this.$refs.appMenuRef)
          }
          break
        }
      }
      return refs
    },
    handleUpldMbIconError(error) {
      // this.$refs.dlgMbIconEditRef.displayErrorMessage(error.message)
      this.$message.info(error.message)
    },
    // 首页改变处理
    handleHomeChange(item, menuRela) {
      menuRela.homeCode = item && item.code ? item.code : ''
      menuRela.homeName = item && item.name ? item.name : ''
    },
    // 父级菜单处理
    handleParentChange(parentIds, menuRela) {
      if (parentIds && parentIds.length > 0) {
        menuRela.parentId = parentIds[parentIds.length - 1]
      } else {
        menuRela.parentId = ''
      }
    },
    handleDeleteMenu(index) {
      this.$confirm
        .delete()
        .then(() => {
          const deleteMenu = this.tempMenuRelaList[index]
          if (deleteMenu.menuId !== '') {
            this.deleteMenuList.push(deleteMenu)
          }
          this.tempMenuRelaList.splice(index, 1)
        })
        .catch((_) => {})
    },
    handleAddMenu() {
      let data = { ...this.pcMenuData }
      if (this.menuData.type === '2') {
        data = { ...this.appMenuData }
      }
      data.moduleCode = this.menuData.data.code
      this.tempMenuRelaList.unshift(data)
    },
    saveMenu(resolve, reject) {
      const validateArr = []
      const refs = this.getCurrentMenuRefs()
      refs.forEach((ref) => {
        validateArr.push(ref.validate())
      })
      Promise.all(validateArr)
        .then(() => {
          const nowMenuDTOList = this.tempMenuRelaList.filter((item) => {
            return item.type + '' === this.menuData.type
          })
          const nowDeleteMenuList = this.deleteMenuList.filter((item) => {
            return item.type + '' === this.menuData.type
          })

          nowMenuDTOList.forEach((item) => {
            item.appCode = this.appCode
          })

          // 验证成功
          this.$http
            .post(ModuleAPI.MODULE_PUBLISH_MENU(), {
              menuDTOList: nowMenuDTOList,
              deleteMenuList: nowDeleteMenuList,
            })
            .then((res) => {
              if (res.resultcode !== '0') {
                // 禁用http请求报错的默认提示
                this.$message.closeAll()
                this.$message.error(res.message ? res.message : '生成菜单出错')
              } else {
                this.$message.success('操作成功')
                resolve()
                this.closeMenu()
                this.$emit('after-save')
              }
            })
            .catch((err) => {
              // 禁用http请求报错的默认提示
              this.$message.closeAll()
              this.$message.error(err)
              reject()
            })
        })
        .catch(() => {
          // 无具体错误信息
          this.$message.error('验证不通过,请检查')
          reject()
        })
    },
    closeMenu() {
      this.clearMenuValidate()
      this.menuWindow = false
    },
    handleUpldMbIconRemove(uploadFileList, index) {
      if (this.$refs.appMenuRef) {
        const ref = this.$refs.appMenuRef[index]
        if (ref) {
          ref.validateField('importFile', () => {
            // this.$message.error(errorMessage)
          })
        }
      }
    },
    handleUpldMbIconSuccess(uploadFileList, index) {
      if (this.$refs.appMenuRef) {
        const ref = this.$refs.appMenuRef[index]
        if (ref) {
          ref.validateField('importFile', () => {
            // this.$message.error(errorMessage)
          })
        }
      }
    },
  },
}
</script>

<style scoped>
.box-card {
  margin-right: 15px;
  margin-bottom: 15px;
  width: 350px;
}
</style>
