<template>
  <div id="ModuleList">
    <!-- 模块列表 -->
    <xn-table
      id="tblModuleList"
      ref="tblModuleListRef"
      :url="moduleListUrl"
      :toolbars="tblModuleList.toolbars"
      :querymodel="queryForm"
      default-order="name:asc"
      @add="handleAddModule"
      @import="handleTblModuleListImport"
      @exportbatch="setExport"
      @exportjson="handleModuleExportJson"
      @generateFormCode="handleGenerateFormCode"
    >
      <div slot="search">
        <el-form :model="queryForm" :inline="true" is-query-input>
          <el-form-item label="名称">
            <xn-input
              v-model.trim="queryForm.name"
              placeholder="请输入名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="编码">
            <xn-input
              v-model.trim="queryForm.code"
              placeholder="请输入编码"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="handleBtnQueryClick"
            >
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <template slot="sync">
        <div>
          <sync-dialog
            ref="syncDialog"
            type="module"
            :ref-table="$refs.tblModuleListRef"
            :app-info="appInfo"
          />
        </div>
      </template>
      <xn-col type="selection" min-width="40" />
      <xn-col
        prop="code"
        label="模块编码"
        min-width="180"
        align="left"
        sortable="true"
        show-overflow-tooltip
      />
      <xn-col
        prop="name"
        label="模块名称"
        min-width="180"
        align="left"
        sortable="true"
        show-overflow-tooltip
      />
      <xn-col
        prop="description"
        label="描述"
        align="left"
        min-width="200"
        show-overflow-tooltip
      />
      <xn-col
        prop="createTimeStamp"
        label="创建时间"
        width="180"
        align="center"
        sortable="true"
      />
      <xn-col
        prop="updateTimeStamp"
        label="更新时间"
        width="180"
        align="center"
        sortable="true"
      />
      <xn-col label="操作" min-width="140" fixed="right" align="center">
        <template slot-scope="scope">
          <xn-col-action-group :keys="scope.row">
            <xn-col-action
              v-for="(colBtn, btnIndex) in operations"
              :key="btnIndex"
              @click="handleClickOpera(scope, colBtn)"
            >
              {{ colBtn.label }}
            </xn-col-action>
          </xn-col-action-group>
        </template>
      </xn-col>
    </xn-table>
    <sync-dialog
      ref="syncDialogSingle"
      type="module"
      :is-sync-all="false"
      :app-info="appInfo"
    />
    <!-- 模块导入弹出框 -->
    <xn-dialog
      id="dlgModuleListImport"
      ref="dlgModuleListImportRef"
      :title="dlgModuleListImport.title"
      :show.sync="dlgModuleListImport.show"
      width-size="small"
      :buttons="dlgModuleListImport.buttons"
      @save="showImportList"
      @cancel="handleDlgModuleListImportCancel"
    >
      <xn-form
        id="formDlgModuleListImport"
        ref="formDlgModuleListImportRef"
        slot="dialog-form"
        :model="formDlgModuleListImport.model"
        :rules="formDlgModuleListImport.rules"
        label-width="150px"
      >
        <xn-form-item label="模块模板" prop="dataModuleTemplate">
          <xn-upload
            v-if="dlgModuleListImport.show"
            id="uplFormDlgModuleListImportUploader"
            ref="uplFormDlgModuleListImportUploaderRef"
            v-model="formDlgModuleListImport.model.importFile"
            mode="upload"
            accept=".json,.zip"
            :file-preview="{ isEnable: false }"
            @onError="handleUplFormDlgModuleListImportUploaderError"
          />
          <div style="color: red">
            提示：只允许上传从当前功能模块下导出的数据
          </div>
        </xn-form-item>
        <xn-form-item label="是否自动发布表单" prop="autoPublishFlag">
          <el-switch v-model="formDlgModuleListImport.model.autoPublishFlag">
          </el-switch>
        </xn-form-item>
      </xn-form>
    </xn-dialog>

    <!-- 模块导入数据列表框 -->
    <xn-dialog
      id="dlgDataSetTable"
      ref="dlgModuleListImportTable"
      title="导入数据列表"
      :show.sync="showTable"
      width-size="small"
      :buttons="importTableButtons"
      @save="handleDlgModuleImportSave"
      @cancel="
        () => {
          showTable = false
        }
      "
    >
      <el-tabs v-model="tabName">
        <el-tab-pane label="覆盖数据" name="overrideData"></el-tab-pane>
        <el-tab-pane label="新导入数据" name="newImportData"></el-tab-pane>
        <el-tab-pane label="覆盖表单数据" name="overrideFormData"></el-tab-pane>
        <el-tab-pane
          label="新导入表单数据"
          name="newImportFormData"
        ></el-tab-pane>
        <el-tab-pane
          label="覆盖数据模型数据"
          name="overrideDataModelData"
        ></el-tab-pane>
        <el-tab-pane
          label="新导入数据模型数据"
          name="newImportDataModelData"
        ></el-tab-pane>
      </el-tabs>
      <el-table :data="tableData[tabName]" border>
        <el-table-column prop="code" label="编码"> </el-table-column>
        <el-table-column prop="name" label="名称"> </el-table-column>
      </el-table>
    </xn-dialog>

    <xn-dialog
      id="dlgModuleListExport"
      ref="dlgModuleListExportRef"
      title="模块导出设置"
      :show.sync="dlgModuleListExport.show"
      width-size="small"
      :buttons="dlgModuleListExport.buttons"
      @save="handleModuleExportBatch"
      @cancel="
        () => {
          dlgModuleListExport.show = false
        }
      "
    >
      <xn-form
        id="formdlgModuleListExport"
        ref="formDlgModuleListExportRef"
        slot="dialog-form"
        :model="formDlgModuleListExport.model"
        label-width="25%"
        size="small"
      >
        <xn-form-item label="导出表单" prop="exportFormFlag">
          <el-switch
            v-model="formDlgModuleListExport.model.exportFormFlag"
          ></el-switch>
        </xn-form-item>
        <xn-form-item label="导出数据模型" prop="exportModelFlag">
          <el-switch
            v-model="formDlgModuleListExport.model.exportModelFlag"
          ></el-switch>
        </xn-form-item>
      </xn-form>
    </xn-dialog>
  </div>
</template>

<script>
import { ModuleAPI } from 'src/api'
import { handleErrorMessage } from 'src/utils'
import { operation } from 'sinitek-util'
import sync from 'src/mixins/sync'
export default {
  name: 'ModuleList',
  mixins: [sync],
  inject: ['getAppCode'],
  data() {
    return {
      showTable: false,
      tabName: 'overrideData',
      tableData: {
        overrideData: [],
        newImportData: [],
      },
      importTableButtons: [
        { label: '确定', action: 'save', type: 'primary', event: 'save' },
        { label: '取消', action: 'cancel', type: 'info', event: 'cancel' },
      ],
      importParam: {},
      rows: [],
      moduleListUrl: Object.freeze(ModuleAPI.MODULE_SEARCH()),
      tblModuleList: {
        toolbars: [
          {
            label: '新增',
            type: 'add',
            event: 'add',
          },
          {
            label: '导入',
            type: 'import',
            event: 'import',
          },
          {
            label: '导出',
            icon: 'icon-daochu',
            type: 'button',
            event: 'exportbatch',
          },
          // {
          //   label: '导出表单JSON',
          //   icon: 'icon-daochu',
          //   type: 'button',
          //   event: 'exportjson',
          // },
          // {
          //   label: '生成代码',
          //   icon: 'icon-daochu',
          //   type: 'button',
          //   event: 'generateFormCode',
          // },
        ],
      },
      // 模块导入弹框
      dlgModuleListImport: {
        title: '模块导入',
        show: false,
        width: '435px',
        buttons: [
          { label: '确定', action: 'import', type: 'primary', event: 'save' },
          { label: '取消', action: 'cancel', type: 'info', event: 'cancel' },
        ],
      },
      // 模块导出弹框
      dlgModuleListExport: {
        title: '模块导出',
        show: false,
        width: '435px',
        buttons: [
          { label: '确定', action: 'save', type: 'primary', event: 'save' },
          { label: '取消', action: 'cancel', type: 'info', event: 'cancel' },
        ],
      },
      // 模块导入弹框内表单
      formDlgModuleListImport: {
        model: {
          importFile: [],
          autoPublishFlag: false,
        },
        rules: {
          dataModuleTemplate: [
            { validator: this.validateImport, required: true },
          ],
        },
      },
      // 模块导出弹框内表单
      formDlgModuleListExport: {
        model: {
          exportFormFlag: true,
          exportModelFlag: true,
        },
      },
      // 模块查询参数
      queryForm: {
        name: '',
        code: '',
        appCode: this.getAppCode(),
      },
      tableRef: 'tblModuleListRef',
      operations: [
        {
          label: '编辑',
          functionName: 'handleEditModule',
        },
        {
          label: '删除',
          functionName: 'handleModuleDelete',
        },
        // {
        //   label: '导出',
        //   functionName: 'handleTblModuleListExport',
        // },
        // {
        //   label: '生成代码',
        //   functionName: 'handleGenerateFormCode',
        // },
      ],
    }
  },
  methods: {
    query() {
      this.$refs.tblModuleListRef.query()
    },
    handleBtnQueryClick() {
      this.query()
    },
    setExport(rows) {
      this.rows = rows
      if (Array.isArray(this.rows)) {
        if (this.rows.length < 1) {
          this.$message.info('请选择要导出的数据')
        } else {
          this.dlgModuleListExport.show = true
        }
      } else {
        if (!this.rows.id) {
          this.$message.info('请选择要导出的数据')
        } else {
          this.dlgModuleListExport.show = true
        }
      }
    },
    // 添加模块
    handleAddModule() {
      this.$emit('add-module')
    },
    // 模块导入
    handleTblModuleListImport() {
      this.dlgModuleListImport.show = true
    },
    // 导入前验证是否上传了文件
    validateImport(rule, value, callback) {
      // 获取上传文件
      const template = this.$refs.uplFormDlgModuleListImportUploaderRef.fileList
      if (template.length === 0) {
        callback(new Error('请上传模块模板文件'))
      } else {
        callback()
      }
    },
    // 导入时的异常处理
    handleUplFormDlgModuleListImportUploaderError(error, file, fileList) {
      console.warn('导入时异常', error, file, fileList)
      this.$refs.dlgModuleListImportRef.displayErrorMessage(error.message)
    },
    // 导入弹框中，模块的保存按钮
    showImportList(resolve, reject) {
      // 判重
      this.$refs.formDlgModuleListImportRef.validate((valid) => {
        if (valid) {
          const param = {
            importFile: this.formDlgModuleListImport.model.importFile,
            autoPublishFlag: this.formDlgModuleListImport.model.autoPublishFlag,
          }
          if (this.isMultiAppMode) {
            param.appCode = this.getAppCode()
          }
          this.importParam = param
          this.$easypost(ModuleAPI.MODEL_IMPORT_VALIDATE(), param).then(
            (result) => {
              resolve('导入校验成功')
              this.handleDlgModuleListImportCancel()
              this.showTable = true
              this.tableData = result
            },
            () => {
              reject()
            }
          )
        } else {
          reject()
        }
      })
    },
    handleDlgModuleImportSave(resolve, reject) {
      this.$easypost(ModuleAPI.MODEL_IMPORT(), this.importParam)
        .then(() => {
          resolve('导入成功')
          this.dlgModuleListImport.show = false
          this.showTable = false
          this.query()
          this.$emit('tree-refresh')
        })
        .catch((error) => {
          // 禁用http请求报错的默认提示
          this.$message.closeAll()
          reject(error)
        })
    },
    // 导入弹框中的取消按钮
    handleDlgModuleListImportCancel(resolve, reject) {
      this.$refs.uplFormDlgModuleListImportUploaderRef.clear()
      this.formDlgModuleListImport.model.importFile = []
      // 清空验证信息
      this.$refs.formDlgModuleListImportRef.clearValidate()
      this.dlgModuleListImport.show = false
      reject && reject('')
    },
    // 模块批量导出
    handleModuleExportBatch() {
      if (Array.isArray(this.rows)) {
        const ids = this.rows.map((item) => item.id)
        operation.download(ModuleAPI.MODEL_EXPORT(), {
          ids,
          exportFormFlag: this.formDlgModuleListExport.model.exportFormFlag,
          exportModelFlag: this.formDlgModuleListExport.model.exportModelFlag,
          prettyPrinterFlag: true,
        })
        this.dlgModuleListExport.show = false
      } else {
        operation.download(ModuleAPI.MODEL_EXPORT(), {
          ids: [this.rows.id],
          exportFormFlag: true,
          exportModelFlag: true,
          prettyPrinterFlag: true,
        })
        this.dlgModuleListExport.show = false
      }
    },
    // 导出表单json
    handleModuleExportJson(rows) {
      if (Array.isArray(rows)) {
        if (rows.length < 1) {
          this.$message.info('请选择要导出的数据')
          return
        }
        const ids = rows.map((item) => item.id)
        operation.download(ModuleAPI.MODEL_EXPORT_JSON() + '?objid=' + ids)
      } else {
        if (!rows.id) {
          this.$message.info('请选择要导出的数据')
          return
        }
        operation.download(
          ModuleAPI.MODEL_EXPORT_JSON() + '?objid=' + [rows.id]
        )
      }
    },
    // 生成表单代码(多选模块或单个模块)
    handleGenerateFormCode(rowsOrRow) {
      console.warn('暂不支持生成代码', rowsOrRow)
      this.$message.info('暂不支持生成代码')
      // let moduleIds = []
      // if (Array.isArray(rowsOrRow)) {
      //   if (rowsOrRow.length < 1) {
      //     this.$message.info('请选择要生成代码的数据')
      //     return
      //   }
      //   moduleIds = rowsOrRow.map((row) => row.id).filter((id) => id)
      // } else {
      //   if (!rowsOrRow.id) {
      //     this.$message.info('请选择要生成代码的数据')
      //     return
      //   }
      //   moduleIds.push(rowsOrRow.id)
      // }
      // if (moduleIds.length > 0) {
      //   this.$http
      //     .post(FormAPI.FORM_LIST_BY_MODULE_IDS(), {
      //       moduleIds,
      //       formType: [1, 2],
      //     })
      //     .then((res) => {
      //       if (Array.isArray(res.data)) {
      //         if (res.data.length < 1) {
      //           this.$message.info('获取到的表单数据为空')
      //           return
      //         }
      //         const zip = new JSZip()
      //         res.data.forEach(({ moduleCode, forms }) => {
      //           const module = zip.folder(moduleCode)
      //           forms.forEach(({ code, pageData }) => {
      //             pageData = JSON.parse(pageData)
      //             const decodePageData = decodePageDataFunc(pageData)
      //             console.log('decodePageData', decodePageData)
      //             // const generatedAssist = generateCode(decodePageData, {
      //             //   formCode: code,
      //             // })
      //             const generatedAssist = ''
      //             module.file(`${code}.vue`, generatedAssist)
      //           })
      //         })
      //         zip.generateAsync({ type: 'blob' }).then(function (content) {
      //           saveAs(content, '表单代码.zip')
      //         })
      //       } else {
      //         console.warn('无法获取表单数据', res)
      //         this.$message.info('无法获取表单数据')
      //       }
      //     })
      //     .catch((error) => {
      //       console.warn('无法获取表单数据', error)
      //       this.$message.info('无法获取表单数据')
      //     })
      // } else {
      //   this.$message.info('无法获取模块信息')
      // }
    },
    // 编辑模块
    handleEditModule(data) {
      this.$emit('edit-module', data)
    },
    // 模块删除
    handleModuleDelete(row) {
      this.$confirm
        .delete()
        .then(() => {
          this.$http
            .get(ModuleAPI.MODULE_DELETE(), { id: row.id })
            .then(() => {
              this.$message.deleteWithSuccess()
              this.query()
              this.$emit('tree-refresh')
            })
            .catch((err) => {
              handleErrorMessage(this, err)
            })
        })
        .catch(() => {
          console.warn('用户取消删除')
        })
    },
    // 模块导出按钮
    handleTblModuleListExport(data) {
      operation.download(ModuleAPI.MODEL_EXPORT() + '?objid=' + data.id)
    },
  },
}
</script>
