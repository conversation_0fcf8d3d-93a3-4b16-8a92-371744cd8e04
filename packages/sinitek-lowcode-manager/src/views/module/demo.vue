<template>
  <SinitekLowcodeSimulator ref="simulator" :config="config" />
</template>

<script>
import demo from '../../../../sinitek-lowcode-render/example/json/demo-element.js'
import { ModuleAPI, TemplateAPI, DataModelAPI, EnumDesignAPI } from 'src/api'
// import { components as advancedComponents } from 'sinitek-lowcode-advanced'

export default {
  name: 'ModuleManageDemo',
  mounted() {
    setTimeout(() => {
      // this.$refs.simulator.addComponents(advancedComponents)
      this.$refs.simulator.setSchema(demo)
    }, 1000)
    this.$http.post(EnumDesignAPI.ENUM_LOAD_ALL(), [
      {
        catalog: '',
        name: '',
        type: '',
      },
    ])
  },
  computed: {
    config() {
      return {
        fetcher: {
          // datasouce获取数据函数
          fetcher: async (options) => {
            return this.$http[options.method.toLowerCase()](
              options.url,
              options.data
            )
          },
          getAIProps: async (params) => {
            const res = await this.$http.get(
              ModuleAPI.RECOMMAND_COMPONENT_PROPS(),
              {
                ...params,
                platform: 'SINITEK',
                formId: '1762758968511434753',
                modelCode: 'productDTO',
              },
              {
                showLoading: true,
                loadingOptions: {
                  background: 'rgba(0,0,0,0.3)',
                },
              }
            )
            return res.data || []
          },
          getTemplate: async (params) => {
            const res = await this.$http.post(
              TemplateAPI.TEMPLATE_SEARCH(),
              params
            )
            if (res.resultcode === '0') {
              return { data: res.data.datalist, total: res.data.totalsize }
            } else {
              this.$message.error(res.message)
            }
            return null
          },
          setSchema: async (templateId) => {
            let jsonSchema = {}
            const res = await this.$http.get(
              TemplateAPI.TEMPLATE_LIST_DETAIL(),
              {
                id: templateId,
              }
            )
            if (res.resultcode === '0') {
              if (res.data.pcJson) {
                jsonSchema = JSON.parse(res.data.pcJson)

                return jsonSchema
              } else {
                this.$message.error('获取失败')
              }
            } else {
              this.$message.error(res.message)
            }
            return null
          },
          getModelList: async () => {
            return this.$http
              .get(DataModelAPI.MODEL_AVAILABLELIST(), { modelCode: 'd-a' })
              .then((res) => {
                return res.data.map((e) => ({
                  label: e.name,
                  value: e.code,
                }))
              })
          },
          getRefModelList: async () => {
            return this.$http
              .get(DataModelAPI.MODEL_REFLIST(), { moduleCode: 'd-a' })
              .then((res) => {
                return res.data.map((e) => ({
                  label: e.name,
                  value: e.code,
                }))
              })
          },
          // 获取模型字段用在条件筛选上
          getModelFields: async (code) => {
            const getChildren = async (v) => {
              const res = await this.$http.post(DataModelAPI.MODEL_LOAD(), {
                modelCode: v,
              })
              const promise = []
              res?.data?.fieldProps?.forEach((e) => {
                if (e.type === 'FOREIGN_KEY') {
                  promise.push(getChildren(e.relaModelCode))
                }
              })
              const promiseResult = await Promise.all(promise)
              return res?.data?.fieldProps?.map((e) => {
                const result = {
                  label: e.comments || e.colName,
                  value: e.propName,
                  data: e,
                }
                if (e.type === 'FOREIGN_KEY') {
                  result.children = promiseResult.shift()
                }
                return result
              })
            }
            const load = await getChildren(code)
            if (load) return load
            return []
          },
          // 模型的crud
          modelFetchers: {
            create: (params) => {
              return this.$http.post(
                DataModelAPI.MODEL_DYNAMIC_SAVE(),
                params.data
              )
            },
            // this.fetcher.xxx.list({conditin:{}})
            list: (params) => {
              const defaultParams = {
                page: 1,
                pageSize: 20,
                condition: {},
              }
              const newParams = Object.assign({}, defaultParams, params.data)
              return this.$http.post(
                DataModelAPI.MODEL_DYNAMIC_READ(),
                newParams
              )
            },
            single: (params) => {
              const newParams = {}
              if (params.data) {
                newParams.modelCode = params.data.modelCode
                newParams.param = {
                  id: params.data.id,
                }
              }
              return this.$http.post(
                DataModelAPI.MODEL_DYNAMIC_LOAD_DETAIL_BY_KEY(),
                newParams
              )
            },
            update: (params) => {
              return this.$http.post(
                DataModelAPI.MODEL_DYNAMIC_UPDATE_BY_KEY(),
                params.data
              )
            },
            delete: (params) => {
              const newParams = {}
              if (params.data) {
                newParams.modelCode = params.data.modelCode
                newParams.param = {
                  id: params.data.id,
                  ids: params.data.ids,
                }
              }
              return this.$http.post(
                DataModelAPI.MODEL_DYNAMIC_DELETE_BY_KEY(),
                newParams
              )
            },
          },
          // 获取枚举配置
          getEnumList: async (
            params = {
              catalog: '',
              name: '',
              type: '',
            }
          ) => {
            const res = await this.$http.post(EnumDesignAPI.ENUM_LOAD_ALL(), [
              params,
            ])
            const result = []
            if (res.data) {
              Object.keys(res.data).forEach((key) => {
                const value = res.data[key]
                const option = {
                  label: key,
                  value: key,
                  children: Object.keys(value).map((e) => ({
                    label: e,
                    value: e,
                  })),
                }
                result.push(option)
              })
            }
            return result
          },
          getEnum: async (params) => {
            const res = await this.$http.post(
              EnumDesignAPI.ENUM_LOAD(),
              params.data
            )
            return res.data || []
          },
        },
      }
    },
  },
}
</script>
