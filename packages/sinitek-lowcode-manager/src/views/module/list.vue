<template>
  <div
    id="ModuleManage"
    class="app-container custom-tree-container"
    :class="isDirectFormMaking ? 'app-container-coverage' : ''"
  >
    <transition name="el-fade-in-linear">
      <xn-tree
        v-show="treeCollaspace"
        ref="moduleTree"
        node-key="id"
        :param="moduleTreeParam"
        clearable
        show-filter
        :select-id="currentKey"
        :url="moduleTree.url"
        :expand-on-click-node="true"
        :render-content="renderTree"
        :default-expand-all="false"
        :default-expanded-keys="defaultExpandedKeys"
        :filter-node-method="filterNodeMethod"
        @nodeClick="handleNodeClick"
      />
    </transition>
    <div
      class="drawer-btn"
      :class="{ 'drawer-btn-collaspace': !treeCollaspace }"
      @click="handleTreeCollaspace"
    >
      <span>
        <i class="iconfont icon-tianjiashoucangjiazhankai" />
      </span>
    </div>
    <div class="block" :class="{ 'block-collaspace': treeCollaspace }">
      <div class="header-nav">
        <xn-form>
          <xn-form-item label="当前位置" label-width="unset" show-label-colon>
            <xn-breadcrumb>
              <xn-breadcrumb-item
                v-for="(data, index) in tapNamePath.path"
                :key="data.id"
                :is-link="index !== tapNamePath.path.length - 1"
                max-width="200"
                effect="light"
                placement="top"
                @click="clickNode(data)"
              >
                <template #tooltip>
                  {{ data.name
                  }}{{ data.code === null ? '' : `(${data.code})` }}
                </template>
                {{ data.name }}{{ data.code === null ? '' : `(${data.code})` }}
              </xn-breadcrumb-item>
            </xn-breadcrumb>
          </xn-form-item>
        </xn-form>
        <el-button
          v-if="
            (currentNode.dataType === ModuleConstant.TREE_TYPE_PC_FORM ||
              currentNode.dataType === ModuleConstant.TREE_TYPE_MOBILE_FORM) &&
            currentData.code
          "
          type="primary"
          @click="goToDesign"
        >
          进入编辑
        </el-button>
      </div>

      <div v-if="currentNode.id === '0'" class="app-container">
        <!-- 模块列表 -->
        <module-list
          ref="moduleListRef"
          @add-module="handleAddModule"
          @edit-module="handleEditModule"
          @tree-refresh="treeQuery"
        />
      </div>
      <div
        v-else
        class="app-container"
        :class="isDirectFormMaking ? 'app-container-coverage' : ''"
      >
        <!-- 点击 PC页面,APP页面,数据模型时 -->
        <template v-if="currentNode.id.indexOf('0') === 0">
          <!--模型列表页面-->
          <template
            v-if="currentNode.dataType === ModuleConstant.TREE_TYPE_MODEL"
          >
            <model-list
              :key="currentNode.id"
              ref="modelListRef"
              :module-code="moduleScop.moduleCode"
              show-search
              @afterSave="treeQuery"
            />
          </template>
          <!--PC表单页面-->
          <template
            v-else-if="
              currentNode.dataType === ModuleConstant.TREE_TYPE_PC_FORM
            "
          >
            <form-list
              :key="currentNode.id"
              ref="pcMetaForm"
              :type="ModuleConstant.TREE_TYPE_PC_FORM"
              show-search
              @afterSave="treeQuery"
            />
          </template>
          <!--移动表单页面-->
          <template v-else>
            <form-list
              :key="currentNode.id"
              ref="mobileMetaForm"
              :type="ModuleConstant.TREE_TYPE_MOBILE_FORM"
              show-search
              @afterSave="treeQuery"
            />
          </template>
        </template>
        <!-- 具体数据模型,表单，模块点击时，具体项展示 -->
        <template v-else>
          <!--表单设计器-->
          <template
            v-if="
              (currentNode.dataType === ModuleConstant.TREE_TYPE_PC_FORM ||
                currentNode.dataType ===
                  ModuleConstant.TREE_TYPE_MOBILE_FORM) &&
              currentData.code
            "
          >
            <Simulator
              :id="currentData.id"
              ref="simulator"
              :module-code="currentData.moduleCode"
              :mode="DesignMode.PREVIEW"
              class="preview-mask"
            />
          </template>
          <!--模型详情页面-->
          <template
            v-if="currentNode.dataType === ModuleConstant.TREE_TYPE_MODEL"
          >
            <Model-detail :id="currentNode.id" ref="modelDetailRef" />
          </template>
          <!--tab分类页面：1.表单 2.模型-->
          <template
            v-if="currentNode.dataType === ModuleConstant.TREE_TYPE_MODULE"
          >
            <el-tabs
              id="tabModule"
              :key="currentNode.id"
              type="border-card"
              :value="tabModule.currentTab"
              @tab-click="handleTabClick"
            >
              <!-- PC表单 -->
              <el-tab-pane label="PC表单" name="tabPCFormList">
                <form-list
                  ref="tabPCFormListRef"
                  :type="ModuleConstant.TREE_TYPE_PC_FORM"
                  @afterSave="treeQuery"
                />
              </el-tab-pane>
              <!-- 移动表单 -->
              <el-tab-pane label="移动表单" name="tabMobileFormList">
                <form-list
                  ref="tabMobileFormListRef"
                  :type="ModuleConstant.TREE_TYPE_MOBILE_FORM"
                  @afterSave="treeQuery"
                />
              </el-tab-pane>
              <!-- 数据模型 -->
              <el-tab-pane label="数据模型" name="tabModelList">
                <model-list
                  ref="tabModelListRef"
                  :module-code="moduleScop.moduleCode"
                  @afterSave="treeQuery"
                />
              </el-tab-pane>
            </el-tabs>
          </template>
        </template>
      </div>
    </div>

    <!-- 模块信息编辑弹框 -->
    <xn-dialog
      :title="moduleEditTitle"
      :show.sync="showModuleEdit"
      :buttons="buttons"
      @save="handleModuleDlgSave"
      @close="handleModuleDlgClose"
    >
      <template slot="dialog-form">
        <xn-form
          ref="moduleForm"
          :model="currentModule"
          label-width="80px"
          :rules="moduleRules"
        >
          <xn-form-item label="编码" prop="code">
            <template v-if="!!currentModule.id">
              <span v-if="appCode">
                {{ appCode }}-{{ currentModule.code }}
              </span>
              <span v-else>
                {{ currentModule.code }}
              </span>
            </template>
            <xn-input
              v-else
              v-model.trim="currentModule.code"
              :maxlength="50 - initCodeLen"
            >
              <span v-if="isMultiAppMode" slot="prepend"> {{ appCode }}- </span>
              <div
                slot="suffix"
                style="display: flex; align-items: center; height: 100%"
              >
                <span>{{ currentModule.code.length + initCodeLen }}/50</span>
              </div>
            </xn-input>
          </xn-form-item>
          <xn-form-item label="名称" prop="name">
            <xn-input
              v-model.trim="currentModule.name"
              :maxlength="50"
              show-word-limit
            />
          </xn-form-item>
          <xn-form-item prop="path">
            <span slot="label">
              URL
              <el-tooltip class="icon_question" effect="light" placement="top">
                <div slot="content">
                  <span style="display: block"
                    >该模块下所有页面的访问url前缀，修改后请自行添加路由数据</span
                  >
                </div>
                <i class="el-icon-question" />
              </el-tooltip>
            </span>
            <xn-input
              v-model.trim="currentModule.path"
              :maxlength="150"
              show-word-limit
              disabled
            />
          </xn-form-item>
          <xn-form-item label="描述" prop="description">
            <el-input
              v-model.trim="currentModule.description"
              type="textarea"
              :rows="5"
              :maxlength="300"
              show-word-limit
            />
          </xn-form-item>
        </xn-form>
      </template>
    </xn-dialog>

    <!-- 页面信息编辑 -->
    <form-dialog ref="formDialog" :title="title" @refreshTableData="queryAll" />

    <!-- 生成菜单 -->
    <menu-save-dialog
      ref="menuWindow"
      :app-code="appCode"
      @after-save="treeQuery"
    />

    <!-- 页面移动 -->
    <xn-dialog
      ref="moveDialog"
      title="移动页面"
      width="600px"
      :show.sync="moveWindow"
      :buttons="moveButtons"
      @saveMove="saveMove"
      @closeMove="closeMove"
    >
      <el-form
        slot="dialog-form"
        ref="moveData"
        :model="moveData"
        :rules="moveRules"
        label-width="100px"
      >
        <el-form-item label="移动至" prop="moduleId">
          <el-select
            v-model="moveData.moduleId"
            clearable
            filterable
            :popper-append-to-body="true"
          >
            <el-option
              v-for="item in moduleList.filter(
                (item) => item.id !== moveData.oldModuleId
              )"
              :key="item.id"
              :value="item.id"
              :label="item.name"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </xn-dialog>

    <copy-dialog
      ref="copyDlg"
      :dlg-title="copyDlgTitle"
      :handle-copy="handleCopy"
      :default-code-max-length="defaultCodeMaxLengthOnCopy"
      @copy-success="handleCopySuccess"
    />
  </div>
</template>

<script lang="jsx">
import { ModuleAPI, FormAPI, DataModelAPI, MfAppAPI, CommonAPI } from 'src/api'
import { handleErrorMessage, validateCode } from 'src/utils'
import GlobalConstant from 'src/views/GlobalConstant'
import { ModuleConstant } from 'src/constant'
import { isMultiAppMode } from 'src/config'
import { DesignMode } from 'sinitek-lowcode-shared'

export default {
  name: 'LcModuleManage',
  components: {
    MenuSaveDialog: () => import('./components/menuSaveDialog'),
    ModelDetail: () => import('../model/components/ModelDetail'),
    ModelList: () => import('../model/ModelList'),
    FormDialog: () => import('../form/components/FormDialog'),
    FormList: () => import('../form/FormList'),
    ModuleList: () => import('./components/ModuleList'),
    CopyDialog: () => import('src/views/components/copyDialog'),
    Simulator: () => import('../components/simulator/render'),
  },
  provide() {
    return {
      getAppCode: () => {
        return this.appCode
      },
      getAppInfo: () => {
        return this.appInfo
      },
      getAppName: () => this.appName,
      getCurrentModuleCode: () => this.moduleScop.code,
      getCurrentModuleName: () => this.moduleScop.name,
    }
  },
  data() {
    return {
      // 复制时编码长度
      defaultCodeMaxLengthOnCopy: 50,
      currentCopyDataType: '',
      copyDlgTitle: '复制',
      ModuleConstant,
      moduleTree: {
        url: ModuleAPI.MODULE_LIST_TREE(),
      },
      tapNamePath: {
        path: {},
      },
      currentNode: {
        id: '',
        dataType: '',
      },
      currentData: {
        id: '',
        name: '',
        code: '',
        homeCode: '',
        description: '',
      },
      // 新增模块时的参数
      currentModule: {
        appCode: '',
        id: '',
        name: '',
        path: GlobalConstant.RENDERPATH,
        code: '',
        description: '',
        children: [],
      },
      tabModule: {
        currentTab: 'tabPCFormList',
      },
      moduleEditTitle: '新增',
      showModuleEdit: false,
      moduleRules: {
        code: [
          { required: true, message: '请填写编码' },
          {
            validator: validateCode,
            message:
              '以字母开头，由英文字母、数字、下划线（_）、连字符（-）组成',
          },
        ],
        name: [{ required: true, message: '请填写名称' }],
      },
      buttons: [
        { action: 'save', type: 'primary', event: 'save' },
        { action: 'cancel', type: 'info', event: 'close' },
      ],
      title: '编辑',
      moveWindow: false,
      moveRules: {
        moduleId: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: '请选择目标模块',
          },
        ],
      },
      moveData: {
        id: '',
        oldModuleId: '',
        moduleId: '',
        dataType: '',
      },
      moveButtons: [
        { action: 'confirm', type: 'primary', event: 'saveMove' },
        { action: 'cancel', type: 'info', event: 'closeMove' },
      ],
      moduleList: [],
      treeCollaspace: true, // 用于标识树展开收起
      treeData: [],
      // 查询左侧所有模块参数
      moduleTreeParam: {
        appCode: '',
      },
      appCode: '',
      isMultiAppMode: isMultiAppMode(),
      appInfo: {
        serviceName: '',
        name: '',
      }, // 这个appInfo是从接口中获得的，主要用于同步中
      initCodeLen: 0,
      appName: '',
      DesignMode,
    }
  },
  computed: {
    // 当前被选中节点
    currentKey() {
      return this.$store.getters.selectedForm
    },
    defaultExpandedKeys() {
      return [this.currentKey]
    },
    pcFormMenuOptions() {
      const moduleDatas = this.menuData.data.children
      let pcMenuData = []
      for (let i = 0; i < moduleDatas.length; i++) {
        pcMenuData = moduleDatas[i]
        if (pcMenuData.dataType === ModuleConstant.TREE_TYPE_PC_FORM) {
          break
        }
      }
      return pcMenuData.children
    },
    isDirectFormMaking() {
      return (
        this.$route.path.startsWith('/direct') &&
        (this.currentNode.dataType === ModuleConstant.TREE_TYPE_PC_FORM ||
          this.currentNode.dataType === ModuleConstant.TREE_TYPE_MOBILE_FORM) &&
        this.currentData.code
      )
    },
    moduleScop() {
      // 所属模块,用来表明当前表单/数据模型所属模块
      return {
        id: this.currentData.moduleId || this.currentData.id,
        name: this.currentData.moduleName || this.currentData.name,
        code: this.currentData.moduleCode || this.currentData.code,
      }
    },
  },
  created() {
    if (this.isMultiAppMode) {
      if (this.$route.query.appCode) {
        this.currentModule.appCode = this.$route.query.appCode
        this.moduleTreeParam.appCode = this.$route.query.appCode
        this.appCode = this.$route.query.appCode
        this.initCodeLen = this.appCode.length + 1
        this.getAppInfo(this.appCode)
      }
      this.appName =
        decodeURIComponent(this.$route.query.title.split('-')[0]) + '-'
    }
  },
  methods: {
    // 刷新表格与树组件
    queryAll() {
      this.treeQuery()
      this.query()
    },
    // 表格刷新
    query() {
      if (this.$refs.pcMetaForm) {
        this.$refs.pcMetaForm.query()
      }
      if (this.$refs.mobileMetaForm) {
        this.$refs.mobileMetaForm.query()
      }
      if (this.$refs.tabPCFormListRef) {
        this.$refs.tabPCFormListRef.query()
      }
      if (this.$refs.tabMobileFormListRef) {
        this.$refs.tabMobileFormListRef.query()
      }
      if (this.$refs.tabModelListRef) {
        this.$refs.tabModelListRef.query()
      }
      if (this.$refs.modelListRef) {
        this.$refs.modelListRef.query()
      }
      if (this.$refs.moduleListRef) {
        this.$refs.moduleListRef.query()
      }
      if (this.$refs.tabModelListRef) {
        this.$refs.tabModelListRef.query()
      }
    },
    treeQuery() {
      this.$refs.moduleTree?.query()
    },
    // ↓↓↓↓↓模块相关操作↓↓↓↓↓
    // 模块新增
    handleAddModule() {
      this.moduleEditTitle = '新增'
      this.showModuleEdit = true
      this.$nextTick(function () {
        this.$refs.moduleForm.clearValidate()
      })
    },
    // 模块编辑
    handleEditModule(row) {
      this.moduleEditTitle = '编辑'
      this.$http.get(ModuleAPI.MODULE_DETAIL(), { id: row.id }).then((res) => {
        this.resetModuleCurrentData(res.data)
        this.showModuleEdit = true
      })
    },
    // 模块dlg保存
    handleModuleDlgSave(resolve, reject) {
      if (
        this.isMultiAppMode &&
        (!this.currentModule.appCode || !this.appCode)
      ) {
        reject('缺少应用数据，无法保存')
      }
      this.$refs.moduleForm.validate((valid) => {
        if (valid) {
          const form = { ...this.currentModule }
          // 组合code
          if (this.isMultiAppMode) {
            form.code = this.appCode + '-' + this.currentModule.code
          }
          this.$http
            .post(ModuleAPI.MODULE_SAVE(), form)
            .then(() => {
              this.handleModuleDlgClose()
              this.queryAll()
              resolve()
            })
            .catch((error) => {
              // 禁用http请求报错的默认提示
              this.$message.closeAll()
              reject(error)
            })
        } else {
          reject()
        }
      })
    },
    // 模块dlg不保存关闭
    handleModuleDlgClose() {
      this.showModuleEdit = false
      this.resetModuleCurrentData()
      this.$refs.moduleForm.clearValidate()
    },
    // 重置当前模块数据
    resetModuleCurrentData(data = {}) {
      this.currentModule.appCode = this.appCode
      this.currentModule.id = data?.id ? data.id : ''
      // 拆分code
      if (this.isMultiAppMode) {
        if (data?.code && this.appCode) {
          if (data.code.startsWith(this.appCode)) {
            this.currentModule.code = data.code.slice(this.appCode.length + 1)
          } else {
            this.currentModule.code = ''
          }
        } else {
          this.currentModule.code = ''
        }
      } else {
        this.currentModule.code = data?.code ? data.code : ''
      }
      this.currentModule.name = data?.name ? data.name : ''
      this.currentModule.path =
        data?.path && data.path.trim() !== ''
          ? data.path
          : GlobalConstant.RENDERPATH
      this.currentModule.description = data?.description ? data.description : ''
      this.currentModule.children = data?.children ? data.children : []
    },
    // ↑↑↑↑↑模块相关操作↑↑↑↑↑
    // ↓↓↓↓↓树节点操作↓↓↓↓↓
    delete(node, data) {
      this.$confirm
        .delete()
        .then(() => {
          // 删除模块
          let isDel = false
          if (
            data.id ==
            this.tapNamePath.path[this.tapNamePath.path.length - 1].id
          ) {
            isDel = true
          }
          if (data.dataType === ModuleConstant.TREE_TYPE_MODULE) {
            this.$http
              .get(ModuleAPI.MODULE_DELETE(), { id: data.id })
              .then(() => {
                this.$message.deleteWithSuccess()
                this.queryAll()
                if (isDel) {
                  this.tapNamePath.path.pop()
                  this.clickNode(
                    this.tapNamePath.path[this.tapNamePath.path.length - 1]
                  )
                }
              })
              .catch((err) => {
                handleErrorMessage(this, err)
              })
          } else if (data.dataType === ModuleConstant.TREE_TYPE_MODEL) {
            // 删除数据模型
            this.$http
              .post(DataModelAPI.MODEL_DELETE(), [data.id])
              .then(() => {
                this.$message.deleteWithSuccess()
                this.queryAll()
              })
              .catch((err) => {
                handleErrorMessage(this, err)
              })
          } else {
            // 删除表单
            this.$http
              .post(FormAPI.FORM_DELETE(), [data.id])
              .then(() => {
                this.$message.deleteWithSuccess()
                this.queryAll()
              })
              .catch((err) => {
                handleErrorMessage(this, err)
              })
          }
        })
        .catch(() => {})
    },
    add(node, data) {
      // this.currentData = { ...this.currentData, ...data }
      // 在未选中tree node时，点击新增，添加的模块还是上一次的
      this.handleNodeClick(data)
      // 这里加nextTick是因为上面函数使用了nextTick。直接运行会导致数据拿不到
      this.$nextTick(() => {
        if (data.dataType === ModuleConstant.TREE_TYPE_MODULE) {
          this.handleAddModule()
        } else {
          this.handleAdd(data)
        }
      })
    },
    edit(node, data) {
      if (data.dataType === ModuleConstant.TREE_TYPE_MODULE) {
        this.handleEditModule(data)
      } else {
        this.handleEdit(data)
      }
    },
    handleCopy({ id, code: newCode, name: newName }) {
      const that = this
      return new Promise(function (resolve, reject) {
        that.$http
          .post(CommonAPI.COPY(), {
            id,
            newCode,
            newName,
            dataType: that.currentCopyDataType,
          })
          .then(() => {
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    },
    handleCopySuccess() {
      this.queryAll()
      this.currentCopyDataType = ''
      this.defaultCodeMaxLengthOnCopy = 50
    },
    copy(node, data) {
      this.currentCopyDataType = data.dataType
      if (this.currentCopyDataType === ModuleConstant.TREE_TYPE_MODEL) {
        this.defaultCodeMaxLengthOnCopy = 30
      } else {
        this.defaultCodeMaxLengthOnCopy = 50
      }
      this.$refs.copyDlg.show({
        id: data.id,
        code: data.code,
        name: data.name,
      })
    },
    // ↑↑↑↑↑树节点操作结束↑↑↑↑↑
    handleMove(node, data) {
      this.moduleList = this.$refs.moduleTree.getData()[0].children
      this.moveData.id = data.id
      this.moveData.oldModuleId = data.moduleId
      this.moveData.moduleId = ''
      this.moveData.dataType = data.dataType
      this.moveWindow = true
      this.$nextTick(function () {
        this.$refs.moveData.clearValidate()
      })
    },
    saveMove(resolve, reject) {
      const that = this
      this.$refs.moveData.validate((valid) => {
        if (valid) {
          that.$http
            .post(CommonAPI.MOVE(), this.moveData)
            .then((res) => {
              if (res.resultcode !== '0') {
                // 禁用http请求报错的默认提示
                that.$message.closeAll()
                reject(res.message ? res.message : '移动出错')
              } else {
                resolve('移动成功')
                that.closeMove()
                that.queryAll()
              }
            })
            .catch((err) => {
              // 禁用http请求报错的默认提示
              this.$message.closeAll()
              reject(err)
            })
        } else {
          reject()
        }
      })
    },
    closeMove() {
      this.$refs.moveData.resetFields()
      this.$refs.moveData.clearValidate()
      this.moveWindow = false
    },
    handleMenuSave(node, data) {
      // 收集所有的菜单组
      const collectMenuGroup = (nodes) => {
        const isNodesHasData = Array.isArray(nodes) && nodes.length > 0
        if (isNodesHasData) {
          // 有数据,过滤仅保留菜单组
          const groupMenu = nodes.filter((item) => item.type === 'group')
          for (const node of groupMenu) {
            node.value = node.id

            const hasChildren =
              Array.isArray(node.children) && node.children.length > 0

            if (hasChildren) {
              const childMenuGroup = collectMenuGroup(node.children)
              if (Array.isArray(childMenuGroup) && childMenuGroup.length > 0) {
                node.children = childMenuGroup
              } else {
                delete node.children
              }
            }
          }
          return groupMenu
        } else {
          return null
        }
      }
      const that = this
      this.$http
        .get(ModuleAPI.MENU_LIST())
        .then((res) => {
          if (res.message || !res.data) {
            that.$message.error(res.message ? res.message : '获取菜单列表失败')
          } else {
            that.$refs.menuWindow.menuList = collectMenuGroup(res.data)
            that.$refs.menuWindow.menuData.data = data
            // that.$refs.menuWindow.menuData.type = '1'
            that.$refs.menuWindow.show()
          }
        })
        .catch((err) => {
          handleErrorMessage(this, err)
        })
    },
    handleNodeClick(data) {
      if (this.currentData?.id === data.id) return
      // 设置空保证simulator 每次都是重新渲染，防止快速切换复用导致的bug
      this.currentData = {}
      const done = () => {
        this.$store.dispatch('setSelectedForm', data.id)
        this.tapNamePath.path = this.$refs.moduleTree.getTapPath(data)
        this.treeData.push(this.tapNamePath.path[0].id)
        this.currentNode.id = data.id ? data.id : ''
        this.currentNode.dataType = data.dataType
        this.currentData = { ...this.currentData, ...data }
        this.$nextTick(() => {
          this.$refs.moduleTree.setCurrentKey(data.id)
        })
      }
      this.$nextTick(() => {
        done()
      })
    },
    clickNode(data) {
      this.$store.dispatch('setSelectedForm', data.id)
      this.$refs.moduleTree.setCurrentKey(data.id)
      this.handleNodeClick(data)
    },
    // 渲染根节点操作
    renderFixedTreeNodeOpBtn(h, { node, data }) {
      // 固定节点
      // 所有模块
      let iconclass = 'iconfont icon-mokuaiguanli'
      if (
        data.dataType === ModuleConstant.TREE_TYPE_PC_FORM ||
        data.dataType === ModuleConstant.TREE_TYPE_MOBILE_FORM
      ) {
        // 页面
        iconclass = 'iconfont icon-yemian'
      } else if (data.dataType === ModuleConstant.TREE_TYPE_MODEL) {
        // 数据模型
        iconclass = 'iconfont icon-shujumoxing'
      }
      return (
        <div class="custom-tree-node">
          <span>
            <i class={iconclass}></i>&nbsp;&nbsp;{data.name}
          </span>
          <div
            class="custom-tree-node-btns"
            on-click={(e) => {
              e.stopPropagation()
            }}
          >
            <el-button
              size="mini"
              icon="iconfont icon-add"
              title="新增"
              type="text"
              on-click={() => this.add(node, data)}
            ></el-button>
          </div>
        </div>
      )
    },
    // 渲染模块树节点操作
    renderModuleTreeNodeOpBtn(h, { node, data }) {
      // 模块
      return (
        <div class="custom-tree-node">
          <span title={data.name} style="width:200px">
            <i class="iconfont icon-mokuaiguanli"></i>&nbsp;&nbsp;
            {data.name}
          </span>
          <div
            class="custom-tree-node-btns"
            on-click={(e) => {
              e.stopPropagation()
            }}
          >
            <el-dropdown on-command={this.handleCommand}>
              <i class="iconfont icon-more"></i>
              <el-dropdown-menu
                slot="dropdown"
                style="overflow: hidden !important;"
              >
                <el-dropdown-item
                  command={this.beforeHandleCommand(node, data, 'edit')}
                >
                  <el-button size="mini" type="text">
                    编辑
                  </el-button>
                </el-dropdown-item>
                <el-dropdown-item
                  command={this.beforeHandleCommand(node, data, 'menu')}
                >
                  <el-button size="mini" type="text">
                    生成菜单
                  </el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      )
    },
    // 渲染表单或数据模型节点操作
    renderFormAndModelTreeNodeOpBtn(h, { node, data }) {
      let iconclass = 'iconfont icon-yemian'
      let homeClass = 'width:200px;'
      if (data.dataType === ModuleConstant.TREE_TYPE_MODEL) {
        // 数据模型
        iconclass = 'iconfont icon-shujumoxing'
      } else {
        // PC页面、APP页面
        const homeCode = node.parent.data.homeCode
          ? node.parent.data.homeCode.split(',')
          : []
        if (homeCode.indexOf(data.code) !== -1) {
          homeClass =
            'color:rgb(23,126,173);font-weight:bolder;style:width:200px;'
          iconclass = 'iconfont icon-shouye1'
        }
      }
      return (
        <div class="custom-tree-node">
          <span title={data.name} style1={homeClass} style={homeClass}>
            <i class={iconclass}></i>&nbsp;&nbsp;{data.name}
          </span>
          <div
            class="custom-tree-node-btns"
            on-click={(e) => {
              e.stopPropagation()
            }}
          >
            <el-dropdown on-command={this.handleCommand}>
              <i class="iconfont icon-more"></i>
              <el-dropdown-menu
                slot="dropdown"
                style="overflow: hidden !important;"
              >
                {data.publishStatus !== 2 ? (
                  <el-dropdown-item
                    command={this.beforeHandleCommand(node, data, 'edit')}
                  >
                    <el-button size="mini" type="text">
                      编辑
                    </el-button>
                  </el-dropdown-item>
                ) : (
                  ''
                )}
                <el-dropdown-item
                  command={this.beforeHandleCommand(node, data, 'copy')}
                >
                  <el-button size="mini" type="text">
                    复制
                  </el-button>
                </el-dropdown-item>
                <el-dropdown-item
                  command={this.beforeHandleCommand(node, data, 'move')}
                >
                  <el-button size="mini" type="text">
                    移动
                  </el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      )
    },
    renderTree(h, param) {
      const { data } = param
      // 所有固定节点(所有模块,表单下的PC页面,APP页面)id都以0开头
      if (data.id && !data.id.startsWith('0')) {
        if (data.dataType === ModuleConstant.TREE_TYPE_MODULE) {
          return this.renderModuleTreeNodeOpBtn(h, param)
        } else if (
          [
            ModuleConstant.TREE_TYPE_PC_FORM,
            ModuleConstant.TREE_TYPE_MOBILE_FORM,
            ModuleConstant.TREE_TYPE_MODEL,
          ].includes(data.dataType)
        ) {
          return this.renderFormAndModelTreeNodeOpBtn(h, param)
        }
      } else {
        return this.renderFixedTreeNodeOpBtn(h, param)
      }
    },
    filterNodeMethod(value, data) {
      if (!value) return true
      return (
        data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1 ||
        (data.code &&
          data.code.toLowerCase().indexOf(value.toLowerCase()) !== -1)
      )
    },
    beforeHandleCommand(node, data, command) {
      return {
        node,
        data,
        command,
      }
    },
    handleCommand(command) {
      switch (command.command) {
        case 'edit':
          this.edit(command.node, command.data)
          break
        case 'delete':
          this.delete(command.node, command.data)
          break
        case 'copy':
          this.copy(command.node, command.data)
          break
        case 'menu':
          this.handleMenuSave(command.node, command.data)
          break
        case 'move':
          this.handleMove(command.node, command.data)
      }
    },
    // 表单/数据模型新增
    handleAdd(data) {
      if (Array.isArray(data)) {
        data = {
          moduleId: this.currentData.id,
          moduleName: this.currentData.name,
        }
      }
      this.title = '新增'
      if (
        data.dataType === ModuleConstant.TREE_TYPE_PC_FORM ||
        data.dataType === ModuleConstant.TREE_TYPE_MOBILE_FORM
      ) {
        this.$refs.formDialog.setDataType(data.dataType)
        this.$refs.formDialog.show()
        this.$refs.formDialog.resetFields({
          moduleCode: this.moduleScop.code,
          moduleName: this.moduleScop.name,
          appCode: this.appName,
        })
      } else if (data.dataType === 'model') {
        this.$openTab(
          '/lowcode/model/edit',
          {
            query: {
              title: this.appName + '数据模型新增',
              moduleCode: this.moduleScop.code,
              appCode: this.appCode,
            },
          },
          () => {
            this.queryAll()
          }
        )
      }
    },
    // 表单/数据模型编辑
    handleEdit(row) {
      this.title = '编辑'
      if (
        row.dataType === ModuleConstant.TREE_TYPE_PC_FORM ||
        row.dataType === ModuleConstant.TREE_TYPE_MOBILE_FORM
      ) {
        this.$refs.formDialog.setDataType(row.dataType)
        this.$refs.formDialog.show()
        this.$refs.formDialog.resetFields(row)
      } else if (row.dataType === 'model') {
        // 此时row为当前模型,modelKind应该取其所在的type,这里有没有值,因此就不取modelKind,待通过id初始化后再复制type
        this.$openTab(
          '/lowcode/model/edit',
          {
            title: this.appName + '数据模型编辑',
            query: { id: row.id, appCode: this.appCode },
          },
          () => {
            if (this.$refs.modelDetailRef) {
              this.$refs.modelDetailRef.loadDataModel()
            }
            this.queryAll()
          }
        )
      }
    },
    handleTabClick(tab) {
      this.$refs[tab.name + 'Ref'].query()
    },
    handleTreeCollaspace() {
      this.treeCollaspace = !this.treeCollaspace
    },
    getAppInfo(appCode) {
      this.$http.get(MfAppAPI.MFAPP_DETAIL(), { code: appCode }).then((res) => {
        this.appInfo = res.data
      })
    },
    goToDesign() {
      this.$openTab(
        '/lowcode/form/design',
        {
          query: {
            id: this.currentData.id,
            moduleCode: this.moduleScop.code,
            appCode: this.appCode,
          },
          title: `设计-${this.currentData.name}`,
        },
        () => {}
      )
    },
  },
}
</script>

<style scoped lang="scss">
$drawer-btn-border: #a9abae99;

// 展开收起按钮
.drawer-btn {
  font-size: 14px;
  color: #3d4a55;
  background: #ffffff;
  text-align: center;
  line-height: 2px;
  border: 1px solid $drawer-btn-border;
  border-left: none;
  position: absolute;
  top: calc(50% - 25px);
  left: 24.5em;
  padding: 30px 5px;
  writing-mode: tb-rl;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  cursor: pointer;
  span {
    bottom: 10px;
    left: 12px;
    color: #666666f0;
    display: inline-block;
    transform: rotate(90deg);
  }
  &.drawer-btn-collaspace {
    left: 0px !important;
    span {
      transform: rotate(270deg);
    }
  }
}
.custom-tree-container {
  flex: 1;
  display: flex;
  position: relative;
  .tree-index.tree-leftmenu {
    min-width: 330px;
    max-width: 330px;
  }
}

.block {
  width: 100%;
  &.block-collaspace {
    width: -webkit-calc(100% - 330px);
    width: -moz-calc(100% - 330px);
    width: calc(100% - 330px);
    margin-left: 10px;
  }
  // 和下方内容宽度对齐
  .header-nav {
    margin: 0px 10px;
    display: flex;
    justify-content: space-between;
    .xn-form {
      width: 100%;
    }
  }
  // 第二个app-container不设置padding
  .app-container .app-container {
    padding: 0px;
    height: 100%;
  }
}
.appendPath {
  max-width: 100px;
  display: block;
}
.el-message-box {
  width: 520px !important;
}
.app-container-coverage,
.app-container-coverage .app-container {
  height: calc(100% - 20px) !important;
}
.preview-mask {
  position: relative;
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }
  ::v-deep .lc-page {
    overflow: auto;
  }
}
</style>
