<template>
  <xn-dialog
    :title="title"
    width="520px"
    :show.sync="display"
    :buttons="buttons"
    @save="handleSave"
    @close="handleClose"
  >
    <template slot="dialog-form">
      <el-form
        id="formDataSet"
        :model="model"
        :rules="rules"
        label-width="100px"
        ref="formDataSetRef"
      >
        <el-form-item label="编码" prop="code">
          <span v-if="!!model.id">{{ model.code }}</span>
          <el-input
            v-else
            v-model.trim="model.code"
            class="formComponent_item"
            :maxlength="50 - initCodeLen"
          >
            <span slot="prepend" v-if="isMultiAppMode">{{ appCode }}-</span>
            <div
              slot="suffix"
              style="display: flex; align-items: center; height: 100%"
            >
              <span>{{ model.code.length + initCodeLen }}/50</span>
            </div>
          </el-input>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input
            v-model.trim="model.name"
            class="formComponent_item"
            show-word-limit
            :maxlength="50"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model.trim="model.description"
            class="dialog-input"
            type="textarea"
            :rows="6"
            show-word-limit
            :maxlength="300"
          />
        </el-form-item>
      </el-form>
    </template>
  </xn-dialog>
</template>
<script>
import { isMultiAppMode } from 'src/config'
import { validateFormCode } from 'src/utils'
import { LcComponentAPI } from 'src/api'
import { FORM_TYPE, ModuleConstant } from '@/constant'

export default {
  name: 'LcDataSetEdit',
  props: {
    title: {
      type: String,
      default: '新增',
    },
  },
  data() {
    return {
      buttons: [
        { action: 'save', type: 'primary', event: 'save' },
        { action: 'cancel', type: 'info', event: 'close' },
      ],
      display: false,
      rules: {
        code: [
          { required: true, message: '请填写编码' },
          {
            validator: validateFormCode,
            message:
              '组件编码必须以字母开头，由英文字母、数字、连字符（-）组成',
          },
        ],
        name: [{ required: true, message: '请填写名称' }],
      },
      model: {
        appCode: '',
        id: '',
        code: '',
        name: '',
        description: '',
      },
      isMultiAppMode: isMultiAppMode(),
      initCodeLen: 0,
    }
  },
  methods: {
    show() {
      this.display = true
    },
    async resetFields(data) {
      if (data.id) {
        await this.$http
          .get(LcComponentAPI.GET_FORM_WITH_DESIGN_DATA_BY_ID_ON_DESIGN(), {
            id: data.id,
          })
          .then((res) => {
            this.resetValue(res.data)
          })
      } else {
        this.resetValue(data)
      }
    },
    resetValue(data) {
      this.model.appCode = data.appCode || ''
      this.model.id = data.id || ''
      this.model.name = data.name || ''
      if (data.type) {
        this.model.type = data.type
      } else {
        if (this.dataType === ModuleConstant.TREE_TYPE_PC_FORM) {
          this.model.type = FORM_TYPE.PC_PAGE
        }
      }

      // 拆分code
      if (this.isMultiAppMode) {
        if (data?.code && data.code !== '') {
          if (data.code.startsWith(this.getAppCode())) {
            this.model.code = data.code.slice(this.getAppCode().length + 1)
          }
        } else {
          this.model.code = ''
        }
      } else {
        this.model.code = data.code || ''
      }
      this.model.description = data.description || ''
      this.$nextTick(() => {
        this.$refs.formDataSetRef.clearValidate()
      })
    },
    handleSave(resolve, reject) {
      if (this.isMultiAppMode && (!this.model.appCode || !this.getAppCode())) {
        reject('缺少应用数据，无法保存')
      }
      this.$refs.formDataSetRef.validate((valid) => {
        if (valid) {
          const form = { ...this.model }
          // 组合code
          if (this.isMultiAppMode) {
            form.code = this.getAppCode() + '-' + this.model.code
          }
          this.$http
            .post(LcComponentAPI.COMPONENT_SAVE(), form)
            .then(() => {
              resolve()
              this.handleClose()
              this.$emit('refreshTableData')
            })
            .catch((error) => {
              // 禁用http请求报错的默认提示
              this.$message.closeAll()
              reject(error)
            })
        } else {
          reject()
        }
      })
    },
    handleClose() {
      this.display = false
      this.$refs.formDataSetRef.clearValidate()
    },
  },
}
</script>
