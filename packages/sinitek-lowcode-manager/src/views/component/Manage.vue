<template>
  <div id="ComponentList" class="app-container filter-container">
    <div>
      <xn-table
        id="componentList"
        :querymodel="formQuery.model"
        :url="componentList.listUrl"
        :toolbars="componentList.buttons"
        default-order="name:asc"
        @add="handleAdd"
        @delete="handleDelete"
        @publish="handlePublish"
        @doc="handleDoc"
        ref="componentListRef"
      >
        <div slot="search">
          <el-form
            id="dataSetQuery"
            :inline="true"
            :model="formQuery.model"
            is-query-input
          >
            <el-form-item label="编码">
              <el-input
                clearable
                placeholder="请输入编码"
                v-model.trim="formQuery.model.code"
              />
            </el-form-item>
            <el-form-item label="名称">
              <el-input
                clearable
                placeholder="请输入名称"
                v-model.trim="formQuery.model.name"
              />
            </el-form-item>
            <el-form-item>
              <xn-button
                id="btnFormQueryQuery"
                class="filter-item"
                type="primary"
                icon="el-icon-search"
                :show-loading="false"
                @click="handleQueryComponent"
              >
                查询
              </xn-button>
            </el-form-item>
          </el-form>
        </div>
        <xn-col type="selection" prop="组件id" width="40" />
        <xn-col
          prop="code"
          label="编码"
          min-width="180"
          align="left"
          sortable="true"
          show-overflow-tooltip
        />
        <xn-col
          prop="name"
          label="名称"
          min-width="180"
          align="left"
          sortable="true"
          show-overflow-tooltip
        />
        <!-- <xn-col
          prop="publishVersion"
          label="版本号"
          min-width="80px"
          align="right"
          show-overflow-tooltip
        />
        <xn-col
          prop="publishStatus"
          label="版本状态"
          min-width="100px"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ currentVersionStatusLabel(scope.row.publishStatus) }}
          </template>
        </xn-col>-->
        <xn-col
          prop="createTimeStamp"
          label="创建时间"
          width="180"
          align="center"
          sortable="true"
        />
        <xn-col
          prop="updateTimeStamp"
          label="更新时间"
          width="180"
          align="center"
          sortable="true"
        />
        <xn-col label="操作" min-width="140" align="center" fixed="right">
          <template slot-scope="scope">
            <xn-col-action-group :keys="scope.row">
              <xn-col-action
                v-for="(colBtn, btnIndex) in operations"
                :key="btnIndex"
                @click="handleClickOpera(scope, colBtn)"
              >
                {{ colBtn.label }}
              </xn-col-action>
            </xn-col-action-group>
          </template>
        </xn-col>
      </xn-table>
      <form-dialog
        :title="title"
        :forms="forms"
        @refreshTableData="queryAll"
        ref="formDialog"
      />
    </div>
  </div>
</template>
<script>
import { isMultiAppMode } from 'src/config'
import { LcComponentAPI, MfAppAPI } from 'src/api'
import { VERSION_STATUS_LABELS } from '@/constant'
export default {
  name: 'LcComponentManage',
  props: {
    param: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  components: {
    FormDialog: () => import('./components/LcComponentEdit'),
  },
  created() {
    if (isMultiAppMode()) {
      if (this.$route.query.appCode) {
        this.appCode = this.$route.query.appCode
        this.getMfAppInfo(this.appCode)
      }
      this.appName =
        decodeURIComponent(this.$route.query.title.split('-')[0]) + '-'
    }
  },
  data: function () {
    return {
      formQuery: {
        model: {
          name: '',
          code: '',
        },
      },
      title: '组件管理',
      forms: [],
      appCode: '',
      appInfo: {
        serviceName: '',
        name: '',
        contextPath: '',
      },
      appName: '',
      componentList: {
        listUrl: Object.freeze(LcComponentAPI.COMPONENT_SEARCH()),
        buttons: [
          { label: '新增', type: 'add', event: 'add' },
          { label: '删除', type: 'delete', event: 'delete' },
          // { label: '发布', type: 'publish', event: 'publish' },
          { label: '说明文档', type: 'doc', event: 'doc' },
        ],
      },
      operations: [
        {
          label: '设计',
          functionName: 'handleDesign',
        },
        {
          label: '编辑',
          functionName: 'handleEdit',
        },
        {
          label: '删除',
          functionName: 'handleDelete',
        },
        /* {
          label: '发布',
          functionName: 'handlePublish',
        }, */
      ],
    }
  },
  methods: {
    getAppCode: () => {
      return this.appCode
    },
    getAppInfo: () => {
      return this.appInfo
    },
    getAppName: () => {
      return this.appName
    },
    query() {
      this.$nextTick(() => {
        this.$refs.componentListRef.query()
      })
    },
    handleAdd() {
      this.title = '新增'
      this.$refs.formDialog.show()
      this.$refs.formDialog.resetFields({
        appCode: this.appCode,
      })
    },
    handleEdit(row) {
      this.title = '编辑'
      this.$refs.formDialog.show()
      this.$refs.formDialog.resetFields(row)
    },
    handleDesign({ id, name, code }) {
      sessionStorage.setItem(
        'LCComponentData',
        JSON.stringify({ id, name, code })
      )
      // 传值通过sessionStorage, 保证只会开一个tab
      this.$openTab('/lowcode/form/form-component')
    },
    handleQueryComponent() {
      this.$refs.componentListRef.query()
    },
    getMfAppInfo(appCode) {
      this.$http.get(MfAppAPI.MFAPP_DETAIL(), { code: appCode }).then((res) => {
        if (res.data) {
          this.appInfo = res.data
        } else {
          this.$message.info(`根据应用编码【${appCode}】无法获取应用信息`)
        }
      })
    },
    currentVersionStatusLabel(status) {
      return VERSION_STATUS_LABELS[status] || ''
    },
    handleClickOpera(scope, item) {
      this[item.functionName](scope.row)
    },
    handleDelete(row) {
      this.$confirm
        .delete()
        .then(() => {
          const ids = []
          if (Array.isArray(row)) {
            row.forEach((item) => ids.push(item.id))
          } else {
            ids.push(row.id)
          }
          const that = this
          this.$http
            .post(LcComponentAPI.COMPONENT_DELETE(), { ids })
            .then(() => {
              that.$message.deleteWithSuccess()
              that.query()
            })
            .catch((err) => {
              if (typeof err === 'object' && err && err.message) {
                that.$message.error(err.message)
              } else if (typeof err === 'string' && err) {
                that.$message.error(err)
              } else {
                that.$message.error('服务器错误，请联系管理员')
              }
            })
        })
        .catch(() => {})
    },
    handlePublish(row) {
      this.$confirm
        .publish()
        .then(() => {
          const ids = []
          if (Array.isArray(row)) {
            row.forEach((item) => ids.push(item.id))
          } else {
            ids.push(row.id)
          }
          const that = this
          this.$http
            .post(LcComponentAPI.COMPONENT_PUBLISH(), { ids })
            .then(() => {
              that.$message.publishWithSuccess()
              that.query()
            })
            .catch((err) => {
              if (typeof err === 'object' && err && err.message) {
                that.$message.error(err.message)
              } else if (typeof err === 'string' && err) {
                that.$message.error(err)
              } else {
                that.$message.error('服务器错误，请联系管理员')
              }
            })
        })
        .catch(() => {})
    },
    queryAll() {
      this.query()
      this.$emit('afterSave')
    },
    openEdit(title, data) {
      const query = {
        title: this.appName + title,
        componentId: data ? data.id : undefined,
      }
      if (this.isMultiAppMode) {
        query.appCode = this.appCode
      }
      this.$openTab('/lowcode/component/save', { query }, () => {
        this.query()
      })
    },
    handleDoc() {
      window.open(
        'http://*************:18056/doc/sinitek-lowcode/guide/component/manage.html#%E5%88%97%E8%A1%A8',
        '_blank'
      )
    },
  },
}
</script>
