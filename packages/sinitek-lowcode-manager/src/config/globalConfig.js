import { RuntimeMode } from 'src/constant'
import {
  registry,
  setTransformJSFunctionState as stjs,
} from 'sinitek-lowcode-shared'
// 调试
let debug = false
// 运行模式
let RUNTIME_MODE = RuntimeMode.SINGLE_APP
// 是否表单服务
let isMfService = false

// 切换到新接口
/**
 * /frontend/api/lowcode/model/dynamic/save
/frontend/api/lowcode/model/dynamic/{pageCode}/{componentCode}/{dataModelCode}/save
 */
let useNewAPI = false

export function setDebugMode(value) {
  debug = value
}

export function isDebugMode() {
  return debug
}

export function setMetaFormRuntimeMode(value) {
  RUNTIME_MODE = value
}

export function getMetaFormRuntimeMode() {
  return RUNTIME_MODE
}

export function isMultiAppMode() {
  return RUNTIME_MODE === RuntimeMode.MULTI_APP
}

export function isSingleAppMode() {
  return RUNTIME_MODE === RuntimeMode.SINGLE_APP
}

export function setIsMetaFormService(value) {
  isMfService = !!value
}

export function isMetaFormService() {
  // 单应用下都是true
  if (isSingleAppMode()) {
    return true
  }
  // 多应用下看isMfService配置
  return isMfService
}
export function registryLowcodePack(...args) {
  registry(...args)
}

export function registryLowcodeComponent(vueComponent) {
  registry({
    install(simulator) {
      simulator.addComponent(vueComponent.name, vueComponent)
      simulator.addMaterials(vueComponent.material)
    },
  })
}
export function registryLowcodeMaterials(material) {
  registry({
    install(simulator) {
      simulator.addMaterials(material)
    },
  })
}
export function setTransformJSFunctionState(value = true) {
  stjs(value)
}

export function isUseNewAPI() {
  return useNewAPI
}

export function setUseNewAPI(value) {
  useNewAPI = value
}
