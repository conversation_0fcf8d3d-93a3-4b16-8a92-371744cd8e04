import {
  formManageRoutes,
  formDebugRoutes,
  formAppRoutes,
  templateRoutes,
} from 'src/router'
import stores from 'src/store'
import { isDebugMode, isMultiAppMode, isMetaFormService } from 'src/config'
import LcEditor from 'src/views/components/LcEditor'
import 'src/styles/index.css'
import 'src/assets/icons/iconfont.css'
import 'sinitek-lowcode-simulator/dist/sinitek-lowcode-simulator.css'
import 'sinitek-lowcode-advanced/dist/sinitek-lowcode-advanced.css'
import 'sinitek-lowcode-render/dist/sinitek-lowcode-render.css'
import LcRender from 'sinitek-lowcode-render'
import icons from './icons'

export { LcRender }
export const LcCanvas = () =>
  import('sinitek-lowcode-simulator').then((res) => res.Canvas)

export * from 'src/constant'
export * from 'src/api'
export * from 'src/config'
export * from './views/GlobalConstant'

const svgReq = require.context('src/assets/svg', false, /\.svg$/)
const requireAllSvg = (ctx) => {
  ctx.keys().map(ctx)
}
requireAllSvg(svgReq)

const components = [LcEditor]

const install = function (Vue, options) {
  Vue.prototype.$sinitekVersion = {
    ...Vue.prototype.$sinitekVersion,
    // eslint-disable-next-line no-undef
    'sinitek-lowcode-manager': VUE_APP_VERSION,
  }
  if (options?.popupManager) {
    window.__LC_popupManager__ = options.popupManager
  }

  // 组件注册
  components.forEach((component) => {
    Vue.component(component.name, component)
  })
}

export default {
  install,
  getMenuRoutes: function () {
    const routes = []
    if (isDebugMode()) {
      routes.push(...formDebugRoutes)
    }
    if (isMultiAppMode()) {
      if (isMetaFormService()) {
        routes.push(...formAppRoutes)
        routes.push(...formManageRoutes)
      }
    } else {
      routes.push(...formManageRoutes)
    }
    routes.push(...templateRoutes)
    return routes
  },
  getStores: function () {
    return stores
  },
  getIcons: function () {
    return icons
  },
}
