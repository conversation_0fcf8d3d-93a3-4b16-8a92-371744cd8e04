@font-face {
  font-family: "iconfont"; /* Project id 4567656 */
  src: url('iconfont.woff2?t=1717049101078') format('woff2'),
       url('iconfont.woff?t=1717049101078') format('woff'),
       url('iconfont.ttf?t=1717049101078') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-tianjiashoucangjiazhankai:before {
  content: "\e6f3";
}

.icon-add:before {
  content: "\e60f";
}

.icon-biaodanyemian:before {
  content: "\e7cc";
}

.icon-shouye1:before {
  content: "\e69b";
}

.icon-yemian:before {
  content: "\e69a";
}

.icon-mokuaiguanli:before {
  content: "\e696";
}

.icon-more:before {
  content: "\e716";
}

.icon-shujumoxing:before {
  content: "\e6ab";
}

