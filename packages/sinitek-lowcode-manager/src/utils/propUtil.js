import {
  PROP_TYPE_AND_COMPONENT_OPTIONS_MAP,
  COMPONENT_TYPE_AND_OPTION_MAP,
  COMPONENT_TYPE,
  PROP_TYPE,
  DATA_MODEL_ASSOCIATION,
  DATA_MODEL_ASSOCIATION_AND_OPTION_MAP,
} from 'src/constant'

export const findModelPropComponentTypeOptionsByPropType = (propType) => {
  const options = PROP_TYPE_AND_COMPONENT_OPTIONS_MAP[propType]
  if (Array.isArray(options)) {
    return options
  } else {
    return [COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.INPUT]]
  }
}

export const findModelAssociationOptions = (propType, componentType) => {
  const props = []
  if (propType === PROP_TYPE.FOREIGN_KEY) {
    props.push(
      DATA_MODEL_ASSOCIATION_AND_OPTION_MAP[DATA_MODEL_ASSOCIATION.ONE2ONE]
    )
  } else if (
    propType === PROP_TYPE.MODEL &&
    componentType === COMPONENT_TYPE.CHILDREN_FORM
  ) {
    props.push(
      DATA_MODEL_ASSOCIATION_AND_OPTION_MAP[DATA_MODEL_ASSOCIATION.ONE2MANY]
    )
  } else if (
    propType === PROP_TYPE.RELATABLE &&
    componentType === COMPONENT_TYPE.RELA_LIST
  ) {
    props.push(
      DATA_MODEL_ASSOCIATION_AND_OPTION_MAP[DATA_MODEL_ASSOCIATION.ONE2MANY]
    )
  }
  return props
}

// 判断模型的类型是否是框架字段
export const isFrameworkPropType = (type) => {
  return (
    type === PROP_TYPE.KEY ||
    type === PROP_TYPE.CREATETIMESTAMP ||
    type === PROP_TYPE.UPDATETIMESTAMP ||
    type === PROP_TYPE.VERSION
  )
}

// 判断是否为多值
export const isMvValue = (propType) => {
  return (
    propType === PROP_TYPE.SIRMORG_MV ||
    propType === PROP_TYPE.STRING_SIRMENUM_MV ||
    propType === PROP_TYPE.NUMBER_SIRMENUM_MV ||
    propType === PROP_TYPE.FOREIGN_KEY_MV
  )
}

// 是否为枚举
export const isEnumType = (propType) => {
  return (
    propType === PROP_TYPE.STRING_SIRMENUM ||
    propType === PROP_TYPE.NUMBER_SIRMENUM ||
    propType === PROP_TYPE.STRING_SIRMENUM_MV ||
    propType === PROP_TYPE.NUMBER_SIRMENUM_MV
  )
}
