import { PROP_TYPE_MAP, COMPONENT_TYPE_AND_OPTION_MAP } from 'src/constant'

/**
 * 转换列表的数据
 * @param data
 */
export const formatDataModel = (data) => {
  // 类型
  const modelType = PROP_TYPE_MAP[data.type]
  const componentTypeOption = COMPONENT_TYPE_AND_OPTION_MAP[data.componentType]
  // 格式化关联关联属性 `parentName:childName`, 逗号分割
  const relaModelProp = []
  if (data.propRelas && data.propRelas.length > 0) {
    data.propRelas.forEach((item) => {
      relaModelProp.push(`${item.parentName}:${item.childName}`)
    })
  }
  return {
    modelTypeLabel: modelType?.label || '',
    componentTypeLabel: componentTypeOption ? componentTypeOption.label : '',
    relaModelProp: relaModelProp.join(','),
  }
}
