export function sqlValidate(value) {
  const reg = /^select\s.*/i
  const reg2 = /^(insert|delete|update|alter|truncate).*/i
  return reg.test(value) && !reg2.test(value)
}

export function validateSql(rule, value, callback) {
  if (sqlValidate(value)) {
    callback()
  }
  callback(new Error(rule.message))
}

export function validateCode(rule, value, callback) {
  const reg = /^[a-zA-Z][\w-]*$/
  if (value && !reg.test(value)) {
    callback(new Error(rule.message))
  }
  callback()
}

// 表单编码是出现在url中的,不能含有_
export function validateFormCode(rule, value, callback) {
  const reg = /^[A-Za-z/][A-Za-z0-9/-]*$/
  if (value && !reg.test(value)) {
    callback(new Error(rule.message))
  }
  callback()
}

// 验证输入的url格式
export function validateUrl(rule, value, callback) {
  const reg = /^[A-Za-z/][A-Za-z0-9_/-]*$/
  if (!reg.test(value)) {
    callback(new Error(rule.message))
  }
  callback()
}

/**
 * 验证输入的是否是整数
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function validateIntNum(rule, value, callback) {
  const reg = /^[0-9]+$/
  if (!reg.test(value)) {
    callback(new Error(rule.message))
  }
  callback()
}
