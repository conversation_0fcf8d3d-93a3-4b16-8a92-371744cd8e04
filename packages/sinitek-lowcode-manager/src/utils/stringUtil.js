/**
 * 把colName(approve_status)转成PropName(approveStatus)
 * @param {String} colName
 */
export const makeColName2PropName = (colName) => {
  colName = colName
    .replace(/_/g, ' ')
    .replace(/\b\w+\b/g, function (word) {
      return word.substring(0, 1).toUpperCase() + word.substring(1)
    })
    .replace(/\s/g, '')
  return colName.substring(0, 1).toLowerCase() + colName.substring(1)
}

export const makePropName2ColName = (propName) => {
  return propName.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase()
}
