export const module = {
  state: {
    selectedForm: '0',
    appName: '',
  },
  getters: {
    selectedForm: (state) => state.selectedForm,
    appName: (state) => state.appName,
  },
  mutations: {
    SET_CURRENT_FORM(state, selectedForm) {
      state.selectedForm = selectedForm
    },
    SET_CURRENT_APPNAME(state, appName) {
      state.appName = appName
    },
  },
  actions: {
    setSelectedForm({ commit }, selectedFormKey) {
      commit('SET_CURRENT_FORM', selectedFormKey)
    },
    setAppName({ commit }, appNameKey) {
      commit('SET_CURRENT_APPNAME', appNameKey)
    },
  },
}
