export const formAppRoutes = [
  {
    component: () => import('src/views/lcapp/LcAppList'),
    name: '<PERSON><PERSON><PERSON>ppList',
    path: '/lowcode/form/app/list',
    meta: { title: '表单应用管理' },
  },
]

export const formManageRoutes = [
  {
    component: () => import('src/views/module/list'),
    name: 'LcModuleManage',
    path: '/lowcode/module/list',
    meta: { title: '表单管理' },
  },
  // {
  //   component: () => import('src/views/module/demo'),
  //   name: 'ModuleManageDemo',
  //   path: '/lowcode/module/demo',
  //   meta: { title: '表单管理demo' },
  // },
  {
    component: () => import('src/views/form/FormHistoryList'),
    name: 'LcFormHistoryList',
    path: '/lowcode/form/history-list/:formId',
    meta: { title: '表单历史' },
  },
  {
    component: () => import('src/views/data-set/DataSetList'),
    name: 'LcDataSetList',
    path: '/lowcode/dataset/list',
    meta: { title: '数据集管理' },
  },
  {
    component: () => import('src/views/data-set/components/DataSetEdit'),
    name: 'LcDataSetEdit',
    path: '/lowcode/dataset/edit',
    meta: { title: '数据集编辑' },
  },
  {
    component: () => import('src/views/model/components/ModelEdit'),
    name: 'LcModelEdit',
    path: '/lowcode/model/edit',
    meta: { title: '数据模型编辑' },
  },
  {
    component: () => import('src/views/url-proxy/UrlProxyList'),
    name: 'LcUrlProxyList',
    path: '/lowcode/url-proxy/list',
    meta: { title: 'URL请求代理管理' },
  },
  {
    component: () => import('src/views/url-proxy/components/UrlProxyEdit'),
    name: 'LcUrlProxyEdit',
    path: '/lowcode/url-proxy/edit',
    meta: { title: 'URL请求代理编辑' },
  },
  {
    component: () => import('src/views/form/FormMaking'),
    name: 'LcFormMaking',
    path: '/lowcode/form/form-making',
    meta: { title: '设计器' },
  },
  {
    component: () => import('src/views/form/FormPreview'),
    name: 'LcFormPreview',
    path: '/lowcode/form/form-preview',
    meta: { title: '低代码预览' },
  },
  {
    component: () => import('src/views/form/FormRender'),
    name: 'LcFormRender',
    path: '/lowcode/form/form-render/:code',
    meta: { title: '低代码渲染' },
  },
  {
    component: () => import('src/views/form/FormDebug'),
    name: 'LcFormDebug',
    path: '/lowcode/form/form-debug',
    meta: { title: '低代码调试' },
  },
  {
    component: () => import('src/views/form/FormComponent'),
    name: 'LcFormComponent',
    path: '/lowcode/form/form-component',
    meta: { title: '低代码组件设计' },
  },
  {
    component: () => import('src/views/form/design.vue'),
    name: 'LcFormDesign',
    path: '/lowcode/form/design',
    meta: { title: '低代码表单设计' },
  },
  {
    component: () => import('src/views/component/Manage'),
    name: 'LcComponentManage',
    path: '/lowcode/component/manage',
    meta: { title: '低代码组件管理' },
  },
  {
    component: () => import('src/views/entity-relation/index'),
    name: 'LcEntityRelation',
    path: '/lowcode/entity-relation',
    meta: { title: '实体关系' },
  },
]

export const formDebugRoutes = []
