import { isMultiAppMode } from 'src/config'
export default {
  components: {
    syncDialog: () => import('../views/components/syncDialog.vue'),
  },
  data() {
    return {
      isMultiAppMode: isMultiAppMode(),
    }
  },
  inject: {
    getAppInfo: {
      type: Function,
      default: () => function () {},
    },
  },
  computed: {
    appInfo() {
      return this.getAppInfo()
    },
  },
  mounted() {
    if (this.isMultiAppMode) {
      // this.$refs[this.tableRef].toolbars.push({
      //   type: 'slot',
      //   name: 'sync',
      // })
      this.operations.push({
        // label: '同步',
        // functionName: 'handleClickSyncSingle',
      })
    }
  },
  methods: {
    // 这是操作栏中xn-col
    handleClickSyncSingle(row) {
      this.$refs.syncDialogSingle.selectionsData = [row]
      // 同步组件同步单行数据
      this.$refs.syncDialogSingle.handleDataSetSync()
    },
    handleClickOpera(scope, item) {
      this[item.functionName](scope.row)
    },
  },
}
