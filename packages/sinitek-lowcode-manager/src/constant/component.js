export const COMPONENT_TYPE = {
  TEXT: 'TEXT',
  INPUT: 'INPUT',
  TEXTAREA: 'TEXTAREA',
  NUMBER: 'NUMBER',
  MONEY: 'MONEY',
  RADIO_GROUP: 'RADIO_GROUP',
  CHECKBOX: 'CHECKBOX',
  CHECKBOX_GROUP: 'CHECKBOX_GROUP',
  TIME: 'TIME',
  DATE: 'DATE',
  DATETIME: 'DATETIME',
  RATE: 'RATE',
  COLOR: 'COLOR',
  SELECT: 'SELECT',
  SWITCH: 'SWITCH',
  SLIDER: 'SLIDER',
  LINK: 'LINK',
  SIRMORG: 'SIRMORG',
  FILE: 'FILE',
  RELA_FORM: 'RELA_FORM',
  CHILDREN_FORM: 'CHILDREN_FORM',
  RELA_LIST: 'RELA_LIST',
}

// key: 组件类型
// value: 组件选项
export const COMPONENT_TYPE_AND_OPTION_MAP = {
  [COMPONENT_TYPE.TEXT]: { value: COMPONENT_TYPE.TEXT, label: '文本' },
  [COMPONENT_TYPE.INPUT]: { value: COMPONENT_TYPE.INPUT, label: '输入框' },
  [COMPONENT_TYPE.TEXTAREA]: {
    value: COMPONENT_TYPE.TEXTAREA,
    label: '多行文本输入框',
  },
  [COMPONENT_TYPE.NUMBER]: {
    value: COMPONENT_TYPE.NUMBER,
    label: '数字输入框',
  },
  [COMPONENT_TYPE.MONEY]: { value: COMPONENT_TYPE.MONEY, label: '金额输入框' },
  [COMPONENT_TYPE.RADIO_GROUP]: {
    value: COMPONENT_TYPE.RADIO_GROUP,
    label: '单选按钮组',
  },
  [COMPONENT_TYPE.CHECKBOX]: {
    value: COMPONENT_TYPE.CHECKBOX,
    label: '复选框',
  },
  [COMPONENT_TYPE.CHECKBOX_GROUP]: {
    value: COMPONENT_TYPE.CHECKBOX_GROUP,
    label: '复选框组',
  },
  [COMPONENT_TYPE.TIME]: { value: COMPONENT_TYPE.TIME, label: '时间选择器' },
  [COMPONENT_TYPE.DATE]: { value: COMPONENT_TYPE.DATE, label: '日期选择器' },
  [COMPONENT_TYPE.DATETIME]: {
    value: COMPONENT_TYPE.DATETIME,
    label: '日期时间选择器',
  },
  [COMPONENT_TYPE.RATE]: { value: COMPONENT_TYPE.RATE, label: '评分' },
  [COMPONENT_TYPE.COLOR]: { value: COMPONENT_TYPE.COLOR, label: '颜色选择器' },
  [COMPONENT_TYPE.SELECT]: {
    value: COMPONENT_TYPE.SELECT,
    label: '下拉选择器',
  },
  [COMPONENT_TYPE.SWITCH]: { value: COMPONENT_TYPE.SWITCH, label: '开关' },
  [COMPONENT_TYPE.SLIDER]: { value: COMPONENT_TYPE.SLIDER, label: '滑块' },
  [COMPONENT_TYPE.LINK]: { value: COMPONENT_TYPE.LINK, label: '链接' },
  [COMPONENT_TYPE.SIRMORG]: {
    value: COMPONENT_TYPE.SIRMORG,
    label: '人员组织选择器',
  },
  [COMPONENT_TYPE.FILE]: { value: COMPONENT_TYPE.FILE, label: '文件' },
  [COMPONENT_TYPE.RELA_FORM]: {
    value: COMPONENT_TYPE.RELA_FORM,
    label: '关联表单',
  },
  [COMPONENT_TYPE.CHILDREN_FORM]: {
    value: COMPONENT_TYPE.CHILDREN_FORM,
    label: '子表单',
  },
  [COMPONENT_TYPE.RELA_LIST]: {
    value: COMPONENT_TYPE.RELA_LIST,
    label: '关联列表',
  },
}

let componentTypeOptions = []

for (const key in COMPONENT_TYPE_AND_OPTION_MAP) {
  componentTypeOptions = componentTypeOptions.concat(
    COMPONENT_TYPE_AND_OPTION_MAP[key]
  )
}

// 组件类型选项
export const COMPONENT_TYPE_OPTIONS = [...componentTypeOptions]
