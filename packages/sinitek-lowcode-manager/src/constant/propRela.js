export const RELA_TYPE = {
  // 关联键
  RELAKEY: 'RELAKEY',
  // 固定值
  VALUE: 'VALUE',
}

export const RELA_TYPE_OPTIONS = [
  { value: RELA_TYPE.RELAKEY, label: '关联键' },
  { value: RELA_TYPE.VALUE, label: '固定值' },
]

export const RELA_VALUE_TYPE = {
  STRING: 0,
  NUMBER: 1,
}

export const RELA_VALUE_TYPE_OPTIONS = [
  { value: RELA_VALUE_TYPE.STRING, label: '字符串' },
  { value: RELA_VALUE_TYPE.NUMBER, label: '数字' },
]
