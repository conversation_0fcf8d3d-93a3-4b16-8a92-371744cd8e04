export const DATA_SET_FORMAT_TYPE = {
  ENUM: 'ENUM',
  EMPLOYEE: 'EMPLOYEE',
  DATE: 'DATE',
}

const DATA_SET_FORMAT_TYPE_LABELS = {
  [DATA_SET_FORMAT_TYPE.ENUM]: '枚举',
  [DATA_SET_FORMAT_TYPE.EMPLOYEE]: '人员',
  [DATA_SET_FORMAT_TYPE.DATE]: '日期',
}

const result = []

Object.keys(DATA_SET_FORMAT_TYPE).forEach((key) => {
  result.push({
    label: DATA_SET_FORMAT_TYPE_LABELS[DATA_SET_FORMAT_TYPE[key]],
    value: DATA_SET_FORMAT_TYPE[key],
  })
})

export const DATA_SET_FORMAT_OPTIONS = result

export const DATA_SET_SOURCE_TYPE = {
  DATABASE: 1,
  APPLICATION: 2,
}

export const MAX_CONDITION_SQL_STRING_LENGTH = 1000

export const MAX_CONDITIONS_SQL_STRING_TOTAL_LENGTH = 3000

export const MAX_RELATED_QUERY_SQL_STRING_LENGTH = 1000

export const MAX_RELATED_QUERY_STRING_TOTAL_LENGTH = 3000
