export const MODEL_TYPE = {
  // 通用模型
  COMMON: 'COMMON',
  // 附件模型,不推荐使用
  ATTACHMENT: 'ATTACHMENT',
  // 审批模型
  WORKFLOW: 'WORKFLOW',
  // 留痕模型
  DATATRACE: 'DATATRACE',
  // 版本模型
  VERSION: 'VERSION',
  // 自定义模型
  CUSTOM: 'CUSTOM',
  // 树模型
  TREE: 'TREE',
}

export const MODEL_TYPE_OPTIONS = [
  { value: MODEL_TYPE.COMMON, label: '通用模型' },
  { value: MODEL_TYPE.TREE, label: '树模型' },
  // { value: MODEL_TYPE.WORKFLOW, label: '审批模型' },
  // { value: MODEL_TYPE.DATATRACE, label: '留痕模型' },
  // { value: MODEL_TYPE.VERSION, label: '版本模型' },
  { value: MODEL_TYPE.CUSTOM, label: '自定义模型' },
]

// key: 模型类型
// value: 模型选项
export const MODEL_TYPE_AND_OPTION_MAP = {}

MODEL_TYPE_OPTIONS.forEach((item) => {
  MODEL_TYPE_AND_OPTION_MAP[item.value] = item
})

export const DATA_MODEL_ASSOCIATION = {
  ONE2MANY: 'ONE2MANY',
  ONE2ONE: 'ONE2ONE',
}

export const DATA_MODEL_ASSOCIATION_LABEL = {
  [DATA_MODEL_ASSOCIATION.ONE2MANY]: '一对多',
  [DATA_MODEL_ASSOCIATION.ONE2ONE]: '一对一',
}

export const DATA_MODEL_ASSOCIATION_AND_OPTION_MAP = {}

const result = []

for (const key in DATA_MODEL_ASSOCIATION) {
  DATA_MODEL_ASSOCIATION_AND_OPTION_MAP[key] = {
    label: DATA_MODEL_ASSOCIATION_LABEL[DATA_MODEL_ASSOCIATION[key]],
    value: DATA_MODEL_ASSOCIATION[key],
  }
  result.push(DATA_MODEL_ASSOCIATION_AND_OPTION_MAP[key])
}

export const DATA_MODEL_ASSOCIATION_OPTIONS = result

export const ID_TYPE_OPTIONS = [
  { label: '分配ID', value: 3 },
  { label: '数据库自动生成', value: 0 },
]
