import { PROP_TYPE } from './prop'

// 这里维护了表单PropType与数据库DataType的映射关系
export const PROP_TYPE_AND_DB_TYPE_MAP = {
  // 参考http://sinitek2.3322.org:18056/doc/sirmapp-dev/guides/specification/%E6%95%B0%E6%8D%AE%E5%BA%93%E8%AE%BE%E8%AE%A1%E8%A7%84%E8%8C%83.html#%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B
  // mysql
  char: PROP_TYPE.STRING,
  varchar: PROP_TYPE.STRING,
  varchar2: PROP_TYPE.STRING,
  text: PROP_TYPE.STRING,
  longtext: PROP_TYPE.STRING,
  // mysql数字
  tinyint: PROP_TYPE.INTEGER,
  smallint: PROP_TYPE.INTEGER,
  int: PROP_TYPE.INTEGER,
  bigint: PROP_TYPE.INTEGER,
  decimal: PROP_TYPE.DECIMAL,
  // mysql中日期或者日期事件都作为PROP_TYPE.DATE处理
  date: PROP_TYPE.DATE,
  datetime: PROP_TYPE.DATE,

  // oracle
  clob: PROP_TYPE.STRING,
  // oracel的number暂不支持
  timestamp: PROP_TYPE.DATE,

  // postgresql
  int8: PROP_TYPE.INTEGER,
  int4: PROP_TYPE.INTEGER,
  int2: PROP_TYPE.INTEGER,
  numeric: PROP_TYPE.DECIMAL,
}
