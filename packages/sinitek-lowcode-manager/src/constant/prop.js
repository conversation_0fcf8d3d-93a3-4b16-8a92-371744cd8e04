import { <PERSON><PERSON><PERSON>_TYPE } from './model'
import { COMPONENT_TYPE, COMPONENT_TYPE_AND_OPTION_MAP } from './component'
import { COL_NAME } from './col'

export const PROP_TYPE = {
  STRING: 'STRING',
  INTEGER: 'INTEGER',
  LONG: 'LONG',
  BOOLEAN: 'BOOLEAN',
  DECIMAL: 'DECIMAL',
  // 主键
  KEY: 'KEY',
  CREATETIMESTAMP: 'CREATETIMESTAMP',
  UPDATETIMESTAMP: 'UPDATETIMESTAMP',
  VERSION: 'VERSION',
  SIRMORG: 'SIRMORG',
  // 组织结构多值
  SIRMORG_MV: 'SIRMORG_MV',
  STRING_SIRMENUM: 'STRING_SIRMENUM',
  STRING_SIRMENUM_MV: 'STRING_SIRMENUM_MV',
  NUMBER_SIRMENUM: 'NUMBER_SIRMENUM',
  NUMBER_SIRMENUM_MV: 'NUMBER_SIRMENUM_MV',
  DATE: 'DATE',
  // FOREIGN_KEY
  FOREIGN_KEY: 'FOREIGN_KEY',
  FOREIGN_KEY_MV: 'FOREIGN_KEY_MV',
  MODEL: 'MODEL',
  RELATABLE: 'RELATABLE',
  ATTACHMENT: 'ATTACHMENT',
  SYSTEM: 'SYSTEM',
  CUSTOM: 'CUSTOM',
  TREE_NODE_NAME: 'TREE_NODE_NAME',
  // 添加人
  CREATORID: 'CREATORID',
  // 删除人
  REMOVERID: 'REMOVERID',
  // 更新人
  UPDATERID: 'UPDATERID',
}

export const PROP_TYPE_ER_NAME = {
  STRING: '字符串',
  INTEGER: '整数',
  LONG: '长整数',
  BOOLEAN: '布尔',
  DECIMAL: '小数',
  // 主键
  KEY: '主键',
  CREATETIMESTAMP: '日期',
  UPDATETIMESTAMP: '日期',
  VERSION: '整数',
  SIRMORG: '用户',
  SIRMORG_MV: '用户数组',
  STRING_SIRMENUM: '字符串',
  STRING_SIRMENUM_MV: '字符串数组',
  NUMBER_SIRMENUM: '数值',
  NUMBER_SIRMENUM_MV: '数值数组',
  DATE: '日期',
  // FOREIGN_KEY
  FOREIGN_KEY: '外键',
  FOREIGN_KEY_MV: '外键数组',
  MODEL: '子模型',
  RELATABLE: '关联表',
  ATTACHMENT: '附件',
  SYSTEM: '系统',
  CUSTOM: '自定义',
  TREE_NODE_NAME: '树节点名称',
  // 添加人
  CREATORID: '字符串',
  // 删除人
  REMOVERID: '字符串',
  // 更新人
  UPDATERID: '字符串',
}

export const PROP_TYPE_NAME = {
  STRING: '字符串',
  INTEGER: '整数',
  LONG: '长整数',
  BOOLEAN: '布尔',
  DECIMAL: '小数',
  // 主键
  KEY: '主键',
  CREATETIMESTAMP: '新增时间',
  UPDATETIMESTAMP: '更新时间',
  VERSION: '乐观锁',
  SIRMORG: '组织结构',
  SIRMORG_MV: '组织结构(多值)',
  STRING_SIRMENUM: '字符串枚举',
  STRING_SIRMENUM_MV: '字符串枚举(多值)',
  NUMBER_SIRMENUM: '数值枚举',
  NUMBER_SIRMENUM_MV: '数值枚举(多值)',
  DATE: '日期',
  // FOREIGN_KEY
  FOREIGN_KEY: '外键',
  FOREIGN_KEY_MV: '外键（多值）',
  MODEL: '子模型',
  RELATABLE: '关联表',
  ATTACHMENT: '附件',
  SYSTEM: '系统',
  CUSTOM: '自定义',
  TREE_NODE_NAME: '树节点名称',
  // 添加人
  CREATORID: '创建人',
  // 删除人
  REMOVERID: '删除人',
  // 更新人
  UPDATERID: '更新人',
}

/**
 *  模型属性类型选项
 */
export const PROP_TYPE_OPTIONS = [
  { value: PROP_TYPE.STRING, label: PROP_TYPE_NAME.STRING },
  { value: PROP_TYPE.INTEGER, label: PROP_TYPE_NAME.INTEGER },
  { value: PROP_TYPE.LONG, label: PROP_TYPE_NAME.LONG },
  { value: PROP_TYPE.BOOLEAN, label: PROP_TYPE_NAME.BOOLEAN },
  { value: PROP_TYPE.DECIMAL, label: PROP_TYPE_NAME.DECIMAL },
  { value: PROP_TYPE.KEY, label: PROP_TYPE_NAME.KEY },
  { value: PROP_TYPE.FOREIGN_KEY, label: PROP_TYPE_NAME.FOREIGN_KEY },
  { value: PROP_TYPE.FOREIGN_KEY_MV, label: PROP_TYPE_NAME.FOREIGN_KEY_MV },
  { value: PROP_TYPE.SIRMORG, label: PROP_TYPE_NAME.SIRMORG },
  { value: PROP_TYPE.SIRMORG_MV, label: PROP_TYPE_NAME.SIRMORG_MV },
  { value: PROP_TYPE.STRING_SIRMENUM, label: PROP_TYPE_NAME.STRING_SIRMENUM },
  {
    value: PROP_TYPE.STRING_SIRMENUM_MV,
    label: PROP_TYPE_NAME.STRING_SIRMENUM_MV,
  },
  { value: PROP_TYPE.NUMBER_SIRMENUM, label: PROP_TYPE_NAME.NUMBER_SIRMENUM },
  {
    value: PROP_TYPE.NUMBER_SIRMENUM_MV,
    label: PROP_TYPE_NAME.NUMBER_SIRMENUM_MV,
  },
  { value: PROP_TYPE.DATE, label: PROP_TYPE_NAME.DATE },
  { value: PROP_TYPE.MODEL, label: PROP_TYPE_NAME.MODEL },
  // { value: PROP_TYPE.RELATABLE, label: PROP_TYPE_NAME.RELATABLE },
  { value: PROP_TYPE.CREATETIMESTAMP, label: PROP_TYPE_NAME.CREATETIMESTAMP },
  { value: PROP_TYPE.UPDATETIMESTAMP, label: PROP_TYPE_NAME.UPDATETIMESTAMP },
  { value: PROP_TYPE.VERSION, label: PROP_TYPE_NAME.VERSION },
  { value: PROP_TYPE.CUSTOM, label: PROP_TYPE_NAME.CUSTOM },
  { value: PROP_TYPE.ATTACHMENT, label: PROP_TYPE_NAME.ATTACHMENT },
  { value: PROP_TYPE.SYSTEM, label: PROP_TYPE_NAME.SYSTEM, disabled: true },
  {
    value: PROP_TYPE.TREE_NODE_NAME,
    label: PROP_TYPE_NAME.TREE_NODE_NAME,
    disabled: true,
  },
  {
    value: PROP_TYPE.CREATORID,
    label: PROP_TYPE_NAME.CREATORID,
  },
  {
    value: PROP_TYPE.UPDATERID,
    label: PROP_TYPE_NAME.UPDATERID,
  },
  {
    value: PROP_TYPE.REMOVERID,
    label: PROP_TYPE_NAME.REMOVERID,
  },
]

export const INT_NUM_PROP_TYPE_COMPONENTS = [
  COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.INPUT],
  COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.NUMBER],
  COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.SELECT],
  COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.RATE],
  COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.SWITCH],
  COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.SLIDER],
  COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.TEXT],
]

// key: 属性类型
// value: 组件类型选项集合
export const PROP_TYPE_AND_COMPONENT_OPTIONS_MAP = {
  [PROP_TYPE.STRING]: [
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.INPUT],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.TEXTAREA],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.COLOR],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.SELECT],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.LINK],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.TEXT],
  ],
  [PROP_TYPE.INTEGER]: [...INT_NUM_PROP_TYPE_COMPONENTS],
  [PROP_TYPE.LONG]: [...INT_NUM_PROP_TYPE_COMPONENTS],

  [PROP_TYPE.DECIMAL]: [
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.INPUT],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.NUMBER],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.RATE],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.TEXT],
  ],
  [PROP_TYPE.DATE]: [
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.DATETIME],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.DATE],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.TIME],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.TEXT],
  ],
  [PROP_TYPE.BOOLEAN]: [
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.CHECKBOX],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.SWITCH],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.TEXT],
  ],
  [PROP_TYPE.SIRMORG]: [
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.SIRMORG],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.TEXT],
  ],
  [PROP_TYPE.SIRMORG_MV]: [
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.SIRMORG],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.TEXT],
  ],
  [PROP_TYPE.FOREIGN_KEY]: [
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.RELA_FORM],
  ],
  [PROP_TYPE.FOREIGN_KEY_MV]: [
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.RELA_FORM],
  ],
  [PROP_TYPE.MODEL]: [
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.CHILDREN_FORM],
  ],
  [PROP_TYPE.ATTACHMENT]: [COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.FILE]],
  [PROP_TYPE.STRING_SIRMENUM]: [
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.RADIO_GROUP],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.SELECT],
  ],
  [PROP_TYPE.NUMBER_SIRMENUM]: [
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.RADIO_GROUP],
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.SELECT],
  ],
  [PROP_TYPE.STRING_SIRMENUM_MV]: [
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.CHECKBOX_GROUP],
  ],
  [PROP_TYPE.NUMBER_SIRMENUM_MV]: [
    COMPONENT_TYPE_AND_OPTION_MAP[COMPONENT_TYPE.CHECKBOX_GROUP],
  ],
}

// key: 属性类型值
// value: 属性对象
export const PROP_TYPE_MAP = {}

PROP_TYPE_OPTIONS.forEach((item) => {
  PROP_TYPE_MAP[item.value] = item
})

export const LATEST_FLAG_COL = {
  name: 'latestFlag',
  colName: 'latest_flag',
  type: PROP_TYPE.SYSTEM,
  modelTypeLabel: PROP_TYPE_NAME.SYSTEM,
  comments: '最新有效标志',
  default: true,
}

/**
 * 抽象出来的版本模型,供审批+版本模型使用,它没有latest_flag与留痕模型保持一致
 */
export const VERSION_NO_LATEST_FLAG = [
  {
    name: 'publishVersion',
    colName: 'publish_version',
    type: PROP_TYPE.SYSTEM,
    modelTypeLabel: PROP_TYPE_NAME.SYSTEM,
    comments: '发布版本',
    default: true,
  },
  {
    name: 'publishStatus',
    colName: 'publish_status',
    type: PROP_TYPE.SYSTEM,
    modelTypeLabel: PROP_TYPE_NAME.SYSTEM,
    comments: '发布状态',
    default: true,
  },
  {
    name: 'publishDate',
    colName: 'publish_date',
    type: PROP_TYPE.SYSTEM,
    modelTypeLabel: PROP_TYPE_NAME.SYSTEM,
    comments: '发布日期',
    default: true,
  },
]

export const DEFAULT_DATE_FORMAT = 'yyyy-MM-dd'

// 框架基本属性
const FRAME_WORK_BASE_PROP = [
  {
    name: COL_NAME.ID,
    colName: COL_NAME.ID,
    type: PROP_TYPE.KEY,
    componentType: COMPONENT_TYPE.TEXT,
    componentTypeLabel: '文本',
    modelTypeLabel: PROP_TYPE_NAME.KEY,
    comments: PROP_TYPE_NAME.KEY,
    default: true,
  },
  {
    name: COL_NAME.VERSION,
    colName: COL_NAME.VERSION,
    type: PROP_TYPE.VERSION,
    componentType: COMPONENT_TYPE.TEXT,
    componentTypeLabel: '文本',
    modelTypeLabel: PROP_TYPE_NAME.VERSION,
    comments: PROP_TYPE_NAME.VERSION,
    default: true,
  },
  {
    name: COL_NAME.CREATETIMESTAMP,
    colName: COL_NAME.CREATETIMESTAMP,
    type: PROP_TYPE.CREATETIMESTAMP,
    componentType: COMPONENT_TYPE.TEXT,
    componentTypeLabel: '文本',
    modelTypeLabel: PROP_TYPE_NAME.CREATETIMESTAMP,
    comments: PROP_TYPE_NAME.CREATETIMESTAMP,
    default: true,
  },
  {
    name: COL_NAME.UPDATETIMESTAMP,
    colName: COL_NAME.UPDATETIMESTAMP,
    type: PROP_TYPE.UPDATETIMESTAMP,
    componentType: COMPONENT_TYPE.TEXT,
    componentTypeLabel: '文本',
    modelTypeLabel: PROP_TYPE_NAME.UPDATETIMESTAMP,
    comments: PROP_TYPE_NAME.UPDATETIMESTAMP,
    default: true,
  },
]

export const COMMON = [...FRAME_WORK_BASE_PROP]

export const WORKFLOW = [
  ...FRAME_WORK_BASE_PROP,
  {
    name: 'approveStatus',
    colName: 'approve_status',
    type: PROP_TYPE.SYSTEM,
    componentType: COMPONENT_TYPE.TEXT,
    componentTypeLabel: '文本',
    modelTypeLabel: PROP_TYPE_NAME.SYSTEM,
    comments: '审批状态',
    default: true,
  },
]

export const DATATRACE = [
  ...FRAME_WORK_BASE_PROP,
  {
    name: 'threadId',
    colName: 'thread_id',
    type: PROP_TYPE.SYSTEM,
    componentType: COMPONENT_TYPE.TEXT,
    componentTypeLabel: '文本',
    modelTypeLabel: PROP_TYPE_NAME.SYSTEM,
    comments: '线索ID',
    default: true,
  },
  {
    name: 'threadLatestFlag',
    colName: 'thread_latest_flag',
    type: PROP_TYPE.SYSTEM,
    componentType: COMPONENT_TYPE.TEXT,
    componentTypeLabel: '文本',
    modelTypeLabel: PROP_TYPE_NAME.SYSTEM,
    comments: '最新标志',
    default: true,
  },
]

export const VERSION = [
  ...FRAME_WORK_BASE_PROP,
  ...VERSION_NO_LATEST_FLAG,
  LATEST_FLAG_COL,
]

export const REMOVE_FLAG_PROP = {
  name: 'removeFlag',
  colName: 'remove_flag',
  type: PROP_TYPE.SYSTEM,
  modelTypeLabel: PROP_TYPE_NAME.SYSTEM,
  comments: '删除标志',
  default: true,
}

// 逻辑删除
export const LOGIC_DELETE_PROP = [REMOVE_FLAG_PROP]

export const TREE = [
  ...FRAME_WORK_BASE_PROP,
  {
    name: 'name',
    colName: 'name',
    type: PROP_TYPE.TREE_NODE_NAME,
    componentType: COMPONENT_TYPE.TEXT,
    componentTypeLabel: '文本',
    modelTypeLabel: PROP_TYPE_NAME.TREE_NODE_NAME,
    comments: PROP_TYPE_NAME.TREE_NODE_NAME,
    default: true,
  },
  {
    name: 'parentId',
    colName: 'parent_id',
    type: PROP_TYPE.SYSTEM,
    componentType: COMPONENT_TYPE.TEXT,
    componentTypeLabel: '文本',
    modelTypeLabel: PROP_TYPE_NAME.SYSTEM,
    comments: '父节点ID',
    default: true,
  },
  {
    name: 'sort',
    colName: 'sort',
    type: PROP_TYPE.SYSTEM,
    componentType: COMPONENT_TYPE.TEXT,
    componentTypeLabel: '文本',
    modelTypeLabel: PROP_TYPE_NAME.SYSTEM,
    comments: '节点顺序',
    default: true,
  },
  {
    name: 'highVal',
    colName: 'high_val',
    type: PROP_TYPE.SYSTEM,
    componentType: COMPONENT_TYPE.TEXT,
    componentTypeLabel: '文本',
    modelTypeLabel: PROP_TYPE_NAME.SYSTEM,
    comments: '节点高度',
    default: true,
  },
  {
    name: 'codeVal',
    colName: 'code_val',
    type: PROP_TYPE.SYSTEM,
    componentType: COMPONENT_TYPE.TEXT,
    componentTypeLabel: '文本',
    modelTypeLabel: PROP_TYPE_NAME.SYSTEM,
    comments: '节点编码',
    default: true,
  },
  {
    name: 'nameVal',
    colName: 'name_val',
    type: PROP_TYPE.SYSTEM,
    componentType: COMPONENT_TYPE.TEXT,
    componentTypeLabel: '文本',
    modelTypeLabel: PROP_TYPE_NAME.SYSTEM,
    comments: '名称(冗余)',
    default: true,
  },
]

export const ALL_DEFAULT_PROP = [
  ...COMMON,
  ...WORKFLOW,
  ...DATATRACE,
  ...VERSION,
  ...TREE,
]

// key: 物理字段名
// value: 对应属性配置
export const ALL_DEFAULT_PROP_COL_MAP = {}

ALL_DEFAULT_PROP.forEach((item) => {
  ALL_DEFAULT_PROP_COL_MAP[item.colName] = item
})

// key: 模型类型
// value: 属性集合
export const MODEL_TYPE_AND_PROP_MAP = {}
MODEL_TYPE_AND_PROP_MAP[MODEL_TYPE.COMMON] = COMMON
MODEL_TYPE_AND_PROP_MAP[MODEL_TYPE.WORKFLOW] = WORKFLOW
MODEL_TYPE_AND_PROP_MAP[MODEL_TYPE.DATATRACE] = DATATRACE
MODEL_TYPE_AND_PROP_MAP[MODEL_TYPE.VERSION] = VERSION
MODEL_TYPE_AND_PROP_MAP[MODEL_TYPE.TREE] = TREE

export const MV_STORE_TYPE = {
  MV_TABLE: 1,
}

export const MV_STORE_TYPE_OPTIONS = [
  {
    value: MV_STORE_TYPE.MV_TABLE,
    label: '多值表',
  },
]
