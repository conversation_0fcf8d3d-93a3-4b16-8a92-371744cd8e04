import rspack from '@rspack/core'
import CopyWebpackPluginOption from './build/copy-webpack-plugin-option.cjs'
import NodePolyfillPlugin from 'node-polyfill-webpack-plugin'
import MonacoPlugin from 'monaco-editor-webpack-plugin'
import defineConfig, { getEnv } from '../../config/rspack/base.mjs'
import path from 'path'
import fs from 'fs'
import { createRequire } from 'module'

const require = createRequire(import.meta.url)
const packageJson = require('./package.json')

const CopyWebpackPlugin = rspack.CopyRspackPlugin
const HtmlWebpackPlugin = rspack.HtmlRspackPlugin
const appVersion = packageJson.version
function resolve(dir) {
  return path.resolve(dir)
}

const srcDirs = fs.readdirSync(resolve('../sinitek-lowcode-simulator/src/'))

export default defineConfig({
  context: resolve('./'),
  assetsDir: 'static', // 静态资源目录名称
  // TODO: 调试其他库时打开
  // unoConfigPath: path.resolve( 'uno.config.js'),
  productionSourceMap: process.env.NODE_ENV === 'development', // 去掉打包的时候生成的map文件
  library: {
    type: 'umd',
    name: 'sinitek-lowcode-manager',
    entry: './src/index.js',
  },
  devServer: {
    // devMiddleware: {
    //   writeToDisk: true,
    // },
    static: [resolve('./node_modules/sinitek-lowcode-advanced/dist')],
    proxy: [
      {
        context: ['/frontend'],
        target: process.env.PROXY_URL,
        // ws: true, // 开启websocket配置
        changeOrigin: true,
        pathRewrite: {
          '^/': '',
        },
        router: () => getEnv().PROXY_URL,
      },
    ],
    client: {
      progress: true,
      overlay: {
        warnings: false,
        runtimeErrors: (error) => {
          if (
            error.message ===
            'ResizeObserver loop completed with undelivered notifications.'
          ) {
            return false
          }
          return true
        },
      },
    },
  },
  // sass全局变量配置
  css: {
    extract:
      process.env.NODE_ENV === 'development'
        ? {
            filename: 'css/[name].css',
            chunkFilename: 'css/[name].css',
          }
        : false, // 样式强制内联
  },
  configureWebpack: (config) => {
    config.stats = {
      assets: true,
      assetsSpace: 1000,
      relatedAssets: true,
    }
    // webpack简单配置，比如增加一个plugin
    const plugins = []

    plugins.push(
      new rspack.DefinePlugin({
        VUE_APP_VERSION: JSON.stringify(appVersion),
      })
    )

    plugins.push(
      new rspack.ProvidePlugin({
        $: 'jquery',
        jquery: 'jquery',
        jQuery: 'jquery',
        'windows.jQuery': 'jquery',
      })
    )
    plugins.push(
      new CopyWebpackPlugin({
        patterns: CopyWebpackPluginOption(
          process.env.NODE_ENV === 'development'
        ),
      })
    )
    if (process.env.NODE_ENV === 'development') {
      // plugins.push(new NodePolyfillPlugin())
      plugins.push(
        new NodePolyfillPlugin({
          additionalAliases: ['process'],
        })
      )
      plugins.push(
        new MonacoPlugin({
          languages: ['json', 'javascript', 'typescript', 'css'],
          features: ['!gotoSymbol'],
        })
      )
      config.devtool = 'source-map'
    }
    config.plugins = [...config.plugins, ...plugins]
    if (process.env.NODE_ENV === 'development') {
      config.resolve.alias['adaptor/element-ui'] = resolve(
        `../sinitek-lowcode-simulator/src/adaptor/element-ui/vue2.7.js`
      )
      srcDirs.forEach((dir) => {
        config.resolve.alias['@' + dir] = resolve(
          `../sinitek-lowcode-simulator/src/${dir}`
        )
      })
    }
  },
  chainWebpack: (config) => {
    // 配置别名

    config.resolve.alias
      .set('src', resolve('./src'))
      .set('assets', resolve('./src/assets'))
      .set('example', resolve('./example'))

    // svg rule调整
    config.module.rule('svg').exclude.add(resolve('./src/icons')).end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('./src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]',
      })

    if (process.env.NODE_ENV === 'production') {
      /* 为生产环境修改配置... */
      config.optimization.minimizer('terser').tap((options) => {
        options[0].minimizerOptions.compress.drop_console = true
        options[0].minimizerOptions.compress.drop_debugger = true
        return options
      })

      // 配置webpack-bundle-analyzer打包分析插件
      // config.plugin('webpack-report').use(BundleAnalyzerPlugin, [
      //   {
      //     analyzerMode: 'static',
      //     // openAnalyzer: false
      //   },
      // ])
      // 打包时排除
      config.externals([
        {
          'element-ui': 'element-ui',
          jquery: 'jquery',
          'sinitek-css': 'sinitek-css',
          'sinitek-util': 'sinitek-util',
          'sinitek-ui': 'sinitek-ui',
          sirmapp: 'sirmapp',
          'sinitek-workflow': 'sinitek-workflow',
          'monaco-editor': 'monaco-editor',
          '@iconify/vue2': '@iconify/vue2',
          vue: 'vue',
          'core-js': 'core-js',
          moment: 'moment',
        },
        /^echarts\/lib/i,
        /^element-ui\//i,
      ])
    } else {
      /* 为开发环境修改配置... */
      config.entryPoints
        .clear()
        .end()
        .entry('main')
        .add(resolve('./example/src/main.js'))

      // 修改 webpack-html-plugin 配置
      config.plugin('html').tap((args) => {
        return [
          // 传递给 html-webpack-plugin 构造函数的新参数
          {
            template: path.join('./example', 'index.html'),
            title: process.env.VUE_APP_DEFAULT_TITLE,
            excludeChunks: ['canvas'],
          },
        ]
      })
      config.entry('canvas').add('./example/src/canvas.js')
      config.plugin('html-canvas').use(HtmlWebpackPlugin, [
        {
          template: 'example/canvas.html',
          filename: 'canvas.html',
          excludeChunks: ['app', 'main'],
        },
      ])
    }
  },
})
