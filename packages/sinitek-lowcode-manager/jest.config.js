const path = require('path')
const setUpFilepath = path.resolve(__dirname, './tests/jest.setup.js')

module.exports = {
  preset: '@vue/cli-plugin-unit-jest',
  moduleFileExtensions: ['js', 'vue', 'json'],
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
    '^@/(.*)$': '<rootDir>/src/$1',
    '^example/(.*)$': '<rootDir>/example/$1',
    '^normalize.css/(.*)$': '<rootDir>/node_modules/normalize.css/$1',
    '^sinitek-css/(.*)$': '<rootDir>/node_modules/sinitek-css/$1',
    '^lodash-es$': 'lodash',
  },
  transform: {
    // 用 `vue-jest` 处理 `*.vue` 文件
    '.*\\.(vue)$': 'vue-jest',
    '.+\\.(css|styl|less|sass|scss|svg|png|jpg|ttf|woff|woff2)$':
      'jest-transform-stub',
  },
  setupFiles: [setUpFilepath],
}
