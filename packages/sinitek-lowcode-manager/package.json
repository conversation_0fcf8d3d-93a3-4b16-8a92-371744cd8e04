{"name": "sinitek-lowcode-manager", "version": "1.0.0-SNAPSHOT.135", "description": "动态表单开发", "author": "FIA", "main": "dist/sinitek-lowcode-manager.umd.min.js", "files": ["dist"], "private": false, "scripts": {"dev": "rspack serve", "dev:bun": "bun run rspack serve", "build": "rspack build", "doctor": "set RSDOCTOR=true && rspack build ", "test:unit": "vue-cli-service test:unit", "lint": "eslint . --fix", "format": "prettier --write \"{src,example}/**/*.{vue,js}\""}, "dependencies": {"js-base64": "3.7.7", "lodash": "catalog:", "monaco-editor": "catalog:", "sinitek-lowcode-shared": "workspace:^"}, "peerDependencies": {"@iconify/vue2": "catalog:", "core-js": "catalog:", "echarts": "catalog:", "element-ui": "catalog:", "sinitek-lowcode-advanced": "workspace:^", "sinitek-lowcode-flow": "workspace:^", "sinitek-lowcode-materials": "workspace:^", "sinitek-lowcode-render": "workspace:^", "sinitek-lowcode-simulator": "workspace:^"}, "devDependencies": {"@babel/core": "catalog:", "@babel/eslint-parser": "catalog:", "@babel/generator": "catalog:", "@babel/parser": "catalog:", "@babel/runtime": "7.26.0", "@vue/eslint-config-standard": "8.0.1", "acorn": "8.14.0", "babel-plugin-component": "1.1.1", "element-ui": "catalog:", "eslint": "catalog:", "eslint-plugin-vue": "9.26.0", "file-saver": "2.0.5", "html2canvas": "1.4.1", "jquery": "3.7.1", "js-cookie": "3.0.5", "jszip": "3.10.1", "mockjs": "1.1.0", "monaco-editor-webpack-plugin": "7.1.0", "node-polyfill-webpack-plugin": "4.0.0", "normalize.css": "8.0.1", "prettier": "3.3.3", "sinitek-css": "catalog:", "sinitek-message": "catalog:", "sinitek-ui": "catalog:", "sinitek-util": "catalog:", "sinitek-workflow": "catalog:", "sirmapp": "catalog:", "svg-sprite-loader": "6.0.11", "vue": "catalog:", "vue-i18n": "8.18.2", "vue-router": "3.3.4", "vue-template-compiler": "catalog:", "vuex": "3.4.0"}}