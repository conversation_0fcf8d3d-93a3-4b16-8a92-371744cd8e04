import Render from 'sinitek-lowcode-render'

// 组件名称记得修改
const componentName = 'LCComponent2'

export const material = {
  title: '低代码组件',
  lowcodeComponent: true,
  componentName,
  props: [
    {
      title: '属性标题',
      name: 'propName',
      setter: 'SelectSetter',
      options: [
        { label: 'primary', value: 'primary' },
        { label: 'success', value: 'success' },
      ],
    },
    {
      name: 'icon',
      title: { label: '图标', tip: '使用https://icon-sets.iconify.design/' },
      setter: { componentName: 'IconSetter', props: { type: 'iconify' } },
    },
    { name: 'text', title: '文本', setter: 'TextareaSetter' },
  ],
  configure: {
    component: { isContainer: false },
    supports: { condition: true, style: true },
  },
  snippets: [
    {
      title: '低代码组件2',
      screenshot: '',
      schema: {
        componentName,
        props: { icon: 'uil:icons', text: '按钮' },
        children: [],
      },
    },
  ],
}

export const config = {
  componentName: 'Page',
  datasource: [],
  state: {},
  methods: {
    mounted: {
      type: 'JSFunction',
      value: "function mounted() {\n  console.log(this.props, 'props');\n}",
    },
  },
  children: [
    {
      componentName: 'LCIcon',
      props: {
        icon: { type: 'JSExpression', value: 'this.props.icon' },
        height: 24,
        width: 24,
      },
      title: '图标',
      hidden: false,
      id: '54365332',
      ref: 'LCIcon1',
    },
    {
      componentName: 'XnButton',
      props: { showLoading: false },
      children: [
        {
          componentName: 'LCText',
          props: { text: { type: 'JSExpression', value: 'this.props.text' } },
          id: 'd56a3432',
          ref: 'LCText1',
        },
      ],
      title: '按钮',
      hidden: false,
      id: '44446235',
      ref: 'XnButton1',
    },
  ],
  id: 'a220znx6dn',
  ref: 'Page1',
  stateComment: {},
}

export default {
  name: componentName,
  props: ['propName', 'icon', 'text'],
  material,
  provide() {
    return {
      lowcodeProps: this.$props,
      lowcodeEmit: this.emit,
    }
  },
  render() {
    return (
      <div>
        <Render config={config} isLCComponent ref="render" />
      </div>
    )
  },
  mounted() {
    // 实现methods调用
    // 下划线开头和生命周期不会被调用
    const methods = this.$refs.render.getMethods()
    if (Object.keys(methods).length) {
      Object.entries(methods).forEach(([key, value]) => {
        this[key] = value
      })
    }
  },
  methods: {
    emit(...args) {
      this.$emit(...args)
    },
  },
}
