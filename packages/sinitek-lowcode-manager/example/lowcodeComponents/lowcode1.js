import Render from 'sinitek-lowcode-render'

// 组件名称记得修改
const componentName = 'LCComponent'

export const material = {
  title: '低代码组件',
  lowcodeComponent: true,
  componentName,
  props: [
    { title: '属性标题', name: 'propName', setter: 'StringSetter' },
    { name: 'ttt', title: '文本', setter: 'TextareaSetter' },
  ],
  configure: {
    component: { isContainer: false },
    supports: {
      condition: true,
      style: true,
    },
  },
  snippets: [
    {
      title: '低代码组件',
      screenshot: '',
      schema: {
        componentName,
        props: { propName: 'fffff', ttt: '按钮' },
        children: [],
      },
    },
  ],
}

const config = {
  componentName: 'Page',
  name: '',
  style: '',
  props: {},
  ref: 'a1',
  id: '5322266d',
  css: '.line{text-align:center;}',
  state: {
    form: {
      name: '产品名称',
      productTypeName: '产品类型',
      investTypeName: '投资',
    },
    d1: false,
  },
  datasource: [],
  methods: {
    mounted: {
      type: 'JSFunction',
      value: 'function mounted() {\n  console.log(this.props.propName);\n}',
    },
    submit: {
      type: 'JSFunction',
      value: 'function submit(e) {\n  console.log("fn", this, this.state);\n}',
    },
    a: {
      type: 'JSFunction',
      value:
        'function a(...args) {\n  console.log(this, "this");\n  console.log(...args, "a1");\n}',
    },
    close: {
      type: 'JSFunction',
      value: 'function close(v) {\n  this.state.d1 = v;\n}',
    },
  },
  children: [
    {
      componentName: 'LCIcon',
      props: { icon: 'uil:icons', height: 24, width: 24 },
      hidden: false,
      id: '3353144c',
      ref: 'LCIcon1',
    },
    {
      componentName: 'XnButton',
      props: { showLoading: false },
      children: [
        {
          componentName: 'LCText',
          props: { text: { type: 'JSExpression', value: 'this.props.ttt' } },
          id: '6d636324',
          ref: 'LCText1',
          hidden: false,
        },
      ],
      hidden: false,
      id: '122ef452',
      ref: 'XnButton1',
    },
  ],
  hidden: false,
  propsMap: [],
  stateComment: {},
}

export default {
  name: componentName,
  props: ['propName', 'ttt'],
  material,
  provide() {
    return {
      lowcodeProps: this.$props,
      lowcodeEmit: this.emit,
    }
  },
  render() {
    return (
      <div>
        <Render config={config} />
      </div>
    )
  },
  methods: {
    emit(...args) {
      this.$emit(...args)
    },
  },
}
