import Render from 'sinitek-lowcode-render'

// 组件名称记得修改
const componentName = 'rela-import-form'

export const material = {
  title: '关联方导入表单',
  lowcodeComponent: true,
  componentName,
  props: [
    {
      title: '显示导入弹窗',
      name: 'showImportDialog',
      setter: 'BoolSetter',
      defaultValue: false,
      valueChange: {
        type: 'JSFunction',
        value:
          'function(){return this.methods.onChange.apply(this,[].concat(Array.prototype.slice.call(arguments))) }',
      },
    },
  ],
  configure: {
    component: {
      isModal: true,
      modalVisibleProp: 'showImportDialog',
      rootSelector: '.el-dialog',
    },
    supports: {
      condition: true,
      style: true,
      supportBindState: false,
      events: [
        {
          name: 'input',
          description: '显示状态修改',
          template: 'function input (v) {}',
        },
      ],
    },
  },
  snippets: [
    {
      title: '关联方导入表单',
      screenshot: '',
      schema: {
        componentName,
        props: { showImportDialog: false },
        children: [],
      },
    },
  ],
}

export const config = {
  componentName: 'Page',
  datasource: [
    {
      id: 'upload_related',
      name: '',
      auto: false,
      data: {},
      url: '/frontend/api/related/detail/upload',
      headers: {},
      method: 'post',
      type: 'custom',
      modelCode: '',
      condition: { and: [] },
      enum: '',
      shouldFetch: 'function shouldFetch() {\n  return true\n}',
      errorHandler: 'function errorHandler(err) {}',
      dataHandler: 'function dataHandler(res) {\n  return res\n}',
      willFetch: 'function willFetch(options) {\n  return options\n}',
      writeable: true,
    },
  ],
  state: {
    uploadForm: {
      file: {
        uploadFileList: [],
        model: 'upload',
        type: '',
        removeFileList: [],
      },
    },
    showUploadDialog: false,
  },
  methods: {
    onHideImport: {
      type: 'JSFunction',
      value:
        'function onHideImport() {\n  this.state.showUploadDialog = false;\n  this.emit("input", false);\n}',
    },
    mounted: {
      type: 'JSFunction',
      value:
        'function mounted() {\n  this.state.showUploadDialog = this.props.showImportDialog;\n}',
    },
    onSubmitImport: {
      type: 'JSFunction',
      value:
        'function onSubmitImport(resolve, reject) {\n  if (!this.state.uploadForm.file.uploadFileList.length) {\n    this.vm.$message({\n      type: "error",\n      message: "请上传文件"\n    });\n    reject();\n  } else {\n    this.fetcher.upload_related.\n    send(this.state.uploadForm.file).\n    then((res) => {\n      resolve();\n      this.methods.onHideImport();\n    });\n  }\n}',
    },
    onChange: {
      type: 'JSFunction',
      value: 'function onChange(v) {\n  this.state.showUploadDialog = v;\n}',
    },
    onVisibleChange: {
      type: 'JSFunction',
      value: "function onVisibleChange(v) {\n  this.emit('input', v);\n}",
    },
  },
  children: [
    {
      componentName: 'LADialog',
      props: {
        title: '导入关联详情',
        buttons: [
          { label: '保存', type: 'primary', action: 'save', event: 'save' },
          { label: '取消', type: 'info', action: 'cancel', event: 'cancel' },
        ],
        top: '15vh',
        innerTop: '5vh',
        limitToTab: false,
        loadingNotAllowClose: true,
        showFooter: true,
        showFullscreenIcon: true,
        width: '400px',
      },
      children: [
        {
          componentName: 'Slot',
          props: { name: 'default' },
          children: [
            {
              componentName: 'LCCol',
              props: { gap: 10 },
              children: [
                {
                  componentName: 'LAForm',
                  props: {
                    column: '2',
                    labelPosition: 'left',
                    status: 'edit',
                    model: {
                      type: 'JSExpression',
                      value: 'this.state.uploadForm',
                    },
                  },
                  children: [
                    {
                      componentName: 'XnUpload',
                      props: {
                        showFileList: true,
                        style: '',
                        type: '',
                        action: '/frontend/api/related/detail/upload',
                        sourceId: '100',
                        sourceEntity: '',
                        accept: '',
                      },
                      children: [],
                      hidden: false,
                      id: 'e2f65585',
                      ref: 'XnUpload1',
                      condition: true,
                      LCBindState: 'uploadForm.file',
                    },
                  ],
                  hidden: false,
                  id: 'a233a352',
                  ref: 'LAForm1',
                  LCBindState: 'uploadForm',
                },
                {
                  componentName: 'LCFlex',
                  props: { gap: 10 },
                  children: [
                    {
                      componentName: 'XnButton',
                      props: { showLoading: false },
                      children: [
                        {
                          componentName: 'LCText',
                          props: {
                            text: '点击下载附件模版',
                            showLoading: false,
                          },
                          id: '66286624',
                          ref: 'LCText1',
                          condition: true,
                        },
                      ],
                      hidden: false,
                      id: '25144152',
                      ref: 'XnButton1',
                      condition: true,
                      style: 'border: 0px;',
                      events: {
                        click: {
                          type: 'JSFunction',
                          value:
                            'function(){return this.methods.onDownloadTemplate.apply(this,[].concat(Array.prototype.slice.call(arguments))) }',
                        },
                      },
                    },
                  ],
                  hidden: false,
                  id: '83425246',
                  ref: 'LCFlex1',
                  style: 'color: blue;',
                },
              ],
              hidden: false,
              id: 'a1553443',
              ref: 'LCCol1',
            },
          ],
          id: '5c1e5652',
          ref: 'Slot1',
        },
      ],
      hidden: false,
      id: '4468b745',
      ref: 'LADialog1',
      condition: true,
      events: {
        save: {
          type: 'JSFunction',
          value:
            'function(){return this.methods.onSubmitImport.apply(this,[].concat(Array.prototype.slice.call(arguments))) }',
        },
        cancel: {
          type: 'JSFunction',
          value:
            'function(){return this.methods.onHideImport.apply(this,[].concat(Array.prototype.slice.call(arguments))) }',
        },
        input: {
          type: 'JSFunction',
          value:
            'function(){return this.methods.onVisibleChange.apply(this,[].concat(Array.prototype.slice.call(arguments))) }',
        },
      },
      LCBindState: 'showUploadDialog',
      style: '',
    },
  ],
  id: 'j6yj5efl1a',
  ref: 'Page1',
  stateComment: {},
}

export default {
  name: componentName,
  props: ['showImportDialog'],
  material,
  inject: {
    mode: { default: null },
  },
  provide() {
    return {
      lowcodeProps: this.$props,
      lowcodeEmit: this.emit,
      lowcodeScope: {
        mode: this.mode,
        componentName,
      },
    }
  },
  render() {
    return (
      <div>
        <Render config={config} isLCComponent ref="render" />
      </div>
    )
  },
  methods: {
    emit(...args) {
      this.$emit(...args)
    },
  },
  mounted() {
    // 实现methods调用
    // 下划线开头和生命周期会过滤掉掉，不能被调用。
    const methods = this.$refs.render.getMethods()
    if (Object.keys(methods).length) {
      Object.entries(methods).forEach(([key, value]) => {
        this[key] = value
      })
    }
  },
}
