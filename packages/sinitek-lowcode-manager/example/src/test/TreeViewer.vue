<template>
  <div class="app-container center">
    <el-container>
      <el-aside>
        <div v-if="showTree">
          <h2>树组件:</h2>
          <xn-tree
            show-filter
            :url="url"
            :param="param"
            is-post
            :props="props"
            @nodeContextmenu="handleNodeContextmenu"
          />
        </div>
      </el-aside>
      <el-main>
        <div class="padding-top-10px">
          url: <xn-input v-model="url" clearable />
        </div>
        <div class="padding-top-10px">
          modelCode: <xn-input v-model="modelCode" clearable />
        </div>
        <div class="padding-top-10px">
          id: <xn-input v-model="id" clearable />
        </div>
        <div class="padding-top-10px">
          <xn-button @click="refreshTree">刷新</xn-button>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON>iewer',
  data() {
    return {
      url: '/frontend/api/lowcode/model/dynamic/tree/list-tree',
      showTree: false,
      modelCode: '',
      id: '0',
      param: undefined,
      props: {
        label: 'name',
        children: 'children',
      },
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleNodeContextmenu() {},
    refreshTree(resolve) {
      this.recordData()
      this.showTree = false
      this.$nextTick(() => {
        this.param = {
          modelCode: this.modelCode,
          param: {
            id: this.id,
          },
        }
        this.$nextTick(() => {
          this.showTree = true
          resolve()
        })
      })
    },
    recordData() {
      localStorage.setItem('url', this.url)
      localStorage.setItem('modelCode', this.modelCode)
      localStorage.setItem('id', this.id)
    },
    loadData() {
      this.url = localStorage.getItem('url')
      this.modelCode = localStorage.getItem('modelCode')
      this.id = localStorage.getItem('id')
    },
  },
}
</script>

<style scoped>
.center {
  text-align: center;
}
.upload-wrapper {
  padding-top: 10px;
}
.download-wrapper {
  padding-top: 20px;
}
.padding-top-10px {
  padding-top: 10px;
}
</style>
