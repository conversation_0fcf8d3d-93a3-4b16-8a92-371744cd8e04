<template>
  <div class="app-container center">
    <div>
      <div class="padding-top-10px">
        sourceEntity: <xn-input v-model="sourceEntity" clearable />
      </div>
      <div class="padding-top-10px">
        sourceId: <xn-input v-model.trim="sourceId" clearable />
      </div>
      <div class="padding-top-10px">
        type: <xn-input v-model.number="type" clearable />
      </div>
      <div class="padding-top-10px">
        <xn-button @click="refreshUpload">刷新上传组件</xn-button>
        <xn-button @click="copyUploadData" v-if="isShowUpload"
          >复制上传组件数据</xn-button
        >
      </div>
      <div class="padding-top-10px">
        <xn-button @click="refreshDownload">刷新下载组件</xn-button>
        <xn-button @click="copyDownloadData" v-if="isShowDownload"
          >复制下载组件数据</xn-button
        >
      </div>
    </div>
    <div v-if="isShowUpload" class="upload-wrapper">
      <h2>上传组件:</h2>
      <xn-upload
        v-model="uploadData"
        mode="upload"
        :source-id="sourceId"
        :source-entity="sourceEntity"
        :type="type"
      />
    </div>
    <div v-if="isShowDownload" class="download-wrapper">
      <h2>下载组件:</h2>
      <xn-download
        ref="download"
        :source-id="sourceId"
        :source-entity="sourceEntity"
        :type="type"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'AttachmentTest',
  data() {
    return {
      sourceEntity: undefined,
      sourceId: undefined,
      type: undefined,
      uploadData: undefined,
      isShowUpload: false,
      isShowDownload: false,
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    refreshUpload(resolve) {
      this.recordData()
      this.isShowUpload = false
      this.$nextTick(() => {
        this.uploadData = null
        this.isShowUpload = true
        resolve()
      })
    },
    copyUploadData(resolve, reject) {
      this.copyData(JSON.stringify(this.uploadData), resolve, reject)
    },
    copyDownloadData(resolve, reject) {
      this.copyData(
        JSON.stringify(this.$refs.download.$refs.download.files),
        resolve,
        reject
      )
    },
    copyData(data, resolve, reject) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(data)
        resolve()
      } else {
        this.$message.info('暂不支持复制')
        reject()
      }
    },
    refreshDownload(resolve) {
      this.recordData()
      this.isShowDownload = false
      this.$nextTick(() => {
        this.isShowDownload = true
        resolve()
      })
    },
    recordData() {
      localStorage.setItem('sourceEntity', this.sourceEntity)
      localStorage.setItem('sourceId', this.sourceId)
      localStorage.setItem('type', this.type)
    },
    loadData() {
      this.sourceEntity = localStorage.getItem('sourceEntity')
      this.sourceId = localStorage.getItem('sourceId')
      this.type = localStorage.getItem('type')
    },
  },
}
</script>

<style scoped>
.center {
  text-align: center;
}
.upload-wrapper {
  padding-top: 10px;
}
.download-wrapper {
  padding-top: 20px;
}
.padding-top-10px {
  padding-top: 10px;
}
</style>
