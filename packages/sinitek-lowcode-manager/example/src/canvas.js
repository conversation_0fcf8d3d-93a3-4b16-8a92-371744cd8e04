import { LcCanvas } from '../../src/index.js'
import './init'
import Vue from 'vue'

import 'normalize.css/normalize.css'
import 'sinitek-css/dist/icons/iconfont.css'
import 'sinitek-css/dist/theme/default.css'
import 'sinitek-css/dist/theme/mars.css'
import 'sinitek-css/dist/theme/earth.css'

new Vue({
  render: (h) => h(LcCanvas),
  i18n: window.parent.globalI18n,
  store: window.parent.globalStore,
  mounted() {
    const svg = window.parent.document.querySelector('#__SVG_SPRITE_NODE__')
    if (svg) {
      const clone = svg.cloneNode(true)
      document.body.appendChild(clone)
    }
  },
}).$mount('#canvas')
