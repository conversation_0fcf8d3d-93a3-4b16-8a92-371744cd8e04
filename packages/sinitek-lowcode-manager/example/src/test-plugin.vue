<template>
  <div>
    <!--    <el-button @click="handleClick">js修改schema</el-button>-->
    <el-dialog :visible.sync="show" title="js修改schema">
      <el-input v-model="code" type="textarea" :rows="10" />
      <el-button @click="handleConfirm">确定</el-button>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TestPlugin',
  inject: ['$doc'],
  data() {
    return {
      show: false,
      code: '',
    }
  },
  methods: {
    handleClick() {
      this.show = true
    },
    handleConfirm() {
      this.show = false
      const schema = this.$doc.getSchema(true)
      const code = this.code
      const func = new Function(`return ${code}`)
      const result = func()
      this.$doc.setSchema(result(schema))
    },
  },
}
</script>

<style lang="scss" scoped></style>
