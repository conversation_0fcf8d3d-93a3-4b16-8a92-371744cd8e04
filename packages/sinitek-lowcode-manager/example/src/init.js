import Vue from 'vue'
import Vuex from 'vuex'
import VueI18n from 'vue-i18n'
import VueRouter from 'vue-router'
import ElementUI from 'element-ui'

import SinitekUI from 'sinitek-ui'
import SinitekUtil, { http, operation, i18nUtil, auth } from 'sinitek-util'
import SinitekWorkflow from 'sinitek-workflow'
import '../lowcodeComponents/lowcode'
import SinitekLowcode, {
  RuntimeMode,
  setMetaFormRuntimeMode,
  setIsMetaFormService,
  setDebugMode,
} from 'src'
import Sirmapp from 'sirmapp'
import ElementZhCN from 'element-ui/lib/locale/lang/zh-CN'
import popupManager from 'element-ui/src/utils/popup/popup-manager'
import { menuRoutes } from './router'

import * as echarts from 'echarts'
window.echarts = echarts

if (process.env.VUE_APP_MODE === 'MULTI') {
  console.warn('当前为多应用运行模式')
  // 在添加路由前设置
  // 设置当前运行模式
  // RuntimeMode.MULTI_APP 多应用模式
  // RuntimeMode.SINGLE_APP 单应用模式
  setMetaFormRuntimeMode(RuntimeMode.MULTI_APP)
  // 设置当前是否为表单服务角色
  setIsMetaFormService(true)
  // 设置当前是否为调试模式
  setDebugMode(false)
} else {
  console.warn('当前为单应用运行模式')
}

const modules = [
  SinitekUI,
  SinitekUtil,
  Sirmapp,
  SinitekLowcode,
  SinitekWorkflow,
]

// 加载路由和首页模块的配置
modules.forEach((mode) => {
  if (mode.getMenuRoutes) {
    Sirmapp.appendMenuRoutes(mode.getMenuRoutes())
  }
  if (mode.getDashboardModules) {
    Sirmapp.appendDashboardModules(mode.getDashboardModules())
  }
})

// 添加路由
Sirmapp.appendMenuRoutes(menuRoutes)

// 加载白名单
const whiteRoutes = []
modules.forEach((mode) => {
  if (mode.getMenuRoutes) {
    whiteRoutes.push(...mode.getMenuRoutes())
  }
})

// router
Vue.use(VueRouter)
const router = new VueRouter({
  scrollBehavior: () => ({ y: 0 }),
  routes: Sirmapp.getRoutes(),
})

operation.initRoute(router, whiteRoutes)

// vuex
Vue.use(Vuex)
const store = new Vuex.Store({
  modules: operation.mergeStoreModules(modules),
})
auth.setTokenKey('flcm-accesstoken')
const mergeI18nMessage = (messages, locale) => {
  const keys = messages ? Object.keys(messages) : []
  keys.forEach((key) => {
    locale[key] = { ...locale[key], ...messages[key] }
  })
}

// i18N
const zhCN = {}
modules.forEach((mode) => {
  if (mode.getI18nZhCN) {
    mergeI18nMessage(mode.getI18nZhCN(), zhCN)
  }
})

Vue.use(VueI18n)
const i18n = new VueI18n({
  locale: 'zh_CN',
  messages: {
    zh_CN: {
      ...zhCN,
      ...ElementZhCN,
    },
  },
})
i18nUtil.initI18n(i18n)

// Vue全局配置
// 阻止 vue 在启动时生成生产提示。
Vue.config.productionTip = false

Vue.use(ElementUI, {
  i18n: (key, value) => i18n.t(key, value),
})
Vue.use(SinitekUI, { http })
Vue.use(SinitekUtil, { store, i18n })
Vue.use(Sirmapp, { store, i18n })
Vue.use(SinitekLowcode, { popupManager })
Vue.use(SinitekWorkflow)

export { router, store, i18n }
