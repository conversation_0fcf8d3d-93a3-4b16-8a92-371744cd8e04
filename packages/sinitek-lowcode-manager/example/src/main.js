import Vue from 'vue'
import App from './App'
import { router, store, i18n } from './init'

import 'normalize.css/normalize.css'
import 'sinitek-css/dist/icons/iconfont.css'
import 'sinitek-css/dist/theme/default.css'
import 'sinitek-css/dist/theme/mars.css'
import 'sinitek-css/dist/theme/earth.css'

import { plugin } from 'sinitek-lowcode-shared'
import TestPlugin from './test-plugin.vue'
window.globalStore = store
window.globalI18n = i18n
plugin.register({
  id: 'test-plugin',
  component: TestPlugin,
  area: 'topArea',
  align: 'right',
  title: '测试插件',
  order: 1,
  name: 'TestPlugin',
})

window.__LCShowAI__ = true
export default new Vue({
  router,
  store,
  i18n,
  render: (h) => h(App),
}).$mount('#app')
