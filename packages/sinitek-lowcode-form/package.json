{"name": "sinitek-lowcode-form", "version": "1.0.0-SNAPSHOT.135", "description": "", "main": "dist/sinitek-lowcode-form.umd.min.js", "scripts": {"dev": "rspack serve", "build": "rspack build", "format": "prettier --write \"{src,example}/**/*.{vue,js,jsx}\"", "test": "vue-cli-service test:unit", "test:coverage": "vue-cli-service test:unit --coverage", "lint": "eslint . --fix", "doctor": "set RSDOCTOR=true && rspack build "}, "keywords": [], "files": ["dist"], "author": "", "license": "ISC", "dependencies": {"@atlaskit/pragmatic-drag-and-drop-hitbox": "1.0.3", "js-base64": "3.7.7", "lodash": "^4.17.21", "monaco-editor": "catalog:"}, "devDependencies": {"@atlaskit/pragmatic-drag-and-drop": "1.4.0", "@atlaskit/pragmatic-drag-and-drop-auto-scroll": "^2.1.0", "@babel/core": "catalog:", "@babel/eslint-parser": "catalog:", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@iconify-json/healthicons": "^1.2.8", "@iconify-json/ic": "^1.2.2", "@iconify-json/lucide": "^1.2.43", "@iconify-json/material-symbols": "^1.2.19", "@iconify-json/material-symbols-light": "^1.2.17", "@iconify-json/mingcute": "^1.2.3", "@iconify-json/tabler": "^1.2.17", "@rspack/cli": "catalog:", "@rspack/core": "catalog:", "@unocss/eslint-config": "catalog:", "@unocss/postcss": "catalog:", "@unocss/preset-icons": "catalog:", "@unocss/preset-legacy-compat": "catalog:", "@unocss/preset-rem-to-px": "catalog:", "@unocss/reset": "catalog:", "@unocss/transformer-attributify-jsx": "catalog:", "@unocss/webpack": "catalog:", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/cli-plugin-unit-jest": "5.0.8", "@vue/cli-service": "5.0.8", "@vue/test-utils": "^1.1.3", "@vue/vue2-jest": "^27.0.0-alpha.3", "acorn": "8.14.0", "babel-jest": "^27.1.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^4.0.0", "element-ui": "catalog:", "eslint": "catalog:", "eslint-config-prettier": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-vue": "catalog:", "globals": "^16.0.0", "jest": "^27.1.0", "jest-environment-jsdom": "^27.1.0", "jest-serializer-vue": "^2.0.2", "jest-transform-stub": "^2.0.0", "mockjs": "1.1.0", "monaco-editor-webpack-plugin": "7.1.0", "sinitek-css": "catalog:", "sinitek-lowcode-shared": "workspace:^", "sinitek-lowcode-simulator": "workspace:^", "sinitek-ui": "catalog:", "sinitek-util": "catalog:", "svg-sprite-loader": "6.0.11", "unocss": "catalog:", "vue": "catalog:", "vue-i18n": "8.18.2", "vue-jest": "^3.0.7", "vue-loader": "catalog:", "vue-router": "3.3.4", "vue-template-compiler": "catalog:"}, "volta": {"node": "18.0.0"}, "peerDependencies": {"core-js": "catalog:", "moment": "2.30.1", "vue": "catalog:"}, "gitHooks": {"pre-commit": "lint-staged"}}