import config from '../../config/eslint-base.mjs'
import globals from 'globals'
export default [
  ...config(),
  {
    files: [
      '**/__tests__/*.{j,t}s?(x)',
      '**/tests/unit/**/*.spec.{j,t}s?(x)',
      '**/tests/**/jest.setup.{j,t}s?(x)',
    ],
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.jest,
        module: true,
        __dirname: true,
      },
    },
  },
]
