import path from 'node:path'
const setUpFilepath = path.resolve(import.meta.dirname, './tests/jest.setup.js')
/** @type {import('jest').Config} */
export default {
  preset: '@vue/cli-plugin-unit-jest',
  transformIgnorePatterns: [
    '/node_modules/(?!element-ui)/',
    '/node_modules/sinitek-lowcode-shared',
  ],
  setupFiles: [setUpFilepath],
  transform: {
    '^.+\\.vue$': '@vue/vue2-jest',
    '^.+\\.jsx?$': [
      'babel-jest',
      {
        presets: [
          [
            '@babel/preset-env',
            {
              targets: {
                node: 'current',
              },
            },
            '@vue/babel-preset-jsx',
          ],
        ],
        plugins: [
          '@babel/plugin-transform-modules-commonjs',
          'transform-vue-jsx',
        ],
      },
    ],
  },
  collectCoverageFrom: [
    'src/zd/{components,setters,common,mixins}/**/*.{js,vue}',
  ],
  moduleNameMapper: {
    '^sinitek-lowcode-shared$': '<rootDir>/tests/mock/shared.js',
  },
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov'],
  coverageThreshold: {
    global: {
      branches: 100,
    },
  },
}
