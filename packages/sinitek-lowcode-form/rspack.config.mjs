import defineConfig, { getEnv } from '../../config/rspack/base.mjs'
import rspack from '@rspack/core'
import MonacoPlugin from 'monaco-editor-webpack-plugin'

const CopyWebpackPlugin = rspack.CopyRspackPlugin

const HtmlWebpackPlugin = rspack.HtmlRspackPlugin
import path from 'path'
function resolve(dir) {
  return path.resolve(dir)
}

export default defineConfig({
  context: resolve('./'),
  assetsDir: 'static', // 静态资源目录名称
  unoConfigPath: resolve('./uno.config.js'),
  library: {
    type: 'umd',
    name: 'sinitek-lowcode-form',
    entry: './index.js',
  },
  // transpileDependencies: ['sinitek-lowcode-shared'],
  css: {
    // extract:
    //   process.env.NODE_ENV === 'development'
    //     ? {
    //         filename: '[name].css',
    //         chunkFilename: 'css/[name].css',
    //       }
    //     : false,
    extract: false,
  },
  devServer: {
    // port: '8991',
    proxy: [
      {
        context: ['/zhida', '/frontend'],
        target: process.env.PROXY_URL,
        // ws: true, // 开启websocket配置
        changeOrigin: true,
        pathRewrite: {
          '^/': '',
        },
        router: () => getEnv().PROXY_URL,
      },
    ],
    client: {
      progress: true,
      overlay: {
        warnings: false,
        runtimeErrors: (error) => {
          if (
            error.message ===
            'ResizeObserver loop completed with undelivered notifications.'
          ) {
            return false
          }
          return true
        },
      },
    },
  },
  assetsDir: 'statics', // 静态资源目录名称
  productionSourceMap: process.env.NODE_ENV === 'development', // 去掉打包的时候生成的map文件

  chainWebpack: (config) => {
    // svg rule调整
    config.module.rule('svg').exclude.add(resolve('./src/icons')).end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('./src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]',
      })

    config.resolve.alias
      .set(
        '@logicflow/extension$',
        resolve('./node_modules/@logicflow/extension/dist/index.min.js')
      )
      .set('src', resolve('./src'))

    // 配置别名
    if (process.env.NODE_ENV === 'production') {
      // 打包时排除
      config.externals([
        {
          vue: 'vue',
          'vue-router': 'vue-router',
          'core-js': 'core-js',
          'monaco-editor': 'monaco-editor',
          '@iconify/vue2': '@iconify/vue2',
          moment: 'moment',
          sirmapp: 'sirmapp',
          'sinitek-lowcode-advanced': 'sinitek-lowcode-advanced',
          'sinitek-lowcode-materials': 'sinitek-lowcode-materials',
          'sinitek-lowcode-render': 'sinitek-lowcode-render',
          'sinitek-lowcode-shared': 'sinitek-lowcode-shared',
          'sinitek-lowcode-simulator': 'sinitek-lowcode-simulator',
          'sinitek-css': 'sinitek-css',
          'sinitek-ui': 'sinitek-ui',
          'sinitek-util': 'sinitek-util',
          sirmapp: 'sirmapp',
          'element-ui': 'element-ui',
        },
        /^echarts\/lib/i,
        /^core-js\//i,
        /^element-ui\//i,
      ])
    } else {
      // 添加 copy 插件配置
      config.plugin('copy').use(CopyWebpackPlugin, [
        {
          patterns: [
            {
              from: resolve('./node_modules/sinitek-lowcode-simulator/dist/'),
              to: resolve('./dist/'),
              globOptions: {
                ignore: ['*.umd.min.js', `*simulator.css`],
              },
            },
          ],
        },
      ])

      config.plugin('monaco').use(MonacoPlugin, [
        {
          languages: ['json', 'javascript', 'typescript', 'css'],
          features: ['!gotoSymbol'],
        },
      ])

      /* 为开发环境修改配置... */
      // config.module.rule('vue').uses.delete('cache-loader')
      // config.merge({
      //   cache: false,
      // })
      /* 为开发环境修改配置... */
      // config.module.rule('vue').uses.delete('cache-loader')
      // config.merge({
      //   cache: false,
      // })
      config.entryPoints
        .clear()
        .end()
        .entry('main')
        .add(resolve('./example/main.js'))

      // 修改 webpack-html-plugin 配置
      config.plugin('html').tap(() => {
        return [
          // 传递给 html-webpack-plugin 构造函数的新参数
          {
            template: './example/index.html',
            excludeChunks: ['canvas'],
          },
        ]
      })
      config.entry('canvas').add('./example/canvas.js')
      config.plugin('html-canvas').use(HtmlWebpackPlugin, [
        {
          template: './example/canvas.html',
          filename: 'canvas.html',
          excludeChunks: ['app', 'main'],
        },
      ])
    }
  },
})
