// import '@unocss/reset/tailwind-compat.css'
import 'sinitek-css/dist/icons/iconfont.css'
import 'sinitek-css/dist/theme/default.css'
import 'sinitek-css/dist/theme/mars.css'
import 'sinitek-css/dist/theme/earth.css'

import Vue from 'vue'
import App from './App.vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import 'sinitek-css/dist/icons/iconfont.css'
import 'sinitek-css/dist/theme/default.css'
import 'sinitek-css/dist/theme/mars.css'
import 'sinitek-css/dist/theme/earth.css'
import 'sinitek-lowcode-simulator/dist/sinitek-lowcode-simulator.css'
import SinitekUI from 'sinitek-ui'
import { http } from 'sinitek-util'
import routes from '@/routes'
import { PopupManager } from 'element-ui/lib/utils/popup'
import VueRouter from 'vue-router'
import '../index'
import { setHttp } from '@/utils/http'

Vue.use(VueRouter)

if (process.env.NODE_ENV !== 'production') {
  // require('./mock/index.js')
}

const router = new VueRouter({
  routes,
})

// 添加低代码例子
// import './lowcode/lowcode.js'

Vue.use(ElementUI)

Vue.use(SinitekUI, { http, popupManager: PopupManager })
// Vue.config.productionTip = false
new Vue({
  router,
  render: (h) => h(App),
}).$mount('#app')

const customConfig = {
  httpRequest: {
    header: {
      accesstoken: 'ab48b78f-4f2f-4f3f-bdb6-375b06c87e38',
    },
  },
}

http.customizeHttpConfig(customConfig)
setHttp(http)
export default http
