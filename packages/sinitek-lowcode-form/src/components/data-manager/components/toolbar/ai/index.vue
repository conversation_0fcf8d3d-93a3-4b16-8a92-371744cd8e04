<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="ai-assistant flex-shrink-0">
    <!-- 浮动按钮 -->
    <div class="ai-float-button mr-2 flex-shrink-0" @click="toggleChat">
      AI分析
    </div>

    <!-- 聊天界面 -->
    <div
      v-if="showChat"
      class="ai-chat-container"
      :class="{
        'ai-chat-dragging': isDragging,
        'ai-chat-expanded': isExpanded,
      }"
      :style="chatContainerStyle"
    >
      <!-- 聊天头部 -->
      <div class="ai-chat-header" @mousedown="startDrag">
        <div class="ai-chat-title">
          <AIIcon class="ai-header-icon" />
          <span>{{ name }}</span>
        </div>
        <div class="ai-chat-actions">
          <div
            class="ai-chat-expand"
            :title="isExpanded ? '恢复宽度' : '扩展宽度'"
            @click="toggleExpanded"
          >
            <i
              :class="
                isExpanded
                  ? 'i-mingcute:contract-left-line'
                  : 'i-mingcute:expand-width-line'
              "
            ></i>
          </div>
          <div
            class="ai-chat-reset"
            title="发起新对话"
            @click="resetConversation"
          >
            <i class="el-icon-plus"></i>
          </div>
          <div class="ai-chat-close" title="关闭" @click="toggleChat">
            <i class="el-icon-close"></i>
          </div>
        </div>
      </div>

      <!-- 聊天内容区域 -->
      <div ref="chatContent" class="ai-chat-content">
        <div class="ai-chat-messages">
          <!-- 欢迎消息 -->
          <div class="ai-message ai-message-bot">
            <div class="ai-message-content">
              <div class="ai-message-text user-select-text ai-example-text">
                <!-- AI搭建页面的欢迎消息 -->
                <template v-if="activeTab === 'generate'">
                  <div class="ai-welcome-text">
                    Hi，我可以帮你分析数据并生成图表，你只需要提供分析需求，比如你可以这么对我说：
                  </div>
                  <div class="ai-welcome-examples">
                    <div
                      class="ai-example-link"
                      @click="fillExample('帮我分析一下用户数据的分布情况')"
                    >
                      "帮我分析一下用户数据的分布情况"
                    </div>
                    <div
                      class="ai-example-link"
                      @click="fillExample('生成数据流程图')"
                    >
                      "生成数据流程图"
                    </div>
                  </div>
                </template>

                <!-- AI帮助的欢迎消息 -->
                <template v-else>
                  <div class="ai-welcome-text">
                    Hi，页面搭建有任何疑问可以咨询我。比如你可以这么对我说：
                  </div>
                  <div class="ai-welcome-examples">
                    <div
                      class="ai-example-link"
                      @click="fillExample('如何配置字段必填')"
                    >
                      "如何配置字段必填"
                    </div>
                    <div
                      class="ai-example-link"
                      @click="fillExample('子表单组件怎么使用')"
                    >
                      "子表单组件怎么使用"
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>

          <!-- 动态消息列表 -->
          <div
            v-for="message in messages"
            :key="message.id"
            class="ai-message"
            :class="[
              message.type === 'user' ? 'ai-message-user' : 'ai-message-bot',
              { 'ai-message-completion': message.isCompletion },
            ]"
          >
            <div v-if="message.type === 'bot'" class="ai-message-avatar">
              {{ name }}
            </div>
            <div class="ai-message-content">
              <div
                class="ai-message-text"
                :class="{
                  'user-select-text': true,
                  'ai-completion-text': message.isCompletion,
                }"
              >
                <!-- 如果是HTML内容，使用v-html渲染 -->
                <div v-if="message.isHtml" class="ai-html-content-wrapper">
                  <div class="ai-html-content" v-html="message.content"></div>
                  <!-- 操作按钮区域 -->
                  <div class="ai-content-actions">
                    <!-- 全屏查看按钮 -->
                    <button
                      v-if="message.content && message.content.length > 500"
                      class="ai-fullscreen-view-btn"
                      title="全屏查看内容"
                      @click="showFullscreenContent(message.content)"
                    >
                      <i class="i-mingcute:fullscreen-line"></i>
                      全屏查看
                    </button>
                    <!-- 查看Mermaid代码按钮 -->
                    <button
                      v-if="hasMermaidChart(message.content)"
                      class="ai-mermaid-code-btn"
                      title="查看Mermaid代码"
                      @click="showMermaidCode(message.content)"
                    >
                      <i class="i-mingcute:code-line"></i>
                      查看代码
                    </button>
                  </div>
                </div>
                <!-- 否则使用普通文本 -->
                <template v-else>{{ message.content }}</template>
              </div>
              <div class="ai-message-actions">
                <div v-if="message.timestamp" class="ai-message-time">
                  {{ formatTime(message.timestamp) }}
                </div>

                <button
                  v-if="message.executionFailed && message.functionName"
                  class="ai-retry-button"
                  title="自动重试"
                  @click="
                    retryFailedFunction(
                      message.functionName,
                      message.errorMessage
                    )
                  "
                >
                  <i class="i-mingcute:refresh-1-line"></i>
                  重试
                </button>
              </div>
            </div>
          </div>

          <!-- 等待状态显示 -->
          <div
            v-if="sending || isWaitingForContinue"
            class="ai-message ai-message-bot"
          >
            <div class="ai-message-avatar">
              {{ name }}
              <!-- <div class="ai-loading-spinner"></div> -->
            </div>
            <div class="ai-message-content">
              <div class="ai-message-text ai-waiting-message">
                <template v-if="activeTab === 'generate'">
                  页面修改中...
                </template>
                <template v-else> AI正在思考中... </template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 聊天输入区域 -->
      <div class="ai-chat-input">
        <div class="ai-input-tabs">
          <div
            class="ai-input-tab"
            :class="{
              active: activeTab === 'generate',
              disabled: sending || isWaitingForContinue,
            }"
            @click="switchTab('generate')"
          >
            <AIIcon class="ai-icon" />
            <span>AI 搭建页面</span>
          </div>

          <div
            class="ai-input-tab"
            :class="{
              active: activeTab === 'help',
              disabled: sending || isWaitingForContinue,
            }"
            @click="switchTab('help')"
          >
            <svg-icon icon-class="help" />
            <span>使用帮助</span>
          </div>
        </div>
        <div class="ai-input-container">
          <div class="ai-input-wrapper">
            <!-- 图片上传区域 - 仅在AI搭建页面显示 -->
            <div v-if="activeTab === 'generate'" class="ai-upload-section">
              <div class="ai-upload-header">
                <span class="ai-upload-label">
                  上传图片（可选）
                  <span
                    v-if="selectedImages.length > 0"
                    class="ai-upload-count"
                  >
                    已选择 {{ selectedImages.length }} 个
                  </span>
                </span>
                <div class="ai-upload-actions">
                  <button
                    v-if="selectedImages.length > 0"
                    class="ai-upload-clear"
                    title="清空已选择的图片"
                    @click="clearSelectedImages"
                  >
                    <svg-icon icon-class="remove" />
                    清空
                  </button>
                  <button
                    v-if="!showImages"
                    class="ai-upload-toggle"
                    @click="toggleImages"
                  >
                    <svg-icon icon-class="plus" />
                    添加图片
                  </button>
                  <button v-else class="ai-upload-toggle" @click="toggleImages">
                    <i class="el-icon-document-copy"></i>
                    收起
                  </button>
                </div>
              </div>

              <div v-if="showImages" class="ai-upload-content">
                <div class="ai-image-upload-container">
                  <!-- 图片上传按钮 -->
                  <div
                    v-if="selectedImages.length < 6"
                    class="ai-image-upload-button"
                    @click="triggerImageUpload"
                  >
                    <svg-icon icon-class="plus" />
                    <span>选择图片</span>
                    <div class="ai-image-upload-hint">
                      最多6张，支持jpg/png/gif
                    </div>
                  </div>

                  <!-- 隐藏的文件输入框 -->
                  <input
                    ref="imageInput"
                    type="file"
                    accept="image/*"
                    multiple
                    style="display: none"
                    @change="handleImageSelect"
                  />

                  <!-- 已选择的图片预览 -->
                  <div
                    v-for="(image, index) in selectedImages"
                    :key="index"
                    class="ai-image-preview"
                  >
                    <img :src="image.preview" :alt="image.name" />
                    <div class="ai-image-overlay">
                      <div class="ai-image-name">{{ image.name }}</div>
                      <button
                        class="ai-image-remove"
                        title="删除图片"
                        @click="removeImage(index)"
                      >
                        <svg-icon icon-class="remove" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 多行文本输入框 -->
            <div class="ai-textarea-wrapper">
              <textarea
                ref="textareaInput"
                v-model="inputMessage"
                class="ai-textarea"
                placeholder="请输入你的需求，按Ctrl+Enter发送"
                rows="10"
                :maxlength="1000"
                @keydown="handleKeydown"
                @focus="handleInputFocus"
              ></textarea>

              <div
                class="ai-input-counter"
                :class="{
                  warning: inputMessage.length >= 800,
                  danger: inputMessage.length >= 950,
                }"
              >
                {{ inputMessage.length }}/1000
              </div>

              <button
                class="ai-send-button"
                :disabled="
                  !inputMessage.trim() || sending || isWaitingForContinue
                "
                @click="sendMessage"
              >
                <svg-icon
                  v-if="!sending && !isWaitingForContinue"
                  icon-class="send"
                />
                <i v-else class="ai-loading el-icon-loader"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全屏内容查看对话框 -->
    <div
      v-if="showFullscreenDialog"
      class="ai-fullscreen-overlay"
      @click="closeFullscreenContent"
    >
      <div class="ai-fullscreen-dialog" @click.stop>
        <div class="ai-fullscreen-header">
          <h3>内容详情</h3>
          <button class="ai-fullscreen-close" @click="closeFullscreenContent">
            <svg-icon icon-class="remove" />
          </button>
        </div>
        <div class="ai-fullscreen-content">
          <div
            class="ai-fullscreen-html-content"
            v-html="fullscreenContent"
          ></div>
        </div>
      </div>
    </div>

    <!-- Mermaid图表浮层 -->
    <div
      v-if="showMermaidModal"
      class="mermaid-overlay"
      @click="closeMermaidModal"
    >
      <div class="mermaid-popup" @click.stop>
        <button class="mermaid-close" @click="closeMermaidModal">
          <i class="el-icon-close"></i>
        </button>
        <div
          id="mermaid-popup-chart"
          class="mermaid-chart-container"
          v-html="mermaidModalContent"
        ></div>
      </div>
    </div>

    <!-- Mermaid代码查看弹窗 -->
    <div
      v-if="showMermaidCodeModal"
      class="mermaid-overlay"
      @click="closeMermaidCodeModal"
    >
      <div class="mermaid-code-popup" @click.stop>
        <div class="mermaid-code-header">
          <h3>Mermaid 代码</h3>
          <button class="mermaid-close" @click="closeMermaidCodeModal">
            <i class="el-icon-close"></i>
          </button>
        </div>
        <div class="mermaid-code-content">
          <pre><code class="mermaid-code-text">{{ mermaidCodeContent }}</code></pre>
          <button
            class="mermaid-copy-btn"
            title="复制代码"
            @click="copyMermaidCode"
          >
            <i class="i-mingcute:copy-line"></i>
            复制代码
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { log } from 'sinitek-lowcode-shared'
import { http } from '@/utils/http'
export default {
  name: 'AIAssistant',
  inject: {
    $doc: {
      default: () => ({}),
    },
    fetcher: {
      default: () => ({}),
    },
    message: {
      default: () => ({}),
    },
    // 上面的都可以删掉了  没有了
    DM: {
      default: () => ({}),
    },
  }, // 注入文档对象、fetcher和消息对象
  data() {
    return {
      name: 'AI分析',
      showChat: false,
      activeTab: 'generate',
      inputMessage: '',
      sending: false,
      generateMessages: [], // AI搭建页面的消息
      helpMessages: [], // AI帮助的消息
      messageIdCounter: 1,
      sessionId: null, // AI会话ID
      helpSessionId: null, // AI帮助的会话ID
      isWaitingForContinue: false, // 是否在等待继续调用
      // 拖拽相关
      isDragging: false,
      dragStartX: 0,
      dragStartY: 0,
      chatPosition: {
        x: 0,
        y: 0,
      },

      // 图片上传相关
      showImages: false, // 是否显示图片上传区域
      selectedImages: [], // 选中的图片文件，格式为 [{file: File, name: string, preview: string}]
      uploadingImages: false, // 是否正在上传图片
      // 扩展宽度相关
      isExpanded: false, // 是否扩展宽度模式
      showFullscreenDialog: false, // 是否显示全屏内容对话框
      fullscreenContent: '', // 全屏显示的内容
      showMermaidModal: false, // 是否显示mermaid弹窗
      mermaidModalContent: '', // mermaid弹窗内容
      showMermaidCodeModal: false, // 是否显示mermaid代码查看弹窗
      mermaidCodeContent: '', // mermaid代码内容
    }
  },
  computed: {
    chatContainerStyle() {
      return {
        transform: `translate(${this.chatPosition.x}px, ${this.chatPosition.y}px)`,
      }
    },
    // 根据当前标签页返回对应的消息
    messages() {
      return this.activeTab === 'generate'
        ? this.generateMessages
        : this.helpMessages
    },
    // 根据当前标签页返回对应的会话ID
    currentSessionId() {
      return this.activeTab === 'generate' ? this.sessionId : this.helpSessionId
    },
  },
  mounted() {
    // 设置初始位置为右下角
    this.setInitialPosition()
  },
  beforeDestroy() {
    // 清理拖拽事件监听器
    document.removeEventListener('mousemove', this.onDrag)
    document.removeEventListener('mouseup', this.stopDrag)
  },
  methods: {
    // 处理键盘事件
    handleKeydown(event) {
      // Ctrl+Enter 或 Shift+Enter 发送消息
      if (event.key === 'Enter' && (event.ctrlKey || event.shiftKey)) {
        event.preventDefault()
        this.sendMessage()
      }
      // 普通 Enter 键允许换行（不阻止默认行为）
    },

    // 处理输入框聚焦事件
    handleInputFocus() {
      // 聚焦输入框时收起图片选择区域
      this.showImages = false
    },

    // 判断是否是重试请求
    isRetryRequest(message) {
      const retryKeywords = [
        '函数执行失败',
        '执行失败',
        '重新生成',
        '重试',
        '再试一次',
        '重新执行',
        '报错',
        '错误',
        '失败了',
        '不行',
        '有问题',
      ]

      const lowerMessage = message.toLowerCase()
      return retryKeywords.some(
        (keyword) =>
          lowerMessage.includes(keyword) ||
          lowerMessage.includes(keyword.toLowerCase())
      )
    },

    // 切换图片上传区域显示/隐藏
    toggleImages() {
      this.showImages = !this.showImages
    },

    // 清空已选择的图片
    clearSelectedImages() {
      // 释放预览URL内存
      this.selectedImages.forEach((image) => {
        if (image.preview) {
          URL.revokeObjectURL(image.preview)
        }
      })
      this.selectedImages = []
    },

    // 触发图片选择
    triggerImageUpload() {
      if (this.$refs.imageInput) {
        this.$refs.imageInput.click()
      }
    },

    // 处理图片选择
    handleImageSelect(event) {
      const files = Array.from(event.target.files)
      if (!files.length) return

      // 检查总数量限制
      const remainingSlots = 6 - this.selectedImages.length
      if (remainingSlots <= 0) {
        this.message?.warning('最多只能选择6张图片')
        return
      }

      // 取前面可用的文件
      const filesToProcess = files.slice(0, remainingSlots)
      const validImages = []

      filesToProcess.forEach((file) => {
        // 验证文件类型
        if (!file.type.startsWith('image/')) {
          this.message?.warning(`文件 ${file.name} 不是有效的图片格式`)
          return
        }

        // 验证文件大小（限制为10MB）
        if (file.size > 10 * 1024 * 1024) {
          this.message?.warning(`图片 ${file.name} 大小超过10MB限制`)
          return
        }

        // 创建预览URL
        const preview = URL.createObjectURL(file)
        validImages.push({
          file,
          name: file.name,
          preview,
        })
      })

      // 添加到已选择的图片列表
      this.selectedImages.push(...validImages)

      // 清空input值，允许重复选择同一文件
      event.target.value = ''

      if (validImages.length > 0) {
        this.message?.success(`成功添加 ${validImages.length} 张图片`)
      }
    },

    // 删除指定图片
    removeImage(index) {
      const image = this.selectedImages[index]
      if (image && image.preview) {
        // 释放预览URL内存
        URL.revokeObjectURL(image.preview)
      }
      this.selectedImages.splice(index, 1)
    },

    // 设置初始位置
    setInitialPosition() {
      // 设置为右下角位置，距离右边和上边各20px
      const containerWidth = this.isExpanded ? 760 : 380 // 根据扩展状态调整宽度
      this.chatPosition.x = window.innerWidth - containerWidth - 20
      this.chatPosition.y = 20
    },

    // 切换扩展宽度
    toggleExpanded() {
      this.isExpanded = !this.isExpanded
      // 重新计算位置，确保扩展后不会超出屏幕
      this.adjustPositionAfterExpand()
      // 重新调整所有mermaid图表的尺寸
      this.$nextTick(() => {
        this.adjustAllMermaidSizes()
      })
    },

    // 扩展后调整位置
    adjustPositionAfterExpand() {
      const containerWidth = this.isExpanded ? 760 : 380
      const minVisibleWidth = 100

      // 确保扩展后不会超出屏幕右边界
      const maxX = window.innerWidth - minVisibleWidth
      if (this.chatPosition.x + containerWidth > window.innerWidth) {
        this.chatPosition.x = Math.max(
          maxX - containerWidth,
          -containerWidth + minVisibleWidth
        )
      }
    },

    toggleChat() {
      this.showChat = !this.showChat
      if (this.showChat) {
        this.$nextTick(() => {
          this.scrollToBottom()
          // 聚焦到文本输入框
          const textareaInput = this.$refs.textareaInput
          if (textareaInput) {
            textareaInput.focus()
          }
        })
      } else {
        // 关闭聊天时重置会话
        this.resetSession()
      }
    },

    // 切换标签页
    switchTab(tab) {
      // 如果AI正在处理，不允许切换标签页
      if (this.sending || this.isWaitingForContinue) {
        this.message?.warning('AI正在处理中，请等待完成后再切换标签页')
        return
      }

      if (this.activeTab !== tab) {
        this.activeTab = tab
        // 切换标签页时重置会话
        this.resetSession()
      }
    },

    // 重置会话
    resetSession() {
      this.sessionId = null
      this.isWaitingForContinue = false
      this.sending = false
    },

    // 重置对话
    resetConversation() {
      // 如果AI正在处理，不允许重置对话
      if (this.sending || this.isWaitingForContinue) {
        this.message?.warning('AI正在处理中，请等待完成后再重置对话')
        return
      }

      // 清空对应标签页的聊天记录和会话ID
      if (this.activeTab === 'generate') {
        this.generateMessages = []
        this.sessionId = null // 重置AI搭建页面的会话ID
      } else {
        this.helpMessages = []
        this.helpSessionId = null // 重置AI帮助的会话ID
      }

      // 重置会话状态
      this.isWaitingForContinue = false
      this.sending = false

      // 清空输入框和选中的图片
      this.inputMessage = ''
      this.clearSelectedImages()
      this.showImages = false

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    async sendMessage() {
      if (!this.inputMessage.trim() || this.sending) return

      // 检查是否是重试请求（包含"函数执行失败"或"重新生成"等关键词）
      const isRetryRequest = this.isRetryRequest(this.inputMessage.trim())

      // 只有在AI搭建页面且不是重试请求时，才自动保存页面
      if (this.activeTab === 'generate' && !isRetryRequest) {
        try {
          await this.autoSavePage()
          // 保存成功后在对话中添加消息
          this.addBotMessage('📄 页面已保存，可从历史恢复')
        } catch (error) {
          console.error('自动保存失败:', error)
          this.addBotMessage('⚠️ 页面保存失败，但对话将继续')
        }
      }

      // 构建用户消息显示内容
      let displayContent = this.inputMessage.trim()
      if (this.selectedImages.length > 0) {
        const imageNames = this.selectedImages.map((image) => image.name)
        displayContent += `\n\n🖼️ 上传的图片: ${imageNames.join(', ')}`
      }

      const userMessage = {
        id: this.messageIdCounter++,
        type: 'user',
        content: displayContent,
        timestamp: new Date(),
      }

      // 根据当前标签页添加到对应的消息数组
      if (this.activeTab === 'generate') {
        this.generateMessages.push(userMessage)
      } else {
        this.helpMessages.push(userMessage)
      }
      const userInput = this.inputMessage.trim()

      // 清空输入框和选中的图片
      this.inputMessage = ''
      this.clearSelectedImages() // 使用方法清空图片，释放内存
      this.sending = true

      // 立即滚动到底部显示等待状态
      this.$nextTick(() => {
        this.scrollToBottom()
      })

      try {
        if (this.activeTab === 'generate') {
          // AI 分析 - 调用图表生成API
          await this.generateDiagramResponse(userInput)
        } else {
          // AI 帮助 - 使用模拟回复
          await this.generateMockResponse(userInput)
        }
      } catch (error) {
        console.error('发送消息失败:', error)
        this.addBotMessage('抱歉，服务暂时不可用，请稍后再试。')
      } finally {
        this.sending = false
      }
    },

    // 自动保存页面
    async autoSavePage() {
      try {
        // 检查$doc是否可用
        if (!this.$doc || !this.$doc.getSaveSchema) {
          console.warn('文档对象不可用，跳过自动保存')
          return
        }

        // 获取保存用的schema（用于验证数据完整性）
        const schema = this.$doc.getSaveSchema()

        // 模拟保存操作 - 在实际环境中，这里会调用真实的保存API
        // 这里我们只是标记页面已保存，实际保存由外部系统处理
        // 验证schema数据完整性
        if (!schema || typeof schema !== 'object') {
          throw new Error('页面数据格式错误')
        }

        // 标记页面已保存（重置原始状态）
        this.$doc.resetOriginal()
      } catch (error) {
        console.error('自动保存页面失败:', error)
        throw error
      }
    },

    // 调用AI生成API
    async callAIGenerateAPI(userInput, images = []) {
      try {
        // 获取当前页面的schema
        const currentSchema = this.getCurrentSchema()
        // 准备请求参数
        const formData = new FormData()
        formData.append('schema', JSON.stringify(currentSchema))

        const isFirstCall = !this.isWaitingForContinue

        if (this.isWaitingForContinue) {
          // 追问调用
          formData.append('prompt', '继续')
          formData.append('sessionId', this.sessionId)
        } else {
          // 首次调用
          formData.append('prompt', userInput)
          if (this.sessionId) {
            formData.append('sessionId', this.sessionId)
          }
          // 添加选中的图片文件（File数组）
          if (images.length > 0) {
            images.forEach((imageItem, index) => {
              formData.append('images', imageItem.file)
            })
          }
        }

        // 发送请求
        const response = await this.sendAIRequest(formData)

        if (response.success && response.data) {
          const { text, finishFlag, sessionId } = response.data

          // 保存sessionId
          if (sessionId) {
            this.sessionId = sessionId
          }

          // 执行返回的函数
          if (text) {
            const executionSuccess = await this.executeGeneratedFunction(
              text,
              currentSchema
            )

            // 如果函数执行报错，不继续追问
            if (!executionSuccess) {
              this.isWaitingForContinue = false
              return
            }
          } else {
            this.addBotMessage('⚠️ 服务返回了空的函数内容')
            // 如果是首次调用且没有返回函数，不继续追问
            if (isFirstCall) {
              this.isWaitingForContinue = false
              return
            }
          }

          // 检查是否需要继续调用
          if (finishFlag === 0) {
            this.isWaitingForContinue = true
            // 滚动到底部显示继续等待状态
            this.$nextTick(() => {
              this.scrollToBottom()
            })
            // 自动继续调用
            setTimeout(() => {
              this.callAIGenerateAPI('继续', [])
            }, 1000)
          } else {
            this.isWaitingForContinue = false
            this.addCompletionMessage(
              '🎉 页面生成完成！所有任务已执行完毕，您可以查看生成的页面结构。'
            )
          }
        } else {
          throw new Error(response.message || '服务调用失败')
        }
      } catch (error) {
        console.error('AI生成API调用失败:', error)
        this.addBotMessage('生成失败，请重试。错误信息：' + error.message)
        this.isWaitingForContinue = false
      }
    },

    // 发送AI请求
    async sendAIRequest(formData) {
      // 使用项目的http工具，会自动带上配置的accesstoken
      return await http.post(
        '/zhida/frontend/api/nocode/llm/agent-generate-function',
        formData
      )
    },

    // 调用AI生成图表API
    async callAIGenerateDiagramAPI(userInput) {
      try {
        // 准备请求参数
        const formData = new FormData()
        formData.append('prompt', userInput)
        formData.append('formCode', this.DM.formcode) // 获取当前表单的formCode

        // 如果有会话ID，添加到请求中
        if (this.sessionId) {
          formData.append('sessionId', this.sessionId)
        }

        // 发送请求
        const result = await http.post(
          '/zhida/frontend/api/nocode/llm/agent-generate-diagram',
          formData
        )

        // 保存sessionId（如果返回了）
        if (result.data && result.data.sessionId) {
          this.sessionId = result.data.sessionId
        }

        return result
      } catch (error) {
        console.error('调用AI生成图表API失败:', error)
        throw error
      }
    },

    // 解析并渲染mermaid图表
    parseMermaidContent(text) {
      // 匹配mermaid代码块
      const mermaidRegex = /```mermaid\n([\s\S]*?)\n```/g
      let match
      let processedText = text

      while ((match = mermaidRegex.exec(text)) !== null) {
        const mermaidCode = match[1].trim()

        // 基本验证 - 检查是否为空
        if (!mermaidCode || mermaidCode.length < 3) {
          const errorDiv = `<div class="mermaid-container">
            <div style="color: #ef4444; padding: 15px; border: 1px solid #fecaca; border-radius: 8px; background: #fef2f2;">
              ⚠️ 图表内容为空
            </div>
          </div>`
          processedText = processedText.replace(match[0], errorDiv)
          continue
        }

        const mermaidId = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

        // 直接使用代码，不做复杂预处理
        const mermaidDiv = `<div class="mermaid-container">
          <div id="${mermaidId}" class="mermaid">${mermaidCode}</div>
        </div>`

        processedText = processedText.replace(match[0], mermaidDiv)
      }

      return processedText
    },

    // HTML转义函数
    escapeHtml(text) {
      const div = document.createElement('div')
      div.textContent = text
      return div.innerHTML
    },

    // 渲染mermaid图表
    renderMermaidCharts() {
      console.log('=== renderMermaidCharts 开始 ===')
      this.$nextTick(() => {
        // 如果页面有mermaid图表，需要重新渲染
        const mermaidElements = document.querySelectorAll(
          '.mermaid:not([data-processed])'
        )
        console.log('查找到的未处理mermaid元素:', mermaidElements.length)

        if (mermaidElements.length > 0) {
          // 检查是否已加载mermaid库
          if (typeof window.mermaid !== 'undefined') {
            console.log('mermaid库已加载，开始渲染')
            this.simpleMermaidRender(mermaidElements)
          } else {
            console.log('mermaid库未加载，开始加载')
            // 动态加载mermaid库
            this.loadMermaidLibrary()
              .then(() => {
                console.log('mermaid库加载成功，开始渲染')
                this.simpleMermaidRender(mermaidElements)
              })
              .catch((error) => {
                console.error('加载mermaid失败:', error)
                mermaidElements.forEach((element) => {
                  element.innerHTML =
                    '<div style="color: #ef4444; padding: 10px;">图表库加载失败</div>'
                })
              })
          }
        } else {
          console.log('没有找到需要渲染的mermaid元素')
        }
        console.log('=== renderMermaidCharts 结束 ===')
      })
    },

    // 使用弹窗同样的简单渲染方法
    simpleMermaidRender(elements) {
      try {
        console.log('=== 使用简单渲染方法 ===')
        console.log('待渲染元素数量:', elements.length)

        // 使用default主题配置，优化图表渲染尺寸
        window.mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          // 设置更大的默认尺寸
          themeConfig: {
            primaryColor: '#ff6b6b',
            primaryTextColor: '#333',
            primaryBorderColor: '#ff6b6b',
            lineColor: '#333',
            secondaryColor: '#fff',
            tertiaryColor: '#fff',
          },
          // 图表配置
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis',
          },
          pie: {
            useMaxWidth: true,
          },
          gantt: {
            useMaxWidth: true,
          },
          journey: {
            useMaxWidth: true,
          },
          gitgraph: {
            useMaxWidth: true,
          },
          // 新增 XY 图表支持
          xyChart: {
            useMaxWidth: true,
            width: 700,
            height: 500,
          },
          // 其他新支持的图表类型
          radar: {
            useMaxWidth: true,
          },
          block: {
            useMaxWidth: true,
          },
          packet: {
            useMaxWidth: true,
          },
          kanban: {
            useMaxWidth: true,
          },
          architecture: {
            useMaxWidth: true,
          },
        })

        elements.forEach((element, index) => {
          try {
            console.log(`--- 处理第${index + 1}个元素 ---`)

            const originalCode = element.textContent.trim()
            console.log('原始代码:', originalCode)

            // 基本验证
            if (!originalCode || originalCode.length < 3) {
              console.log('代码为空，跳过')
              element.innerHTML =
                '<div style="color: #ef4444; padding: 10px;">图表内容为空</div>'
              return
            }

            // 清空元素内容，准备重新渲染
            element.innerHTML = ''

            // 标记为已处理
            element.setAttribute('data-processed', 'true')

            // 使用 mermaid.render 方法替代 init 方法
            console.log('调用 mermaid.render...')
            const graphId = `graph-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

            // 尝试使用 mermaid.render 方法
            if (typeof window.mermaid.render === 'function') {
              window.mermaid.render(graphId, originalCode, (svgCode) => {
                console.log('mermaid.render 成功，SVG长度:', svgCode.length)
                element.innerHTML = svgCode
                // 强制调整SVG尺寸以撑满容器
                this.adjustMermaidSvgSize(element)
                // 添加点击事件
                this.addClickToElement(element, originalCode)
              })
            } else {
              // 如果没有 render 方法，回退到 init
              console.log('使用 mermaid.init 方法...')
              element.textContent = originalCode
              window.mermaid.init(undefined, element)

              console.log('渲染完成，内容长度:', element.innerHTML.length)
              console.log('包含SVG:', element.innerHTML.includes('<svg'))

              // 强制调整SVG尺寸以撑满容器
              this.$nextTick(() => {
                this.adjustMermaidSvgSize(element)
              })

              // 添加点击事件
              this.addClickToElement(element, originalCode)
            }
          } catch (error) {
            console.error('单个图表渲染失败:', error)
            element.innerHTML =
              '<div style="color: #ef4444; padding: 10px;">图表渲染失败: ' +
              error.message +
              '</div>'
          }
        })
        console.log('=== 简单渲染结束 ===')
      } catch (error) {
        console.error('mermaid渲染失败:', error)
        elements.forEach((element) => {
          element.innerHTML =
            '<div style="color: #ef4444; padding: 10px;">渲染引擎错误: ' +
            error.message +
            '</div>'
        })
      }
    },

    // 调整所有Mermaid图表尺寸
    adjustAllMermaidSizes() {
      try {
        const mermaidElements = document.querySelectorAll('.mermaid')
        mermaidElements.forEach((element) => {
          this.adjustMermaidSvgSize(element)
        })
      } catch (error) {
        console.error('调整所有mermaid尺寸失败:', error)
      }
    },

    // 调整Mermaid SVG尺寸以撑满容器
    adjustMermaidSvgSize(element) {
      try {
        const svg = element.querySelector('svg')
        if (!svg) {
          console.log('未找到SVG元素')
          return
        }

        // 获取容器宽度
        const containerWidth =
          element.offsetWidth || element.parentElement.offsetWidth
        console.log('容器宽度:', containerWidth)

        if (containerWidth > 0) {
          // 获取SVG的viewBox或原始尺寸
          let originalViewBox = svg.getAttribute('viewBox')
          let originalWidth, originalHeight

          if (originalViewBox) {
            // 如果有viewBox，从中提取宽高
            const viewBoxValues = originalViewBox.split(/\s+/)
            if (viewBoxValues.length >= 4) {
              originalWidth = parseFloat(viewBoxValues[2])
              originalHeight = parseFloat(viewBoxValues[3])
            }
          }

          // 如果没有从viewBox获取到尺寸，尝试从属性获取
          if (!originalWidth || !originalHeight) {
            originalWidth =
              parseFloat(svg.getAttribute('width')) ||
              svg.getBoundingClientRect().width ||
              300 // 默认宽度
            originalHeight =
              parseFloat(svg.getAttribute('height')) ||
              svg.getBoundingClientRect().height ||
              200 // 默认高度
          }

          console.log('SVG原始尺寸:', {
            originalWidth,
            originalHeight,
            originalViewBox,
          })

          // 设置合理的最小尺寸
          const minHeight = 200
          const minWidth = containerWidth - 40 // 减去padding

          // 计算缩放比例
          const aspectRatio = originalHeight / originalWidth
          let targetWidth = minWidth
          let targetHeight = targetWidth * aspectRatio

          // 确保高度不低于最小值
          if (targetHeight < minHeight) {
            targetHeight = minHeight
          }

          console.log('目标尺寸:', { targetWidth, targetHeight })

          // 重新设置viewBox以确保内容撑满容器
          if (!originalViewBox && originalWidth && originalHeight) {
            svg.setAttribute(
              'viewBox',
              `0 0 ${originalWidth} ${originalHeight}`
            )
          }

          // 强制设置SVG尺寸和样式
          svg.style.width = '100%'
          svg.style.height = `${targetHeight}px`
          svg.style.minHeight = `${minHeight}px`
          svg.style.maxWidth = '100%'
          svg.style.display = 'block'
          svg.style.margin = '0 auto'

          // 移除原来的width和height属性，让CSS控制
          svg.removeAttribute('width')
          svg.removeAttribute('height')

          // 设置preserveAspectRatio让内容撑满
          svg.setAttribute('preserveAspectRatio', 'xMidYMid slice')

          // 如果图表内容过小，尝试放大viewBox
          const svgRect = svg.getBoundingClientRect()
          if (svgRect.width > 0 && svgRect.height > 0) {
            // 获取图表内容的实际大小
            const bbox = svg.getBBox()
            if (bbox.width > 0 && bbox.height > 0) {
              // 添加一些边距
              const padding = Math.min(bbox.width, bbox.height) * 0.1
              const newViewBox = `${bbox.x - padding} ${bbox.y - padding} ${bbox.width + padding * 2} ${bbox.height + padding * 2}`
              svg.setAttribute('viewBox', newViewBox)
              console.log('更新viewBox为:', newViewBox)
            }
          }

          console.log('SVG尺寸调整完成')
        }
      } catch (error) {
        console.error('调整SVG尺寸失败:', error)
      }
    },

    // 为单个元素添加点击事件
    addClickToElement(element, originalCode) {
      if (element.dataset.clickAdded) return

      element.style.cursor = 'pointer'
      element.addEventListener('click', () => {
        // 获取聊天框中图表的当前尺寸信息
        const svg = element.querySelector('svg')
        const chatDimensions = svg
          ? {
              width: svg.getBoundingClientRect().width,
              height: svg.getBoundingClientRect().height,
              viewBox: svg.getAttribute('viewBox'),
            }
          : null

        this.openMermaidModal(element.id, originalCode, chatDimensions)
      })
      element.dataset.clickAdded = 'true'
    },

    // 显示mermaid浮层
    async openMermaidModal(id, code, chatDimensions = null) {
      const popupMermaidId = `mermaid-popup-${Date.now()}`
      this.mermaidModalContent = `<div id="${popupMermaidId}" class="mermaid">${code}</div>`
      this.showMermaidModal = true

      // 渲染浮层中的mermaid图表
      this.$nextTick(() => {
        try {
          const element = document.getElementById(popupMermaidId)
          if (element && typeof window.mermaid !== 'undefined') {
            // 使用简单的init方法
            window.mermaid.init(undefined, element)

            // 延迟调整弹窗图表尺寸，确保渲染完成
            setTimeout(() => {
              this.adjustModalMermaidSize(element, chatDimensions)
            }, 100)
          }
        } catch (error) {
          console.error('浮层mermaid渲染失败:', error)
          const element = document.getElementById(popupMermaidId)
          if (element) {
            element.innerHTML = `<div style="color: #ef4444; padding: 20px; text-align: center;">图表加载失败: ${error.message}</div>`
          }
        }
      })
    },

    // 调整弹窗中的Mermaid图表尺寸（3倍放大）
    adjustModalMermaidSize(element, chatDimensions = null) {
      try {
        const svg = element.querySelector('svg')
        if (!svg) {
          console.log('弹窗中未找到SVG元素')
          return
        }

        // 获取弹窗容器的尺寸
        const modalContainer = document.querySelector(
          '.mermaid-chart-container'
        )
        if (!modalContainer) return

        const containerWidth = modalContainer.offsetWidth - 40 // 减去padding
        const containerHeight = modalContainer.offsetHeight - 40

        console.log('弹窗容器尺寸:', { containerWidth, containerHeight })

        // 获取SVG的viewBox或原始尺寸
        let originalViewBox = svg.getAttribute('viewBox')
        let originalWidth, originalHeight

        if (originalViewBox) {
          const viewBoxValues = originalViewBox.split(/\s+/)
          if (viewBoxValues.length >= 4) {
            originalWidth = parseFloat(viewBoxValues[2])
            originalHeight = parseFloat(viewBoxValues[3])
          }
        }

        if (!originalWidth || !originalHeight) {
          originalWidth = parseFloat(svg.getAttribute('width')) || 300
          originalHeight = parseFloat(svg.getAttribute('height')) || 200
        }

        console.log('弹窗SVG原始尺寸:', { originalWidth, originalHeight })

        // 如果有聊天框尺寸信息，基于其进行3倍放大
        let targetWidth, targetHeight

        if (chatDimensions && chatDimensions.width && chatDimensions.height) {
          // 基于聊天框的实际显示尺寸进行3倍放大
          targetWidth = Math.min(containerWidth, chatDimensions.width * 3)
          targetHeight = Math.min(containerHeight, chatDimensions.height * 3)

          console.log('基于聊天框尺寸放大:', {
            chat: chatDimensions,
            target: { targetWidth, targetHeight },
          })
        } else {
          // 回退到原始逻辑
          const aspectRatio = originalHeight / originalWidth
          targetWidth = Math.min(containerWidth, originalWidth * 3)
          targetHeight = targetWidth * aspectRatio

          // 如果高度超过容器，按高度调整
          if (targetHeight > containerHeight) {
            targetHeight = containerHeight
            targetWidth = targetHeight / aspectRatio
          }
        }

        // 确保最小尺寸（比聊天框中的大3倍）
        const minWidth = 600 // 聊天框约200px * 3
        const minHeight = 600 // 聊天框约200px * 3

        if (targetWidth < minWidth) {
          targetWidth = minWidth
        }
        if (targetHeight < minHeight) {
          targetHeight = minHeight
        }

        console.log('弹窗目标尺寸:', { targetWidth, targetHeight })

        // 设置viewBox（如果需要）
        if (!originalViewBox && originalWidth && originalHeight) {
          svg.setAttribute('viewBox', `0 0 ${originalWidth} ${originalHeight}`)
        }

        // 强制设置SVG尺寸
        svg.style.width = `${targetWidth}px`
        svg.style.height = `${targetHeight}px`
        svg.style.maxWidth = '100%'
        svg.style.maxHeight = '100%'
        svg.style.display = 'block'
        svg.style.margin = '0 auto'

        // 移除原始尺寸属性
        svg.removeAttribute('width')
        svg.removeAttribute('height')

        // 使用slice让内容撑满（与聊天框一致）
        svg.setAttribute('preserveAspectRatio', 'xMidYMid slice')

        // 动态调整viewBox（与聊天框逻辑一致）
        try {
          const bbox = svg.getBBox()
          if (bbox.width > 0 && bbox.height > 0) {
            const padding = Math.min(bbox.width, bbox.height) * 0.1
            const newViewBox = `${bbox.x - padding} ${bbox.y - padding} ${bbox.width + padding * 2} ${bbox.height + padding * 2}`
            svg.setAttribute('viewBox', newViewBox)
            console.log('弹窗更新viewBox为:', newViewBox)
          }
        } catch (error) {
          console.log('无法获取bbox，跳过viewBox调整')
        }

        console.log('弹窗SVG尺寸调整完成')
      } catch (error) {
        console.error('调整弹窗SVG尺寸失败:', error)
      }
    },

    // 动态加载mermaid库
    loadMermaidLibrary() {
      return new Promise((resolve, reject) => {
        if (typeof window.mermaid !== 'undefined') {
          resolve()
          return
        }

        const script = document.createElement('script')
        // 使用最新的mermaid版本，支持xychart-beta等新特性
        script.src =
          'https://cdn.jsdelivr.net/npm/mermaid@11.7.0/dist/mermaid.min.js'
        script.onload = () => {
          try {
            window.mermaid.initialize({
              startOnLoad: false,
              theme: 'default',
              // 图表配置
              flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis',
              },
              pie: {
                useMaxWidth: true,
              },
              gantt: {
                useMaxWidth: true,
              },
              journey: {
                useMaxWidth: true,
              },
              gitgraph: {
                useMaxWidth: true,
              },
              // 新增 XY 图表支持
              xyChart: {
                useMaxWidth: true,
                width: 700,
                height: 500,
              },
              // 其他新支持的图表类型
              radar: {
                useMaxWidth: true,
              },
              block: {
                useMaxWidth: true,
              },
              packet: {
                useMaxWidth: true,
              },
              kanban: {
                useMaxWidth: true,
              },
              architecture: {
                useMaxWidth: true,
              },
            })
            resolve()
          } catch (error) {
            console.error('Mermaid初始化失败:', error)
            reject(error)
          }
        }
        script.onerror = (error) => {
          console.error('Mermaid脚本加载失败:', error)
          reject(error)
        }
        document.head.appendChild(script)
      })
    },

    // 获取当前页面的schema
    getCurrentSchema() {
      try {
        if (this.$doc && this.$doc.getSimpleSchema) {
          return this.$doc.getSimpleSchema()
        }
        return {}
      } catch (error) {
        console.error('获取schema失败:', error)
        return {}
      }
    },

    // 执行生成的函数
    async executeGeneratedFunction(functionText, currentSchema) {
      try {
        // 获取当前schema
        const schema = this.$doc.getSimpleSchema()

        // 提取函数名
        const functionNameMatch = functionText.match(/function\s+(\w+)\s*\(/)
        if (!functionNameMatch) {
          throw new Error('无法解析函数名')
        }
        const functionName = functionNameMatch[1]

        // 如果函数名是 end，直接显示完成消息
        if (functionName === 'end') {
          return true
        }

        // 创建执行环境，定义函数并调用
        const executeCode = `
          ${functionText}
          return ${functionName}(arguments[0]);
        `

        // 执行函数
        const func = new Function(executeCode)
        const result = func(schema)

        // 更新schema
        if (result) {
          this.$doc.setSchema(result)
        }

        // 提取函数注释作为执行描述
        const functionDescription =
          this.extractFunctionDescription(functionText)
        const successMessage = functionDescription
          ? `✅ 已执行: ${functionDescription}`
          : '✅ 页面结构已更新！'

        // 成功执行
        this.addBotMessage(successMessage, false)

        // 添加历史记录
        if (this.$doc.history && this.$doc.history.addHistory) {
          this.$doc.history.addHistory()
        }

        return true // 返回成功状态
      } catch (error) {
        console.error('执行生成的函数失败:', error)

        // 提取函数名
        const functionNameMatch = functionText.match(/function\s+(\w+)\s*\(/)
        const functionName = functionNameMatch
          ? functionNameMatch[1]
          : '未知函数'

        this.addBotMessage(
          '❌ 函数执行失败：' + error.message,
          true, // 标记执行失败
          functionName, // 函数名
          error.message // 错误信息
        )
        return false // 返回失败状态
      }
    },

    // 添加机器人消息
    addBotMessage(
      content,
      executionFailed = false,
      functionName = '',
      errorMessage = '',
      isHtml = false
    ) {
      const botMessage = {
        id: this.messageIdCounter++,
        type: 'bot',
        content,
        timestamp: new Date(),
        executionFailed, // 标记脚本是否执行失败
        functionName, // 失败的函数名
        errorMessage, // 错误信息
        isHtml, // 标记内容是否为HTML
      }

      // 根据当前标签页添加到对应的消息数组
      if (this.activeTab === 'generate') {
        this.generateMessages.push(botMessage)
      } else {
        this.helpMessages.push(botMessage)
      }

      this.$nextTick(() => {
        this.scrollToBottom()

        // 如果是HTML内容且包含mermaid，触发渲染
        if (isHtml && content.includes('class="mermaid"')) {
          console.log('检测到mermaid内容，准备渲染')
          // 延迟一下再渲染，确保DOM已更新
          setTimeout(() => {
            this.renderMermaidCharts()
          }, 100)
        }
      })
    },

    // 自动重试失败的函数
    retryFailedFunction(functionName, errorMessage) {
      if (this.sending || this.isWaitingForContinue) {
        return // 如果正在发送或等待，不允许重试
      }

      // 立即隐藏重试按钮 - 找到对应的消息并修改其executionFailed状态
      const messages =
        this.activeTab === 'generate'
          ? this.generateMessages
          : this.helpMessages
      const failedMessage = messages.find(
        (msg) =>
          msg.executionFailed &&
          msg.functionName === functionName &&
          msg.errorMessage === errorMessage
      )

      if (failedMessage) {
        failedMessage.executionFailed = false // 隐藏重试按钮
      }

      // 构建重试消息
      const retryMessage = `${functionName} 函数执行失败，请重试继续，错误信息：${errorMessage}`

      // 设置输入框内容并自动发送
      this.inputMessage = retryMessage
      this.sendMessage()
    },

    // 添加完成消息（带特殊样式）
    addCompletionMessage(content) {
      const completionMessage = {
        id: this.messageIdCounter++,
        type: 'bot',
        content,
        timestamp: new Date(),
        isCompletion: true, // 标记为完成消息
      }

      // 根据当前标签页添加到对应的消息数组
      if (this.activeTab === 'generate') {
        this.generateMessages.push(completionMessage)
      } else {
        this.helpMessages.push(completionMessage)
      }

      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    // 填充示例到输入框
    fillExample(example) {
      this.inputMessage = example
      // 聚焦到文本输入框
      this.$nextTick(() => {
        const textareaInput = this.$refs.textareaInput
        if (textareaInput) {
          textareaInput.focus()
        }
      })
    },

    // 显示全屏内容
    showFullscreenContent(content) {
      this.fullscreenContent = content
      this.showFullscreenDialog = true
    },

    // 关闭全屏内容
    closeFullscreenContent() {
      this.showFullscreenDialog = false
      this.fullscreenContent = ''
    },

    // 检测消息内容是否包含Mermaid图表
    hasMermaidChart(content) {
      if (!content || typeof content !== 'string') return false
      return (
        content.includes('class="mermaid"') ||
        content.includes('<div class="mermaid">')
      )
    },

    // 显示Mermaid代码
    showMermaidCode(content) {
      const mermaidCodes = this.extractMermaidCodes(content)
      if (mermaidCodes.length > 0) {
        // 如果有多个图表，合并显示
        this.mermaidCodeContent = mermaidCodes.join('\n\n---\n\n')
        this.showMermaidCodeModal = true
      } else {
        this.message?.warning('未找到Mermaid代码')
      }
    },

    // 关闭Mermaid代码查看弹窗
    closeMermaidCodeModal() {
      this.showMermaidCodeModal = false
      this.mermaidCodeContent = ''
    },

    // 提取Mermaid代码
    extractMermaidCodes(content) {
      const codes = []

      // 方法1: 从mermaid代码块中提取
      const mermaidRegex = /```mermaid\n([\s\S]*?)\n```/g
      let match
      while ((match = mermaidRegex.exec(content)) !== null) {
        codes.push(match[1].trim())
      }

      // 方法2: 从HTML中的.mermaid元素提取
      if (codes.length === 0) {
        const tempDiv = document.createElement('div')
        tempDiv.innerHTML = content
        const mermaidElements = tempDiv.querySelectorAll('.mermaid')

        mermaidElements.forEach((element) => {
          const code = element.textContent?.trim()
          if (code && code.length > 0) {
            codes.push(code)
          }
        })
      }

      return codes
    },

    // 复制Mermaid代码
    async copyMermaidCode() {
      try {
        if (navigator.clipboard && window.isSecureContext) {
          // 现代浏览器
          await navigator.clipboard.writeText(this.mermaidCodeContent)
        } else {
          // 降级方案
          const textArea = document.createElement('textarea')
          textArea.value = this.mermaidCodeContent
          textArea.style.position = 'fixed'
          textArea.style.left = '-999999px'
          textArea.style.top = '-999999px'
          document.body.appendChild(textArea)
          textArea.focus()
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
        }
        this.message?.success('代码已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        this.message?.error('复制失败，请手动选择复制')
      }
    },

    // AI帮助功能 - 调用真实API
    async generateMockResponse(userInput) {
      try {
        // 调用AI帮助API
        const response = await this.callAIHelpAPI(userInput)

        log('AI帮助API响应:', response) // 调试日志

        if (response.success && response.data && response.data.text) {
          // 清理和处理HTML内容
          const htmlContent = this.processHtmlContent(response.data.text)
          log('处理后的HTML内容:', htmlContent) // 调试日志
          // 将HTML内容作为机器人回复
          this.addBotMessage(htmlContent, false, '', '', true)
        } else {
          this.addBotMessage('抱歉，暂时无法获取帮助信息，请稍后再试。')
        }
      } catch (error) {
        console.error('AI帮助API调用失败:', error)
        this.addBotMessage('抱歉，服务暂时不可用，请稍后再试。')
      }
    },

    // 处理HTML内容
    processHtmlContent(htmlText) {
      if (!htmlText) return ''

      // 确保HTML内容被正确处理
      let processedHtml = htmlText.trim()

      // 如果内容不包含HTML标签，直接返回
      if (!processedHtml.includes('<')) {
        return processedHtml
      }

      // 处理链接标签，添加样式和target属性
      processedHtml = processedHtml.replace(
        /<a\s+([^>]*?)>/gi,
        (match, attributes) => {
          // 强制添加样式
          const linkStyle =
            'color: #1e40af !important; text-decoration: underline !important;'

          // 检查是否已有style属性
          if (attributes.includes('style=')) {
            // 如果已有style，在现有样式后添加
            attributes = attributes.replace(
              /style\s*=\s*["']([^"']*?)["']/i,
              (styleMatch, existingStyle) => {
                return `style="${existingStyle}; ${linkStyle}"`
              }
            )
          } else {
            // 如果没有style属性，添加新的
            attributes += ` style="${linkStyle}"`
          }

          // 确保有target="_blank"
          if (!attributes.includes('target=')) {
            attributes += ' target="_blank"'
          }

          return `<a ${attributes}>`
        }
      )

      return processedHtml
    },

    // 调用AI帮助API
    async callAIHelpAPI(userInput) {
      try {
        // 准备请求参数
        const formData = new FormData()
        formData.append('prompt', userInput)

        // 如果有AI帮助的会话ID，添加到请求中
        if (this.helpSessionId) {
          formData.append('sessionId', this.helpSessionId)
        }

        // 发送请求 - 使用项目的http工具，会自动带上配置的accesstoken
        const result = await http.post(
          '/zhida/frontend/api/nocode/llm/agent-manual',
          formData
        )

        // 保存AI帮助的sessionId（如果返回了）
        if (result.data && result.data.sessionId) {
          this.helpSessionId = result.data.sessionId
        }

        return result
      } catch (error) {
        console.error('调用AI帮助API失败:', error)
        throw error
      }
    },

    formatTime(timestamp) {
      return timestamp.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
      })
    },

    scrollToBottom() {
      if (this.$refs.chatContent) {
        this.$refs.chatContent.scrollTop = this.$refs.chatContent.scrollHeight
      }
    },

    // 提取函数描述注释
    extractFunctionDescription(functionText) {
      try {
        // 按行分割代码
        const lines = functionText.split('\n')

        // 查找函数定义行
        let functionLineIndex = -1
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim()
          if (line.startsWith('function ') && line.includes('(')) {
            functionLineIndex = i
            break
          }
        }

        // 如果找到函数定义，查找其上方的注释
        if (functionLineIndex > 0) {
          const previousLine = lines[functionLineIndex - 1].trim()

          // 检查上一行是否是注释
          if (previousLine.startsWith('//')) {
            // 提取注释内容，去掉 // 前缀
            return previousLine.replace(/^\/\/\s*/, '')
          }
        }

        return null
      } catch (error) {
        console.error('提取函数描述失败:', error)
        return null
      }
    },

    // 拖拽相关方法
    startDrag(event) {
      // 防止在点击按钮时触发拖拽
      if (event.target.closest('.ai-chat-actions')) {
        return
      }

      this.isDragging = true
      this.dragStartX = event.clientX - this.chatPosition.x
      this.dragStartY = event.clientY - this.chatPosition.y

      // 添加全局事件监听
      document.addEventListener('mousemove', this.onDrag, { passive: false })
      document.addEventListener('mouseup', this.stopDrag)

      // 防止文本选择和默认行为
      event.preventDefault()
      event.stopPropagation()
    },

    onDrag(event) {
      if (!this.isDragging) return

      const newX = event.clientX - this.dragStartX
      const newY = event.clientY - this.dragStartY

      // 获取对话框的实际尺寸
      const chatContainer = document.querySelector('.ai-chat-container')
      const containerWidth = chatContainer ? chatContainer.offsetWidth : 380

      const minVisibleWidth = 100 // 至少保持100px可见
      const minVisibleHeight = 50 // 至少保持50px可见

      // 计算边界 - 允许更大的拖拽范围
      const minX = -containerWidth + minVisibleWidth // 允许大部分移出左边，但保持一部分可见
      const maxX = window.innerWidth - minVisibleWidth // 允许大部分移出右边，但保持一部分可见
      const minY = -minVisibleHeight // 允许部分移出顶部
      const maxY = window.innerHeight - minVisibleHeight // 允许部分移出底部

      this.chatPosition.x = Math.max(minX, Math.min(maxX, newX))
      this.chatPosition.y = Math.max(minY, Math.min(maxY, newY))
    },

    stopDrag() {
      this.isDragging = false

      // 移除全局事件监听
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },

    // AI图表生成响应处理
    async generateDiagramResponse(userInput) {
      try {
        // 调用AI图表生成API
        const response = await this.callAIGenerateDiagramAPI(userInput)

        log('AI图表生成API响应:', response) // 调试日志

        if (response.success && response.data && response.data.text) {
          // 处理mermaid内容
          const processedContent = this.parseMermaidContent(response.data.text)
          log('处理后的mermaid内容:', processedContent) // 调试日志

          // 将处理后的内容作为机器人回复（mermaid渲染会在addBotMessage中自动处理）
          this.addBotMessage(processedContent, false, '', '', true)
        } else {
          this.addBotMessage('抱歉，暂时无法生成图表分析，请稍后再试。')
        }
      } catch (error) {
        console.error('AI图表生成失败:', error)
        this.addBotMessage('抱歉，图表生成服务暂时不可用，请稍后再试。')
      }
    },

    // 处理mermaid弹窗事件
    handleShowMermaidModal(event) {
      const { id, code } = event.detail
      if (id && code) {
        // 创建新的mermaid图表ID
        const modalMermaidId = `mermaid-modal-${Date.now()}`
        this.mermaidModalContent = `<div id="${modalMermaidId}" class="mermaid">${code}</div>`
        this.showMermaidModal = true

        // 渲染弹窗中的mermaid图表
        this.$nextTick(() => {
          if (typeof window.mermaid !== 'undefined') {
            const element = document.getElementById(modalMermaidId)
            if (element) {
              window.mermaid.init(undefined, element)
            }
          } else {
            this.loadMermaidLibrary().then(() => {
              const element = document.getElementById(modalMermaidId)
              if (element) {
                window.mermaid.init(undefined, element)
              }
            })
          }
        })
      }
    },

    // 关闭mermaid浮层
    closeMermaidModal() {
      this.showMermaidModal = false
      this.mermaidModalContent = ''
    },
  },
}
</script>

<style scoped lang="scss">
.ai-assistant {
  font-size: 14px;
}

/* 浮动按钮样式 */
.ai-float-button {
}

.ai-float-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

.ai-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 2px;
}

.ai-button-text {
  font-size: 10px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* 聊天容器样式 */
.ai-chat-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 300px;
  height: calc(100vh - 40px);
  min-height: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  transition:
    width 0.3s ease,
    opacity 0.2s ease;
  z-index: 9999;
}

/* 扩展宽度样式 */
.ai-chat-container.ai-chat-expanded {
  width: 760px;
}

/* 拖拽状态样式 */
.ai-chat-container.ai-chat-dragging {
  opacity: 0.7;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.25);
  z-index: 10001;
}

/* 聊天头部 */
.ai-chat-header {
  padding: 0 10px;
  height: 38px;
  background: #fff;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: move;
  user-select: none;
  border-bottom: 1px solid #e0e3e5;
}

.ai-chat-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 14px;
}

.ai-header-icon {
  width: 17px;
  height: 13px;
  margin-right: 5px;
}

.ai-chat-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-chat-expand,
.ai-chat-reset,
.ai-chat-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  color: #666;
}

.ai-chat-expand:hover,
.ai-chat-reset:hover,
.ai-chat-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.ai-chat-expand i,
.ai-chat-reset i,
.ai-chat-close i {
  font-size: 16px;
}

/* 聊天内容区域 */
.ai-chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #fff;
}

.ai-chat-messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 消息样式 */
.ai-message {
  display: flex;
  gap: 5px;
}

.ai-message-user {
  flex-direction: row-reverse;
}

.ai-message-avatar {
  flex-shrink: 0;
  font-size: 13px;
  color: #333333;
  letter-spacing: 0;
  font-weight: 600;
}

.ai-avatar-icon {
  width: 16px;
  height: 16px;
  color: white;
}

.ai-message-content {
  max-width: 260px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 扩展模式下的消息内容宽度 */
.ai-chat-expanded .ai-message-content {
  max-width: 660px;
}

.ai-message-text {
  padding: 10px;
  border-radius: 8px;
  font-size: 13px;
  line-height: 1.4;
  word-wrap: break-word;
}

.user-select-text {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}
.ai-message-bot {
  flex-direction: column;
}
.ai-message-bot .ai-message-text {
  background: white;
  color: #333333;
  background: #f5f6f8;
}
.ai-message-bot .ai-example-text {
  background: #f4f8ff;
  border-radius: 8px;
  border: 0;
  width: 100%;
}

.ai-message-bot .ai-waiting-message {
  background-image: linear-gradient(
    269deg,
    #3e82eb 0%,
    #56adff 100%
  ) !important;
  color: white !important;
  border: none !important;
}

/* 完成消息特殊样式 */
.ai-message-completion {
  animation: completionPulse 2s ease-in-out;
}

.ai-completion-text {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
  border: none !important;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  position: relative;
}

.ai-completion-text::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 20px;
  z-index: -1;
  opacity: 0.3;
  animation: completionGlow 2s ease-in-out infinite alternate;
}

.ai-message-user .ai-message-text {
  background-image: linear-gradient(269deg, #3e82eb 0%, #56adff 100%);
  color: white;
}

.ai-message-suggestion {
  padding: 8px 16px;
  background: #f3f4f6;
  border-radius: 12px;
  font-size: 13px;
  color: #6b7280;
  margin-top: 4px;
}

.ai-message-actions {
  display: flex;
  align-items: center;
  justify-content: right;
  gap: 8px;
  margin-top: 4px;
}

.ai-message-time {
  font-size: 11px;
  color: #9ca3af;
  padding: 0 4px;
}

.ai-retry-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 12px;
  color: #ef4444;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: 4px;
}

.ai-retry-button:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
}

/* HTML内容样式 */
.ai-html-content-wrapper {
  position: relative;
}

.ai-html-content {
  line-height: 1.6;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-word;
}

.ai-html-content p {
  margin: 8px 0;
}

.ai-html-content ol,
.ai-html-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.ai-html-content li {
  margin: 4px 0;
}

.ai-html-content strong {
  font-weight: 600;
  color: #333333;
}

.ai-html-content a {
  color: #1e40af !important;
  text-decoration: underline !important;
  transition: all 0.2s;
}

.ai-html-content a:hover {
  color: #1d4ed8 !important;
  text-decoration: underline !important;
}

.ai-html-content div {
  margin: 4px 0;
}

.ai-html-content table {
  width: 100%;
  max-width: 100%;
  table-layout: fixed;
  word-wrap: break-word;
}

.ai-html-content img {
  max-width: 100%;
  height: auto;
}

.ai-html-content pre {
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.ai-html-content code {
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 全屏查看按钮 */
.ai-fullscreen-view-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(102, 126, 234, 0.9);
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
  z-index: 10;
}

.ai-fullscreen-view-btn:hover {
  background: rgba(102, 126, 234, 1);
  transform: translateY(-1px);
}

/* 欢迎消息样式 */
.ai-welcome-text {
  margin-bottom: 12px;
  line-height: 1.5;
}

.ai-welcome-examples {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.ai-example-link {
  font-size: 12px;
  color: var(--xn-color-primary);
  letter-spacing: 0;
  font-weight: 400;
}

.ai-example-link:hover {
  transform: translateY(-1px);
}

/* 输入区域 */
.ai-chat-input {
  background: white;
}

.ai-input-tabs {
  display: flex;
  gap: 4px;
  padding: 0 8px;
}

.ai-input-tab {
  padding: 0 4px;
  height: 23px;
  flex: 1;
  background: #ffffff;
  border: 1px solid rgba(216, 217, 220, 1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s;
  .ai-icon {
    width: 15px;
    height: 15px;
  }
  span {
    margin-left: 2px;
  }
}

.ai-input-tab.active {
  color: var(--xn-color-primary);
  border-color: var(--xn-color-primary);
  background: color-mix(in srgb, var(--xn-color-primary) 13%, white);
}

.ai-input-tab.disabled {
  color: #d1d5db !important;
  background: #f9fafb !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.ai-input-tab.disabled:hover {
  background: #f9fafb !important;
  color: #d1d5db !important;
}

.ai-input-container {
  padding: 8px;
}

.ai-input-wrapper {
  position: relative;
  width: 100%;
}

.ai-input-textarea {
  width: 100%;
  min-height: 40px;
  max-height: 120px;
  padding: 10px 44px 24px 12px;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  resize: none;
  font-size: 14px;
  line-height: 1.4;
  outline: none;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.ai-input-textarea:focus {
  border-color: var(--xn-color-primary);
}

.ai-input-textarea::placeholder {
  color: #9ca3af;
}

.ai-input-counter {
  position: absolute;
  bottom: 15px;
  right: 32px;
  font-size: 11px;
  color: #9ca3af;
  pointer-events: none;
  user-select: none;
  transition: color 0.2s;
}

.ai-input-counter.warning {
  color: #f59e0b;
}

.ai-input-counter.danger {
  color: #ef4444;
}

.ai-send-button {
  position: absolute;
  bottom: 10px;
  right: 5px;
  .svg-icon {
    width: 20px;
    height: 20px;
  }
  border: none;
  border-radius: 100%;
  color: var(--xn-color-primary);
}

.ai-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.ai-send-button i {
  font-size: 16px;
}

.ai-waiting-message {
  background-image: linear-gradient(269deg, #3e82eb 0%, #56adff 100%);
  color: white !important;
  border: none;
  padding: 12px 16px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
}

.ai-waiting-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.ai-waiting-dots {
  display: flex;
  gap: 4px;
}

.ai-waiting-dots span {
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.ai-waiting-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.ai-waiting-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.ai-waiting-text {
  font-size: 14px;
  font-weight: 500;
  color: white;
}

.ai-waiting-tip {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.ai-status-tip {
  padding: 8px 16px;
  background: #f0f9ff;
  border-top: 1px solid #e0f2fe;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #0369a1;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes completionPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes completionGlow {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.6;
  }
}

/* 滚动条样式 */
.ai-chat-content::-webkit-scrollbar {
  width: 4px;
}

.ai-chat-content::-webkit-scrollbar-track {
  background: transparent;
}

.ai-chat-content::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.ai-chat-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 图片上传区域 */
.ai-upload-section {
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background: #f9fafb;
  margin-bottom: 8px;
}

.ai-upload-header {
  display: flex;
  justify-content: space-between;
  padding: 4px 12px;
}

.ai-upload-label {
  font-size: 13px;
  font-weight: 500;
  color: #333333;
  display: flex;
  flex-wrap: wrap;
}

.ai-upload-count {
  padding: 2px 6px;
  background: var(--xn-color-primary);
  color: white;
  border-radius: 10px;
  font-size: 11px;
  font-weight: normal;
}

.ai-upload-actions {
  display: flex;
  align-items: start;
  gap: 8px;
  flex-shrink: 0;
}

.ai-upload-clear {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  background: transparent;
  border: 1px solid #ef4444;
  border-radius: 6px;
  color: #ef4444;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;
}

.ai-upload-clear:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.ai-upload-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  background: transparent;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: var(--xn-color-primary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.ai-upload-toggle:hover {
  background: var(--xn-color-primary);
  color: white;
  border-color: var(--xn-color-primary);
}

.ai-upload-content {
  padding: 12px;
  background: white;
  border-radius: 0 0 8px 8px;
}

/* 文本输入区域 */
.ai-textarea-wrapper {
  position: relative;
  width: 100%;
}

.ai-textarea {
  width: 100%;
  height: 120px;
  padding: 10px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  resize: vertical;
  font-size: 14px;
  line-height: 1.4;
  outline: none;
  transition: border-color 0.2s;
  box-sizing: border-box;
  font-family: inherit;
  background: white;
}

.ai-textarea:focus {
  border-color: var(--xn-color-primary);
}

.ai-textarea::placeholder {
  color: #9ca3af;
}

/* 全屏内容对话框样式 */
.ai-fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
}

.ai-fullscreen-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 90vw;
  max-height: 90vh;
  width: 80vw;
  height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ai-fullscreen-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.ai-fullscreen-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.ai-fullscreen-close {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s;
}

.ai-fullscreen-close:hover {
  background: #e5e7eb;
  color: #333333;
}

.ai-fullscreen-content {
  flex: 1;
  padding: 20px 24px;
  overflow: auto;
}

.ai-fullscreen-html-content {
  line-height: 1.6;
  word-wrap: break-word;
  word-break: break-word;
}

.ai-fullscreen-html-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.ai-fullscreen-html-content th,
.ai-fullscreen-html-content td {
  border: 1px solid #e5e7eb;
  padding: 8px 12px;
  text-align: left;
}

.ai-fullscreen-html-content th {
  background: #f9fafb;
  font-weight: 600;
}

.ai-fullscreen-html-content img {
  max-width: 100%;
  height: auto;
  margin: 16px 0;
}

.ai-fullscreen-html-content pre {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 16px 0;
}

.ai-fullscreen-html-content code {
  background: #f1f5f9;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.ai-fullscreen-html-content a {
  color: #1e40af !important;
  text-decoration: underline !important;
  transition: all 0.2s;
}

.ai-fullscreen-html-content a:hover {
  color: #1d4ed8 !important;
}

/* 图片上传容器样式 */
.ai-image-upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
  background: #fafafa;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

/* 图片上传按钮样式 */
.ai-image-upload-button {
  width: 80px;
  height: 80px;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: #ffffff;
  transition: all 0.2s ease;
  color: #6b7280;
  font-size: 12px;
}

.ai-image-upload-button:hover {
  border-color: var(--xn-color-primary);
  color: var(--xn-color-primary);
  background: #f8faff;
}

.ai-image-upload-button i {
  font-size: 20px;
  margin-bottom: 4px;
}

.ai-image-upload-hint {
  font-size: 10px;
  color: #9ca3af;
  text-align: center;
  line-height: 1.2;
  margin-top: 2px;
}

/* 图片预览样式 */
.ai-image-preview {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  background: #ffffff;
  border: 1px solid #e5e7eb;
}

.ai-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.ai-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.ai-image-preview:hover .ai-image-overlay {
  opacity: 1;
}

.ai-image-name {
  color: white;
  font-size: 10px;
  line-height: 1.2;
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.ai-image-remove {
  align-self: flex-end;
  width: 20px;
  height: 20px;
  border: none;
  background: rgba(239, 68, 68, 0.8);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: background 0.2s ease;
}

.ai-image-remove:hover {
  background: rgba(239, 68, 68, 1);
}

/* Mermaid图表样式 */
.mermaid-container {
  margin: 10px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  width: 100%;
  box-sizing: border-box;
}

.mermaid {
  text-align: center;
  background: white;
  border-radius: 4px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 200px;
  display: block;
  width: 100%;
  box-sizing: border-box;
  overflow: visible;
}

.mermaid:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* mermaid SVG样式 - 确保图表撑满容器 */
.mermaid svg {
  width: 100% !important;
  height: auto !important;
  min-height: 180px !important;
  max-width: 100%;
  display: block;
  margin: 0 auto;
}

/* 针对聊天消息中的mermaid图表特殊样式 */
.ai-message-content .mermaid-container {
  width: 100%;
  max-width: none;
}

.ai-message-content .mermaid {
  min-height: 200px;
  width: 100%;
  padding: 15px;
  background: white;
  border-radius: 4px;
}

.ai-message-content .mermaid svg {
  width: 100% !important;
  height: auto !important;
  min-height: 200px !important;
  min-width: 100% !important;
  display: block !important;
  margin: 0 auto !important;
}

/* 强制Mermaid图表元素撑满 */
.ai-message-content .mermaid svg g {
  transform-origin: center center !important;
}

.ai-message-content .mermaid svg .flowchart,
.ai-message-content .mermaid svg .pie,
.ai-message-content .mermaid svg .gantt,
.ai-message-content .mermaid svg .journey {
  width: 100% !important;
  height: 100% !important;
}

/* Mermaid图表浮层样式 */
.mermaid-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
}

.mermaid-popup {
  position: relative;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  max-width: 95vw;
  max-height: 95vh;
  min-width: 800px;
  min-height: 600px;
  width: 85vw;
  height: 80vh;
  padding: 20px;
  overflow: auto;
  animation: mermaidPopupFadeIn 0.2s ease-out;
}

.mermaid-close {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666;
  transition: all 0.2s;
  z-index: 1;
}

.mermaid-close:hover {
  background: rgba(0, 0, 0, 0.2);
  color: #333;
}

.mermaid-chart-container {
  line-height: 1.6;
  word-wrap: break-word;
  word-break: break-word;
  text-align: center;
  min-height: 500px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
  overflow: hidden;
}

/* 弹窗中的Mermaid图表样式 */
.mermaid-chart-container .mermaid {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  max-height: 100%;
}

.mermaid-chart-container .mermaid svg {
  display: block !important;
  margin: 0 auto !important;
  max-width: 100% !important;
  max-height: 100% !important;
}

/* 强制弹窗中的Mermaid图表元素撑满 */
.mermaid-chart-container .mermaid svg g {
  transform-origin: center center !important;
}

.mermaid-chart-container .mermaid svg .flowchart,
.mermaid-chart-container .mermaid svg .pie,
.mermaid-chart-container .mermaid svg .gantt,
.mermaid-chart-container .mermaid svg .journey {
  width: 100% !important;
  height: 100% !important;
}

@keyframes mermaidPopupFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式处理 */
@media (max-width: 768px) {
  .mermaid-popup {
    width: 95vw;
    height: 80vh;
    min-width: 300px;
    min-height: 250px;
    padding: 15px;
  }

  .mermaid-chart-container {
    min-height: 200px;
    padding: 15px;
  }
}

/* AI内容操作按钮区域 */
.ai-content-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* Mermaid代码查看按钮样式 */
.ai-mermaid-code-btn {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.ai-mermaid-code-btn:hover {
  background: #e5e7eb;
  color: #374151;
  border-color: #9ca3af;
}

/* Mermaid代码查看弹窗样式 */
.mermaid-code-popup {
  position: relative;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  max-width: 90vw;
  max-height: 90vh;
  width: 700px;
  height: 500px;
  overflow: hidden;
  animation: mermaidPopupFadeIn 0.2s ease-out;
  display: flex;
  flex-direction: column;
}

.mermaid-code-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f9fafb;
  flex-shrink: 0;
}

.mermaid-code-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.mermaid-code-content {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.mermaid-code-content pre {
  flex: 1;
  margin: 0;
  padding: 20px;
  background: #f8fafc;
  border: none;
  overflow: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.mermaid-code-text {
  color: #1f2937;
  background: transparent;
  display: block;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

.mermaid-copy-btn {
  position: absolute;
  bottom: 16px;
  right: 16px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #4f46e5;
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

.mermaid-copy-btn:hover {
  background: #4338ca;
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
  transform: translateY(-1px);
}

/* 响应式处理 */
@media (max-width: 768px) {
  .mermaid-code-popup {
    width: 95vw;
    height: 80vh;
    min-width: 300px;
  }

  .ai-content-actions {
    justify-content: center;
  }

  .ai-mermaid-code-btn {
    font-size: 11px;
    padding: 3px 6px;
  }
}
</style>
