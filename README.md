# sinitek-lowcode-frontend

低代码说明，兼容 vue3 以 2.7 为准。

## 开发环境

- node 20.x
- pnpm 8.x
- vue2.7

用npm全局装一下**pnpm**，使用**pnpm**管理的依赖。

```bash
npm i -g pnpm
```

## 运行开发环境

### 第一次安装依赖

可以直接在主目录进行安装

```bash
pnpm install
// or
pnpm i
```

### 启动开发环境

自动给依赖的子库进行build。

```bash
// 启动模拟器
pnpm run dev:simulator
// 启动管理页面
pnpm run dev:manager
```

.env.development.local 修改代理地址不需要重启

子库build会缓存，当需要清除缓存时，使用下面的命令

```bash
npx nx reset
```

### 给单独的项目添加依赖

下面以 simulator 为例

```bash
pnpm add packageName --filter *simulator -D
// or
pnpm add packageName --filter sinitek-lowcode-simulator -D
// 开发时的依赖
pnpm add packageName -D
// 例如vue这种全局都需要使用的库, 将添加到peerDependencies中
pnpm add vue --save-peer
```

## 开发注意点

- 样式使用原子化去开发，减少样式体积。**管理页面不用**
- [https://unocss.dev/interactive/](https://unocss.dev/interactive/) 可以直接搜索对应的样式名称，进行开发。
- vscode 安装 unocss 插件，可以自动补全样式。

## 命名规则

文件夹和文件名使用 `kebab-case`
组件的使用，使用 `pascal-case`

## commit规范

- type must be one of [build, chore, ci, docs, feat, fix, init, perf, refactor, revert, style, test, log] [type-enum]
- Get help: https://github.com/conventional-changelog/commitlint/#what-is-commitlint

```txt
chore ：构建过程或辅助工具的变动
docs ：文档（documentation）
feat ：新功能（feature）
fix ：修补bug
refactor ：重构（即不是新增功能，也不是修改bug的代码变动）
style ： 格式（不影响代码运行的变动）
test ：增加测试
```

## 版本发布

修改主目录中的 `package.json` 中的版本号

提交合并后,手动点击发布

![alt text](./assets/image.png)
