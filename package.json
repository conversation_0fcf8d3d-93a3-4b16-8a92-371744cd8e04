{"name": "sinitek-lowcode-frontend", "description": "To make it easy for you to get started with GitLab, here's a list of recommended next steps.", "main": "index.js", "packageManager": "pnpm@9.6.0", "scripts": {"preinstall": "npx only-allow pnpm", "cai": "node ./scripts/clean.mjs && pnpm i", "bootstrap": "pnpm install", "prepare": "husky", "lint-staged": "cross-env lintStaged=1 lint-staged", "commitlint": "commitlint -e", "setNodeMaxOldSpaceSize": "set NODE_OPTIONS='--max-old-space-size=8192'", "dev:manager": "nx dev sinitek-lowcode-manager", "dev:manager:multi": "pnpm run build:render && pnpm run build:advanced && pnpm run build:simulator && pnpm run -C packages/sinitek-lowcode-manager dev:multi", "dev:simulator": "nx dev sinitek-lowcode-simulator", "dev:flow": "nx dev sinitek-lowcode-flow", "dev:fe": "nx dev sinitek-lowcode-flow-execute", "dev:sb": "bun run nx dev:bun sinitek-lowcode-simulator", "dev:mb": "bun run nx dev:bun sinitek-lowcode-manager", "dev:zd": "nx dev sinitek-lowcode-form", "dev:form": "nx dev sinitek-lowcode-form", "vite:simulator": "nx vite sinitek-lowcode-simulator", "test:simulator": "nx test sinitek-lowcode-simulator", "test:zd": "nx test sinitek-lowcode-form", "dev:render": "pnpm run -C packages/sinitek-lowcode-render dev", "clean": "lerna clean", "format": "lerna run format", "lint": "nx run-many --target=lint --all", "updateDep": "taze -r", "// ---------- 构建【beta】版本 ----------": "", "prerelease": "lerna version prerelease --conventional-prerelease  --preid SNAPSHOT --no-push --no-git-tag-version --yes", "patch": "lerna version patch --conventional-graduate --no-push --no-git-tag-version --yes", "// ---------- 构建【生产】版本 ----------": "", "build": "pnpm run build:fe && pnpm run build:flow && pnpm run build:render && pnpm run build:advanced && pnpm run build:simulator  && pnpm run build:manager && pnpm run build:zd", "build:css": "pnpm run -C packages/sinitek-lowcode-css build", "build:manager": "pnpm run -C packages/sinitek-lowcode-manager build", "build:simulator": "pnpm run -C packages/sinitek-lowcode-simulator build", "build:render": "pnpm run -C packages/sinitek-lowcode-render build", "build:materials": "pnpm run -C packages/sinitek-lowcode-materials build", "build:advanced": "pnpm run -C packages/sinitek-lowcode-advanced build", "build:flow": "pnpm run -C packages/sinitek-lowcode-flow build", "build:fe": "pnpm run -C packages/sinitek-lowcode-flow-execute build", "build:zd": "pnpm run -C packages/sinitek-lowcode-form build", "// ---------- 构建【生产 doctor】版本 ----------": "", "doctor:manager": "pnpm run -C packages/sinitek-lowcode-manager doctor", "doctor:simulator": "pnpm run -C packages/sinitek-lowcode-simulator doctor", "doctor:zd": "pnpm run -C packages/sinitek-lowcode-form doctor", "lint:m": "pnpm run -C packages/sinitek-lowcode-manager lint"}, "author": "", "license": "ISC", "devDependencies": {"@babel/eslint-parser": "^7.26.10", "@babel/preset-env": "^7.26.9", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@commitlint/cz-commitlint": "^19.8.0", "@commitlint/prompt-cli": "^19.8.0", "@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.22.0", "@rsdoctor/rspack-plugin": "^1.0.0", "@rspack/cli": "catalog:", "@rspack/core": "catalog:", "@unocss/preset-icons": "catalog:", "@unocss/preset-legacy-compat": "catalog:", "@unocss/preset-rem-to-px": "catalog:", "@unocss/webpack": "catalog:", "@vue/babel-preset-app": "^5.0.8", "babel-loader": "catalog:", "conventional-changelog-conventionalcommits": "^8.0.0", "css-loader": "^7.1.2", "dotenv": "^16.4.7", "eslint": "catalog:", "eslint-config-prettier": "^10.0.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^10.0.0", "eslint-rspack-plugin": "^4.2.1", "eslint-webpack-plugin": "^5.0.0", "husky": "^9.1.7", "jest-environment-jsdom": "^29.7.0", "lerna": "^8.2.1", "lint-staged": "^15.5.0", "lodash": "^4.17.21", "npm-run-all": "^4.1.5", "nx": "19.8.4", "postcss": "^8.5.3", "postcss-loader": "^8.1.1", "prettier": "^3.5.3", "rspack-chain": "^1.2.1", "sass": "catalog:", "sass-loader": "catalog:", "sinitek-lowcode-render": "workspace:^", "sinitek-lowcode-simulator": "workspace:^", "taze": "^19.0.2", "unocss": "catalog:", "vue-loader": "catalog:", "vue-style-loader": "^4.1.3", "webpack-merge": "^6.0.1"}, "workspaces": ["./packages/*"], "volta": {"node": "20.12.2"}, "peerDependencies": {"core-js": "catalog:", "sinitek-lowcode-render": "workspace:^", "sinitek-lowcode-simulator": "workspace:^", "vue": "catalog:"}, "dependencies": {"cross-env": "^7.0.3", "sinitek-lowcode-materials": "workspace:^", "vue": "catalog:"}, "engines": {"node": ">=18.20.0"}, "pnpm": {"overrides": {"async-validator": "catalog:", "core-js": "catalog:", "vue": "catalog:"}}}