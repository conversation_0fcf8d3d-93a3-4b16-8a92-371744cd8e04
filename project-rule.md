# 项目规则文档（cursor 项目规则引用）

## 技术栈
- **Vue 2.x** + Element UI 组件库
- **UnoCSS** 原子化样式框架  
- **Rspack** 构建工具
- **Lerna** monorepo 管理
- **ESLint + Prettier** 代码规范

## 项目架构
```
packages/
├── sinitek-lowcode-advanced/     # 高级组件
├── sinitek-lowcode-css/          # CSS样式
├── sinitek-lowcode-flow/         # 流程图组件  
├── sinitek-lowcode-form/         # 表单组件
├── sinitek-lowcode-manager/      # 管理器
├── sinitek-lowcode-materials/    # 物料库
├── sinitek-lowcode-render/       # 渲染器
├── sinitek-lowcode-shared/       # 共享工具
└── sinitek-lowcode-simulator/    # 模拟器
```

## 命名规范
- **组件名**：`LA`前缀（如LACalendar）或`ZD`前缀（如ZDForm）
- **CSS类名**：`slc-`前缀，如`slc-button`
- **CSS变量**：`--lc-`前缀，如`--lc-primary`
- **文件名**：kebab-case，如`form-item.vue`

## 目录结构规范
```
package/
├── components/           # 组件实现
│   └── component-name/
│       └── index.vue
├── materials/           # 物料定义
│   └── component-name/
│       ├── meta.js      # 物料配置
│       └── __screenshots__/
└── src/                # 源码目录
```

## 编码规范

### Vue组件
- 使用**选项式API**（Vue 2.x）
- 组件order：`name,components,props,data,computed,watch,methods,created,mounted`
- 支持JSX语法
```vue
<template>
  <div class="component-wrapper">
    <!-- 使用UnoCSS原子类 -->
  </div>
</template>

<script>
export default {
  name: 'LAComponentName',
  props: {},
  data() { return {} },
  methods: {}
}
</script>
```

### 样式规范
- 优先使用**UnoCSS原子类**
- 使用CSS变量：`var(--lc-primary)`
- 主题色变体：`primary-100`（浅色）、`primary-500`（默认）、`primary-700`（深色）

### Materials配置
```js
export default {
  componentName: 'LAComponentName',
  title: '组件标题',
  category: '分类',
  props: [...],
  snippets: [...],
  configure: {...}
}
```

## 构建规范
- 使用**rspack**构建
- 支持Vue单文件组件
- UnoCSS PostCSS插件
- Babel转译ES6+
- 生成UMD格式包

## AI助手组件规范

### 3个核心AI助手
1. **低代码AI助手** (`LAIAssistant`)
   - 位置：`sinitek-lowcode-manager`
   - 功能：AI搭建页面、服务选择、枚举选择、图片上传
   - API：`/frontend/api/lowcode/llm/form/`
   - 标签页：`generate`(AI搭建)、`help`(使用帮助)、`reset`(新对话)

2. **AI分析** (`AIAnalysis`)
   - 位置：`sinitek-lowcode-form/data-manager`
   - 功能：数据管理AI分析，生成mermaid图表
   - API：`/zhida/frontend/api/nocode/llm/agent-generate-sql`
   - 入参：`prompt`(用户输入)、`formCode`(表单代码)、`sessionId`(会话ID)
   - 返回：`text`字段包含mermaid脚本，自动渲染为图表
   - 特性：数据分析，图表生成，mermaid渲染

3. **零代码AI助手** (`ZDIAssistant`)
   - 位置：`sinitek-lowcode-form/zd/plugins`
   - 功能：零代码表单构建
   - API：`/zhida/frontend/api/nocode/llm/`
   - 特性：会话管理、图片上传、全屏查看

### AI组件通用特性
- **会话管理**：支持`sessionId`会话持续
- **消息类型**：用户消息、机器人回复、加载状态
- **拖拽定位**：可拖拽浮动窗口
- **键盘操作**：`Ctrl+Enter`发送消息
- **状态管理**：`sending`、`isWaitingForContinue`
- **图片上传**：支持多图片上传(最多6张，10MB限制)
- **图表渲染**：自动解析并渲染mermaid图表

### Mermaid图表渲染规范
- **解析规则**：识别`\`\`\`mermaid\n...\n\`\`\``代码块
- **动态加载**：自动加载mermaid.js库(CDN: jsdelivr)
- **渲染方式**：替换代码块为可渲染的div元素
- **样式支持**：`.mermaid-container`和`.mermaid`类样式
- **交互功能**：点击图表弹窗放大查看
- **弹窗样式**：全屏遮罩+居中对话框+关闭按钮
- **错误处理**：捕获渲染错误，显示友好错误信息
- **API兼容**：支持新旧mermaid API，自动降级处理
- **防重复**：使用`data-processed`属性避免重复渲染

### AI组件开发规范
```vue
<script>
export default {
  name: 'AIAssistant',
  data() {
    return {
      name: 'AI助手名称',
      showChat: false,
      activeTab: 'generate',
      sessionId: null,
      sending: false,
      messages: []
    }
  },
  methods: {
    // 通用API调用格式
    async callAIAPI(prompt) {
      const formData = new FormData()
      formData.append('prompt', prompt)
      if (this.sessionId) {
        formData.append('sessionId', this.sessionId)
      }
      return await http.post(API_ENDPOINT, formData)
    },
    
    // AI分析图表生成API调用示例
    async callAIGenerateDiagramAPI(prompt) {
      const formData = new FormData()
      formData.append('prompt', prompt)
      formData.append('formCode', this.DM.formcode) // 获取formCode
      if (this.sessionId) {
        formData.append('sessionId', this.sessionId)
      }
      return await http.post('/zhida/frontend/api/nocode/llm/agent-generate-sql', formData)
    },
    
    // mermaid渲染处理（带错误处理）
    async initMermaidElements(elements) {
      try {
        window.mermaid.initialize({ startOnLoad: false, theme: 'default' })
        
        for (let i = 0; i < elements.length; i++) {
          const element = elements[i]
          const graphDefinition = element.textContent.trim()
          
          if (!graphDefinition || graphDefinition.length < 5) {
            element.innerHTML = '<div style="color: #ef4444;">图表定义无效</div>'
            continue
          }
          
          element.innerHTML = ''
          
          try {
            // 新API优先
            const { svg } = await window.mermaid.render(element.id + '_svg', graphDefinition)
            element.innerHTML = svg
            element.setAttribute('data-processed', 'true')
          } catch (renderError) {
            // 旧API降级
            window.mermaid.render(element.id + '_svg', graphDefinition, (svgCode) => {
              element.innerHTML = svgCode
              element.setAttribute('data-processed', 'true')
            })
          }
        }
        
        setTimeout(() => this.addMermaidClickEvents(), 100)
      } catch (error) {
        console.error('Mermaid初始化失败:', error)
      }
    },
    
    // 处理mermaid弹窗事件
    handleShowMermaidModal(event) {
      const { id, code } = event.detail
      if (id && code) {
        const modalMermaidId = `mermaid-modal-${Date.now()}`
        this.mermaidModalContent = `<div id="${modalMermaidId}" class="mermaid">${code}</div>`
        this.showMermaidModal = true
        
        this.$nextTick(() => {
          if (typeof window.mermaid !== 'undefined') {
            const element = document.getElementById(modalMermaidId)
            if (element) window.mermaid.init(undefined, element)
          }
        })
      }
    }
  }
}
</script>
```