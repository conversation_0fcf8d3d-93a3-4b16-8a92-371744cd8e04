/** @type {import('vls').VeturConfig} */
module.exports = {
  // support monorepos
  projects: [
    {
      root: './packages/sinitek-lowcode-simulator',
      package: './package.json',
      tsconfig: './jsconfig.json',
    },
    {
      root: './packages/sinitek-lowcode-advanced',
      package: './package.json',
      tsconfig: './jsconfig.json',
    },
    {
      root: './packages/sinitek-lowcode-manager',
      package: './package.json',
      tsconfig: './jsconfig.json',
    },
    {
      root: './packages/sinitek-lowcode-flow',
      package: './package.json',
      tsconfig: './jsconfig.json',
    },
  ],
}
